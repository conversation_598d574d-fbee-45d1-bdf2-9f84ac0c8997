import { test, expect } from "@playwright/test"

import { navigateFromPowermenu } from "../utils/testUtils"

test.describe("Calendar Navigation from Power Menu", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("http://localhost:3000/")
    await page.waitForLoadState()
  })

  test("Should open Provider Week view from Calendar in power menu", async ({
    page,
  }) => {
    // Arrange
    // Act
    await navigateFromPowermenu(page, "Calendar", ["calendar"])

    // Assert
    await expect(page).toHaveURL(
      new RegExp(
        /calendar\/week\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/.source
      )
    )
  })

  test("Should open Provider Week view from power menu", async ({ page }) => {
    // Arrange
    // Act
    await navigateFromPowermenu(page, "Calendar", ["calendar"])
    await navigateFromPowermenu(page, "Week", ["week"])

    // Assert
    await expect(page).toHaveURL(
      new RegExp(
        /calendar\/week\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/.source
      )
    )
  })

  test("Should open Provider Day view from power menu", async ({ page }) => {
    // Arrange
    // Act
    await navigateFromPowermenu(page, "Calendar", ["calendar"])
    await navigateFromPowermenu(page, "Day", ["day"])

    // Assert
    await expect(page).toHaveURL(new RegExp(/calendar\/day/))
  })

  test("Should open Provider Month view from power menu", async ({ page }) => {
    // Arrange
    // Act
    await navigateFromPowermenu(page, "Calendar", ["calendar"])
    await navigateFromPowermenu(page, "Month", ["month"])

    // Assert
    await expect(page).toHaveURL(
      new RegExp(
        /calendar\/month\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/.source
      )
    )
  })
})

test.describe("Calendar Navigation from other than Power menu", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("http://localhost:3000/")
    await page.waitForLoadState()
  })

  test("Should open Provider Week view from Provider Box", async ({ page }) => {
    // Arrange
    // Act
    await page.click('[data-testid="provider-box-calendar"]')

    // Assert
    await expect(page).toHaveURL(
      new RegExp(/calendar\/week\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/)
    )
  })
})
