import { ReactNode } from "react"

import usePermissions from "features/authentication/hooks/usePermissions"

import { PermissionKey } from "generated/graphql"

export type RestrictedProps =
  | {
      to: PermissionKey | PermissionKey[]
      toAny?: undefined
      children: ReactNode
      fallback?: ReactNode
    }
  | {
      to?: undefined
      toAny: PermissionKey[]
      children: ReactNode
      fallback?: ReactNode
    }

export default function Restricted({
  to,
  toAny,
  children,
  fallback,
}: RestrictedProps) {
  const { hasAllPermissions, hasSomePermissions } = usePermissions()
  if (toAny) {
    if (hasSomePermissions(toAny)) {
      return <>{children}</>
    }

    return <>{fallback}</>
  }
  if (to) {
    if (hasAllPermissions(new Array<PermissionKey>().concat(to))) {
      return <>{children}</>
    }

    return <>{fallback}</>
  }

  return null
}
