import { useCallback, useEffect, useRef, useState } from "react"
import { useIdle } from "react-use"
import { v4 as uuid } from "uuid"

import { logException } from "lib/sentry/sentry"

// 24 mins is the minimum time where the refresh token outlives the login token
// We want useDetectIdle to manage the logout, so we need to make sure that the
// idle timeout is less than the refresh token expiry
const twentyFourMinutes = 24 * 60 * 1000

const events = {
  newTab: "newTab" as const,
  reportIdle: "reportIdle" as const,
  logout: "logout" as const,
}
type IdleMessage =
  | {
      type: typeof events.newTab
      payload: string
    }
  | {
      type: typeof events.reportIdle
      payload: [tabId: string, isIdle: boolean]
    }
  | {
      type: typeof events.logout
      payload?: undefined
    }
const tabId = uuid()

/**
 * A custom hook that detects user inactivity (idle state) across multiple browser tabs
 * and optionally triggers a deauthentication callback when all tabs are idle.
 *
 * This hook uses the `BroadcastChannel` API to synchronize idle states between tabs
 * and ensures that the idle state is consistent across the application.
 *
 * @param deauthenticate - An optional callback function that is invoked when all tabs
 * are detected to be idle. This can be used to log out the user or perform other
 * deauthentication-related actions. When called from this hook, it will be passed a boolean
 * parameter (true) to indicate the logout is due to idle timeout.
 *
 * @returns A boolean value indicating whether the current tab is idle (`true`) or active (`false`).
 *
 * @remarks
 * - The idle timeout duration can be overridden by appending a query parameter
 *   `isolemnlyswearthatiamuptonogood` to the URL. The value should be in seconds.
 * - The maximum idle timeout is capped at 24 minutes.
 * - The hook ensures that the `BroadcastChannel` is safely managed and recreated
 *   if necessary to handle potential errors.
 *
 */
export function useDetectIdle(
  deauthenticate?: (fromIdleTimeout?: boolean) => void
) {
  // This is so that we can test the idle timeout in without having to wait 15 minutes
  const searchParams = new URLSearchParams(window.location.search)
  const loginTimeoutParam =
    Number(searchParams.get("isolemnlyswearthatiamuptonogood")) ||
    twentyFourMinutes
  // Allow maximum of 24 minutes
  const idleTimeoutMs = Math.min(loginTimeoutParam * 1000, twentyFourMinutes)
  // Save to state so that it persists navigation
  const [idleTimeout] = useState(idleTimeoutMs)

  const isIdle = useIdle(idleTimeout)
  const [idleState, setIdleState] = useState({ [tabId]: isIdle })

  // Create BroadcastChannel inside the hook using a ref
  const channelRef = useRef<BroadcastChannel | null>(null)

  // Safe function to post messages that handles closed channels
  const safePostMessage = useCallback((message: IdleMessage) => {
    if (!channelRef.current) {
      channelRef.current = new BroadcastChannel("idleState")
    }
    try {
      channelRef.current.postMessage(message)
    } catch (error) {
      // Attempt to recreate the channel for future messages
      try {
        if (channelRef.current) {
          channelRef.current.close()
        }
        channelRef.current = new BroadcastChannel("idleState")
      } catch (e) {
        logException(e)
      }
    }
  }, [])

  useEffect(() => {
    // Idle state changed, update the state
    // This will be called when the user becomes idle or active
    setIdleState((prevState) => {
      return { ...prevState, [tabId]: isIdle }
    })
    // Broadcast the idle state to other tabs using the safe method
    safePostMessage({
      type: events.reportIdle,
      payload: [tabId, isIdle],
    })
  }, [isIdle, safePostMessage])

  useEffect(() => {
    // Initialize the channel
    if (!channelRef.current) {
      channelRef.current = new BroadcastChannel("idleState")
    }

    // When a new tab is opened, notify all other tabs so that they can send their idle state
    safePostMessage({
      type: events.newTab,
      payload: tabId,
    })

    const messageHandler = (event: MessageEvent<IdleMessage>) => {
      if (event.data.type === events.newTab) {
        // When a new tab is opened, send the current idle state of this tab to the new tab
        safePostMessage({
          type: events.reportIdle,
          payload: [tabId, isIdle],
        })
        return
      }
      if (event.data.type === events.reportIdle) {
        const [id, idle] = event.data.payload

        setIdleState((prevState) => {
          return { ...prevState, [id]: idle }
        })
      }
      if (event.data.type === events.logout) {
        deauthenticate?.(true)
      }
    }

    // Add event listener to the channel
    channelRef.current.addEventListener("message", messageHandler)

    const beforeUnloadHandler = () => {
      // Send a message to all other tabs that this tab is closing
      try {
        if (channelRef.current) {
          channelRef.current.postMessage({
            type: events.reportIdle,
            payload: [tabId, true],
          })
          channelRef.current.close()
          channelRef.current = null
        }
      } catch (e) {
        logException(e)
      }
    }

    window.addEventListener("beforeunload", beforeUnloadHandler)

    // Clean up function
    return () => {
      if (channelRef.current) {
        channelRef.current.removeEventListener("message", messageHandler)
        try {
          channelRef.current.close()
          channelRef.current = null
        } catch (e) {
          logException(e)
        }
      }
      window.removeEventListener("beforeunload", beforeUnloadHandler)
    }
  }, [isIdle, safePostMessage, deauthenticate])

  useEffect(() => {
    const allTabsIdle = Object.values(idleState).every((idle) => idle)
    if (allTabsIdle) {
      // If all tabs are idle, deauthenticate
      deauthenticate?.(true)
    }
  }, [idleState, deauthenticate])

  return isIdle
}

// Helper to broadcast logout event to all tabs using the same channel
export function broadcastLogout() {
  try {
    const channel = new BroadcastChannel("idleState")
    channel.postMessage({ type: events.logout })
    channel.close()
  } catch (e) {
    // Ignore errors
  }
}
