.modal {
  width: 460px;
}

.modalContent {
  display: grid;
  gap: 16px;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.hiddenContent {
  display: none;
}

.subjectLabel {
  width: 89mm;
  height: 36mm;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
}

.button {
  margin-right: auto;
}

/* Add print-specific styles */
@media print {
  body {
    margin: 0;
    padding: 0;
  }

  .subjectLabel {
    /* Ensure dimensions are exact when printing */
    width: 89mm !important;
    height: 36mm !important;
    page-break-inside: avoid;
  }

  /* This prevents page numbers from appearing in the footer */
  @page {
    size: 89mm 36mm;
    margin: 0;
  }
}
