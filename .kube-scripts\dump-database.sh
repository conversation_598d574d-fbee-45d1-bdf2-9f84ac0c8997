#!/bin/bash


if [ -z $1 ]; then
  echo "Please set secret";
  kubectl get secret
  exit 1
fi

if [ -z $2 ]; then
  echo "Please set variant";
  exit 1
fi


secret=$1

VARIANT=$2


dir="/tmp/leviosa/database/dumps"
mkdir -p $dir

file="${dir}/leviosa-database-$(date '+%Y.%m.%d-%s').sql"

pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)

echo $pod $secret

kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$secret pg_dump  -U postgres leviosa" > $file