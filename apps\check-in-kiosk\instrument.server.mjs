import * as Sentry from "@sentry/remix"
import "dotenv/config"
import process from "node:process"

Sentry.init({
  dsn: process.env.VITE_SENTRY_DSN,
  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for tracing.
  // We recommend adjusting this value in production
  // Learn more at
  // https://docs.sentry.io/platforms/javascript/configuration/options/#traces-sample-rate
  tracesSampleRate: Number(process.env.VITE_SENTRY_TRACES_SAMPLE_RATE) || 1,
  sampleRate: Number(process.env.VITE_SENTRY_SAMPLE_RATE) || 0.05,
  profilesSampleRate:
    Number(process.env.VITE_SENTRY_PROFILES_SAMPLE_RATE) || 0.05,

  // To use Sentry OpenTelemetry auto-instrumentation
  // default: false
  autoInstrumentRemix: true,

  environment: process.env.VITE_SENTRY_ENVIRONMENT || "development",

  // COMEBACK: Not sure if we want to include <PERSON><PERSON> so commenting out for now
  // Optionally capture action formData attributes with errors.
  // This requir `sendDefaultPii` set to true as well.
  // captureActionFormDataKeys: {
  //   key_x: true,
  //   key_y: true,
  // },
  // To capture action formData attributes.
  // sendDefaultPii: true,
})
