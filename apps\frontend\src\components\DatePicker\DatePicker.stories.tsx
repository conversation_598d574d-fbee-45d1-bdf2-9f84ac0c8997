import { Meta, StoryFn } from "@storybook/react-vite"

import { DatePicker, DatePickerProps } from "./DatePicker"

export default {
  title: "Form Components/DatePicker",
  component: DatePicker,
} as Meta<typeof DatePicker>

export const DatePickerDefault: StoryFn<DatePickerProps> = (args) => {
  const [value, setValue] = React.useState("")

  return (
    <DatePicker
      {...args}
      label="Example"
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  )
}
export const DatePickerUncontrolled: StoryFn<DatePickerProps> = (args) => {
  return <DatePicker {...args} label="Example" />
}
