import { useTranslation } from "react-i18next"

import styles from "./OrganisationPriceListTableRowHeader.module.css"

export const OrganisationPriceListTableRowHeader = () => {
  const { t } = useTranslation()

  return (
    <>
      <th>{t("Item ID")}</th>
      <th>{t("Item Name")}</th>
      <th className={styles.numericValue}>{t("Units")}</th>
      <th className={styles.numericValue}>{t("Units Price")}</th>
      <th className={styles.currency}>{t("Currency")}</th>
      <th className={styles.numericValue}>{t("VAT %")}</th>
    </>
  )
}
