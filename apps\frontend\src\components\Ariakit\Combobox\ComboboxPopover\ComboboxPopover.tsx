import {
  ComboboxPopoverProps as ComboboxPopoverPropsAriakit,
  ComboboxPopover as ComboboxPopoverAriakit,
} from "@ariakit/react"
import { forwardRef } from "react"

import {
  AnimatedPopover,
  AnimatedPopoverProps,
} from "components/Popover/Popover"

import styles from "./ComboboxPopover.module.css"

export type ComboboxPopoverProps = ComboboxPopoverPropsAriakit

export const ComboboxPopover = forwardRef<
  HTMLDivElement,
  ComboboxPopoverPropsAriakit & AnimatedPopoverProps
>((props, ref) => {
  const { sameWidth = true, ...rest } = props
  return (
    <ComboboxPopoverAriakit
      render={<AnimatedPopover />}
      sameWidth={sameWidth}
      className={styles.popover}
      gutter={4}
      ref={ref}
      unmountOnHide
      {...rest}
    />
  )
})
