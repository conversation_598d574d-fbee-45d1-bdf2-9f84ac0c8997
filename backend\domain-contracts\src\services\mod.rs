//! Service contracts for inverse dependency architecture
//!
//! This module contains all service interface contracts that other layers
//! can depend on without depending on the domain implementation.

pub mod email;
pub mod pdf_generator;
pub mod nhi_service;
pub mod service_communicator;
pub mod national_registry;
pub mod notification_service;
pub mod customer_service;
pub mod calendar_notification;
pub mod doctor_letter_api;
pub mod external_organisation;
pub mod online_payment;
pub mod oracle_api;
pub mod prescription_api;
pub mod file_repo;
pub mod audit_logger;

// Re-export all service contracts
pub use email::*;
pub use pdf_generator::*;
pub use nhi_service::*;
pub use service_communicator::*;
pub use national_registry::*;
pub use notification_service::*;
pub use customer_service::*;
pub use calendar_notification::*;
pub use doctor_letter_api::*;
pub use external_organisation::*;
pub use online_payment::*;
pub use oracle_api::*;
pub use prescription_api::*;
pub use file_repo::*;
pub use audit_logger::*;
