import c from "classnames"
import { ReactNode, useState } from "react"
import { useTranslation } from "react-i18next"

import { useAttachmentContext } from "components/AttachmentsPreview/AttachmentsPreview"
import Icon from "components/Icon/Icon"
import { useAttachmentRest } from "hooks/useAttachmentRest"
import { color } from "styles/colors"
import { Button, notification, TextWithIcon, Text } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import {
  getFileTypeFromExtension,
  getIcon,
  isInSupportedPreviewExtensions,
} from "utils/attachmentIconMapper"
import { printDocument } from "utils/printDocument"

import { useDeleteBlockMutation } from "generated/graphql"

import styles from "./AttachmentButton.module.css"
import { useSensitiveDownloadAgreement } from "./ConfirmSensitiveDownloadDialog"
import ConfirmSensitiveDownloadDialog from "./ConfirmSensitiveDownloadDialog"

export type AttachmentButtonProps = {
  fileUrl: string
  isSensitiveData?: boolean
  color?: "green" | "blue" | "pink" | "white"
  children: ReactNode
  onDelete?: () => Promise<void>
  className?: string
  isDeleting?: boolean
  extension: string
}

const colorMap = {
  green: color.levGreen.light,
  blue: color.blue.light,
  pink: color.pink.light,
  white: color.levBlue,
}

export const AttachmentButton = ({
  fileUrl,
  isSensitiveData = true,
  onDelete,
  children,
  color = "white",
  className,
  isDeleting,
  extension,
}: AttachmentButtonProps) => {
  const { t } = useTranslation()
  const { openAttachment } = useAttachmentContext()
  const [hasAgreedSensitive] = useSensitiveDownloadAgreement()

  const [isAttachmentDeleteOpen, setIsAttachmentDeleteOpen] = useState(false)
  const [showSensitiveModal, setShowSensitiveModal] = useState(false)

  const fileType = getFileTypeFromExtension(extension)
  const canPreview = isInSupportedPreviewExtensions(extension) && openAttachment

  const canPrint =
    (fileType === "document" || fileType === "image") &&
    isInSupportedPreviewExtensions(extension)

  return (
    <>
      <div className={c(styles.wrapper, className)}>
        {!canPreview && isSensitiveData && !hasAgreedSensitive ? (
          <Text
            as="button"
            size="small"
            className={c(styles.button, colorMap[color])}
            onClick={() => setShowSensitiveModal(true)}
          >
            <Icon name={getIcon(fileType)} className={styles.icon} />
            <div>{children}</div>
          </Text>
        ) : (
          <Text
            as="a"
            size="small"
            href={fileUrl}
            className={c(styles.button, colorMap[color])}
            download
            onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
              if (!canPreview) return
              e.preventDefault()
              openAttachment(fileUrl)
            }}
          >
            <Icon name={getIcon(fileType)} className={styles.icon} />
            <div>{children}</div>
          </Text>
        )}
        <div className={styles.actionIcons}>
          {canPrint && (
            <Button
              variant="clear"
              className={c(styles.iconButton, colorMap[color])}
              icon={<Icon name={"printer-line"} />}
              onClick={() => printDocument(fileUrl)}
            />
          )}

          {onDelete && (
            <Button
              variant="clear"
              className={c(styles.iconButton, colorMap[color])}
              icon={<Icon name={"delete-bin-line"} />}
              onClick={() => setIsAttachmentDeleteOpen(true)}
            />
          )}
        </div>
      </div>
      <ConfirmSensitiveDownloadDialog
        isOpen={showSensitiveModal}
        onClose={() => setShowSensitiveModal(false)}
        fileUrl={fileUrl}
      />
      <Dialog
        title={t("Delete Attachment")}
        isOpen={isAttachmentDeleteOpen}
        onClose={() => setIsAttachmentDeleteOpen(false)}
        actions={
          <div className={styles.actions}>
            <Button onClick={() => setIsAttachmentDeleteOpen(false)}>
              {t("Cancel")}
            </Button>
            <Button
              icon={isDeleting ? <Icon name="loader-4-line" spin /> : null}
              onClick={async () => {
                await onDelete?.()

                setIsAttachmentDeleteOpen(false)
              }}
            >
              {t("Delete")}
            </Button>
          </div>
        }
      >
        <TextWithIcon iconName="information-line">
          {t("Are you sure you want to delete this attachment?")}
        </TextWithIcon>
      </Dialog>
    </>
  )
}

export const JournalAttachmentButton = ({
  blockId,
  fileId,
  ...rest
}: { blockId: string; fileId: string } & Omit<
  AttachmentButtonProps,
  "fileUrl" | "onDelete"
>) => {
  const { t } = useTranslation()

  const { getUrlForFile } = useAttachmentRest()

  const [deleteAttachment] = useDeleteBlockMutation({
    variables: { blockId },
    onCompleted: () => {
      notification.create({
        message: t("Attachment deleted successfully"),
        status: "success",
      })
    },
    onError: () => {
      notification.create({
        message: t("The attachment wasn’t deleted, please try again later."),
        status: "error",
      })
    },
  })

  const handleDelete = async () => {
    await deleteAttachment()
  }

  return (
    <AttachmentButton
      fileUrl={getUrlForFile(fileId)}
      color="white"
      onDelete={handleDelete}
      {...rest}
    />
  )
}
