import { CellContext } from "@tanstack/react-table"
import { useState } from "react"
import { useTranslation } from "react-i18next"

import { Input } from "ui"

import { useUpdateRoomMutation } from "generated/graphql"

import { LocationsTableData } from "../LocationsTable"
import styles from "./LocationTableCell.module.css"

type LocationTableCellProps = CellContext<LocationsTableData, unknown>

export const LocationTableCell = ({
  getValue,
  row: {
    original: { id: locationId },
  },
  column: { id },
}: LocationTableCellProps) => {
  const initialValue = getValue<string>()
  const [value, setValue] = useState(initialValue)
  const { t } = useTranslation()

  const [updateRoom, { loading }] = useUpdateRoomMutation()
  const handleBlur = () => {
    if (id === "capacity") {
      const capacity = Number(value)

      updateRoom({
        variables: {
          input: {
            id: locationId,
            capacity: {
              set: isNaN(capacity) ? null : capacity,
            },
          },
        },
      })

      return
    }

    updateRoom({
      variables: {
        input: {
          id: locationId,
          [id]: value || null,
        },
      },
    })
  }

  switch (id) {
    case "label":
      return (
        <Input
          label={t("label")}
          hideLabel
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onBlur={handleBlur}
          isLoading={loading}
          variant="clear"
        />
      )
    case "capacity":
      return (
        <Input
          label={t("Capacity")}
          hideLabel
          type={"number"}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onBlur={handleBlur}
          isLoading={loading}
          variant="clear"
          className={styles.capacityInput}
        />
      )
  }

  return <>{value}</>
}
