.wrap {
  display: flex;
  align-items: center;
  grid-gap: 8px;
  color: var(--color-text);
}

.userInfo {
  display: flex;
  flex-direction: column;
}

.userInfo a {
  color: var(--color-text);
  text-decoration: none;
}

.removeButton svg {
  font-size: 16px;
  flex-shrink: 0;
}

.info {
  border-radius: var(--radius-button-half);
  height: 24px;
  width: 24px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  align-self: start;
  flex-shrink: 0;
}

.info:has(.conflictIcon) {
  background-color: var(--color-critical-200);
}

.info:has(.missingInformationIcon) {
  background-color: var(--color-warning-200);
}

.info:has(.infoIcon) {
  background-color: var(--color-lev-blue-on-white-hover);
}

.info > svg {
  flex-shrink: 0;
  font-size: 14px;
}

.conflictIcon {
  color: var(--color-critical-500);
}

.tooltip,
.infoTooltip {
  padding: 8px;
}

.missingInformationIcon {
  color: var(--color-warning-800);
}

.infoIcon {
  color: var(--color-brand-primary-blue);
}
