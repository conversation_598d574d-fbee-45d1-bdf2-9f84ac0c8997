// import styles from './PopoverInfo.module.css';
import { ElementType } from "react"

import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"
import { Label, TextProps } from "ui"
import { ElementTypeAcceptingClassName } from "utils/polymorphicTypes"

import styles from "./Popover.module.css"

const defaultElement = "div"

export type PopoverInfoProps<E extends ElementType = typeof defaultElement> = {
  iconName?: IconName
} & Omit<TextProps<E>, "ref">

export default function PopoverInfo<
  E extends ElementTypeAcceptingClassName = "div",
>({ iconName, className = "", children, ...rest }: PopoverInfoProps<E>) {
  return (
    <Label size="small" className={`${className} ${styles.info}`} {...rest}>
      {iconName && <Icon name={iconName} className={styles.infoIcon} />}
      {children}
    </Label>
  )
}
