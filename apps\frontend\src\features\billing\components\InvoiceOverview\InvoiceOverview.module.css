.container {
  min-height: calc(100vh - var(--header-height) - var(--grid-gap));
}

.invoiceHeaderWrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
}

.searchFilters {
  display: flex;
  gap: 16px;
  width: calc(100% / 3 - 16px);
}

/* 36px is width of search filter button */
.searchInput {
  width: 100%;
}

.sidebarWrap {
  grid-column: span 3;
  height: 100%;
  max-width: 400px;
}

.invoiceWrap {
  grid-column: 4 / span 9;
}

@media (min-width: 2000px) {
  .invoiceWrap {
    grid-column: 4 / span 8;
  }
}

@media (max-width: 1280px) {
  .invoiceWrap {
    grid-column: 5 / span 12;
  }
}
