.wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  grid-auto-flow: column;
}

.radioInput {
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  grid-row: 2;
  text-align: center;
  width: 80px;
  height: 80px;
  border-radius: 10px;
  border: 2px dashed var(--color-main);
  cursor: pointer;
  display: inline-block;
  transition:
    all 0.3s ease,
    transform 0.1s ease;
}

.radioInput:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

.radioInput:checked {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  border: 2px solid var(--color-main);
}

.wrap label {
  grid-row: 1;
}

.radioInputDisabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.radioInputDisabled:hover {
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.radioInputDisabled:checked {
  transform: none;
  box-shadow: none;
}
