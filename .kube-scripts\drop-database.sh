#!/bin/bash


if [ -z $1 ]; then
  echo "Please select variant";
  exit 1
fi

VARIANT=$1

DB_PASSWORD=$(kubectl get secret --selector=app=database,variant=$VARIANT -o json  | jq '.items[0].data["postgres-password"]' -r  | base64 -d)
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)

echo "I will connect to $pod"
source "$SCRIPT_DIR/.sure.sh"

echo $DB_PASSWORD

sql='
DROP DATABASE leviosa WITH (FORCE);
'

echo "Will execute '$sql' on the primary database $pod"

source "$SCRIPT_DIR/.sure.sh"



echo $pod
kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql  -U postgres -c '$sql' " 