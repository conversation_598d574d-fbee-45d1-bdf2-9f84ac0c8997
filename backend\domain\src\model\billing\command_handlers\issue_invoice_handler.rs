use super::DownloadInvoicePdfHandler;
use crate::{
    accounts::{GetProviderFilter, GetSubjectFilter},
    auth::AuthenticatedUser,
    billing::{
        translate, BillingCodeNhiId, BillingCodeType, GetInvoiceFilter, Invoice, InvoiceId,
        LocaleKey, NhiLog, NhiLogId,
    },
    command::CommandHandler,
    email::{EmailAttachment, EmailSendInput, EmailSender},
    errors::{CustomError, Result},
    file_repo::IFileRepo,
    i18n::LanguageId,
    nhi_service::{Code, MedicalBill, NhiService, NhiSubmissionResult},
    pdf_generator::PdfGenerator,
    permissions::PermissionKey,
    repo_connection::{IRepoConnection, IRepoTransaction, ITransactionManager},
};
use chrono::Utc;
use std::sync::Arc;

pub struct IssueInvoiceCommand {
    pub id: InvoiceId,
}

pub struct IssueInvoiceHandler<'a, T: ITransactionManager> {
    tx_manager: T,
    user: AuthenticatedUser,
    token: String,
    language_id: LanguageId,
    nhi_service: Arc<dyn NhiService>,
    file_repo: &'a dyn IFileRepo,
    pdf_generator: &'a dyn PdfGenerator,
    email_sender: &'a dyn EmailSender,
}

#[async_trait::async_trait]
impl<T: ITransactionManager> CommandHandler<IssueInvoiceCommand> for IssueInvoiceHandler<'_, T> {
    type Model = Invoice;

    fn required_permission() -> PermissionKey {
        PermissionKey::Billing_Invoice_Create
    }

    async fn execute(&self, command: IssueInvoiceCommand) -> Result<Invoice> {
        let tx = self.tx_manager.transaction().await?;
        let invoice = {
            let invoice_repo = tx.repos().invoice_repo();
            let invoice = invoice_repo.get(GetInvoiceFilter::Id(command.id)).await?;
            let updated_invoice = Invoice::issue(invoice, &self.user);
            let invoice = invoice_repo.save(updated_invoice).await?;
            let email_result = self
                .send_invoice_via_email(&invoice, &tx, self.language_id)
                .await;
            if let Err(err) = email_result {
                tracing::error!("Failed to send invoice email: {:?}", err);
            }
            self.send_to_nhi(&tx, invoice).await?
        };
        tx.commit().await?;

        Ok(invoice)
    }
}

impl<'a, T: ITransactionManager> IssueInvoiceHandler<'a, T> {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        tx_manager: T,
        user: AuthenticatedUser,
        token: String,
        language_id: LanguageId,
        nhi_service: Arc<dyn NhiService>,
        file_repo: &'a dyn IFileRepo,
        pdf_generator: &'a dyn PdfGenerator,
        email_sender: &'a dyn EmailSender,
    ) -> Self {
        Self {
            tx_manager,
            user,
            token,
            language_id,
            nhi_service,
            file_repo,
            pdf_generator,
            email_sender,
        }
    }

    async fn send_invoice_via_email(
        &self,
        invoice: &Invoice,
        tx: &<T as ITransactionManager>::Transaction,
        language_id: LanguageId,
    ) -> Result<()> {
        if !invoice.send_invoice_mail.unwrap_or(false) || invoice.payer_email.is_none() {
            return Ok(());
        }
        let org_repo = tx.repos().organisation_repo();
        let org = org_repo.get(self.user.organisation_id()).await?;

        let subject_repo = tx.repos().subject_repo();
        let subject = subject_repo
            .get(GetSubjectFilter::Id {
                id: invoice.subject_id(),
            })
            .await?;

        let pdf = DownloadInvoicePdfHandler::<T::Transaction>::get_invoice_pdf(
            invoice,
            &self.user,
            &self.token,
            self.pdf_generator,
            self.file_repo,
        )
        .await?;

        self.email_sender
            .send(EmailSendInput {
                to_email: invoice.payer_email.clone().unwrap(),
                to_name: subject.name.value().to_string(),
                subject_template: translate(LocaleKey::InvoiceEmailSubject, language_id)
                    .to_string(),
                body_template: translate(LocaleKey::InvoiceEmailBody, language_id).to_string(),
                params: [("org_name".to_string(), org.name().clone())].into(),
                attachments: vec![EmailAttachment {
                    content_type: "application/pdf".to_string(),
                    filename: "invoice.pdf".to_string(),
                    content: pdf.content,
                }],
                from_email: Some("<EMAIL>".to_string()),
                from_name: Some(org.name().to_string()),
            })
            .await?;

        Ok(())
    }

    async fn send_to_nhi(
        &self,
        tx: &<T as ITransactionManager>::Transaction,
        invoice: Invoice,
    ) -> Result<Invoice> {
        let mut credit = false;
        if invoice.total().is_some()
            && invoice.total().unwrap() < 0.0
            && invoice.invoice_number().starts_with('C')
        {
            credit = true;
        }
        let codes = Self::get_codes(tx, invoice.id(), credit).await?;
        if codes.is_empty() {
            return Ok(invoice);
        }

        let subject_repo = tx.repos().subject_repo();
        let organisation_repo = tx.repos().organisation_repo();
        let provider_repo = tx.repos().provider_repo();
        let invoice_issuer_repo = tx.repos().invoice_issuer_repo();
        let nhi_config_repo = tx.repos().nhi_config_repo();

        let nhi_config = nhi_config_repo.get_by_current_organisation().await?;

        let subject = subject_repo
            .get(GetSubjectFilter::Id {
                id: invoice.subject_id(),
            })
            .await?;
        let org = organisation_repo.get(subject.organisation_id()).await?;

        let provider_id = match invoice.provider_id() {
            Some(provider_id) => provider_id,
            None => {
                return Err(CustomError!("Provider id is missing".to_string()).into());
            }
        };

        let treatment_date = match invoice.treatment_date() {
            Some(treatment_date) => treatment_date,
            None => {
                return Err(CustomError!("Treatment date is missing".to_string()).into());
            }
        };

        let nhi_subject_total = invoice
            .nhi_payable_by_subject()
            .ok_or(CustomError!("NHI payable by subject is missing".to_string()))?;

        let payed_by_patient = nhi_subject_total
            .max(i64::MIN as f64)
            .min(i64::MAX as f64)
            .round() as i64;

        let payed_by_insurance = match invoice.total_payable_by_insurance {
            Some(payed_by_insurance) => payed_by_insurance as i64,
            None => {
                return Err(
                    CustomError!("Total payable by insurance is missing".to_string()).into(),
                );
            }
        };
        let owner_person_id = match invoice.issuer_id() {
            Some(issuer_id) => {
                let invoice_issuer = invoice_issuer_repo.get(issuer_id).await?;
                match invoice_issuer.persona_id() {
                    Some(persona_id) => persona_id.clone(),
                    None => {
                        return Err(CustomError!("Persona id is missing".to_string()).into());
                    }
                }
            }
            None => org.registration_number().clone(),
        };

        let provider = provider_repo
            .get(GetProviderFilter::Id(provider_id))
            .await?;

        let doctor_number_str: String = match provider.doctor_number() {
            Some(doctor_number) => doctor_number.to_string(),
            None => {
                return Err(CustomError!("Doctor number is missing".to_string()).into());
            }
        };

        let doctor_number = match doctor_number_str.parse::<i64>() {
            Ok(doctor_number) => doctor_number,
            Err(_) => {
                return Err(CustomError!("Doctor number must be a number".to_string()).into());
            }
        };

        let medical_bill = MedicalBill {
            owner_person_id,
            clinic_registration_number: nhi_config
                .clinic_registration_number
                .clone()
                .unwrap_or(org.registration_number().clone()),
            issuer_person_id: provider.persona_id().value().to_string(),
            patient_person_id: subject.persona_id().value().to_string(),
            treatment_date,
            payed_by_patient,
            payed_by_insurance,
            receipt_number: invoice.invoice_number().to_string(),
            doctor_number,
            codes,
            radnumer_si: invoice.radnumer_si(),
        };

        if payed_by_insurance != 0 {
            let submission_result = self
                .nhi_service
                .submit_invoice(
                    medical_bill,
                    nhi_config.on_behalf_of().as_str(),
                    self.token.as_str(),
                )
                .await;

            match submission_result {
                Ok(result) => {
                    Self::log_nhi_response(tx, invoice.id(), &result).await?;
                }
                Err(e) => {
                    return Err(e);
                }
            }
        }
        Ok(invoice)
    }

    async fn log_nhi_response(
        tx: &<T as ITransactionManager>::Transaction,
        invoice_id: InvoiceId,
        result: &NhiSubmissionResult,
    ) -> Result<NhiLog> {
        let log_entry = NhiLog {
            invoice_id: invoice_id.into(),
            request_xml: Some(result.request_xml.clone()),
            request_json: Some(result.request_json.clone()),
            response_xml: Some(result.response_xml.clone()),
            error_message: None,
            created_at: Utc::now(),
            id: NhiLogId::generate(),
        };
        let nhi_billing_log_repo = tx.repos().nhi_billing_log_repo();
        let result = nhi_billing_log_repo.save(log_entry).await?;
        Ok(result)
    }

    async fn get_codes(
        tx: &<T as ITransactionManager>::Transaction,
        invoice_id: InvoiceId,
        credit: bool,
    ) -> Result<Vec<Code>> {
        let invoice_line_repo = tx.repos().invoice_line_repo();
        let invoice_lines = invoice_line_repo.get_by_invoice_ids(&[invoice_id]).await?;

        let billing_code_nhi_repo = tx.repos().billing_code_nhi_repo();

        let nhi_lines = invoice_lines
            .iter()
            .filter(|line| line.billing_code_type() == BillingCodeType::Nhi)
            .collect::<Vec<_>>();

        let mut codes: Vec<Code> = vec![];
        for line in nhi_lines {
            let billing_code_nhi = billing_code_nhi_repo
                .get(BillingCodeNhiId::from(line.billing_code_id()))
                .await?;
            codes.push(Code {
                code: billing_code_nhi.code().to_string(),
                quantity: line.quantity() as i64,
                credit,
            });
        }
        Ok(codes)
    }
}
