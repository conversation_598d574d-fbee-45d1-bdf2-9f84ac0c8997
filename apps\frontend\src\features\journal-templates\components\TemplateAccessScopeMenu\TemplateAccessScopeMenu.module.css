.wrap {
  display: flex;
}

.menuButton {
  border: none;
  border-radius: 0 var(--radius-button-half);
}

.menu {
  padding: 0;
}

.menuItem {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  max-width: 320px;
  text-wrap: balance;
  padding: 12px 16px;
}

.menuItem > svg {
  align-self: start;
  margin-top: 2px;
}

.menuItem:hover,
.menuItem[aria-checked="true"] {
  background-color: var(--color-lev-blue-on-white-hover);
}
