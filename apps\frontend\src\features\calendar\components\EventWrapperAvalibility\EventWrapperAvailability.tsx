import c from "classnames"
import React, { useMemo } from "react"
import { EventWrapperProps } from "react-big-calendar"

import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import useResizeObserver from "hooks/useResizeObserver"
import { calendarColorMap } from "styles/colors"

import CalendarAvailabilitySlot from "./CalendarAvailabilitySlot"
import styles from "./EventWrapperAvailability.module.css"

type EventWrapperAvailabilityProps = {
  event: CalendarEvent<"availability">
  eventInfo: EventWrapperProps<CalendarEvent>
  allowCreateEvent?: boolean
}

function stringifyPercent(v?: string | number) {
  return typeof v === "string" ? v : v + "%"
}

function getTimeDifference(fromTime: string, toTime: string) {
  const [fromHours, fromMinutes] = fromTime.split(":").map(Number)
  const [toHours, toMinutes] = toTime.split(":").map(Number)

  const fromTotalMinutes = fromHours * 60 + fromMinutes
  const toTotalMinutes = toHours * 60 + toMinutes

  return toTotalMinutes - fromTotalMinutes
}

export const EventWrapperAvailability = ({
  event,
  eventInfo,
  allowCreateEvent = false,
}: EventWrapperAvailabilityProps) => {
  const [containerHeight, setContainerHeight] = React.useState(1)

  const containerRef = useResizeObserver<HTMLDivElement>((_, entry) => {
    setContainerHeight(entry.contentRect.height)
  })

  const providerId = event.resourceId
  if (!providerId) return null

  const color = event.resource.serviceType.color

  const blockId = event.resource.blockId

  const block = event.resource.blockRules.find((block) => block.id === blockId)

  if (!block || !block.fromTime || !block.toTime) return null

  const blockDurationMinutes = getTimeDifference(block.fromTime, block.toTime)

  // If the block has no duration, set a really long duration to show only one slot
  const AvailabilityDurationMinutes =
    event.resource.appointmentDurationMinutes || 7200000

  const pxPerMinute = containerHeight / blockDurationMinutes

  const calendarAvailabilitySlotCount = Math.floor(
    blockDurationMinutes / AvailabilityDurationMinutes
  )

  const hasGapAfterSlots =
    blockDurationMinutes % AvailabilityDurationMinutes !== 0

  const height = Math.min(
    pxPerMinute * AvailabilityDurationMinutes,
    containerHeight
  )

  const availabilitySlots = useMemo(
    () =>
      Array.from({ length: calendarAvailabilitySlotCount }, (_, index) => (
        <CalendarAvailabilitySlot
          key={index}
          isLastItem={
            !hasGapAfterSlots && index === calendarAvailabilitySlotCount - 1
          }
          height={height}
          allowCreateEvent={allowCreateEvent}
          slotStartTime={
            new Date(
              event.start.getTime() +
                index * AvailabilityDurationMinutes * 60000
            )
          }
          slotEndTime={
            new Date(
              event.start.getTime() +
                (index + 1) * AvailabilityDurationMinutes * 60000
            )
          }
          serviceTypeName={event.resource.serviceType.name}
          serviceTypeId={event.resource.serviceType.id}
          teamId={event.resource.team?.id}
          providerId={providerId}
        />
      )),
    [
      calendarAvailabilitySlotCount,
      hasGapAfterSlots,
      height,
      allowCreateEvent,
      event.start,
      AvailabilityDurationMinutes,
      event.resource.serviceType.name,
      event.resource.serviceType.id,
      event.resource.team?.id,
      providerId,
    ]
  )

  const { isRtl, style } = eventInfo

  const eventStyle = {
    top: `calc(${stringifyPercent(style?.top)} + 1px)`,
    height: `calc(${stringifyPercent(style?.height)} - 0px)`,
    width: `calc(${stringifyPercent(style?.width)} - 2px)`,
    [isRtl ? "right" : "left"]:
      `calc(${stringifyPercent(style?.xOffset)} + 1px)`,
  }

  return (
    <div
      className={c(styles.wrap, calendarColorMap[color].light)}
      style={{
        ...eventStyle,
      }}
      onClick={eventInfo.onClick}
      ref={containerRef}
    >
      {availabilitySlots}
    </div>
  )
}
