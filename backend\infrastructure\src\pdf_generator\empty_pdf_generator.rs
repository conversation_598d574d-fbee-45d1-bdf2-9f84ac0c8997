use async_trait::async_trait;
use leviosa_domain_types::{InvoiceId, MedicalCertificateId};
use leviosa_domain::{
    errors::Error,
    // services::PdfGenerator,  // services module not available
    pdf_generator::PdfGenerator,
};

pub struct EmptyPdfGenerator;

#[async_trait]
impl PdfGenerator for EmptyPdfGenerator {
    async fn medical_certificate(
        &self,
        _: MedicalCertificateId,
        _: &str,
    ) -> Result<Vec<u8>, Error> {
        Err(anyhow::anyhow!("Pdf generator is not configured").into())
    }

    async fn free_text_document(&self, _: MedicalCertificateId, _: &str) -> Result<Vec<u8>, Error> {
        Err(anyhow::anyhow!("Pdf generator is not configured").into())
    }

    async fn invoice(
        &self,
        _: InvoiceId,
        _: &str,
    ) -> Result<Vec<u8>, Error> {
        Err(anyhow::anyhow!("Pdf generator is not configured").into())
    }
}
