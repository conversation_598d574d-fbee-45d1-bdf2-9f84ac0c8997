import {
  Radio,
  RadioGroup,
  RadioProvider,
  useRadioStore,
  VisuallyHidden,
} from "@ariakit/react"
import { useTranslation } from "react-i18next"

import { dashboardPollInterval } from "features/dashboard/components/Dashboard/Dashboard"
import { Button, ButtonGroup } from "ui"
import CountCircled from "ui/components/CountCircled/CountCircled"

import {
  EncounterStatus,
  useDashboardStatusCountHackQuery,
} from "generated/graphql"

import styles from "./DashboardStatusPicker.module.css"

type DashboardStatusPickerProps = {
  store: ReturnType<typeof useRadioStore>
  teamId: string
}

export default function DashboardStatusPicker({
  store,
  teamId,
}: DashboardStatusPickerProps) {
  const { t: tEnum } = useTranslation("enums")
  // Declares both sorting order and what statuses are available to use
  const sortedEncounterStatuses = [
    EncounterStatus.Planned,
    EncounterStatus.InProgress,
    EncounterStatus.CheckedOut,
  ] as const

  const statusFilterValue = (store.useState().value ||
    EncounterStatus.InProgress) as EncounterStatus

  const { data, loading } = useDashboardStatusCountHackQuery({
    variables: { teamId },
    pollInterval: dashboardPollInterval,
  })

  const statusCounts = {
    [EncounterStatus.Planned]: data?.team.PLANNED.length || 0,
    [EncounterStatus.InProgress]: data?.team.IN_PROGRESS.length || 0,
    [EncounterStatus.CheckedOut]: data?.team.CHECKED_OUT.length || 0,
  }

  return (
    <RadioProvider store={store}>
      <RadioGroup render={(p) => <ButtonGroup {...p} />}>
        {sortedEncounterStatuses.map((status) => (
          <Button
            as="label"
            key={status}
            data-testid={status}
            variant={status === statusFilterValue ? "filled" : "outline"}
          >
            <CountCircled size="small" className={styles.counter}>
              {loading ? "…" : statusCounts[status]}
            </CountCircled>
            <VisuallyHidden>
              <Radio value={status} />
            </VisuallyHidden>
            {tEnum(`EncounterStatus_state.${status}`)}
          </Button>
        ))}
      </RadioGroup>
    </RadioProvider>
  )
}
