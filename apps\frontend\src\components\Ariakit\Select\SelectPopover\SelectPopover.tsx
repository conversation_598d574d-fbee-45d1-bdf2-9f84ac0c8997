import {
  SelectPopover as SelectPopoverAriakit,
  SelectPopoverProps as SelectPopoverPropsAriakitProps,
} from "@ariakit/react"

import { AnimatedPopover } from "components/Popover/Popover"

import styles from "./SelectPopover.module.css"

export type SelectPopoverProps = SelectPopoverPropsAriakitProps

export const SelectPopover = ({
  className = "",
  ...rest
}: SelectPopoverProps) => {
  return (
    <SelectPopoverAriakit
      gutter={4}
      sameWidth
      unmountOnHide
      {...rest}
      render={<AnimatedPopover />}
      className={`${styles.popover} ${className}`}
    />
  )
}
