import c from "classnames"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useRef } from "react"

import { ButtonText } from "ui"

import styles from "./Popover.module.css"

export type PopoverMenuItemProps = {
  id?: string
  isActive?: boolean
  onClick?: MouseEventHandler<HTMLButtonElement>
  className?: string
  children?: React.ReactNode
  role?: string
  onMouseEnter?: (e: React.MouseEvent<HTMLElement>) => void
}

export function PopoverMenuItem({
  isActive,
  children,
  className = "",
  onClick,
  ...restProps
}: PopoverMenuItemProps): JSX.Element {
  const ref = useRef<HTMLLIElement>(null)

  useEffect(() => {
    if (isActive) {
      ref.current?.scrollIntoView({ behavior: "smooth", block: "nearest" })
    }
  }, [isActive])

  return (
    <li
      className={c({
        [className]: className,
        [styles.menuItem]: 1,
        [styles.menuItemActive]: isActive,
      })}
      {...restProps}
      tabIndex={isActive ? 0 : -1}
    >
      <ButtonText
        as="button"
        onClick={onClick}
        className={styles.menuItemButton}
      >
        {children}
      </ButtonText>
    </li>
  )
}
