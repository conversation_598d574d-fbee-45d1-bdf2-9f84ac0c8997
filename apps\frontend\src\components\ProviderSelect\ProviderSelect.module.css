.menuListItem {
  padding: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menuListItem:hover {
  background-color: transparent;
}

.menuListItem:has(.clearButton) {
  /* Make space for the clear button */
  padding-right: 16px;
}

.selectContainer {
  position: relative;
  display: flex;
  width: 100%;
}

.clearButton {
  padding: 0;
  border: none;
  position: absolute;
  right: 48px; /* Position for the clear button */
  top: 50%;
  transform: translateY(-50%);
}
