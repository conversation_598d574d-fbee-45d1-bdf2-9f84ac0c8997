import {
  AvailabilityScheduleFragmentFragment,
  AvailabilityScheduleStatus,
  CalendarColor,
  ServiceTypeModality,
  Weekday,
} from "generated/graphql"

export const getAvailabilitySchedulesInput = (
  availabilityScheduleStatus: AvailabilityScheduleStatus
): AvailabilityScheduleFragmentFragment[] => [
  {
    __typename: "AvailabilitySchedule",
    id: "1",
    fromDate: "2024-05-15",
    toDate: "2024-07-21",
    status: availabilityScheduleStatus,
    provider: { __typename: "Provider", id: "p1", name: "Provider 1" },
    serviceTypeAvailabilities: [
      {
        __typename: "ServiceTypeAvailability",
        id: "a1",
        appointmentDurationMinutes: 30,
        isDeleted: false,
        isBookableOnline: true,
        blockRules: [
          {
            __typename: "AvailabilityBlockRule",
            id: "r1",
            weekday: Weekday.Tuesday,
            fromTime: "10:00",
            toTime: "12:00",
            isDeleted: false,
          },
          {
            __typename: "AvailabilityBlockRule",
            id: "r2",
            weekday: Weekday.Wednesday,
            fromTime: "14:00",
            toTime: "16:00",
            isDeleted: false,
          },
          {
            __typename: "AvailabilityBlockRule",
            id: "r3",
            weekday: Weekday.Friday,
            fromTime: "09:00",
            toTime: "11:00",
            isDeleted: false,
          },
        ],
        serviceType: {
          __typename: "ExternalServiceType",
          id: "s1",
          name: "Service 1",
          color: CalendarColor.Pink,
          description: "Service 1 description",
          modality: ServiceTypeModality.Onsite,
          deletedAt: null,
        },
        team: null,
      },
      {
        __typename: "ServiceTypeAvailability",
        id: "a2",
        appointmentDurationMinutes: 45,
        isDeleted: false,
        isBookableOnline: true,
        blockRules: [
          {
            __typename: "AvailabilityBlockRule",
            id: "r4",
            weekday: Weekday.Monday,
            fromTime: "08:00",
            toTime: "10:00",
            isDeleted: false,
          },
          {
            __typename: "AvailabilityBlockRule",
            id: "r5",
            weekday: Weekday.Thursday,
            fromTime: "15:00",
            toTime: "17:00",
            isDeleted: false,
          },
        ],
        serviceType: {
          __typename: "ExternalServiceType",
          id: "s2",
          name: "Service 2",
          color: CalendarColor.LevBlue,
          description: "Service 2 description",
          modality: ServiceTypeModality.Onsite,
          deletedAt: null,
        },
        team: null,
      },
    ],
  },
]
