import { useTranslation } from "react-i18next"

import { useUpdateSubjectMutation } from "generated/graphql"

import { EditFieldForm } from "../EditFieldForm/EditFieldForm"

type OtherInformationProps = {
  other: string | null
  id: string
}

export const OtherInformation = ({ other, id }: OtherInformationProps) => {
  const { t } = useTranslation()

  const [updateSubject, { loading }] = useUpdateSubjectMutation()

  return (
    <EditFieldForm
      name="other"
      value={other || ""}
      label={t("Other information")}
      isLoading={loading}
      placeholder={t("Click edit to add other information")}
      onSubmit={(newValue) => {
        updateSubject({
          variables: {
            id,
            input: {
              other: {
                set: newValue,
              },
            },
          },
        })
      }}
    />
  )
}
