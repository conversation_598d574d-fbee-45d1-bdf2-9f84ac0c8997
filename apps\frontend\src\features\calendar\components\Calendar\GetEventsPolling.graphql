query GetEventsPolling($inputFilter: EventInstancesFilterInput!) {
  eventInstances(inputFilter: $inputFilter) {
    id
    description
    participants {
      objId
      participantId

      attendanceState {
        id
        state
      }
    }
    canceledAt
    cancellationReason
    canceledBy {
      id
      name
    }
    encounter {
      id
      journalEntries {
        id
        status
      }
      invoices {
        id
        issued
        paymentStatus
        totalPayableBySubject
      }
    }
  }
}
