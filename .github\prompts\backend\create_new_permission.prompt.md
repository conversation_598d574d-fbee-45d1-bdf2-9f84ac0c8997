---
mode: "agent"
tools: ["githubRepo", "codebase"]
description: "This prompt creates a new permission in the Leviosa backend system"
---

# Goal

The goal of this workflow is to create a new permission in the Leviosa backend system that can be used to restrict access to specific functionality. We will add the permission to all necessary locations in the codebase, including enum definitions, GraphQL schemas, and test data.

# Workflow

## Preparation

1. Assert the following parameters are provided:
   - `permission_name`: The full name of the permission (e.g., "Calendar_BookingSlot_Edit")
   - `permission_category`: The category the permission belongs to (e.g., "Calendar", "Billing", "SubjectJournal")
   - `permission_resource`: The resource being accessed (e.g., "BookingSlot", "Invoice", "JournalEntry")
   - `permission_action`: The action being permitted (e.g., "View", "Edit", "Create", "Delete")

2. If any of these parameters are missing, ask the user to provide them.

3. Validate the permission format:
   - The permission name should follow the format `{Category}_{Resource}_{Action}`
   - If it doesn't match this format, suggest a properly formatted name

## Adding the Permission

### Step 1: Determine the Numeric ID

Determine the appropriate numeric ID for the new permission based on its category:
- Accounts permissions: 1000-1999
- Subject Journal permissions: 2000-2999
- Calendar permissions: 3000-3999
- Dashboard permissions: 4000-4999
- Billing permissions: 5000-5999
- Kiosk permissions: 6000-6999
- Notifications permissions: 7000-7999
- Auth permissions: 8000-8999
- Lists permissions: 9000-9999
- Integrations permissions: 10000-10999
- External Organisations permissions: 11000-11999
- Beta permissions: 100000+

Find the highest existing ID in the chosen category and increment it by 1.

### Step 2: Add to PermissionKey Enum

Add the new permission to the `PermissionKey` enum in:
```
/backend/domain/src/model/permissions/permission.rs
```

The permission needs to be added in three places:
1. In the enum definition with its numeric ID
2. In the `TryFrom<String>` implementation
3. In the `TryFrom<i32>` implementation

### Step 3: Update GraphQL Schema

Add the permission to:
1. `/backend/api/src/permissions/enums.rs` in the `PermissionKeyGQL` enum

### Step 4: Update Test Data

Add the permission to the `ALL_PERMISSIONS` array in:
```
/backend/testing/src/builder.rs
```

### Step 5: Create Database Migration

Use the [create_db_migration.prompt.md](create_db_migration.prompt.md) workflow to create a new database migration that will add the permission to the database.
The SQL for the migration should add the permission to the `permissions.permission_keys` table:

```sql
INSERT INTO permissions.permission_keys(permission_id)
    VALUES ('{permission_name}');
```

Steps to create the migration:
1. Use the migration prompt file to create the migration file, [create_db_migration.prompt.md](create_db_migration.prompt.md)
2. Once the migration file is created, add the SQL statement to insert the new permission into the `permissions.permission_keys` table
3. Verify that the migration has been properly added to the migration list in `backend/migration/src/lib.rs`

### Step 6: Verify Implementation

Check that the permission has been added correctly to all required locations.

Provide a summary of all changes made:
1. List of files modified:
   - `/backend/domain/src/model/permissions/permission.rs`
   - `/backend/api/src/permissions/enums.rs`
   - `/backend/testing/src/builder.rs`
   - New migration file in `/backend/migration/src/`
   - `/backend/migration/src/lib.rs` (to include the new migration)

2. Details of the new permission:
   - Name: `{permission_name}`
   - ID: `{determined_id}`
   - Category: `{permission_category}`

3. Do not run cargo test or cargo check, your commanding overlord will manage that part.