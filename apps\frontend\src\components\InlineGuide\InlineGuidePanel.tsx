import c from "classnames"

import { InlineGuideId, inlineGuides } from "assets/inlineGuides"
import { Markdown } from "components/Markdown/Markdown"
import { PanelWithIcon } from "components/Panel/Panel"

type Props = {
  id?: keyof typeof InlineGuideId
  content?: string
  className?: string
}

export const InlineGuidePanel = ({ id, content, className }: Props) => {
  const inlineGuideContent = id ? inlineGuides[id].content : content

  return (
    <PanelWithIcon
      status="info"
      size="small"
      className={c("whitespace-pre-wrap", className)}
    >
      <Markdown children={inlineGuideContent} />
    </PanelWithIcon>
  )
}
