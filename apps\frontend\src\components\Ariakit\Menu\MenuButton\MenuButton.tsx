import {
  MenuButton as MenuButtonAriakit,
  MenuButtonProps as MenuButtonPropsAriakit,
} from "@ariakit/react"
import { forwardRef } from "react"

import Button, { ButtonProps } from "ui/components/Button/Button"

export type MenuButtonProps = MenuButtonPropsAriakit & ButtonProps<"button">

export const MenuButton = forwardRef<HTMLButtonElement, MenuButtonProps>(
  (props, ref) => <MenuButtonAriakit render={<Button />} {...props} ref={ref} />
)
