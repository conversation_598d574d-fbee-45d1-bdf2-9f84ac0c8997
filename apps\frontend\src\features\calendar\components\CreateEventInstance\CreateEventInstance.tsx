import { differenceInMinutes } from "date-fns"
import { useTranslation } from "react-i18next"
import { useLocation } from "react-router-dom"
import { z } from "zod"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { EventInstanceFormData } from "features/calendar/types/EventInstanceFormData"
import { DO_NOT_REPEAT } from "features/calendar/utils/eventRepeatIntervalOptions"
import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import participantInputs from "features/calendar/utils/participantInputs"
import { RouteStrings } from "routes/RouteStrings"
import { Modal, notification } from "ui"
import { naiveDate } from "utils/naiveDate"

import {
  EventInstanceCreateInput,
  EventType,
  namedOperations,
  ParticipantEventRelationObject,
  ParticipantEventRelationSource,
  useCreateEventInstanceMutation,
  useCreateEventRecurrenceMutation,
  useSetEventParticipantsMutation,
} from "generated/graphql"

import dateLocalReadableFormat from "../../utils/dateLocalReadableFormat"
import {
  EventInstanceForm,
  EventInstanceSchema,
} from "../forms/EventInstanceForm/EventInstanceForm"
import styles from "./CreateEventInstance.module.css"
import optimisticResponseEventInstanceCreate from "./optimisticResponseEventInstanceCreate"

type CreateEventInstanceProps = {
  formData?: Partial<EventInstanceFormData>
  eventTitle?: string
}

const CreateEventInstance = ({ formData }: CreateEventInstanceProps) => {
  const navigateCalendar = useNavigateCalendar()
  const { state } = useLocation()

  const { globalData } = useGlobalState()

  const actor = globalData.actor

  const { t } = useTranslation()

  const navigateToCalendar = () => {
    navigateCalendar(RouteStrings.calendar, {
      replace: true,
    })
  }

  const startDate = state?.start
  const endDate = state?.end

  const formattedStartDate = dateLocalReadableFormat(startDate)
  const formattedEndDate = dateLocalReadableFormat(endDate)

  const [
    createEventInstance,
    { loading: createEventInstanceLoading, error: createEventInstanceError },
  ] = useCreateEventInstanceMutation()

  const [
    createEventRecurrence,
    {
      loading: createEventRecurrenceLoading,
      error: createEventRecurrenceError,
    },
  ] = useCreateEventRecurrenceMutation()

  const [
    setEventParticipant,
    { loading: setEventParticipantLoading, error: setEventParticipantError },
  ] = useSetEventParticipantsMutation({
    onCompleted: (data) => {
      if (data) {
        notification.create({
          message: t("Event created"),
          status: "success",
          maxWidth: "500px",
        })

        navigateToCalendar()
      }
    },
    // Remove when setEventParticipant return all event instances and cache is updated
    refetchQueries: [
      namedOperations.Query.EventInstances,
      namedOperations.Query.ProviderBox,
    ],
  })

  const setParticipantsToEvent = ({
    eventRelationObject,
    participantType,
    userId,
    eventParticipantId,
  }: {
    eventRelationObject: ParticipantEventRelationObject
    participantType: string[]
    userId: string[]
    eventParticipantId: string[]
  }) => {
    const participants = participantInputs({
      participantType,
      userId,
      eventParticipantId,
    })

    setEventParticipant({
      variables: {
        eventRelationObject: eventRelationObject,
        participantInputs: participants,
      },
    })
  }

  const handleSubmit = async (data: z.infer<typeof EventInstanceSchema>) => {
    const {
      attendanceRequest: _,
      participantType,
      userId,
      eventParticipantId,
      repeatInterval,
      untilDate,
      serviceTypeId,
      teamId,
      //not used for create event instance
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      locationId,
      ...restData
    } = data

    if (repeatInterval && repeatInterval !== DO_NOT_REPEAT && untilDate) {
      const durationMins = differenceInMinutes(
        restData.toDate,
        restData.fromDate
      )

      const resp = await createEventRecurrence({
        variables: {
          input: {
            ...restData,
            ownerId: actor.id,
            repeatInterval: repeatInterval,
            toDate: naiveDate(untilDate),
            durationMins: durationMins,
            locationId: locationId,
            eventType: EventType.Appointment,
            serviceTypeId:
              serviceTypeId === "none" || !serviceTypeId ? null : serviceTypeId,
          },
        },
      })

      const eventRecurrentId = resp.data?.createEventRecurrence?.id

      if (!eventRecurrentId) return

      const eventRelationObject: ParticipantEventRelationObject = {
        objId: eventRecurrentId,
        source: ParticipantEventRelationSource.EventRecurrence,
      }

      setParticipantsToEvent({
        eventRelationObject,
        participantType,
        userId,
        eventParticipantId,
      })

      return
    }

    const eventInstanceInput: EventInstanceCreateInput = {
      ...restData,
      serviceTypeId:
        serviceTypeId === "none" || !serviceTypeId ? null : serviceTypeId,
      teamId: teamId || null,
      ownerId: actor.id,
      locationId: locationId || null,
      eventType: EventType.Appointment,
    }

    const resp = await createEventInstance({
      variables: {
        input: eventInstanceInput,
        id: null,
      },
      optimisticResponse: optimisticResponseEventInstanceCreate({
        id: "",
        description: restData.description,
        start: restData.fromDate,
        end: restData.toDate,
        title: restData.title,
        creatorName: actor.name,
        creatorId: actor.id,
        ownerId: actor.id,
      }),
    })

    const eventInstanceId = resp.data?.createEventInstance?.id

    if (!eventInstanceId) return

    const eventRelationObject: ParticipantEventRelationObject = {
      objId: eventInstanceId,
      source: ParticipantEventRelationSource.EventInstance,
    }

    setParticipantsToEvent({
      eventRelationObject,
      participantType,
      userId,
      eventParticipantId,
    })
  }

  const isLoading =
    createEventInstanceLoading ||
    setEventParticipantLoading ||
    createEventRecurrenceLoading

  const error =
    createEventInstanceError ||
    setEventParticipantError ||
    createEventRecurrenceError

  return (
    <Modal
      isOpen={true}
      onClose={navigateToCalendar}
      title={"Create Event"}
      contentClassName={styles.modalContent}
      data-testid="create-event-instance-form"
    >
      <EventInstanceForm
        formType="Create"
        error={error}
        formData={{
          ...state,
          fromDate: formattedStartDate,
          toDate: formattedEndDate,
          ...formData,
        }}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        onCancel={navigateToCalendar}
      />
    </Modal>
  )
}

export default CreateEventInstance
