import { useTranslation } from "react-i18next"

import { useSelectStore } from "components/Ariakit/hooks"

import { Select, SelectItem, SelectPopover } from "../../../Ariakit"
import styles from "./MonthInput.module.css"
import months from "./months"

type MonthInputProps = {
  selectMonth: ReturnType<typeof useSelectStore>
  onSelect?: (value: string) => void
}

export const MonthInput = ({ selectMonth, onSelect }: MonthInputProps) => {
  const { t } = useTranslation()

  const monthsList = months(t)
  const { value: selectValue } = selectMonth.useState()

  const selectedMonth = monthsList.find((month) => month.value === selectValue)

  return (
    <>
      <Select
        store={selectMonth}
        className={`${styles.selectButton} ${
          selectedMonth ? "" : styles.placeholder
        }`}
      >
        <div>{selectedMonth ? selectedMonth.label : "Month"}</div>
      </Select>

      <SelectPopover
        store={selectMonth}
        style={{ width: 300 }}
        sameWidth={false}
        gutter={4}
      >
        {monthsList.map((month) => (
          <SelectItem
            key={month.value}
            value={month.value}
            onClick={() => {
              selectMonth.setValue(month.value)
              onSelect?.(month.value)
            }}
          >
            {month.label}
          </SelectItem>
        ))}
      </SelectPopover>
    </>
  )
}
