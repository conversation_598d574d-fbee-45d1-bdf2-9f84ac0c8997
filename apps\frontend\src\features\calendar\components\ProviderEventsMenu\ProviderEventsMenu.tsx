import { useState } from "react"
import { useTranslation } from "react-i18next"

import { Icon } from "@leviosa/components"

import { Menu, MenuButton, MenuItem, MenuProvider } from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import CancelProviderEventsModal from "features/calendar/components/CancelProviderEventsModal/CancelProviderEventsModal"
import { ButtonOwnProps, notification } from "ui"

import {
  EventType,
  namedOperations,
  useCreateEventInstanceMutation,
} from "generated/graphql"

interface ProviderEventsMenuProps {
  providerId: string
  date: Date
  className?: string
  size?: ButtonOwnProps["size"]
}

const ProviderEventsMenu = ({
  providerId,
  date,
  className,
  size,
}: ProviderEventsMenuProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "calendar",
  })
  const { globalData } = useGlobalState()
  const { actor } = globalData

  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)
  const [createEventInstance] = useCreateEventInstanceMutation()

  const menuStore = useMenuStore()

  const handleOpenCancelModal = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Close menu first
    menuStore.hide()
    // Then open modal
    setIsCancelModalOpen(true)
  }

  const handleMarkDayAsUnavailable = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Create event for current provider from 7:00 - 19:00 with no subject
    createEventInstance({
      variables: {
        id: null,
        input: {
          eventType: EventType.Appointment,
          fromDate: new Date(date.setHours(7, 0, 0)),
          toDate: new Date(date.setHours(19, 0, 0)),
          title: tRoutes("unavailable"),
          description: "",
          subjectAndProviderIds: [providerId],
          ownerId: actor.id,
        },
      },
      refetchQueries: [namedOperations.Query.EventInstances],
      onError: () => {
        notification.create({
          message: tRoutes("errorMarkingDayAsUnavailable"),
          status: "error",
        })
      },
    })
  }

  return (
    <>
      <MenuProvider store={menuStore} placement="bottom-end">
        <MenuButton
          variant="clear"
          size={size}
          icon={<Icon name="more-2-line" />}
          className={className}
          onClick={(e: React.MouseEvent) => e.stopPropagation()}
        />
        <Menu>
          <MenuItem onClick={handleMarkDayAsUnavailable}>
            {tRoutes("markDayAsUnavailable")}
          </MenuItem>
          <MenuItem onClick={handleOpenCancelModal}>
            {tRoutes("cancelEventsForThisDay")}
          </MenuItem>
        </Menu>
      </MenuProvider>

      <CancelProviderEventsModal
        isOpen={isCancelModalOpen}
        onClose={() => setIsCancelModalOpen(false)}
        providerId={providerId}
        date={date}
      />
    </>
  )
}

export default ProviderEventsMenu
