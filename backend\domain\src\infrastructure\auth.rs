mod auth_data;
pub use auth_data::*;

mod auth_scope;
pub use auth_scope::*;

mod tenant_check;
pub use tenant_check::*;

mod authorization;
pub use authorization::*;

// Re-export from contracts
// Auth interfaces - moved to domain for isolation
use async_trait::async_trait;
use crate::errors::Result;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ApiKey {
    pub key: String,
    pub name: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct ElectronicIdUserInfo {
    pub user_id: String,
    pub name: String,
    pub national_register_id: String,
}

#[async_trait]
pub trait ApiKeyManager: Send + Sync {
    async fn create_api_key(&self, name: &str) -> Result<ApiKey>;
    async fn validate_api_key(&self, key: &str) -> Result<bool>;
    async fn generate(&self, prefix: &str) -> Result<String>;
    async fn split(&self, api_key: &str) -> Result<(String, String)>;
}

#[async_trait]
pub trait PasswordHasher: Send + Sync {
    async fn hash(&self, password: &str) -> Result<String>;
    async fn verify(&self, password: &str, hash: &str) -> Result<bool>;
}

#[async_trait]
pub trait ElectronicId: Send + Sync {
    async fn login(&self, phone_number: &str) -> Result<ElectronicIdUserInfo>;
}

mod jwt;
pub use jwt::*;

mod password;
pub use password::*;

mod encryption;
pub use encryption::*;

mod electronic_id;
pub use electronic_id::*;
