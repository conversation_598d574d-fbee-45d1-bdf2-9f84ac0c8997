import getTagsFromNote from "./getTagsFromNote"

describe("getTagsFromNote", () => {
  it("should return an empty array when note is null", () => {
    const result = getTagsFromNote(null)
    expect(result).toEqual([])
  })

  it("should return an empty array when no tags are present", () => {
    const result = getTagsFromNote("This is a note without tags.")
    expect(result).toEqual([])
  })

  it("should return an array of tags when tags are present", () => {
    const result = getTagsFromNote("This is a note with #tag1 and #tag2.")
    expect(result).toEqual(["#tag1", "#tag2"])
  })

  it("should return an array of tags with underscores", () => {
    const result = getTagsFromNote("This is a note with #tag_one and #tag_two.")
    expect(result).toEqual(["#tag_one", "#tag_two"])
  })

  it("should return an array of tags with alphanumeric characters", () => {
    const result = getTagsFromNote("This is a note with #tag123 and #tag456.")
    expect(result).toEqual(["#tag123", "#tag456"])
  })

  it("should return an array of tags when tags are at the beginning or end", () => {
    const result = getTagsFromNote(
      "#startTag This is a note with tags at the #endTag"
    )
    expect(result).toEqual(["#startTag", "#endTag"])
  })

  it("should return an array of tags when tags are adjacent", () => {
    const result = getTagsFromNote("This is a note with #tag1#tag2.")
    expect(result).toEqual(["#tag1", "#tag2"])
  })
})
