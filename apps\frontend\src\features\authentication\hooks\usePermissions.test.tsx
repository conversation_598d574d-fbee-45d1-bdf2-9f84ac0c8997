import { renderHook } from "@testing-library/react"
import { mockGlobalData } from "test/mocks/GlobalStateMock"

import { GlobalProvider } from "components/GlobalDataContext/GlobalData.context"
import usePermissions from "features/authentication/hooks/usePermissions"

// This mock data has only 2 permissions: AccountsProviderCreate and AccountsProviderInvite
import { PermissionKey } from "generated/graphql"

describe("usePermissions", () => {
  it("should return a function to check if user has a specific permission", () => {
    const { AccountsProviderCreate, SubjectJournalInterventionPeriodEdit } =
      PermissionKey
    const { result } = renderHook(() => usePermissions(), {
      wrapper: ({ children }) => (
        <GlobalProvider currentTeamId={"fakeId"} globalData={mockGlobalData}>
          {children}
        </GlobalProvider>
      ),
    })

    expect(result.current.hasPermission(AccountsProviderCreate)).toBe(true)
    expect(
      result.current.hasPermission(SubjectJournalInterventionPeriodEdit)
    ).toBe(false)
  })

  it("should return a function to check if user has all permissions", () => {
    const {
      AccountsProviderCreate,
      AccountsProviderInvite,
      SubjectJournalInterventionPeriodEdit,
    } = PermissionKey
    const { result } = renderHook(() => usePermissions(), {
      wrapper: ({ children }) => (
        <GlobalProvider currentTeamId={"fakeId"} globalData={mockGlobalData}>
          {children}
        </GlobalProvider>
      ),
    })

    expect(
      result.current.hasAllPermissions([
        AccountsProviderCreate,
        AccountsProviderInvite,
      ])
    ).toBe(true)
    expect(
      result.current.hasAllPermissions([
        AccountsProviderCreate,
        SubjectJournalInterventionPeriodEdit,
      ])
    ).toBe(false)
    expect(result.current.hasAllPermissions([])).toBe(true)
  })

  it("should return a function to check if user has some permissions", () => {
    const {
      AccountsProviderCreate,
      AccountsProviderInvite,
      SubjectJournalInterventionPeriodEdit,
      SubjectJournalSign,
    } = PermissionKey
    const { result } = renderHook(() => usePermissions(), {
      wrapper: ({ children }) => (
        <GlobalProvider currentTeamId={"fakeId"} globalData={mockGlobalData}>
          {children}
        </GlobalProvider>
      ),
    })

    expect(
      result.current.hasSomePermissions([
        AccountsProviderCreate,
        AccountsProviderInvite,
      ])
    ).toBe(true)
    expect(
      result.current.hasSomePermissions([
        AccountsProviderCreate,
        SubjectJournalInterventionPeriodEdit,
      ])
    ).toBe(true)
    expect(
      result.current.hasSomePermissions([
        SubjectJournalInterventionPeriodEdit,
        SubjectJournalSign,
      ])
    ).toBe(false)
    expect(result.current.hasSomePermissions([])).toBe(false)
  })
})
