import { Subject } from "generated/graphql"

export type AddressViewProps = {
  address: Subject["address"]
}

export const AddressView = ({ address }: AddressViewProps) => {
  return address === null ? (
    <>n/a</>
  ) : (
    <>
      {address.addressLine1 || "---"},<br />
      {address.addressLine2 ? (
        <>
          {address.addressLine2 + ","}
          <br />
        </>
      ) : null}
      {address.postalCode ? address.postalCode + ", " : "- "}
      {address.region ? address.region + " - " : ""}
      {address.city || "--"}
      <br />
      {address.country}
    </>
  )
}
