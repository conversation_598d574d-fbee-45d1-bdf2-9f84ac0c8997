import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"

import styles from "../ViewSection/ViewSection.module.css"

type ViewSectionProps = {
  children: React.ReactNode
  iconName?: IconName
  hasLabel?: boolean
  hasContent?: boolean
}

export const ViewSection = ({
  iconName,
  children,
  hasLabel = false,
  hasContent = true,
}: ViewSectionProps) => {
  if (!hasContent) return null

  return (
    <div className={styles.wrap} data-has-label={hasLabel}>
      {iconName && <Icon name={iconName} fontSize={24} />}
      <span className={styles.sectionInfo}>{children}</span>
    </div>
  )
}
