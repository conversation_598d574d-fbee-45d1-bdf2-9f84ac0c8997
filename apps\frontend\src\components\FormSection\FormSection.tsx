import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"

import styles from "./FormSection.module.css"

type Props = {
  children: React.ReactNode
  iconName: IconName
  showLine?: boolean
  className?: string
}

export const FormSection = ({
  children,
  iconName,
  showLine = true,
  className = "",
}: Props) => {
  return (
    <div className={`${styles.wrap} ${className}`}>
      <div className={styles.iconWrapper}>
        <Icon name={iconName} className={styles.icon} />
      </div>
      <div className={styles.section} data-show-line={showLine}>
        {children}
      </div>
    </div>
  )
}
