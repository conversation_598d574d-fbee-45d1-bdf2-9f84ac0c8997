import { PopoverDisclosure, PopoverStore } from "@ariakit/react"
import dayjs from "dayjs"
import { useTranslation } from "react-i18next"

import { formatPersonaId } from "@leviosa/utils"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import GenderIcon from "components/Icon/GenderIcon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import JournalBlockLink from "features/subject-journal/components/JournalBlockLink/JournalBlockLink"
import { Tag, Text } from "ui"
import CountCircled from "ui/components/CountCircled/CountCircled"
import { PersonaIdCopyButton } from "ui/components/PersonSummary/PersonaIdCopyButton/PersonaIdCopyButton"
import { ProviderBadgeHoverInfo } from "ui/components/ProviderBadgeMenu/ProviderBadgeHoverInfo/ProviderBadgeHoverInfo"
import { specialtyProperties } from "ui/components/ProviderBadgeMenu/specialtyProperties"
import isDefined from "utils/isDefined"
import parseAgeText from "utils/parseAgeText"

import { DashboardQuery, DietaryAllowanceId } from "generated/graphql"

import { getLocationText } from "../getLocationText"
import styles from "./DashboardRow.module.css"

type DashboardRowProps = {
  row: DashboardQuery["team"]["rows"][0]
  popoverStore: PopoverStore
  onSubjectSelected: (subjectId: string) => void
  onProviderSelected: (name: string) => void
}

export const DashboardRow = ({
  row: _row,
  popoverStore,
  onProviderSelected,
  onSubjectSelected,
}: DashboardRowProps) => {
  const { t: tr } = useTranslation("routes", { keyPrefix: "dashboard" })
  const { t: tEnum } = useTranslation("enums")
  const { t } = useTranslation()

  const { id, subject, fromDate, responsibleProviders, ...row } = _row
  const { id: subjectId, subjectHealthProfile } = subject

  const djsFromDate = dayjs(fromDate)
  const arrivedAtPre = dayjs.duration(dayjs().diff(djsFromDate))

  const ageString = parseAgeText(subject.age)

  const location = getLocationText(subject)

  return (
    <tr
      onClick={(e) => {
        const target = e.target as Element
        const cell = target.closest("td")

        if (cell) {
          onSubjectSelected(subjectId)
          popoverStore.setAnchorElement(cell.parentElement)
          popoverStore.show()
        }
      }}
      className={styles.row}
    >
      <Text as="td" size="small">
        {location}
      </Text>

      <Text
        as="td"
        size="small"
        className={styles.secondaryText}
        data-testid="dashboard-age"
      >
        {subject.age && t(...ageString)}
      </Text>
      <Text as="td" size="large" data-testid="dashboard-gender">
        <GenderIcon genderId={subject.gender} />
      </Text>
      <PiiSensitive as={"td"} data-testid="dashboard-name">
        <div className={styles.subjectNameRow}>
          <Text weight="bold" className={styles.primaryText}>
            {subject.name}
          </Text>
          <PersonaIdCopyButton
            className={styles.personaIdCopyButton}
            personaIdFormatted={formatPersonaId(subject.personaId)}
            iconClassName={styles.personaIdCopyButtonIcon}
          ></PersonaIdCopyButton>
        </div>
      </PiiSensitive>

      <td>
        {isDefined(row.priority) && (
          <CountCircled
            className={styles.countCircled}
            data-testid="dashboard-priority"
          >
            {row.priority}
          </CountCircled>
        )}
      </td>
      <Text
        as="td"
        size="small"
        className={styles.secondaryText}
        title={djsFromDate.format("LLL")}
        data-testid="dashboard-arrived"
      >
        {arrivedAtPre.asDays() >= 1
          ? `${Math.floor(arrivedAtPre.asDays())}d`
          : `${Math.floor(arrivedAtPre.hours())}h ${arrivedAtPre.minutes()}m`}
      </Text>
      <td data-testid="dashboard-reason">
        {row.disposition && (
          <Tag
            color="levBlue"
            size="small"
            className={styles.disposition}
            data-testid="dashboard-disposition"
          >
            {tEnum(`EncounterDisposition.${row.disposition}`)}
          </Tag>
        )}
        {row.reason}
      </td>

      <td data-testid="dashboard-diet">
        {subjectHealthProfile &&
          subjectHealthProfile.dietaryAllowanceId !==
            DietaryAllowanceId.Unset && (
            <Tag>
              {tEnum(
                `DietaryAllowance.${Object.values(DietaryAllowanceId).find(
                  (i) => i === subjectHealthProfile.dietaryAllowanceId
                )}`
              )}
            </Tag>
          )}
      </td>

      <td className={styles.note} data-testid="dashboard-note">
        <Tooltip
          tooltipContent={
            <div className={styles.dashboardNote}>{row.note}</div>
          }
        >
          {row.note}
        </Tooltip>
      </td>

      <td data-testid="dashboard-providers">
        <div className={styles.responsibleProviders}>
          {/* circular Button */}
          {[...responsibleProviders]
            .sort(
              (a, b) =>
                specialtyProperties[a.specialty].order -
                specialtyProperties[b.specialty].order
            )
            .map((provider) => (
              <ProviderBadgeHoverInfo
                key={provider.id}
                onClick={() => onProviderSelected(provider.name)}
                {...provider}
              />
            ))}
        </div>
      </td>

      <td>
        <ul data-debug={id} className={styles.journalEntries}>
          {row.journalEntries.length > 0 ? (
            row.journalEntries.map((journalEntry) => {
              const djsUpdatedAt = dayjs(journalEntry.updatedAt)

              const linkContent = (
                <>
                  {journalEntry.createdBy.nameInitials}{" "}
                  <Text as="span" size="small" className={styles.secondaryText}>
                    {djsUpdatedAt.fromNow(true)}
                  </Text>
                </>
              )

              const sectionId =
                journalEntry.sections?.[journalEntry.sections.length - 1]?.id ??
                null

              return (
                <li
                  key={journalEntry.id}
                  title={`${djsUpdatedAt.format("LLL")} ${
                    journalEntry.createdBy.name
                  }`}
                >
                  <JournalBlockLink
                    // COMEBACK; it would be better if focus accepts journalBlock & gotoLast=true
                    encounterId={!sectionId ? id : null}
                    fallback={linkContent}
                    sectionId={sectionId}
                    subjectId={subjectId}
                  >
                    {linkContent}
                  </JournalBlockLink>
                </li>
              )
            })
          ) : (
            <li>
              <JournalBlockLink
                encounterId={id}
                fallback={tr("empty_encounter")}
                sectionId={null}
                subjectId={subjectId}
              >
                {tr("empty_encounter")}
              </JournalBlockLink>
            </li>
          )}
        </ul>
        <PopoverDisclosure
          store={{
            ...popoverStore,
            setAnchorElement: () => undefined,
          }}
        />
      </td>
    </tr>
  )
}
