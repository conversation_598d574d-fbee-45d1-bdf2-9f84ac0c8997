//! National registry service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NationalRegistryPerson {
    pub persona_id: PersonaIdIs,
    pub name: String,
    pub gender: GenderId,
    pub date_of_birth_inferred: DateTime<Utc>,
    pub address: Option<NationalRegistryAddress>,
    pub family_id: Option<String>,
    pub citizenship: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NationalRegistryAddress {
    pub street: String,
    pub town: String,
    pub postal_code: String,
    pub country: String,
}

/// Contract for national registry integration
#[async_trait]
#[mockall::automock]
pub trait NationalRegistry: Send + Sync {
    /// Get person information from national registry
    async fn get_person(
        &self,
        persona_id: PersonaIdIs,
    ) -> Result<Option<NationalRegistryPerson>>;
}
