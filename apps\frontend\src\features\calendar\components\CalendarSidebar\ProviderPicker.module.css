.container {
  min-width: 0; /* Prevents content from overflowing */
  width: 100%; /* Ensures it doesn’t exceed parent */
}

.popover {
  padding: 0;
}

.selectedProviders {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 30vh;
  overflow-y: auto;
  padding: 2px 8px 2px 2px;
  margin: 0 -8px 16px 0;
}

.popover::-webkit-scrollbar-track {
  margin: 16px 0;
}

.providerWrapper {
  display: flex;
  width: 100%;
  background-color: var(--color-background-subtle);
  border-radius: var(--radius-button-half);
  overflow: hidden;
}

.providerHidden {
  background-color: var(--color-neutral-200);
}

.providerHidden .providerToggleButton,
.providerHidden .removeButton {
  color: var(--color-text-secondary);
  --color-background-subtle: var(--color-neutral-200);
  --color-background-subtle-hover: var(--color-neutral-300);
  --color-background-subtle-active: var(--color-neutral-400);
}

.providerLabel {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.providerToggleButton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-grow: 1;
  padding: 4px 8px 4px 10px;
  margin-right: -4px;
  color: var(--color-text-interactive);
  border-radius: var(--radius-button-half);
  gap: 4px;
  min-width: 0;
  overflow: hidden;
  background-color: var(--color-background-subtle);
}
button.providerToggleButton:hover,
.removeButton:hover {
  cursor: pointer;
  background-color: var(--color-background-subtle-hover);
}
button.providerToggleButton:active,
.removeButton:active {
  background-color: var(--color-background-subtle-active);
}

.providerToggleButton:focus-visible,
.removeButton:focus-visible {
  box-shadow: 0 0 0 2px var(--color-brand-primary-blue);
}

.providerToggleButton:disabled {
  cursor: default;
}

.removeButton {
  flex-shrink: 0;
  padding: 0 5.5px;
  border-radius: var(--radius-button-half);
}

.removeButton:disabled,
.removeButton:disabled:hover {
  background-color: transparent;
  border-color: transparent;
}

.comboboxWrapper {
  position: relative;
  width: 100%;
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  z-index: 1;
  pointer-events: none;
}

.combobox {
  width: 100%;
  margin: 0;
  padding: 8px 12px 8px 36px; /* accommodate for the search icon */
}

.noResults {
  padding: 8px 12px;
  color: var(--color-text-secondary);
}
