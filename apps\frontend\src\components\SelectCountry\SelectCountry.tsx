import { useTranslation } from "react-i18next"

import { useSelectStore } from "components/Ariakit/hooks"
import Select, { SelectProps } from "components/Select/Select"
import { Status } from "ui"

import { useCountriesQuery } from "generated/graphql"

type CountrySelectProps = Omit<
  SelectProps<string>,
  "options" | "selectStore" | "onChange"
> & {
  onChange: (value: string | null) => void
  status?: Status
  defaultValue?: string
}

export const CountrySelect = ({
  label = "country",
  onChange,
  defaultValue = "",
  ...rest
}: CountrySelectProps) => {
  const { t } = useTranslation()

  const { data } = useCountriesQuery()

  const countries = data?.countries || []

  const selectCountryStore = useSelectStore({
    defaultValue: String(defaultValue),
  })

  return (
    <Select
      label={t(label)}
      options={countries.map((c) => ({ ...c, value: c.value }))}
      selectStore={selectCountryStore}
      onSelectChange={(value) => {
        onChange(value)
      }}
      {...rest}
    />
  )
}
