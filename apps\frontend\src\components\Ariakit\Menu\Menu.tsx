import {
  MenuProps as MenuPropsAriakit,
  MenuProviderProps,
  <PERSON><PERSON> as MenuAriakit,
  <PERSON>uProvider as MenuProviderAriakit,
  MenuGroup as MenuGroupAriakit,
  MenuGroupLabel as MenuGroupLabelAriakit,
} from "@ariakit/react"
import c from "classnames"
import { ComponentProps, forwardRef } from "react"

import { AnimatedPopover } from "components/Popover/Popover"
import { color } from "styles/colors"
import { Heading } from "ui"

import styles from "./Menu.module.css"

export type MenuProps = MenuPropsAriakit<"div"> & { hasSubmenu?: boolean }

const Menu = forwardRef<HTMLDivElement, MenuProps>(
  ({ className = "", hasSubmenu, ...rest }, ref) => {
    return (
      <MenuAriakit
        render={(p) => (
          <AnimatedPopover
            className={c(
              className,
              styles.menu,
              color.levBlue,
              hasSubmenu && styles.hasSubmenu
            )}
            {...p}
          />
        )}
        unmountOnHide
        gutter={4}
        {...rest}
        ref={ref}
      />
    )
  }
)

const MenuProvider = (props: MenuProviderProps) => (
  <MenuProviderAriakit animated {...props} />
)

const MenuGroup = forwardRef<
  HTMLDivElement,
  ComponentProps<typeof MenuGroupAriakit>
>(({ className, ...rest }, ref) => {
  return (
    <MenuGroupAriakit
      {...rest}
      className={c(className, styles.group)}
      ref={ref}
    />
  )
})

const MenuGroupLabel = forwardRef<
  HTMLDivElement,
  ComponentProps<typeof MenuGroupLabelAriakit>
>((props, ref) => {
  return (
    <MenuGroupLabelAriakit
      render={<Heading size="xsmall" as="div" />}
      {...props}
      className={c(styles.groupLabel, props.className)}
      ref={ref}
    />
  )
})

export { Menu, MenuProvider, MenuGroup, MenuGroupLabel }
