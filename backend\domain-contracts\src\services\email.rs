//! Email service contract

use crate::errors::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailSubmitInput {
    pub to_email: String,
    pub to_name: String,
    pub subject: String,
    pub body: String,
    pub attachments: Vec<EmailAttachment>,
    pub from_email: Option<String>,
    pub from_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailSendInput {
    pub to_email: String,
    pub to_name: String,
    pub subject_template: String,
    pub body_template: String,
    pub params: HashMap<String, String>,
    pub attachments: Vec<EmailAttachment>,
    pub from_email: Option<String>,
    pub from_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailAttachment {
    pub filename: String,
    pub content: Vec<u8>,
    pub content_type: String,
}

/// Contract for email sending service
#[async_trait]
#[mockall::automock]
pub trait EmailSender: Send + Sync {
    /// Submit an email for sending with pre-processed content
    async fn submit(&self, input: EmailSubmitInput) -> Result<()>;

    /// Send an email with template processing
    async fn send(&self, input: EmailSendInput) -> Result<()>;
}
