import {
  ParticipantAttendanceRequest,
  ParticipantAttendanceState,
  ParticipantAttendeeSource,
  ParticipantRsvpStatus,
} from "generated/graphql"

import { ViewSection } from "../EventInstance/ViewSection/ViewSection"
import { SelectProviders } from "../SelectProviders/SelectProviders"
import { SelectSubjects } from "../SelectSubjects/SelectSubjects"
import styles from "./Participants.module.css"
import { SelectedUser } from "./SelectedUser"
import { useParticipantsState } from "./useParticipantsState"

export type Participant = {
  userId: string
  participantId?: string
  name: string
  attendanceRequest: ParticipantAttendanceRequest
  participantType: ParticipantAttendeeSource
  rsvpStatus: ParticipantRsvpStatus | null
  attendance: ParticipantAttendanceState | null
  isAvailable: boolean
  personaId?: string | null
  specialty?: string | null
  phoneNumber?: string | null
}

type Props = {
  formType?: "Create" | "Edit"
  initialParticipants?: Participant[]
  eventToDate?: string
}

export const Participants = ({
  formType = "Create",
  initialParticipants,
  eventToDate,
}: Props) => {
  const { selectedParticipants, onRemove, onSelect } =
    useParticipantsState(initialParticipants)

  const subjects = selectedParticipants.filter(
    (p) => p.participantType === ParticipantAttendeeSource.Subject
  )

  const providers = selectedParticipants.filter(
    (p) => p.participantType === ParticipantAttendeeSource.Provider
  )

  return (
    <ViewSection hasLabel iconName="group-line">
      <div className={styles.selectParticipantsWrap}>
        <div>
          <SelectSubjects
            onSelect={onSelect}
            selectedParticipants={selectedParticipants}
            formType={formType}
          />
          <ul className={styles.participantsList}>
            {subjects.map((subject) => (
              <SelectedUser
                key={subject.userId}
                user={subject}
                onRemove={() => onRemove(subject)}
                eventToDate={eventToDate}
              />
            ))}
          </ul>
        </div>
        <div>
          <SelectProviders
            onSelect={onSelect}
            selectedParticipants={selectedParticipants}
            formType={formType}
          />
          <ul className={styles.participantsList}>
            {providers.map((provider) => (
              <SelectedUser
                key={provider.userId}
                user={provider}
                onRemove={() => onRemove(provider)}
              />
            ))}
          </ul>
        </div>
      </div>
    </ViewSection>
  )
}
