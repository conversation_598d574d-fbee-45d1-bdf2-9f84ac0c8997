import { TFunction } from "i18next"

import { EventRepeatInterval } from "generated/graphql"

export const DO_NOT_REPEAT = "DO_NOT_REPEAT"

export default (t: TFunction) => {
  return [
    {
      label: t("Do not repeat"),
      value: DO_NOT_REPEAT,
    },
    {
      label: t("Every weekday"),
      value: EventRepeatInterval.EveryWeekday,
    },
    {
      label: t("Weekly"),
      value: EventRepeatInterval.Weekly,
    },
    {
      label: t("Monthly"),
      value: EventRepeatInterval.Monthly,
    },
  ]
}
