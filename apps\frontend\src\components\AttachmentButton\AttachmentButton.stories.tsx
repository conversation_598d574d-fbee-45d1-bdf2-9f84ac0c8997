import { <PERSON><PERSON>, <PERSON> } from "@storybook/react-vite"

import { AttachmentButton, AttachmentButtonProps } from "./AttachmentButton"

export default {
  title: "Form Components/Attachment Button",
  component: AttachmentButton,
} as Meta<typeof AttachmentButton>

export const AttachmentButtonDefault: Story<AttachmentButtonProps> = (args) => {
  return <AttachmentButton {...args}>{"Attachment "}</AttachmentButton>
}
