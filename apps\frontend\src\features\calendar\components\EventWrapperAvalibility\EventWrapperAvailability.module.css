.wrap {
  position: absolute;
  width: 100%;
  border-radius: 8px;
  border: 1px dashed var(--color-700);
}

.innerWrap {
  margin: 0px 1px 1px 2px;
  height: calc(100% - 1px);
}

.wrap :global(.rbc-event) {
  position: absolute;
}

.availabilitySlot {
  width: 100%;
}

.availabilitySlotButton {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: transparent;
  border-bottom: 1px dashed
    color-mix(in oklch, var(--color-700) 30%, transparent);
  background-clip: padding-box;
  position: relative;
  transition:
    background-color 0.1s,
    color 0.1s;
  container-type: size;
}
.availabilitySlotButton:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.availabilitySlotButton:last-of-type {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-bottom: none;
}

button.availabilitySlotButton {
  cursor: pointer;
}

button.availabilitySlotButton:hover {
  color: var(--color-text);
}

.availabilitySlotFooter {
  display: flex;
  justify-content: flex-end;
  gap: var(--grid-gap);
  padding-bottom: var(--grid-gap);
}

.timeSlotLabel {
  position: absolute;
  top: 2px;
  left: 2px;
  font-size: 12px;
}

/* When container width is 85px or less, only show start time */
@container (max-width: 85px) {
  .timeSlotEnd {
    display: none;
  }
}

/* When container height is 18px or less (10 min slots) move the time slot label to the top */
@container (max-height: 18px) {
  .timeSlotLabel {
    top: 0;
    left: 0;
  }
}

/* When container height is 10px or less (5 min slots) we hide the timeSlotLabel */
@container (max-height: 10px) {
  .timeSlotLabel {
    display: none;
  }
}

/* When container width is 28px or less,  we hide the timeSlotLabel */
@container (max-width: 28px) {
  .timeSlotLabel {
    display: none;
  }
}
