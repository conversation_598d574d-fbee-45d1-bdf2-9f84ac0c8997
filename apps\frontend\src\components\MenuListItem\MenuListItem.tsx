import c from "classnames"
import { forwardRef, ReactNode } from "react"

import { Text } from "ui"

import styles from "./MenuListItem.module.css"

export type MenuListItemOwnProps = {
  subContent?: ReactNode
  subContentClassName?: string
  direction?: "vertical" | "horizontal"
}

export default forwardRef<
  HTMLDivElement,
  MenuListItemOwnProps & JSX.IntrinsicElements["div"]
>(function MenuListItem(
  {
    children,
    subContent,
    className,
    subContentClassName,
    direction = "horizontal",
    ...rest
  },
  ref
) {
  return (
    <div
      ref={ref}
      className={c(className, styles.item, {
        [styles.vertical]: direction === "vertical",
      })}
      {...rest}
    >
      {children}
      {subContent && (
        <Text size="small" secondary className={subContentClassName}>
          {subContent}
        </Text>
      )}
    </div>
  )
})
