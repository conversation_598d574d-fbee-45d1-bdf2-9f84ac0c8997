import { addYears } from "date-fns"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import Panel from "components/Panel/Panel"

import { useGetSubjectEventsQuery } from "generated/graphql"

import styles from "./SubjectUpcomingEvents.module.css"
import { UpcomingEventCard } from "./UpcomingEventCard"

type UpcomingEventsProps = {
  subjectId: string
}

export const SubjectUpcomingEvents = ({ subjectId }: UpcomingEventsProps) => {
  const { t } = useTranslation()

  const inputFilter = useMemo(() => {
    return {
      participantSubjectId: [subjectId],
      fromDate: new Date(),
      toDate: addYears(new Date(), 10),
      includeCanceled: false,
    }
  }, [subjectId])

  const { data, error } = useGetSubjectEventsQuery({
    variables: {
      inputFilter,
      limit: null,
    },
  })

  return (
    <ul className={styles.eventList}>
      {error && <Panel status="error">{t("errorLoadingUpcomingEvents")}</Panel>}
      {data?.eventInstances.map((eventInstance) => (
        <UpcomingEventCard
          key={eventInstance.id}
          eventInstance={eventInstance}
        />
      ))}
    </ul>
  )
}
