import { format } from "date-fns"
import { useState } from "react"
import { useTranslation } from "react-i18next"

import MessageIllustration from "@leviosa/assets/illustrations/message.svg?react"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import {
  Button,
  Heading,
  Label,
  notification,
  Table,
  Text,
  Textarea,
  TextWithIcon,
} from "ui"
import { InlineTextForm } from "ui/components/InlineTextForm/InlineTextForm"
import Switch from "ui/components/Switch/Switch"

import {
  useUpdateSubjectMutation,
  useSendCustomMessageMutation,
  useGetMessagesQuery,
  MessageFragmentFragment,
  namedOperations,
  useToggleRemindersForSubjectMutation,
  SubjectNotificationsConfig,
  MessageCategory,
} from "generated/graphql"

import styles from "./SubjectCommunication.module.css"

export type SubjectCommunicationProps = {
  subjectId: string
  subjectPhoneNumber: string | null
  subjectNotificationConfig?: Pick<
    SubjectNotificationsConfig,
    "allowed" | "category"
  >[]
}

const SMSReminderToggle = ({
  subjectId,
  remindersAllowed,
}: {
  subjectId: string
  remindersAllowed: boolean
}) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "communications",
  })

  const [toggleRemindersForSubject] = useToggleRemindersForSubjectMutation()

  return (
    <div className={styles.reminderToggle}>
      <Switch
        id="reminder-toggle"
        checked={remindersAllowed}
        onToggle={() => {
          toggleRemindersForSubject({
            variables: {
              subjectId: subjectId,
              allowed: !remindersAllowed,
            },
          })
        }}
      />
      <Text as="label" htmlFor="reminder-toggle">
        {tRoutes("automaticRemindersForAppointments")}
      </Text>
    </div>
  )
}

const NewMessageForm = ({
  subjectId,
  phoneNumber,
  hideForm,
}: {
  subjectId: string
  phoneNumber: string
  hideForm: () => void
}) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "communications",
  })
  const [message, setMessage] = useState("")
  const [sendCustomMessage] = useSendCustomMessageMutation()

  const countryCode = "+354"

  return (
    <div>
      <Label className={styles.label}>{t("phoneNumber")}</Label>
      <Text
        className={styles.phoneNumber}
      >{`${countryCode} | ${phoneNumber}`}</Text>
      <Textarea
        label={t("message")}
        className={styles.textMessage}
        maxLength={300}
        value={message}
        onChange={(e) => setMessage(e.target.value)}
      />

      <div className={styles.messageFormButtons}>
        <Button variant="clear" onClick={() => hideForm()}>
          Cancel
        </Button>
        <Button
          variant="filled"
          onClick={() => {
            sendCustomMessage({
              variables: {
                input: {
                  subjectId,
                  content: message,
                },
              },
              onCompleted: () => {
                notification.create({
                  message: tRoutes("smsSent"),
                  status: "success",
                })
              },
              onError: () => {
                notification.create({
                  message: tRoutes("smsNotSent"),
                  status: "error",
                })
              },
              refetchQueries: [namedOperations.Query.GetMessages],
            })
            hideForm()
          }}
        >
          {t("send")}
        </Button>
      </div>
    </div>
  )
}

const CommunicationTable = ({
  showMessageForm,
  messages,
  subjectId,
  remindersAllowed,
}: {
  showMessageForm: () => void
  messages: MessageFragmentFragment[]
  subjectId: string
  remindersAllowed: boolean
}) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "communications",
  })

  const getMessageFrom = (message: MessageFragmentFragment) => {
    if (message.category === MessageCategory.Custom) {
      return message.sender.name
    }

    return tEnum(`MessageCategory.${message.category}`)
  }

  return (
    <div className={styles.wrap}>
      <SMSReminderToggle
        subjectId={subjectId}
        remindersAllowed={remindersAllowed}
      />
      <Table spacing="narrow" hoverable={false}>
        <thead>
          <tr>
            <th className={styles.tableHeaderCell}>{t("sent")}</th>
            <th>{t("status")}</th>
            <th>{t("from")}</th>
            <th>{t("type")}</th>
            <th className={styles.messageContentCell} />
          </tr>
        </thead>

        <tbody>
          {messages.slice(0, 5).map((message) => (
            <tr>
              <td>
                {message.sentAt &&
                  format(new Date(message.sentAt), "dd.MM.yyyy HH:mm")}
              </td>
              <td>{tEnum(`MessageStatus.${message.status}`)}</td>
              <td>{getMessageFrom(message)}</td>
              <td>{tEnum(`MessageType.${message.messageType}`)}</td>
              <td>
                {message.content && (
                  <Tooltip
                    tooltipContent={message.content}
                    placement="bottom-end"
                  >
                    <div className={styles.messageIcon}>
                      <Icon name="mail-line" />
                    </div>
                  </Tooltip>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
      <Panel className={styles.infoPanel}>
        <TextWithIcon>{tRoutes("onlyShowingMostRecentMessages")}</TextWithIcon>
      </Panel>
      <Button
        variant="filled"
        className={styles.newMessageButton}
        icon={<Icon name="add-line" />}
        onClick={() => showMessageForm()}
      >
        {t("newMessage")}
      </Button>
    </div>
  )
}

const NoPhoneNumberWarning = ({ subjectId }: { subjectId: string }) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "communications",
  })

  const [updateSubject] = useUpdateSubjectMutation()

  return (
    <Panel status="warning">
      <TextWithIcon weight="bold">{tRoutes("noSubjectPhoneNr")}</TextWithIcon>
      <div className={styles.noPhoneNumber}>
        <Text>{tRoutes("registerPhoneNrToSend")}</Text>
        <InlineTextForm
          onSubmit={({ value }) => {
            const number = Number(value)
            if (isNaN(number)) {
              return
            }

            updateSubject({
              variables: {
                id: subjectId,
                input: {
                  phoneNumber: {
                    set: value,
                  },
                },
              },
            })
          }}
        />
      </div>
    </Panel>
  )
}

const NoMessages = ({
  showForm,
  subjectId,
  remindersAllowed,
}: {
  showForm: () => void
  subjectId: string
  remindersAllowed: boolean
}) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "communications",
  })
  return (
    <div className={styles.noMessages}>
      <SMSReminderToggle
        subjectId={subjectId}
        remindersAllowed={remindersAllowed}
      />

      <MessageIllustration />
      <Heading>{tRoutes("noMessagesSentToSubject")}</Heading>
      <Button
        variant="filled"
        className={styles.newMessageButton}
        icon={<Icon name="add-line" />}
        onClick={() => showForm()}
      >
        {t("newMessage")}
      </Button>
    </div>
  )
}

export default function SubjectCommunication({
  subjectId,
  subjectPhoneNumber,
  subjectNotificationConfig,
}: SubjectCommunicationProps) {
  const [showNewMessageForm, setShowNewMessageForm] = useState(false)

  const { data } = useGetMessagesQuery({
    variables: {
      filter: {
        subjectId,
      },
    },
  })

  const messages = data?.messages || []

  const remindersAllowed =
    subjectNotificationConfig?.some((config) => {
      return config.category === MessageCategory.Reminder && config.allowed
    }) || false

  if (!subjectPhoneNumber) {
    return <NoPhoneNumberWarning subjectId={subjectId} />
  }

  if (showNewMessageForm) {
    return (
      <NewMessageForm
        subjectId={subjectId}
        phoneNumber={subjectPhoneNumber}
        hideForm={() => setShowNewMessageForm(false)}
      />
    )
  }

  if (messages.length === 0) {
    return (
      <NoMessages
        showForm={() => setShowNewMessageForm(true)}
        subjectId={subjectId}
        remindersAllowed={remindersAllowed}
      />
    )
  }

  return (
    <CommunicationTable
      messages={messages}
      showMessageForm={() => setShowNewMessageForm(true)}
      subjectId={subjectId}
      remindersAllowed={remindersAllowed}
    />
  )
}
