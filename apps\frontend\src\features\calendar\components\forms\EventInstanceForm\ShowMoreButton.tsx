import { Disclosure, useDisclosureStore } from "@ariakit/react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Button } from "ui"

type Props = {
  store: ReturnType<typeof useDisclosureStore>
  className?: string
}

export const ShowMoreButton = ({ store, className }: Props) => {
  const { t } = useTranslation()
  const isOpen = store.useState().open
  return (
    <Button
      as={Disclosure}
      store={store}
      variant="clear"
      iconEnd={<Icon name={isOpen ? "arrow-up-s-line" : "arrow-down-s-line"} />}
      className={className}
    >
      {isOpen ? t("Hide more options") : t("More options")}
    </Button>
  )
}
