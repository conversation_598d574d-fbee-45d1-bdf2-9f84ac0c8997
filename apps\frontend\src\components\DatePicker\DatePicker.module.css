.wrap {
  position: relative;
}

.wrap[data-read-only="true"] {
  pointer-events: none;
}

.wrap [data-disabled="true"] {
  pointer-events: none;
}

.datePickerInput::-webkit-calendar-picker-indicator {
  display: none;
}

.wrap input {
  min-width: 140px;
}

.calendarIcon {
  position: absolute;
  top: 22px;
  right: 1px;
  background: transparent;
  cursor: pointer;
  z-index: var(--z-index-base);
  width: 32px;
  height: 44px;
  transition: right 200ms ease-in-out;
}
.calendarIcon.hasIcon {
  right: 26px;
}

.calendar {
  position: absolute;
  top: calc(100% - 4px);
  left: 0;
  background-color: var(--color-white);
  padding: 10px;
  z-index: var(--z-index-select-popover);
  max-height: unset;
  min-width: 280px;
}

.footer {
  display: flex;
  justify-content: flex-end;
}

.customDateInput::-webkit-inner-spin-button,
.customDateInput::-webkit-calendar-picker-indicator {
  display: none;
  -webkit-appearance: none;
}
