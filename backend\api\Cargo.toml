[package]
name = "leviosa-api"
version = "0.1.0"
edition.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
leviosa-macros = { path = "../macros" }
leviosa-domain-contracts = { path = "../domain-contracts" }
leviosa-domain = { path = "../domain" }
leviosa-infrastructure = { path = "../infrastructure" }
async-graphql.workspace = true
async-graphql-axum.workspace = true
axum.workspace = true
anyhow.workspace = true
async-trait.workspace = true
chrono.workspace = true
futures.workspace = true
getset.workspace = true
serde.workspace = true
serde_json.workspace = true
sqlx.workspace = true
thiserror.workspace = true
tokio.workspace = true
uuid.workspace = true
rand.workspace = true
base64.workspace = true
reqwest.workspace = true
heck.workspace = true
validator.workspace = true
regex.workspace = true
paste.workspace = true
sentry.workspace = true
sea-orm.workspace = true
urlencoding.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
sentry-tracing.workspace = true
tower-http.workspace = true
serde_with.workspace = true
mockall.workspace = true
indexmap.workspace = true
cookie.workspace = true

[dev-dependencies]
insta.workspace = true
graphql_client.workspace = true
tokio-shared-rt.workspace = true
migration = { path = "../migration" }
leviosa-testing = { path = "../testing" }

[package.metadata.commands]
doc = "cd ../../ && ./backend/api/scripts/doc.sh && open target/doc/leviosa_backend_rust/index.html"
