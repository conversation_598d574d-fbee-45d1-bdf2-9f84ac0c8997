import { useState } from "react"
import { useTranslation } from "react-i18next"

import { DatePicker } from "components/DatePicker/DatePicker"
import Icon from "components/Icon/Icon"
import { Button, Modal, notification } from "ui"

import {
  AvailabilityScheduleFragmentFragment,
  useUpdateAvailabilityScheduleMutation,
} from "generated/graphql"

import DeleteAvailabilityScheduleDialog from "../DeleteAvailabilityScheduleDialog/DeleteAvailabilityScheduleDialog"
import styles from "./SchedulePeriodModal.module.css"

type SchedulePeriodModalProps = {
  showModal: boolean
  onClose: () => void
  schedules: AvailabilityScheduleFragmentFragment[]
  providerId: string
  setShowCreateScheduleModal: (show: boolean) => void
}

const SchedulePeriodModal = ({
  showModal,
  onClose,
  schedules,
  providerId,
  setShowCreateScheduleModal,
}: SchedulePeriodModalProps) => {
  const { t } = useTranslation()
  const [updateAvailabilitySchedule] = useUpdateAvailabilityScheduleMutation()

  const [showPastSchedules, setShowPastSchedules] = useState(false)
  const [scheduleToDelete, setScheduleToDelete] = useState<string | null>(null)

  const pastSchedules = schedules.filter(
    (schedule) => new Date(schedule.toDate as string) < new Date()
  )
  const futureSchedules = schedules.filter(
    (schedule) => new Date(schedule.toDate as string) > new Date()
  )

  const Schedule = ({
    schedule,
  }: {
    schedule: AvailabilityScheduleFragmentFragment
  }) => {
    return (
      <div key={schedule.id} className={styles.periodContainer}>
        <DatePicker
          hideMessage
          hideLabel
          label={t("From Date")}
          value={schedule.fromDate || undefined}
          className={styles.datePicker}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            updateAvailabilitySchedule({
              variables: {
                input: {
                  id: schedule.id,
                  toDate: schedule.toDate,
                  fromDate: e.target.value,
                },
              },
              onError: (error) => {
                onClose()
                notification.create({
                  message: error.message,
                  status: "error",
                })
              },
            })
          }}
          onKeyDown={(e) => {
            e.preventDefault()
          }}
          inputProps={{
            className: styles.dateInput,
          }}
        />
        {" - "}
        <DatePicker
          hideMessage
          hideLabel
          label={t("To Date")}
          value={schedule.toDate || undefined}
          className={styles.datePicker}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            updateAvailabilitySchedule({
              variables: {
                input: {
                  id: schedule.id,
                  fromDate: schedule.fromDate,
                  toDate: e.target.value,
                },
              },
              onError: (error) => {
                notification.create({
                  message: error.message,
                  status: "error",
                })
              },
            })
          }}
          inputProps={{
            className: styles.dateInput,
          }}
        />
        <Button
          icon={<Icon name="delete-bin-line" />}
          variant="clear"
          className={styles.deleteButton}
          onClick={() => {
            setScheduleToDelete(schedule.id)
          }}
        />
      </div>
    )
  }

  return (
    <Modal
      isOpen={showModal}
      title={t("Schedule Periods")}
      onClose={onClose}
      allowOverflow
    >
      <div className={styles.container}>
        {futureSchedules.map((schedule) => (
          <Schedule key={schedule.id} schedule={schedule} />
        ))}

        <Button
          onClick={() => {
            setShowCreateScheduleModal(true)
          }}
          icon={<Icon name="add-line" />}
          className={styles.addNewPeriodButton}
          size="small"
        >
          {t("Add New Period")}
        </Button>
        {pastSchedules.length > 0 && (
          <>
            <Button
              onClick={() => setShowPastSchedules(!showPastSchedules)}
              className={styles.showPastSchedulesButton}
              variant="clear"
              size="small"
            >
              {showPastSchedules
                ? t("Hide past schedules")
                : t("Show past schedules")}
            </Button>
            {showPastSchedules && (
              <>
                <hr />
                {pastSchedules.map((schedule) => (
                  <Schedule key={schedule.id} schedule={schedule} />
                ))}
              </>
            )}
          </>
        )}
      </div>

      <DeleteAvailabilityScheduleDialog
        isOpen={!!scheduleToDelete}
        scheduleId={scheduleToDelete || ""}
        providerId={providerId}
        onClose={() => setScheduleToDelete(null)}
        schedules={schedules}
      />
    </Modal>
  )
}

export default SchedulePeriodModal
