import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"

import styles from "./ViewSection.module.css"

type ViewSectionConflictProps = {
  message: string
}

export const ViewSectionConflict = ({ message }: ViewSectionConflictProps) => {
  return (
    <Tooltip
      tooltipContent={message}
      tooltipClassName={styles.tooltip}
      className={styles.conflict}
      status="error"
    >
      <Icon name="information-line" className={styles.conflictIcon} />
    </Tooltip>
  )
}
