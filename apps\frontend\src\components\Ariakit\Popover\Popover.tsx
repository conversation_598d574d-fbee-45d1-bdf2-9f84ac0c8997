import {
  PopoverDisclosure as AriakitPopoverDisclosure,
  Popover as AriakitPopover,
  PopoverDisclosureProps,
  PopoverProps,
  usePopoverStore as useAriakitPopoverStore,
  PopoverStoreProps,
} from "@ariakit/react"
import { forwardRef, Ref } from "react"

import { Button, ButtonOwnProps } from "ui"

import { AnimatedPopover, AnimatedPopoverProps } from "../../Popover/Popover"

export {
  PopoverArrow,
  PopoverDescription,
  PopoverHeading,
} from "@ariakit/react"
export type { PopoverDisclosureProps, PopoverProps } from "@ariakit/react"

export const PopoverDisclosure = forwardRef<
  HTMLButtonElement,
  ButtonOwnProps & PopoverDisclosureProps
>(function PopoverDisclosure({ ...props }, ref) {
  return (
    <AriakitPopoverDisclosure
      ref={ref}
      render={
        props.render
          ? props.render
          : (p) => <Button {...p} ref={p.ref as Ref<HTMLButtonElement>} />
      }
      {...props}
    />
  )
})

export const Popover = forwardRef<
  HTMLDivElement,
  PopoverProps & AnimatedPopoverProps
>(function Popover(props, ref) {
  return (
    <AriakitPopover
      render={<AnimatedPopover />}
      ref={ref}
      gutter={4}
      unmountOnHide
      {...props}
    />
  )
})

export const usePopoverStore = (props?: PopoverStoreProps) =>
  useAriakitPopoverStore({
    animated: true,
    ...props,
  })
