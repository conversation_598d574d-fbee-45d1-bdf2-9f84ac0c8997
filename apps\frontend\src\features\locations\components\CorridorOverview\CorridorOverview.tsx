import { useState } from "react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { But<PERSON>, Heading, notification } from "ui"

import { useUpdateCorridorMutation } from "generated/graphql"

import { AddBedToRoom } from "../AddBedToRoom/AddBedToRoom"
import { Building } from "../BuildingOverview/BuildingOverview"
import { RoomTable } from "../RoomTable/RoomTable"
import { UpdateRoom } from "../UpdateRoom/UpdateRoom"
import { CorridorForm } from "../forms/CorridorForm/CorridorForm"
import { AddRoomToCorridor } from "./AddRoomToCorridor"
import styles from "./CorridorOverview.module.css"

type CorridorOverviewProps = {
  buildingId: string
  activeCorridor: Building["corridors"]["corridors"][0]
}

export const CorridorOverview = ({
  activeCorridor,
  buildingId,
}: CorridorOverviewProps) => {
  const [showCorridorForm, setShowCorridorForm] = useState(false)
  const [roomId, setRoomId] = useState<string | null>(null)
  const [addBedRoomId, setAddBedRoomId] = useState<string | null>(null)

  const { t } = useTranslation()

  const { t: tLocation } = useTranslation("routes", {
    keyPrefix: "locations",
  })

  const [updateCorridor, { loading, error: updateCorridorError }] =
    useUpdateCorridorMutation({
      onCompleted: (data) => {
        if (data) {
          notification.create({
            message: t("Corridor has been updated"),
            status: "success",
            maxWidth: "500px",
          })
        }

        setShowCorridorForm(false)
      },
    })

  const { label, rooms, beds } = activeCorridor

  const room = rooms.rooms.find((room) => room.id === roomId)

  return (
    <>
      {!showCorridorForm ? (
        <>
          <div className={styles.corridorTitleWrap}>
            <Heading size="large">{label}</Heading>
            <Button
              icon={<Icon name={"pencil-line"} />}
              onClick={() => setShowCorridorForm(true)}
            >
              {t("Edit")}
            </Button>
          </div>
          <br />
          <p>
            {tLocation("corridorDescription", {
              label: label,
              roomsCount: rooms.count,
              bedsCount: beds.count,
            })}
          </p>
          <br />
        </>
      ) : (
        // replace with InlineTextForm when it's merged
        <CorridorForm
          onSubmit={({ label }) => {
            updateCorridor({
              variables: {
                input: {
                  id: activeCorridor.id,
                  label,
                },
              },
            })
          }}
          label={label}
          onCancel={() => setShowCorridorForm(false)}
          loading={loading}
          error={updateCorridorError}
        />
      )}

      <br />

      <RoomTable
        rooms={rooms.rooms}
        onEditRoomClick={(id) => setRoomId(id)}
        onAddBedClick={(roomId) => {
          setAddBedRoomId(roomId)
        }}
      />
      <br />

      <AddRoomToCorridor
        buildingId={buildingId}
        activeCorridor={activeCorridor}
      />

      <UpdateRoom
        room={room}
        roomId={roomId}
        onCancel={() => setRoomId(null)}
      />

      <AddBedToRoom
        room={room}
        roomId={addBedRoomId}
        onClose={() => {
          setAddBedRoomId(null)
        }}
      />
    </>
  )
}
