/* eslint-disable @typescript-eslint/no-empty-function */
import { createContext, ReactNode, useContext } from "react"

import { usePowerMenuState } from "./usePowerMenuState"

const PowerMenuContext = createContext<ReturnType<typeof usePowerMenuState>>({
  open: () => {},
  close: () => {},
  addPowerMenuGroups: () => {},
  removePowerMenuGroups: () => {},
  changeInputValues: () => {},
  selectItem: async () => {},
  setPrevArgument: () => {},
  isOpen: false,
  inputValue: "",
  args: [],
  filteredGroups: [],
  isLoading: false,
})

export const usePowerMenu = () => {
  return useContext(PowerMenuContext)
}

export const PowerMenuProvider = ({ children }: { children: ReactNode }) => {
  const powerMenu = usePowerMenuState()

  return (
    <PowerMenuContext.Provider value={powerMenu}>
      {children}
    </PowerMenuContext.Provider>
  )
}
