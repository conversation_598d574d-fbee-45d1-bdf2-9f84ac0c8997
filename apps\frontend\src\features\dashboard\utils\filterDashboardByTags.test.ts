import { DashboardSelectedTagMap } from "features/dashboard/components/Dashboard/DashboardTags/DashboardTags"

import filterDashboardByTag from "./filterDashboardByTags"

describe("filterDashboardByTag", () => {
  it("should include rows with at least one included tag and no excluded tags", () => {
    const selectedTags: DashboardSelectedTagMap = { tag1: 1, tag2: -1, tag3: 1 }
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: ["tag1", "tag4"] }
    expect(filter(row)).toBe(true)
  })

  it("should exclude rows with excluded tags even if they have included tags", () => {
    const selectedTags: DashboardSelectedTagMap = { tag1: 1, tag2: -1, tag3: 1 }
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: ["tag1", "tag2"] }
    expect(filter(row)).toBe(false)
  })

  it("should include rows with only included tags", () => {
    const selectedTags: DashboardSelectedTagMap = { tag1: 1, tag3: 1 }
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: ["tag1", "tag4"] }
    expect(filter(row)).toBe(true)
  })

  it("should exclude rows with only excluded tags", () => {
    const selectedTags: DashboardSelectedTagMap = { tag2: -1 }
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: ["tag2", "tag4"] }
    expect(filter(row)).toBe(false)
  })

  it("should include rows with no tags to include or exclude", () => {
    const selectedTags: DashboardSelectedTagMap = {}
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: ["tag1", "tag4"] }
    expect(filter(row)).toBe(true)
  })

  it("should include rows with no tags if there are no tags to include or exclude", () => {
    const selectedTags: DashboardSelectedTagMap = {}
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: [] }
    expect(filter(row)).toBe(true)
  })

  it("should exclude rows with excluded tags even if there are no included tags", () => {
    const selectedTags: DashboardSelectedTagMap = { tag2: -1 }
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: ["tag2"] }
    expect(filter(row)).toBe(false)
  })

  it("should include rows with included tags even if there are no excluded tags", () => {
    const selectedTags: DashboardSelectedTagMap = { tag1: 1 }
    const filter = filterDashboardByTag(selectedTags)
    const row = { tags: ["tag1"] }
    expect(filter(row)).toBe(true)
  })
})
