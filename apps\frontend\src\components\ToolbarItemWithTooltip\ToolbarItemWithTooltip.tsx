import React, { ReactNode } from "react"

import { ToolbarItem } from "components/Ariakit/Toolbar/Toolbar"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"

import styles from "./ToolbarItemWithTooltip.module.css"

type ToolbarItemWithTooltipProps = {
  children?: React.ReactNode
  onClick?: () => void
  icon: React.ReactNode
  isActive?: boolean
  tooltipContent?: ReactNode | null
}

export const ToolbarItemWithTooltip = ({
  onClick,
  children,
  icon,
  isActive = false,
  tooltipContent = null,
}: ToolbarItemWithTooltipProps) => {
  return (
    <Tooltip tooltipContent={tooltipContent}>
      <div className={styles.wrap} data-is-active={isActive}>
        <ToolbarItem onClick={onClick} className={styles.btn}>
          {icon}
        </ToolbarItem>
        {children}
      </div>
    </Tooltip>
  )
}
