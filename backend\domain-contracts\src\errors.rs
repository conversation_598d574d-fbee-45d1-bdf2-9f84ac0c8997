//! Error types for the domain contracts

/// A custom error with a field name and message
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct CustomError {
    pub field: Option<&'static str>,
    pub message: String,
}

impl CustomError {
    pub fn new(field: Option<&'static str>, message: String) -> Self {
        Self { field, message }
    }
}

impl std::fmt::Display for CustomError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self.field {
            Some(field) => write!(f, "{}: {}", field, self.message),
            None => write!(f, "{}", self.message),
        }
    }
}

impl std::error::Error for CustomError {}

/// A not found error
#[derive(Debug, <PERSON>lone)]
pub struct NotFoundError {
    pub entity: String,
    pub value: String,
    pub field: String,
}

impl NotFoundError {
    pub fn new(entity: &str, value: &str, field: &str) -> Self {
        Self {
            entity: entity.to_string(),
            value: value.to_string(),
            field: field.to_string(),
        }
    }

    pub fn custom(entity: &str, value: &str, field: &str) -> Self {
        Self::new(entity, value, field)
    }
}

impl std::fmt::Display for NotFoundError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{} with {} '{}' not found", self.entity, self.field, self.value)
    }
}

impl std::error::Error for NotFoundError {}

impl From<NotFoundError> for Error {
    fn from(err: NotFoundError) -> Self {
        Error::NotFound(err.to_string())
    }
}

/// The main error type for the domain
#[derive(Debug)]
pub enum Error {
    Authorization(AuthorizationError),
    Validation(CustomError),
    NotFound(String),
    Internal(anyhow::Error),
}

impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Error::Authorization(err) => write!(f, "Authorization error: {}", err),
            Error::Validation(err) => write!(f, "Validation error: {}", err),
            Error::NotFound(msg) => write!(f, "Not found: {}", msg),
            Error::Internal(err) => write!(f, "Internal error: {}", err),
        }
    }
}

impl std::error::Error for Error {}

impl From<AuthorizationError> for Error {
    fn from(err: AuthorizationError) -> Self {
        Error::Authorization(err)
    }
}

impl From<CustomError> for Error {
    fn from(err: CustomError) -> Self {
        Error::Validation(err)
    }
}

impl From<anyhow::Error> for Error {
    fn from(err: anyhow::Error) -> Self {
        Error::Internal(err)
    }
}

/// Authorization-specific errors
#[derive(Debug)]
pub enum AuthorizationError {
    NotAuthenticated,
    Unauthorized,
    MalformedToken(anyhow::Error),
}

impl std::fmt::Display for AuthorizationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AuthorizationError::NotAuthenticated => write!(f, "Not authenticated"),
            AuthorizationError::Unauthorized => write!(f, "Unauthorized"),
            AuthorizationError::MalformedToken(err) => write!(f, "Malformed token: {}", err),
        }
    }
}

impl std::error::Error for AuthorizationError {}

/// Result type alias
pub type Result<T> = std::result::Result<T, Error>;

/// Extension trait for Option
pub trait OptionExt<T> {
    fn is_none_or<F>(&self, f: F) -> bool
    where
        F: FnOnce(&T) -> bool;
}

impl<T> OptionExt<T> for Option<T> {
    fn is_none_or<F>(&self, f: F) -> bool
    where
        F: FnOnce(&T) -> bool,
    {
        match self {
            None => true,
            Some(v) => f(v),
        }
    }
}
