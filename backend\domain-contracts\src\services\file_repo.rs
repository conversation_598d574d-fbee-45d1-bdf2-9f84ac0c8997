//! File repository service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileUploadRequest {
    pub filename: String,
    pub content_type: String,
    pub content: Vec<u8>,
    pub metadata: Option<FileMetadata>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct FileMetadata {
    pub description: Option<String>,
    pub tags: Vec<String>,
    pub subject_id: Option<SubjectId>,
    pub provider_id: Option<ProviderId>,
    pub organisation_id: OrganisationId,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FileInfo {
    pub id: String,
    pub filename: String,
    pub content_type: String,
    pub size: u64,
    pub uploaded_at: DateTime<Utc>,
    pub metadata: Option<FileMetadata>,
    pub url: String,
}

/// Contract for file repository service
#[async_trait]
#[mockall::automock]
pub trait FileRepo: Send + Sync {
    /// Upload a file to the repository
    async fn upload_file(
        &self,
        request: FileUploadRequest,
        token: &str,
    ) -> Result<FileInfo>;

    /// Download a file from the repository
    async fn download_file(
        &self,
        file_id: &str,
        token: &str,
    ) -> Result<Vec<u8>>;

    /// Get file information
    async fn get_file_info(
        &self,
        file_id: &str,
        token: &str,
    ) -> Result<FileInfo>;

    /// Delete a file from the repository
    async fn delete_file(
        &self,
        file_id: &str,
        token: &str,
    ) -> Result<()>;

    /// List files with optional filtering
    async fn list_files(
        &self,
        subject_id: Option<SubjectId>,
        provider_id: Option<ProviderId>,
        organisation_id: OrganisationId,
        token: &str,
    ) -> Result<Vec<FileInfo>>;
}
