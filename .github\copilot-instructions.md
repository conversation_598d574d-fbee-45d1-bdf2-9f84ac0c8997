**This file has top-level instructions and rules and should always be used by AI/agent while executing tasks. Instructions and rules in this should be memorized so that as context grows larger they are never bypassed! README_AI files mentioned also are considered highest priority.**

# Information

Leviosa is an enterprise EHR solution, emphasizing increased productivity for its users by tight integration with modern productivity tools inside the clinical environment, e.g. team-based collaboration, calendar and scheduling, task-management. The main application `clinic-portal` is designed for many kinds of healthcare providers and a `patient-portal` is available for patients to access their data and communicate with providers. A `kiosk` application is available for check-in and an `online-booking` app allows patients to directly book appointments with a selected clinical office.

## Monorepo folder structure

Following are the main folders in project. Take notice of `AI-IGNORE!` which should never be accessed by AI:

```
leviosa-app/
├── backend/
│   ├── README_AI.md
│   ├── api/                          # Main backend app
│   ├── cron-jobs/                    # Cron jobs
│   ├── data-import/                  # Importis external data into database
│   ├── domain/                       # Domain logic
│   ├── external-ehr-client/          #
│   ├── hekla/                        # Communications with external Hekla API
│   ├── inbound-messages-client/      #
│   ├── infrastructure/               #
│   ├── macros/                       #
│   ├── migration/                    # Database migrations
│   ├── notification-service-client/  # Notification service
│   ├── public-api/                   # AI-IGNORE!
│   ├── public-graphql/               # AI-IGNORE!
│   ├── testing/                      # backend tests
├── apps/
│   └── README_AI.md
│   ├── check-in-kiosk/               # Kiosk client
│   ├── frontend/                     # Main application (clinic-portal)
│       └── README_AI.md
│       └── e2e/                      # AI-IGNORE!
        ...
│   └── leviosa.is/                   # Standalone landing page
│   └── online-booking/               # Online-booking app
│   └── pdf-generator/                # PDF generator as service
├── data-import/                      # Standalone tool to seed database with mocked data
├── libs/
│   └── assets/                       # Reusable assets, mainly icons and CSS files
│   └── components/                   # Reusable UI components
│   └── graphql/                      # AI-IGNORE!
│   └── utils/                        # Reusable util functions
├── micro-services/
│   └── nhi-service/                  # Proxy service for the National Health Insurance SOAP services
├── docker/                           # AI-IGNORE!
├── scripts/                          # AI-IGNORE!
├── kustomize/                        # AI-IGNORE!
├── terraform/                        # AI-IGNORE!
```

In this folder structure `README_AI.md` files are pinpointed and which contain more specific AI documention for the folder it is contained in. Their rules or instructions override or extend parent documentation (in this file). **These files should always be included in context when coding within the respective folder**.

- **Plain `README.md` files should be ignored by AI** as they have instructions for human DEV-team only.

# Coding rules

- When prompt instructions mention "user" it means the human being who provides you with context. He has great knowledge of how the application works and the environment it is running in, you should ask him if you need more information to finish your work.

## Coding workflow

- Do not use build scripts to check that project works after code changes. Only use existing tests for that.
- You may create temporary modules to analyse a task or validate correctness of code created. Be sure to clean up temporary files once task is done.

### Working with Github and Jira/Confluence

GitHub and Confluence/Jira core parameters are set in workspace settings and should be resolved by AI from the current VS Code workspace settings. The keys are as follows (notice the use of curly brackets which will be used by prompts when the value should be replaced):

- Github owner: {leviosa.github.owner}
- Github repo: {leviosa.github.repo}
- Github main branch: {leviosa.github.mainBranch}
- Github username: {leviosa.github.username}
- Atlassian username: {leviosa.atlassian.username}

## Documentation

- Always document intentions that code is expected to follow. _Never overwrite intentions!_
- Do not comment on single lines, only code blocks.

# Tests

When AI updates codebase, tests specific for each stack should be done:

- backend: CLI `cargo test`
- apps (including frontend): `npm run typecheck`

Ask for user input before creating new tests since they require expertise knowledge which only the user has.

Should an issue alter functionality which affects existing tests, user should be notified and AI may suggest how to update tests to accommodate new functionality but should always wait for user input before commencing.

## End-to-end (e2e) tests

- _AI should not run e2e tests and never change e2e implementation since it is always done in a separate task_.
