import { Link } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { Text } from "ui"

type SubjectLinkProps = {
  isSubject: boolean
  name: string
  objId: string
}

export const SubjectLink = ({ isSubject, name, objId }: SubjectLinkProps) => {
  return isSubject ? (
    <Link to={getRecordPagePath(RouteStrings.subjectJournal, objId)}>
      {name}
    </Link>
  ) : (
    <Text>{name}</Text>
  )
}
