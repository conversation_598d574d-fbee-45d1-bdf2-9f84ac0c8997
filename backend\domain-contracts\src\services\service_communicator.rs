//! Service communicator contract for HTTP communication

use async_trait::async_trait;
use serde_json::Value;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ServiceCommunicatorError {
    #[error("Error communicating with service: {0}")]
    ErrorCommunicating(String),
    #[error("Invalid response from service: {0}")]
    InvalidResponse(String),
    #[error("Invalid input provided: {0}")]
    InvalidInput(String),
}

/// Contract for HTTP service communication
#[mockall::automock]
#[async_trait]
pub trait ServiceCommunicator: Send + Sync {
    /// Get binary data from a service endpoint
    async fn get_bytes(
        &self,
        path: &str,
        token: &str,
    ) -> Result<Vec<u8>, ServiceCommunicatorError>;

    /// Get JSON data from a service endpoint
    async fn get_json(
        &self,
        path: &str,
        token: &str,
    ) -> Result<Value, ServiceCommunicatorError>;

    /// Post JSON data to a service endpoint
    async fn post_json(
        &self,
        path: &str,
        token: &str,
        body: Value,
    ) -> Result<Value, ServiceCommunicatorError>;
}
