//! Authentication-related interfaces

use crate::errors::{AuthorizationError, Result};
use crate::permissions::PermissionKey;

/// Authentication data for a user
#[derive(Clone)]
pub enum AuthData {
    /// Anonymous user
    Anon,
    /// Authenticated user with token
    User(AuthenticatedUser, String),
}

impl AuthData {
    /// Checks if the user has the specified permission
    pub fn check_permission(&self, permission: Option<PermissionKey>) -> Result<AuthenticatedUser> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(user_auth_data, _) => {
                if let Some(perm) = permission {
                    if !user_auth_data.permissions().iter().any(|p| p == &perm) {
                        return Err(AuthorizationError::Unauthorized.into());
                    }
                }
                Ok(user_auth_data.clone())
            }
        }
    }

    /// Checks if the user is authenticated
    pub fn check_if_authenticated(&self) -> Result<AuthenticatedUser> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(user_auth_data, _) => Ok(user_auth_data.clone()),
        }
    }

    /// Gets the authenticated user
    pub fn user(&self) -> Result<AuthenticatedUser> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(user_auth_data, _) => Ok(user_auth_data.clone()),
        }
    }

    /// Gets the authentication token
    pub fn token(&self) -> Result<String> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(_, token) => Ok(token.clone()),
        }
    }
}

/// An authenticated user
#[derive(Debug, Clone, PartialEq)]
pub struct AuthenticatedUser {
    user_id: uuid::Uuid,
    organisation_id: uuid::Uuid,
    team_ids: Vec<uuid::Uuid>,
    permissions: Vec<PermissionKey>,
}

impl AuthenticatedUser {
    /// Creates a new authenticated user
    pub fn new(
        user_id: uuid::Uuid,
        organisation_id: uuid::Uuid,
        team_ids: Vec<uuid::Uuid>,
        permissions: Vec<PermissionKey>,
    ) -> Self {
        Self {
            user_id,
            organisation_id,
            team_ids,
            permissions,
        }
    }

    /// Gets the user ID
    pub fn user_id(&self) -> uuid::Uuid {
        self.user_id
    }

    /// Gets the organisation ID
    pub fn organisation_id(&self) -> uuid::Uuid {
        self.organisation_id
    }

    /// Gets the team IDs
    pub fn team_ids(&self) -> &[uuid::Uuid] {
        &self.team_ids
    }

    /// Gets the permissions
    pub fn permissions(&self) -> &[PermissionKey] {
        &self.permissions
    }
}

// Auth service contracts

/// API Key structure
pub struct ApiKey {
    api_key: String,
    prefix: String,
    short_token: String,
    long_token_hash: String,
}

impl ApiKey {
    pub fn new(
        prefix: String,
        short_token: String,
        long_token_hash: String,
        api_key: String,
    ) -> Self {
        Self {
            prefix,
            short_token,
            long_token_hash,
            api_key,
        }
    }

    pub fn api_key(&self) -> &str {
        &self.api_key
    }

    pub fn prefix(&self) -> &str {
        &self.prefix
    }

    pub fn short_token(&self) -> &str {
        &self.short_token
    }

    pub fn long_token_hash(&self) -> &str {
        &self.long_token_hash
    }
}

/// API Key Manager contract
pub trait ApiKeyManager: Send + Sync {
    fn generate(&self, prefix: &str) -> anyhow::Result<ApiKey>;
    fn split(&self, api_key: &str) -> anyhow::Result<ApiKey>;
}

/// Password hashing errors
#[derive(Debug, thiserror::Error)]
pub enum PasswordError {
    #[error("Invalid password")]
    InvalidPassword,
    #[error("Failed to hash password because of an invalid salt cost: {0}")]
    InvalidSaltCost(String),
    #[error("Failed to hash or verify password: {0}")]
    InternalError(#[from] anyhow::Error),
}

/// Password hasher contract
pub trait PasswordHasher: Send + Sync {
    fn verify(&self, input: &str, hashed: &str) -> std::result::Result<(), PasswordError>;
    fn hash(&self, password: &str) -> std::result::Result<String, PasswordError>;
}

/// Data encryption errors
#[derive(Debug, thiserror::Error)]
pub enum EncryptionError {
    #[error("Encryption failed: {0}")]
    EncryptionFailed(String),
    #[error("Decryption failed: {0}")]
    DecryptionFailed(String),
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Invalid key: {0}")]
    InvalidKey(String),
}

/// Data encryptor contract
pub trait DataEncryptor: Send + Sync {
    /// Encrypts data using AES-GCM-SIV algorithm
    fn encrypt(&self, plaintext: &[u8]) -> std::result::Result<Vec<u8>, EncryptionError>;

    /// Decrypts data that was encrypted with AES-GCM-SIV algorithm
    fn decrypt(&self, ciphertext: &[u8]) -> std::result::Result<Vec<u8>, EncryptionError>;

    /// Encrypts data and returns it as a base64-encoded string
    fn encrypt_to_string(&self, plaintext: &[u8]) -> std::result::Result<String, EncryptionError>;

    /// Decrypts a base64-encoded string that was encrypted with AES-GCM-SIV algorithm
    fn decrypt_from_string(&self, encoded: &str) -> std::result::Result<Vec<u8>, EncryptionError>;
}

/// Electronic ID user information
pub struct ElectronicIdUserInfo {
    pub name: String,
    pub national_register_id: String,
}

/// Electronic ID contract
#[async_trait::async_trait]
pub trait ElectronicId: Send + Sync {
    async fn login(&self, phone_number: &str) -> anyhow::Result<ElectronicIdUserInfo>;
}
