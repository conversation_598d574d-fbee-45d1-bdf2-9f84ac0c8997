use std::fmt::Display;

use super::{Error, Result};

pub trait ErrorContext<T> {
    fn context<C>(self, context: C) -> Result<T>
    where
        C: Display + Send + Sync + 'static;

    fn with_context<C, F>(self, f: F) -> Result<T>
    where
        C: Display + Send + Sync + 'static,
        F: FnOnce() -> C;
}

impl<T> ErrorContext<T> for Result<T> {
    fn context<C>(self, c: C) -> Result<T>
    where
        C: Display + Send + Sync + 'static,
    {
        match self {
            Ok(ok) => Ok(ok),
            Err(error) => match error {
                Error::Authorization(e) => Err(context(e, c)),
                Error::Input(e) => Err(context(e, c)),
                // Error::Password(e) => Err(context(e, c)),  // Commented out - auth module disabled
                Error::NotFound(e) => Err(context(e, c)),
                Error::Permissions(e) => Err(context(e, c)),
                Error::CrossOrganization(e) => Err(context(e, c)),
                Error::Custom(e) => Err(context(e, c)),
                Error::Internal(e) => Err(e.context(c).into()),
                Error::Database(e) => Err(context(e, c)),
                // Error::PrescriptionApi(e) => Err(context(e, c)),  // Commented out
                Error::ServiceCommunicator(e) => Err(context(e, c)),
                Error::NotificationService(e) => Err(context(e, c)),
                Error::TextMessageIntegrationError(e) => Err(context(e, c)),
                // Error::DoctorLetterAndReferralApi(e) => Err(context(e, c)),  // Commented out
                // Error::Encryptor(e) => Err(context(e, c)),  // Commented out - auth module disabled
                Error::NotConfigured(e) => Err(context(e, c)),
            },
        }
    }

    fn with_context<C, F>(self, f: F) -> Result<T>
    where
        C: Display + Send + Sync + 'static,
        F: FnOnce() -> C,
    {
        self.context(f())
    }
}

fn context<E, C>(e: E, c: C) -> Error
where
    E: std::error::Error + Send + Sync + 'static,
    C: Display + Send + Sync + 'static,
{
    anyhow::Error::new(e).context(c).into()
}
