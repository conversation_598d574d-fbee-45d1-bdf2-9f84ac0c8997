.wrap {
  --color-border-disabled: var(--color-gray-20);

  position: relative;
  display: grid;
  grid-template-rows: min-content min-content min-content;
}

.wrap[data-variant="clear"] {
  margin-left: -16px;
}

.wrap label {
  margin-bottom: 8px;
}

.wrap[data-variant="clear"] label {
  margin-left: 16px;
}

.inputsWrapper {
  grid-row: 2;
  grid-column: 1/ -1;
  display: flex;
  justify-content: flex-start;

  background: var(--color-white);
  border: 1px solid var(--color-neutral-300);
  border-radius: 8px;
  line-height: 18px;
  min-height: 42px;

  transition:
    box-shadow 200ms,
    border 100ms,
    background-color 100ms;
}

.wrap[data-disabled="true"] :where(.inputsWrapper, .inputs) {
  background-color: transparent;
  border: var(--color-border-disabled);
  color: var(--color-disabled);
  pointer-events: none;
}

.wrap[data-status="error"] .input:not(:focus),
.input:invalid:not(:focus, :placeholder-shown) {
  border-color: var(--color-critical);
  background-color: var(--color-white);
}

.inputs {
  border: 2px solid transparent;
  border-radius: 6px;
  background: transparent;
  padding: 0;
}

.inputs:focus {
  border-color: var(--color-lev-blue);
}

.day {
  width: 42px;
  padding-left: 16px;
}
.year {
  width: 72px;
  padding-left: 4px;
  padding-right: 16px;
}

.inputsWrapper:focus-within {
  color: var(--color-lev-blue-800);
  border: 1px solid var(--color-lev-blue);
  box-shadow:
    0 0 4px rgba(13, 16, 57, 0.1),
    0 0 20px rgba(13, 16, 57, 0.2);
  background-color: var(--color-white);
}

.message {
  grid-row: 3;
  grid-column: 1 / -1;
}

.wrap[data-status="error"] .inputsWrapper {
  border-color: var(--color-critical);
  background-color: var(--color-white);
}

.wrap[data-status="warning"] .inputsWrapper {
  border-color: var(--color-warning);
  background-color: var(--color-white);
}

.wrap[data-status="success"] .inputsWrapper {
  border-color: var(--color-success);
  background-color: var(--color-white);
}

.wrap[data-status="error"] .message,
:invalid + .message {
  color: var(--color-critical);
}

.wrap[data-status="warning"] .message {
  color: var(--color-warning);
}

.wrap[data-status="success"] .message {
  color: var(--color-success);
}
