import c from "classnames"
import { useState } from "react"
import { useTranslation } from "react-i18next"

import Panel from "components/Panel/Panel"
import { Button, Heading, IconButton, Modal, Textarea, TextWithIcon } from "ui"

import styles from "./InlineGuideInput.module.css"

type Props = {
  className?: string
  buttonClassName?: string
  value?: string
  onChange?: (value: string) => void
  canEdit?: boolean
  formHeading?: string
  fallbackText?: string
}

export const InlineGuideInput = ({
  value,
  onChange,
  formHeading = "Add inline guide",
  fallbackText = "Click to add inline guide",
  className,
  buttonClassName,
  canEdit = true,
}: Props) => {
  const { t } = useTranslation()
  const [showModal, setShowModal] = useState(false)

  if (!canEdit && !value) return null

  if (!canEdit && value) {
    return (
      <Panel className={c(styles.panel, className)}>
        <div className={styles.container}>
          <TextWithIcon iconName="lightbulb-line">{value}</TextWithIcon>
        </div>
      </Panel>
    )
  }

  return (
    <>
      {value ? (
        <Panel className={c(styles.panel, className)}>
          <div className={styles.container}>
            <TextWithIcon iconName="lightbulb-line">{value}</TextWithIcon>
            <IconButton
              iconName="edit-line"
              className={styles.editButton}
              onClick={() => setShowModal(true)}
            />
          </div>
        </Panel>
      ) : (
        <Button
          variant="clear"
          onClick={() => setShowModal(true)}
          className={c(styles.textButton, buttonClassName)}
        >
          {fallbackText}
        </Button>
      )}

      <Modal
        isOpen={showModal}
        contentClassName={styles.modal}
        onClose={() => setShowModal(false)}
      >
        <form
          className={styles.modalForm}
          onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.target as HTMLFormElement)
            const newValue = formData.get("inlineGuide") as string
            onChange?.(newValue)
            setShowModal(false)
          }}
        >
          <Heading>{formHeading}</Heading>
          <Textarea
            name="inlineGuide"
            defaultValue={value}
            label={"Inline Guide"}
            hideLabel
            hideMessage
            autoGrow
            onKeyDown={(e) => {
              if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                e.preventDefault()
                e.currentTarget.form?.dispatchEvent(
                  new Event("submit", { bubbles: true, cancelable: true })
                )
              }
            }}
          />
          <div className={styles.modalFooter}>
            <Button variant="clear" onClick={() => setShowModal(false)}>
              {t("Cancel")}
            </Button>
            <Button type="submit" variant="filled">
              {t("Save")}
            </Button>
          </div>
        </form>
      </Modal>
    </>
  )
}
