import { $generateNodesFromDOM } from "@lexical/html"
import {
  INSERT_CHECK_LIST_COMMAND,
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
} from "@lexical/list"
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import { INSERT_HORIZONTAL_RULE_COMMAND } from "@lexical/react/LexicalHorizontalRuleNode"
import {
  LexicalTypeaheadMenuPlugin,
  useBasicTypeaheadTriggerMatch,
} from "@lexical/react/LexicalTypeaheadMenuPlugin"
import { $createHeadingNode, $createQuoteNode } from "@lexical/rich-text"
import { $setBlocksType } from "@lexical/selection"
import c from "classnames"
import { $getSelection, $isRangeSelection, TextNode } from "lexical"
import { useCallback, useMemo, useState, useEffect, useRef } from "react"
import ReactDOM from "react-dom"
import { useParams } from "react-router-dom"
import { v4 as uuid } from "uuid"

import { IconName } from "@leviosa/assets"

import selectPopoverStyles from "components/Ariakit/Select/SelectPopover/SelectPopover.module.css"
import MenuListItem from "components/MenuListItem/MenuListItem"
import usePermissions from "features/authentication/hooks/usePermissions"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateType,
  PermissionKey,
  useAddDrugPrescriptionToJournalEntryTemplateMutation,
  useApplyJournalTemplateMutation,
  useCreateDrugPrescriptionMutation,
  useCreateMedicalCertificateMutation,
  useCreateOutboundDoctorsLetterMutation,
  useCreateOutboundReferralMutation,
} from "generated/graphql"

import menuStyles from "../menu.module.css"
import { InsertMenuOption } from "./TypeaheadMenuOption"

export default function InsertMenuPlugin({
  journalTemplates,
  journalEntryId,
  sectionId,
  onAddBillingItem,
  onAddAttachment,
  hideBilling,
}: {
  journalEntryId: string
  sectionId: string
  journalTemplates: JournalTemplateFragmentFragment[]
  onAddBillingItem?: () => void
  onAddAttachment?: () => void
  hideBilling?: boolean
}): JSX.Element {
  const [editor] = useLexicalComposerContext()
  const [queryString, setQueryString] = useState<string | null>(null)
  const menuRef = useRef<HTMLDivElement>(null)
  const [hasFocus, setHasFocus] = useState(false)
  const isJournalTemplate = journalEntryId === ""

  const { templateId } = useParams<{ templateId: string }>()

  const [applyJournalTemplate] = useApplyJournalTemplateMutation()
  const [createDrugPrescription] = useCreateDrugPrescriptionMutation()

  const [addDrugPrescriptionToJournalEntryTemplate] =
    useAddDrugPrescriptionToJournalEntryTemplateMutation()
  const [createMedicalCertificate] = useCreateMedicalCertificateMutation()
  const [createOutboundReferral] = useCreateOutboundReferralMutation()
  const [createOutboundDoctorsLetter] = useCreateOutboundDoctorsLetterMutation()
  const { hasSomePermissions } = usePermissions()
  const checkForTriggerMatch = useBasicTypeaheadTriggerMatch("/", {
    minLength: 0,
  })

  const options = useMemo(() => {
    const baseOptions = [
      // # InlineTemplates
      ...(!isJournalTemplate
        ? journalTemplates
            .filter(
              ({ templateType }) =>
                templateType === JournalTemplateType.InlineTemplate
            )
            .map(
              ({ id, name, sections }) =>
                new InsertMenuOption(name, {
                  icon: "file-list-2-line",
                  keywords: [name],
                  permissions: [PermissionKey.SubjectJournalJournalEntryEdit],
                  onSelect: () => {
                    // Append content inline to journal entry if we are applying to an existing section
                    editor.update(() => {
                      const selection = $getSelection()
                      if ($isRangeSelection(selection)) {
                        // Parse the content into a DOM
                        sections.map((section) => {
                          const parser = new DOMParser()
                          const dom = parser.parseFromString(
                            section.content,
                            "text/html"
                          )
                          selection.insertNodes(
                            $generateNodesFromDOM(editor, dom)
                          )
                        })
                      }
                    })
                    applyJournalTemplate({
                      variables: {
                        id,
                        encounterId: null,
                        existingJournalEntry: {
                          id: journalEntryId,
                          sectionId,
                        },
                      },
                      // If there's an error, revert the editor state to what it was before
                      onError: () => {
                        editor.setEditorState(editor.getEditorState())
                      },
                    })
                  },
                })
            )
        : []),

      // # Supplements
      ...(!isJournalTemplate
        ? [
            new InsertMenuOption("Drug Prescription", {
              icon: "capsule-line",
              keywords: ["drug", "prescription"],
              permissions: [PermissionKey.SubjectJournalDrugPrescriptionEdit],
              // COMEBACK needs cache manipulation or refetch so that SJ-Focus can be placed on new Block
              onSelect: () => {
                if (isJournalTemplate && templateId) {
                  addDrugPrescriptionToJournalEntryTemplate({
                    variables: {
                      input: {
                        journalTemplateId: templateId,
                      },
                    },
                  })
                } else {
                  createDrugPrescription({
                    variables: {
                      id: uuid(),
                      input: {
                        journalEntryId,
                        // Apply default values which apply most of the time, saves mouse-click. May be configured per drug in future.
                        packageCount: 1,
                        validFrom: new Date(),
                        // one year between
                        validTo: new Date(
                          Date.now() + 365 * 24 * 60 * 60 * 1000
                        ),
                      },
                    },
                  })
                }
              },
            }),
            new InsertMenuOption("Clinical correspondence", {
              icon: "mail-send-line",
              keywords: [
                "clinical",
                "correspondence",
                "doctor",
                "letter",
                "læknabréf",
              ],
              permissions: [PermissionKey.SubjectJournalDrugPrescriptionEdit], // TODO: use correct permission key
              onSelect: () =>
                createOutboundDoctorsLetter({
                  variables: {
                    id: uuid(),
                    input: {
                      journalEntryId,
                    },
                  },
                }),
            }),
            new InsertMenuOption("Clinical referral", {
              icon: "mail-send-line",
              keywords: ["clinical", "referral", "tilvísun"],
              permissions: [PermissionKey.SubjectJournalDrugPrescriptionEdit], // TODO: use correct permission key
              onSelect: () =>
                createOutboundReferral({
                  variables: {
                    id: uuid(),
                    input: {
                      journalEntryId,
                    },
                  },
                }),
            }),
            new InsertMenuOption("Certificate", {
              icon: "file-text-line",
              keywords: ["certificate", "absence", "school", "work"],
              permissions: [PermissionKey.SubjectJournalJournalEntryEdit],
              onSelect: () => {
                createMedicalCertificate({
                  variables: {
                    createMedicalCertificateId: uuid(),
                    input: {
                      journalEntryId,
                      certType: "Absence",
                      version: 1,
                      content: {
                        employerOrSchool: "",
                        fromDate: "",
                        toDate: "",
                        description: "",
                      },
                    },
                  },
                })
              },
            }),
            new InsertMenuOption("Freetext document", {
              icon: "file-text-line",
              keywords: ["document", "free-text"],
              permissions: [PermissionKey.SubjectJournalJournalEntryEdit],
              onSelect: () => {
                createMedicalCertificate({
                  variables: {
                    createMedicalCertificateId: uuid(),
                    input: {
                      journalEntryId,
                      certType: "Free-text",
                      version: 1,
                      content: {
                        title: "",
                        description: "",
                      },
                    },
                  },
                })
              },
            }),
          ]
        : []),

      // # Attachments
      ...(!isJournalTemplate
        ? [
            new InsertMenuOption("Attachments", {
              icon: "attachment-line",
              keywords: ["attachments", "file"],
              onSelect: () => onAddAttachment?.(),
            }),
          ]
        : []),

      // # Billing
      ...(!isJournalTemplate && onAddBillingItem && !hideBilling
        ? [
            new InsertMenuOption("Billing", {
              icon: "money-dollar-circle-line",
              keywords: ["billing"],
              permissions: [PermissionKey.BillingBillingCodeCreate],
              onSelect: () => onAddBillingItem?.(),
            }),
          ]
        : []),

      // # InlineTemplates for testing fields
      // ...(hasPermission(PermissionKey.BetaFeature)
      //   ? mockTemplates.map(
      //       ({ title, content }) =>
      //         new ComponentPickerOption(title, {
      //           icon: "file-list-line",
      //           keywords: ["template", "form", title],
      //           onSelect: () => {
      //             // Append content inline to journal entry
      //             editor.update(() => {
      //               const selection = $getSelection()
      //               if ($isRangeSelection(selection)) {
      //                 // Parse the content into a DOM
      //                 const parser = new DOMParser()
      //                 const dom = parser.parseFromString(content, "text/html")

      //                 const nodes = $generateNodesFromDOM(editor, dom)

      //                 selection.insertNodes(nodes)
      //               }
      //             })
      //           },
      //         })
      //     )
      //   : []),

      // # Text formatting (shortcuts)
      ...Array.from({ length: 3 }, (_, i) => i + 1).map(
        (n) =>
          new InsertMenuOption(`Heading ${n}`, {
            icon: `heading${n}` as IconName,
            keywords: ["heading", "header", `h${n}`],
            onSelect: () =>
              editor.update(() => {
                const selection = $getSelection()
                if ($isRangeSelection(selection)) {
                  $setBlocksType(selection, () =>
                    // @ts-expect-error same here
                    $createHeadingNode(`h${n}`)
                  )
                }
              }),
          })
      ),
      new InsertMenuOption("Numbered List", {
        icon: "list-number",
        keywords: ["numbered list", "ordered list", "ol"],
        onSelect: () =>
          editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined),
      }),
      new InsertMenuOption("Bulleted List", {
        icon: "list-bullet",
        keywords: ["bulleted list", "unordered list", "ul"],
        onSelect: () =>
          editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined),
      }),
      new InsertMenuOption("Check List", {
        icon: "list-check-3",
        keywords: ["check list", "todo list", "task list", "checklist"],
        onSelect: () =>
          editor.dispatchCommand(INSERT_CHECK_LIST_COMMAND, undefined),
      }),
      new InsertMenuOption("Quote", {
        icon: "quote",
        keywords: ["block quote"],
        onSelect: () =>
          editor.update(() => {
            const selection = $getSelection()
            if ($isRangeSelection(selection)) {
              $setBlocksType(selection, () => $createQuoteNode())
            }
          }),
      }),
      new InsertMenuOption("Divider", {
        icon: "divider",
        keywords: ["horizontal rule", "divider", "hr"],
        onSelect: () =>
          editor.dispatchCommand(INSERT_HORIZONTAL_RULE_COMMAND, undefined),
      }),
    ].filter((option) => {
      return (
        !option.permissions.length || hasSomePermissions(option.permissions)
      )
    })

    return queryString
      ? [
          ...baseOptions.filter((option) => {
            return new RegExp(queryString, "gi").exec(option.title) ||
              option.keywords != null
              ? option.keywords.some((keyword) =>
                  new RegExp(queryString, "gi").exec(keyword)
                )
              : false
          }),
        ]
      : baseOptions
  }, [editor, queryString])

  const onSelectOption = useCallback(
    (
      selectedOption: InsertMenuOption,
      nodeToRemove: TextNode | null,
      closeMenu: () => void,
      matchingString: string
    ) => {
      editor.update(() => {
        if (nodeToRemove) {
          nodeToRemove.remove()
        }
        selectedOption.onSelect(matchingString)
        closeMenu()
      })
    },
    [editor]
  )

  useEffect(() => {
    const handleFocus = () => setHasFocus(true)
    const handleBlur = () => {
      // Set a small delay to allow the menu to be clicked
      // before it disappears. If we don't then the menu will
      // disappear before the click event is triggered.
      setTimeout(() => setHasFocus(false), 200)
    }

    const editorElement = editor.getRootElement()
    if (editorElement) {
      editorElement.addEventListener("focus", handleFocus)
      editorElement.addEventListener("blur", handleBlur)
    }

    return () => {
      if (editorElement) {
        editorElement.removeEventListener("focus", handleFocus)
        editorElement.removeEventListener("blur", handleBlur)
      }
    }
  }, [editor])

  return (
    <LexicalTypeaheadMenuPlugin<InsertMenuOption>
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      triggerFn={checkForTriggerMatch}
      options={options}
      menuRenderFn={(
        anchorElementRef,
        { selectedIndex, selectOptionAndCleanUp, setHighlightedIndex }
      ) =>
        anchorElementRef.current && options.length && hasFocus
          ? ReactDOM.createPortal(
              <div ref={menuRef}>
                <ul
                  className={c(selectPopoverStyles.popover, menuStyles.menuUl)}
                  data-enter
                >
                  {options.map((option, i) => {
                    const isSelected = selectedIndex === i
                    return (
                      <li key={option.key}>
                        <MenuListItem
                          className={menuStyles.item}
                          aria-selected={isSelected}
                          tabIndex={-1}
                          ref={option.setRefElement}
                          role="option"
                          id={"typeahead-item-" + i}
                          onMouseEnter={() => setHighlightedIndex(i)}
                          onClick={() => {
                            setHighlightedIndex(i)
                            selectOptionAndCleanUp(option)
                          }}
                        >
                          {option.icon}
                          <span>{option.title}</span>
                        </MenuListItem>
                      </li>
                    )
                  })}
                </ul>
              </div>,
              anchorElementRef.current
            )
          : null
      }
    />
  )
}
