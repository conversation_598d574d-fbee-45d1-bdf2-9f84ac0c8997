//! Audit logger service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditLogEntry {
    pub id: String,
    pub event_type: AuditEventType,
    pub user_id: Option<ProviderId>,
    pub subject_id: Option<SubjectId>,
    pub organisation_id: OrganisationId,
    pub resource_type: String,
    pub resource_id: Option<String>,
    pub action: String,
    pub details: Option<Value>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuditEventType {
    Authentication,
    Authorization,
    DataAccess,
    DataModification,
    SystemEvent,
    SecurityEvent,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AuditLogFilter {
    pub event_type: Option<AuditEventType>,
    pub user_id: Option<ProviderId>,
    pub subject_id: Option<SubjectId>,
    pub organisation_id: Option<OrganisationId>,
    pub resource_type: Option<String>,
    pub action: Option<String>,
    pub from_date: Option<DateTime<Utc>>,
    pub to_date: Option<DateTime<Utc>>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

/// Contract for audit logging service
#[async_trait]
#[mockall::automock]
pub trait AuditLogger: Send + Sync {
    /// Log an audit event
    async fn log_event(&self, entry: AuditLogEntry) -> Result<()>;

    /// Query audit logs with filtering
    async fn query_logs(
        &self,
        filter: AuditLogFilter,
        token: &str,
    ) -> Result<Vec<AuditLogEntry>>;

    /// Get audit log entry by ID
    async fn get_log_entry(
        &self,
        entry_id: &str,
        token: &str,
    ) -> Result<Option<AuditLogEntry>>;
}
