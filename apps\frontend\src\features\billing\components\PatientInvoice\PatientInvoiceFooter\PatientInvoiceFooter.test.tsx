import { MockedProvider } from "@apollo/client/testing"
import { render, screen, within, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { i18nInstance } from "i18n"
import { I18nextProvider } from "react-i18next"
import { mockGlobalData } from "test/mocks/GlobalStateMock"

import { GlobalProvider } from "components/GlobalDataContext/GlobalData.context"

import {
  GetInvoiceQuery,
  CreateInvoiceDiscountForSubjectDocument,
} from "generated/graphql"

import { PatientInvoiceFooter } from "./PatientInvoiceFooter"

const invoiceMockWithoutNHI: GetInvoiceQuery["invoice"] = {
  id: "1",
  containsNhiItems: false,
  nhiPayableBySubject: null,
  subjectDiscount: 0,
  total: 1000,
  totalVat: 100,
  totalDiscount: 50,
  totalPayableByInsurance: 500,
  totalPayableBySubject: 450,
}

const invoiceMockWithNHI = {
  ...invoiceMockWithoutNHI,
  containsNhiItems: true,
  nhiPayableBySubject: 100,
}

describe("PatientInvoiceFooter", () => {
  it("renders the component with correct values if no NHI items are part of the invoice", () => {
    render(
      <I18nextProvider i18n={i18nInstance}>
        <MockedProvider mocks={[]} addTypename={false}>
          <GlobalProvider currentTeamId="fakeId" globalData={mockGlobalData}>
            <PatientInvoiceFooter invoice={invoiceMockWithoutNHI} />
          </GlobalProvider>
        </MockedProvider>
      </I18nextProvider>
    )

    expect(
      within(screen.getByText(/Total VAT:/)).getByText("100 ISK")
    ).toBeInTheDocument()
    expect(
      within(screen.getByText(/Total discount:/)).getByText("50 ISK")
    ).toBeInTheDocument()
    expect(
      within(screen.getByText(/Total invoice:/)).getByText("1.000 ISK")
    ).toBeInTheDocument()
    expect(
      within(screen.getByText(/Total due for patient:/)).getByText("450 ISK")
    ).toBeInTheDocument()
    expect(
      within(screen.getByText(/Payable by insurance:/)).getByText("500 ISK")
    ).toBeInTheDocument()

    expect(
      screen.queryByText(/Discount patient's insured share/)
    ).not.toBeInTheDocument()
    expect(screen.queryByRole("checkbox")).not.toBeInTheDocument()
    expect(
      screen.queryByText(/Total patient's insured share/)
    ).not.toBeInTheDocument()
  })

  it("shows the discount switch if containsNhiItems is `true`", () => {
    render(
      <I18nextProvider i18n={i18nInstance}>
        <MockedProvider mocks={[]} addTypename={false}>
          <GlobalProvider currentTeamId="fakeId" globalData={mockGlobalData}>
            <PatientInvoiceFooter invoice={invoiceMockWithNHI} />
          </GlobalProvider>
        </MockedProvider>
      </I18nextProvider>
    )

    expect(
      screen.getByRole("checkbox", {
        name: /Discount patient's insured share/i,
      })
    ).toBeInTheDocument()
  })

  it("does not show the discount switch if it is a credit invoice but a Textfield instead", () => {
    render(
      <I18nextProvider i18n={i18nInstance}>
        <MockedProvider mocks={[]} addTypename={false}>
          <GlobalProvider currentTeamId="fakeId" globalData={mockGlobalData}>
            <PatientInvoiceFooter
              invoice={{ ...invoiceMockWithoutNHI, subjectDiscount: -10 }}
            />
          </GlobalProvider>
        </MockedProvider>
      </I18nextProvider>
    )

    expect(
      screen.queryByText(/Discount patient's insured share/)
    ).not.toBeInTheDocument()
    expect(screen.queryByRole("checkbox")).not.toBeInTheDocument()
    expect(
      within(screen.getByText(/Discount patient's share:/)).getByText("-10 ISK")
    ).toBeInTheDocument()
  })

  it("toggles the patient discount switch, shows the input and total patient's insured share and makes a BE call to store nhiPayableBySubject", async () => {
    const user = userEvent.setup()
    const customMocks = [
      {
        request: {
          query: CreateInvoiceDiscountForSubjectDocument,
          variables: {
            invoiceId: invoiceMockWithNHI.id,
            discount: invoiceMockWithNHI.nhiPayableBySubject,
          },
        },
        newData: vi.fn(() => ({
          data: {
            createInvoiceDiscountForSubject: {
              id: invoiceMockWithNHI.id,
              subjectDiscount: 450,
              total: 1000,
              totalDiscount: 100,
              totalPayableBySubject: 1000,
              totalPayableByInsurance: 1000,
            },
          },
        })),
      },
    ]

    render(
      <I18nextProvider i18n={i18nInstance}>
        <MockedProvider mocks={customMocks} addTypename={false}>
          <GlobalProvider currentTeamId="fakeId" globalData={mockGlobalData}>
            <PatientInvoiceFooter invoice={invoiceMockWithNHI} />
          </GlobalProvider>
        </MockedProvider>
      </I18nextProvider>
    )

    const checkbox = screen.getByRole("checkbox", {
      name: /Discount patient's insured share/i,
    })

    expect(screen.queryByDisplayValue("0 ISK")).not.toBeInTheDocument()
    expect(
      screen.queryByText(/Total patient's insured share/)
    ).not.toBeInTheDocument()

    await user.click(checkbox)

    expect(
      within(screen.getByText(/Total patient's insured share:/)).getByText(
        "100 ISK"
      )
    ).toBeInTheDocument()

    const input = screen.getByDisplayValue("100 ISK")
    expect(input).toBeInTheDocument()

    const mutationMock = customMocks[0].newData
    await waitFor(() => expect(mutationMock).toHaveBeenCalled())
  })
})
