import { ButtonProps } from "@ariakit/react"
import { ElementType } from "react"

import Icon from "components/Icon/Icon"
import { Button } from "ui"

import styles from "./ButtonStar.module.css"

type ButtonStarProps<E extends ElementType> = {
  isStarred: boolean
  children?: React.ReactNode
} & Omit<ButtonProps<E>, "icon">

export const ButtonStar = ({
  isStarred,
  className,
  ...rest
}: ButtonStarProps<"button">) => {
  return (
    <Button
      variant={"clear"}
      data-is-starred={isStarred}
      className={`${styles.buttonStar} ${className}`}
      icon={
        <Icon
          className={styles.starIcon}
          data-is-starred={isStarred}
          name="star-line"
        />
      }
      {...rest}
    ></Button>
  )
}
