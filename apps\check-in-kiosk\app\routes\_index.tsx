import { ActionFunctionArgs } from "@remix-run/node"
import type { MetaFunction } from "@remix-run/node"
import { useActionData, useNavigation } from "@remix-run/react"
import { addHours } from "date-fns"
import { useEffect, useState } from "react"
import { redirect } from "react-router-dom"

import Login from "app/pages/Login/Login"
import { getSession, getCommitSessionHeaders } from "app/sessions"
import { fetchAPI } from "app/utils/api"
import getFirstMondayOfNextMonth from "app/utils/getFirstMondayOfNextMonth"

export const meta: MetaFunction = () => {
  return [{ title: "Leviosa Kiosk" }]
}

export const action = async ({ request }: ActionFunctionArgs) => {
  const session = await getSession(request.headers.get("Cookie"))

  // Validate the form data (phone number input)
  const requestData = await request.formData()
  const phoneNumberInput = requestData.get("input") as string
  const isValidPhoneNumber = /^\d{7}$/.test(phoneNumberInput)
  if (!isValidPhoneNumber) {
    return new Response("invalidPhoneNumber", { status: 400 })
  }

  const body = {
    query: `
        mutation KioskLogIn($input: LogInInput!) {
          kioskLogIn(input: $input) {
            accessToken
            organisationLogoUrl
          }
        }
      `,
    variables: {
      input: {
        electronicId: {
          phoneNumber: phoneNumberInput,
        },
      },
    },
  }

  const responseData = await fetchAPI("POST", body)
  const accessToken = responseData.data?.kioskLogIn?.accessToken
  const logoUrl = responseData.data?.kioskLogIn?.organisationLogoUrl

  if (accessToken) {
    session.set("accessToken", accessToken)
    session.set("accessTokenExpires", addHours(getFirstMondayOfNextMonth(), 4))

    if (logoUrl) {
      session.set("logoUrl", logoUrl)
    }

    const headers = await getCommitSessionHeaders(session)
    return redirect("/checkin", headers)
  }

  if (responseData.errors) {
    throw new Error(responseData.errors?.message || "Failed to log in")
  }

  throw new Error("Something went wrong logging in")
}

export default function Index() {
  const actionData = useActionData<typeof action>()
  const navigation = useNavigation()

  const [showValidationError, setShowValidationError] = useState(true)

  useEffect(() => {
    if (navigation.state === "submitting") {
      setShowValidationError(true)
    }
  }, [navigation.state])

  const loading =
    navigation.state === "submitting" || navigation.state === "loading"

  if (
    actionData === "invalidPhoneNumber" &&
    navigation.state !== "submitting" &&
    showValidationError
  ) {
    return (
      <Login
        validationError="invalidPhoneNumber"
        loading={loading}
        onFormUpdate={() => setShowValidationError(false)}
      />
    )
  }

  return <Login loading={loading} />
}
