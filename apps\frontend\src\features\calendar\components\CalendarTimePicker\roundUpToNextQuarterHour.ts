import { addHours, getMinutes, setMinutes, setSeconds } from "date-fns"

export const roundUpToNextQuarterHour = (selectedDate?: Date) => {
  const currentDate = selectedDate ?? new Date()
  const currentMinutes = getMinutes(currentDate)
  let roundedMinutes = currentMinutes

  if (currentMinutes < 15) {
    roundedMinutes = 15
  } else if (currentMinutes < 30) {
    roundedMinutes = 30
  } else if (currentMinutes < 45) {
    roundedMinutes = 45
  } else {
    const nextHour = addHours(currentDate, 1)
    nextHour.setMinutes(0, 0, 0)
    return nextHour
  }

  const roundedDateTime = setMinutes(setSeconds(currentDate, 0), roundedMinutes)

  return roundedDateTime
}
