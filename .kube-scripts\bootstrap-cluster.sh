#!/bin/bash


if [ -z $1 ]; then
  echo "Please select variant";
  exit 1
fi


if [ -z $2 ]; then
  echo "Please set helper url";
  exit 1
fi

VARIANT=$1
HELPERS_URL=$2


VARIANT=$1

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )


DB_PASSWORD=$(kubectl get secret --selector=app=database,variant=$VARIANT -o json  | jq '.items[0].data["postgres-password"]' -r  | base64 -d)

ADMIN_KEY=$(kubectl get secrets  --selector=variant=$VARIANT -o json | jq '.items[] | select(.data.ADMIN_KEY  != null ) | .data.ADMIN_KEY' -r | head -n1  | base64 -d)


pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)


if [[ "$pod" == "null" ]]; then
  echo "Pod not found";
  exit 1
fi

kubectl delete jobs.batch --selector=variant=$VARIANT || true


echo "I will revoke all ON schema public FROM leviosa ;"
kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -c 'revoke all ON schema public FROM leviosa ;' "  || true

echo "I will Create the database leviosa"
kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -c 'drop role leviosa ;' " || true

echo "I will Create the database leviosa"
kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -c 'CREATE DATABASE leviosa;' " 

# echo "I will grant all privileges to leviosa"
# kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -c 'GRANT ALL PRIVILEGES ON DATABASE leviosa TO leviosa;' "  || exit 1

#echo "Will grant ower to leviosaa"
#kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -c 'ALTER DATABASE leviosa OWNER to leviosa;'"  || exit 1

# echo "Will grant ower to leviosaa"
# kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD PGDATABASE=leviosa psql -U postgres -c 'GRANT create,usage ON schema public to leviosa;'"  || exit 1



#echo "Adding UUID extension"
#kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -d leviosa -c 'CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";' "  || exit 1


echo "

Please run the migrations - kubectl apply -k ... 

"

source "$SCRIPT_DIR/.sure.sh"

sleep 1;

kubectl apply -k kustomize/overlays/$VARIANT


echo "Will sleep 3s" 

for i in {1..3}; do
 echo $i
 curl $HELPERS_URL/graphql?debugger -vLsk -o /dev/null  2>&1 | grep issuer
 sleep 1;
done


# backendpod=$(kubectl get pod --selector=variant=$VARIANT | grep backend | grep -v rust   | awk '{ print $1}')

# echo "Will use $backendpod for seeding"


# source "$SCRIPT_DIR/.sure.sh"


# echo "Will run yarn exec createSeed on $backendpod in env lite"
# kubectl exec -it $backendpod -- /usr/local/bin/yarn exec "HELPERS_URL=$HELPERS_URL node -r ./bootstrap.js ./dist/createSeed.js"
# echo "Will run yarn exec createSeed on $backendpod in env mss"

# kubectl exec -it $backendpod -- /usr/local/bin/yarn exec "HELPERS_URL=$HELPERS_URL LEVIOSA_KIND=MSS node -r ./bootstrap.js ./dist/createSeed.js"

# kubectl exec -it 


# rm -rf packages/backend/dist/

# echo "building code"
# (cd packages/backend/ && yarn build || exit 1)



# echo "Running create-seed script for $HELPERS_URL"
# (cd packages/backend/ 
# NODE_ENV=production \
# RUST_URL=http://rust.com \
# PG_DB=leviosa \
# PG_PORT=1345 \
# PG_HOST=localhost \
# PG_PASSWORD=123 \
# PG_USER=leviosa \
# JWT_SECRET=1 \
# PORT=3000 \
# ADMIN_KEY=$ADMIN_KEY \
# HELPERS_URL="$HELPERS_URL" \
# LEVIOSA_KIND=LITE \
# yarn bootstrap)


# echo "Running create-seed script for LITE"
# (cd packages/backend/ 
# NODE_ENV=production \
# RUST_URL=http://rust.com \
# PG_DB=leviosa \
# PG_PORT=1345 \
# PG_HOST=localhost \
# PG_PASSWORD=123 \
# PG_USER=leviosa \
# JWT_SECRET=1 \
# PORT=3000 \
# ADMIN_KEY=$ADMIN_KEY \
# HELPERS_URL="https://lite-staging.leviosa.is" \
# LEVIOSA_KIND=LITE \
# yarn bootstrap)


# echo "Running create-seed script for MSS"
# (cd packages/backend/ 
# NODE_ENV=production \
# RUST_URL=http://rust.com \
# PG_DB=leviosa \
# PG_PORT=1345 \
# PG_HOST=localhost \
# PG_PASSWORD=123 \
# PG_USER=leviosa \
# JWT_SECRET=1 \
# PORT=3000 \
# ADMIN_KEY=$ADMIN_KEY \
# HELPERS_URL="https://mss-staging.leviosa.is" \
# LEVIOSA_KIND=MSS \
# yarn bootstrap)



# echo "Copy seed to database"
# kubectl cp packages/backend/seed/seed.dump $pod:/tmp

# echo "Executing seed script on database"

# kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD pg_restore -d leviosa -U postgres --data-only --no-privileges --no-owner /tmp/seed.dump"
