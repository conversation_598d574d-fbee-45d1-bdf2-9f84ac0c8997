import { ColumnDef } from "@tanstack/react-table"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import { BaseTable, Grid } from "ui"

import { LocationType, RoomType, useGetLocationsQuery } from "generated/graphql"

import styles from "./CreateLocation.module.css"
import { LocationTableCell } from "./LocationTableCell/LocationTableCell"

export type LocationsTableData = {
  id: string
  label: string
  locationType: LocationType
  roomType?: RoomType
  capacity?: number
}

export const LocationsTable = () => {
  const { t } = useTranslation()

  const { data } = useGetLocationsQuery({
    variables: { locationType: LocationType.Room },
  })

  const columns = useMemo(
    () => [
      {
        header: t("label"),
        accessorKey: "label",
      },
      {
        header: t("locationType"),
        accessorKey: "locationType",
      },
      {
        header: t("capacity"),
        accessorKey: "capacity",
      },
      {
        header: t("roomType"),
        accessorKey: "roomType",
      },
    ],
    [data?.locations]
  )

  const tableData = useMemo(
    () =>
      data?.locations.map((location) =>
        location.__typename === "Room"
          ? {
              id: location.id,
              label: location.label,
              locationType: location.locationType,
              capacity: location.capacity || undefined,
              roomType: location.roomType || undefined,
            }
          : {
              id: location.id,
              label: location.label,
              locationType: location.locationType,
            }
      ),
    [data?.locations]
  )

  const defaultColumn: Partial<ColumnDef<LocationsTableData>> = {
    cell: (row) => <LocationTableCell {...row} />,
  }

  if (tableData?.length === 0) return null

  return (
    <Grid rowGap={2}>
      {tableData && (
        <BaseTable
          defaultColumn={defaultColumn}
          data={tableData}
          columns={columns}
          className={styles.table}
        />
      )}
    </Grid>
  )
}
