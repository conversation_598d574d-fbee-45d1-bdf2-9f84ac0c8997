query DashboardStatusCountHack($teamId: UUID!) {
  team(id: $teamId) {
    id
    # This is not ideal. We want to get an actual count (a single number) for each status.
    # But we can't do that with the current schema. So we're getting the IDs of the encounters
    # and counting them in the frontend.
    PLANNED: encounters(status: PLANNED) {
      id
    }
    IN_PROGRESS: encounters(status: IN_PROGRESS) {
      id
    }
    CHECKED_OUT: encounters(status: CHECKED_OUT) {
      id
    }
  }
}
