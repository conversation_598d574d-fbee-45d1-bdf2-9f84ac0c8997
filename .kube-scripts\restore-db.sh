#!/bin/bash


if [ -z $1 ]; then
  echo "Please select variant";
  exit 1
fi

if [ -z $2 ]; then
  echo "Please select backup";
  exit 1
fi


SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )


VARIANT=$1
DB_BACKUP=$2


DB_PASSWORD=$(kubectl get secret --selector=app=database,variant=$VARIANT -o json  | jq '.items[0].data["postgres-password"]' -r  | base64 -d)

pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)


if [[ "$pod" == "null" ]]; then
  echo "Pod not found";
  exit 1;
fi


# LOCALDIR=$(mktemp -d)
LOCALDIR=$(mkdir -p /tmp/restore-test && echo /tmp/restore-test)

echo "Will download the database backup $DB_BACKUP"


s3-cli get $DB_BACKUP $LOCALDIR

FILENAME=$(echo $DB_BACKUP | cut -d / -f 10 )

echo $FILENAME

if echo $FILENAME | grep -q enc ; then
  
  echo "File is encrypted, will decrypt"

  if [[ -z $BACKUP_ENC_PASSWORD ]]; then
    echo "BACKUP_ENC_PASSWORD env is missing";
    exit 1;
  fi

  openssl aes-256-cbc -d -pass pass:"$BACKUP_ENC_PASSWORD" -a -salt -pbkdf2 -in "$LOCALDIR/$FILENAME" -out "$LOCALDIR/dump.sql.gz"

  gzip --force -d "$LOCALDIR/dump.sql.gz"

else 
  mv "$LOCALDIR/$FILENAME" "$LOCALDIR/dump.sql"

fi



echo "will copy dump.sql to $pod"
kubectl cp "$LOCALDIR/dump.sql" $pod:/tmp/restore.sql;

#echo "Exec psql on $pod"

sql='DROP DATABASE leviosa WITH (FORCE);'
sql2='revoke all ON schema public FROM leviosa;
drop role leviosa;'
sql3='CREATE DATABASE leviosa;'

echo "Will run '$sql $sql2 $sql3' on database on $pod"


source "$SCRIPT_DIR/.sure.sh"

kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql  -U postgres -c '$sql' " 
kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql  -U postgres -c '$sql2' " 
kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql  -U postgres -c '$sql3' " 

echo "will restore database on $pod"

kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -d leviosa < /tmp/restore.sql " 






echo "Got pod $pod"


