query GetProvidersAndSubjectForAvailability(
  $inputFilter: ProviderFilterInput
  $fromTime: DateTime!
  $toTime: DateTime!
  $subjectId: UUID!
) {
  providers(inputFilter: $inputFilter) {
    id
    name
    isAvailable(fromTime: $fromTime, toTime: $toTime)
  }
  subject(id: $subjectId) {
    id
    age
    gender
    name
    personaId
    phoneNumber
    isAvailable(fromTime: $fromTime, toTime: $toTime)
  }
}
