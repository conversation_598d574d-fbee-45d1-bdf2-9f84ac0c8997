import {
  Select as SelectAriakit,
  SelectProps as SelectPropsAriakit,
  SelectProviderProps,
  SelectProvider as SelectProviderAriaKit,
  SelectArrow as SelectArrowAriakit,
} from "@ariakit/react"
import c from "classnames"
import { ComponentProps, forwardRef } from "react"

import Icon from "components/Icon/Icon"

import styles from "./Select.module.css"

export type SelectProps = SelectPropsAriakit

export const Select = forwardRef<HTMLButtonElement, SelectProps>(
  ({ className = "", ...rest }, ref) => {
    return (
      <SelectAriakit
        {...rest}
        className={`${styles.select} ${className}`}
        ref={ref}
      />
    )
  }
)

export const SelectProvider = (props: SelectProviderProps) => (
  <SelectProviderAriaKit animated {...props} />
)

export const SelectArrow = (
  props: ComponentProps<typeof SelectArrowAriakit>
) => {
  return (
    <SelectArrowAriakit
      {...props}
      render={
        props.render || (
          <Icon
            className={c(props.className, styles.selectArrow)}
            name="arrow-down-s-line"
          />
        )
      }
    />
  )
}
