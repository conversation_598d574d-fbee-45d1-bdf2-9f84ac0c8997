use super::auth::AuthData;
use crate::{app::ContextBase, lib::errors::Result};
use async_trait::async_trait;
use getset::{CopyGetters, Getters};
use leviosa_domain::{
    accounts::AppConfig,
    audit_logger::AuditLogger,
    auth::JwtEncoder,
    calendar_notification_service::CalendarNotificationService,
    command::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    doctor_letter_and_referral_api::Doctor<PERSON>etterAndReferralApi,
    external_organisation_integration::ExternalOrganisationIntegration,
    file_repo::IFileRepo,
    notification_service_integration::DomainNotificationService,
    oracle_api::OracleApi,
    prescription_api::PrescriptionApi,
    query::RepoQueryHandler,
    repo_connection::{RepoConnection, TransactionManager},
};
use leviosa_domain_contracts::{
    auth::{ElectronicId, PasswordHasher},
    services::{
        CustomerService, EmailSender, NhiService, OnlinePaymentService,
    },
};
use sea_orm::DatabaseConnection;
use sqlx::{Pool, Postgres};
use std::sync::Arc;
use std::sync::RwLock;

mod database;
pub use database::{LeviosaDatabaseContext, Unique};
mod auth;
pub use auth::LeviosaAuthContext;
use leviosa_domain::national_registry_integration::DomainNationalRegistry;
use leviosa_domain_contracts::services::PdfGenerator;

mod loaders;
pub use loaders::*;

mod response_cookies;
pub use response_cookies::*;

mod request_cookies;
pub use request_cookies::*;

#[derive(Getters, CopyGetters, Clone)]
pub struct ContextData {
    #[get = "pub"]
    sea_database: DatabaseConnection,
    email_sender: Arc<dyn EmailSender>,
    response_cookies: ResponseCookies,
    request_cookies: RequestCookies,
    auth_data: Arc<RwLock<AuthData>>,
    #[get = "pub"]
    admin_key: String,
    #[get_copy = "pub"]
    token_lifetime_seconds: u32,
    #[get_copy = "pub"]
    refresh_token_expiration_time: u32,
    jwt: Arc<dyn JwtEncoder>,
    #[get = "pub"]
    host_header: Option<String>,
    password_hasher: Arc<dyn PasswordHasher>,
    audit_logger: Arc<dyn AuditLogger>,
    #[get = "pub"]
    loaders: Arc<Loaders>,
    electronic_id: Arc<dyn ElectronicId>,
    #[get = "pub"]
    customer_service: Arc<dyn CustomerService>,
    oracle_api: Arc<dyn OracleApi>,
    prescription_api: Arc<dyn PrescriptionApi>,
    nhi_service: Arc<dyn NhiService>,
    notification_service: Arc<dyn DomainNotificationService>,
    calendar_notification_service: Arc<dyn CalendarNotificationService>,
    file_repo: Arc<dyn IFileRepo>,
    pdf_generator: Arc<dyn PdfGenerator>,
    national_registry: Arc<dyn DomainNationalRegistry>,
    doctor_letter_and_referral_api: Arc<dyn DoctorLetterAndReferralApi>,
    online_payment_service: Arc<dyn OnlinePaymentService>,
    external_organisation_integration: Arc<dyn ExternalOrganisationIntegration>,
    #[get = "pub"]
    cookie_key: cookie::Key,
    #[get = "pub"]
    cookie_session_minutes: u32,
    #[get = "pub"]
    app_environment: String,
}

impl ContextData {
    pub fn new(
        base: ContextBase,
        auth_data: AuthData,
        host_header: Option<String>,
        cookie_header: Option<&str>,
    ) -> Self {
        let mut request_cookies = RequestCookies::new(Some(base.cookie_key.clone()));
        request_cookies.set_cookies_from_header(cookie_header);

        Self {
            sea_database: base.sea_database.clone(),
            email_sender: base.email_sender,
            response_cookies: ResponseCookies::new(base.cookie_key.clone()),
            request_cookies,
            auth_data: Arc::new(RwLock::new(auth_data.clone())),
            admin_key: base.admin_key,
            refresh_token_expiration_time: base.refresh_token_expiration_time,
            token_lifetime_seconds: base.token_lifetime_seconds,
            jwt: base.jwt,
            host_header,
            password_hasher: base.password_hasher,
            audit_logger: base.audit_logger,
            loaders: Arc::new(Loaders::new(
                base.sea_database,
                base.file_repo.clone(),
                base.external_organisation_integration.clone(),
                auth_data,
            )),
            electronic_id: base.electronic_id,
            customer_service: base.customer_service,
            oracle_api: base.oracle_api,
            prescription_api: base.prescription_api,
            nhi_service: base.nhi_service,
            notification_service: base.notification_service_integration,
            calendar_notification_service: base.calendar_notification_service_integration,
            file_repo: base.file_repo,
            pdf_generator: base.pdf_generator,
            national_registry: base.national_registry,
            doctor_letter_and_referral_api: base.doctor_letter_and_referral_api,
            online_payment_service: base.online_payment_service,
            external_organisation_integration: base.external_organisation_integration,
            cookie_key: base.cookie_key,
            cookie_session_minutes: base.cookie_session_minutes,
            app_environment: base.app_environment,
        }
    }

    pub fn database(&self) -> &Pool<Postgres> {
        self.sea_database.get_postgres_connection_pool()
    }

    pub async fn app_config(&self) -> Result<AppConfig> {
        let db = self.database();
        let auth_data = self.auth_data();
        let config = AppConfig::get(db, &auth_data).await?;
        Ok(config)
    }

    pub fn email_sender(&self) -> &dyn EmailSender {
        self.email_sender.as_ref()
    }

    pub fn jwt(&self) -> &dyn JwtEncoder {
        self.jwt.as_ref()
    }

    pub fn password_hasher(&self) -> &dyn PasswordHasher {
        self.password_hasher.as_ref()
    }

    pub fn audit_logger(&self) -> &dyn AuditLogger {
        self.audit_logger.as_ref()
    }

    pub fn electronic_id(&self) -> &dyn ElectronicId {
        self.electronic_id.as_ref()
    }

    pub fn oracle_api(&self) -> &dyn OracleApi {
        self.oracle_api.as_ref()
    }

    pub fn prescription_api(&self) -> &dyn PrescriptionApi {
        self.prescription_api.as_ref()
    }

    pub fn nhi_service(&self) -> Arc<dyn NhiService> {
        self.nhi_service.clone()
    }

    pub fn pdf_generator(&self) -> &dyn PdfGenerator {
        self.pdf_generator.as_ref()
    }

    pub fn response_cookies(&self) -> &ResponseCookies {
        &self.response_cookies
    }

    pub fn request_cookies(&self) -> &RequestCookies {
        &self.request_cookies
    }
}

#[async_trait]
pub trait LeviosaContext<'a> {
    fn get(&self) -> Result<&'a ContextData>;

    fn customer_service(&self) -> &dyn CustomerService;

    async fn app_config(&self) -> Result<AppConfig>;

    fn password_hasher(&self) -> &dyn PasswordHasher;

    fn repo_connection(&self) -> Result<RepoConnection>;

    fn tx_manager(&self) -> Result<TransactionManager>;

    fn repo_query_handler(&self) -> Result<RepoQueryHandler<RepoConnection>>;

    fn repo_command_handler(&self) -> Result<RepoCommandHandler<TransactionManager>>;

    fn notification_service(&self) -> &dyn DomainNotificationService;

    fn calendar_notification_service(&self) -> &dyn CalendarNotificationService;

    fn file_repo(&self) -> &dyn IFileRepo;

    fn national_registry(&self) -> &dyn DomainNationalRegistry;

    fn doctor_letter_and_referral_api(&self) -> &dyn DoctorLetterAndReferralApi;

    fn external_organisation_integration(&self) -> &dyn ExternalOrganisationIntegration;

    fn online_payment_service(&self) -> &dyn OnlinePaymentService;
}

#[async_trait]
impl<'a> LeviosaContext<'a> for async_graphql::Context<'a> {
    fn get(&self) -> Result<&'a ContextData> {
        Ok(self
            .data::<ContextData>()
            .map_err(|e| anyhow::anyhow!("{e:?}"))?)
    }

    fn customer_service(&self) -> &dyn CustomerService {
        self.get().unwrap().customer_service.as_ref()
    }

    async fn app_config(&self) -> Result<AppConfig> {
        self.get()?.app_config().await
    }

    fn password_hasher(&self) -> &dyn PasswordHasher {
        self.get().unwrap().password_hasher.as_ref()
    }

    fn repo_connection(&self) -> Result<RepoConnection> {
        Ok(RepoConnection::new(
            self.get()?.sea_database.clone(),
            self.get()?.auth_data().user()?,
        ))
    }

    fn tx_manager(&self) -> Result<TransactionManager> {
        Ok(TransactionManager::new(self.db()?, self.user()?))
    }

    fn repo_query_handler(&self) -> Result<RepoQueryHandler<RepoConnection>> {
        Ok(RepoQueryHandler::new(RepoConnection::new(
            self.db()?,
            self.user()?,
        )))
    }

    fn repo_command_handler(&self) -> Result<RepoCommandHandler<TransactionManager>> {
        Ok(RepoCommandHandler::new(self.tx_manager()?, self.user()?))
    }

    fn notification_service(&self) -> &dyn DomainNotificationService {
        self.get().unwrap().notification_service.as_ref()
    }

    fn calendar_notification_service(&self) -> &dyn CalendarNotificationService {
        self.get().unwrap().calendar_notification_service.as_ref()
    }

    fn file_repo(&self) -> &dyn IFileRepo {
        self.get().unwrap().file_repo.as_ref()
    }

    fn national_registry(&self) -> &dyn DomainNationalRegistry {
        self.get().unwrap().national_registry.as_ref()
    }

    fn doctor_letter_and_referral_api(&self) -> &dyn DoctorLetterAndReferralApi {
        self.get().unwrap().doctor_letter_and_referral_api.as_ref()
    }

    fn external_organisation_integration(&self) -> &dyn ExternalOrganisationIntegration {
        self.get()
            .unwrap()
            .external_organisation_integration
            .as_ref()
    }

    fn online_payment_service(&self) -> &dyn OnlinePaymentService {
        self.get().unwrap().online_payment_service.as_ref()
    }
}
