.wrap {
  display: flex;
  position: relative;
}

.comboboxWrap {
  display: grid;
  gap: 8px;
}

.popover {
  background-color: var(--color-white);
  max-height: 360px;
  overflow-y: auto;
  padding: 0;
}

.wrap input {
  margin: 0;
}
.wrap input:user-invalid:not(:focus),
.wrap input.error:not(:focus) {
  border-color: var(--color-critical);
}

.errorTooltip {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  width: 26px;
  font-size: 16px;
  align-items: center;
  padding-bottom: 2px;
  color: var(--color-critical-500);
}
