import { captureException } from "@sentry/react"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link, useNavigate, useParams } from "react-router-dom"
import { v4 as uuid } from "uuid"

import { MenuButton } from "components/Ariakit"
import { useMenuStore, useSelectStore } from "components/Ariakit/hooks"
import { FilterableMenu } from "components/FilterableMenu/FilterableMenu"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import Restricted from "features/authentication/components/Restricted/Restricted"
import ServiceTypeModal from "features/organisation-management/components/ServiceTypeModal/ServiceTypeModal"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading, Tag } from "ui"
import useDateFormatter from "utils/useDateFormatter"

import {
  AvailabilityScheduleFragmentFragment,
  AvailabilityScheduleStatus,
  Permission<PERSON><PERSON>,
  ServiceTypeFragmentFragment,
  useCreateServiceTypeAvailabilityMutation,
  useGetServiceTypesQuery,
} from "generated/graphql"

import AvailabilityScheduleModal from "../AvailabilityScheduleModal/AvailabilityScheduleModal"
import AvailabilityServiceType from "../AvailabilityServiceType/AvailabilityServiceType"
import DeleteAvailabilityScheduleDialog from "../DeleteAvailabilityScheduleDialog/DeleteAvailabilityScheduleDialog"
import PublishAvailabilityScheduleDialog from "../PublishAvailabilityScheduleDialog/PublishAvailabilityScheduleDialog"
import SchedulePeriodModal from "../SchedulePeriodModal/SchedulePeriodModal"
import styles from "./AvailabilityScheduleSidebar.module.css"

const AvailabilityScheduleSidebar = ({
  schedules,
  provider,
}: {
  schedules: AvailabilityScheduleFragmentFragment[]
  provider: {
    id: string
    name: string
    teams: { id: string; name: string }[]
  }
}) => {
  const { scheduleId, serviceId } = useParams<{
    scheduleId: string
    serviceId: string
  }>()

  const activeServiceTypeAvailabilityId = serviceId

  const { t } = useTranslation()
  const { t: tEnums } = useTranslation("enums")
  const navigate = useNavigate()
  const dateFormatter = useDateFormatter()
  const [showAddServiceModal, setShowAddServiceModal] = useState(false)
  const [
    showPublishAvailabilityScheduleDialog,
    setShowPublishAvailabilityScheduleDialog,
  ] = useState(false)
  const [showCreateAvailabilitySchedule, setShowCreateAvailabilitySchedule] =
    useState(false)
  const [showSchedulePeriodModal, setShowSchedulePeriodModal] = useState(false)
  const [selectedServiceType, setSelectedServiceType] =
    useState<ServiceTypeFragmentFragment | null>(null)
  const [scheduleToDelete, setScheduleToDelete] = useState<string | null>(null)

  const { data: serviceTypesData } = useGetServiceTypesQuery()
  const [createServiceTypeAvailability] =
    useCreateServiceTypeAvailabilityMutation()

  const selectScheduleStore = useSelectStore({
    defaultValue: scheduleId,
  })

  const menuStore = useMenuStore({
    placement: "bottom-start",
  })

  const schedule = schedules.find((s) => s.id === scheduleId)

  const scheduleOptions = schedules
    .map((s) => {
      if (s.fromDate === null || s.toDate === null) return
      return {
        label: `${dateFormatter(new Date(s.fromDate))} - ${dateFormatter(
          new Date(s.toDate)
        )}`,
        value: s.id,
      }
    })
    .filter((s) => s !== undefined) as { label: string; value: string }[]

  const serviceTypes = serviceTypesData?.externalServiceTypes || []

  // Only show service types that are not already on the schedule
  const filteredServiceTypes = schedule
    ? serviceTypes.filter(
        (st) =>
          !schedule.serviceTypeAvailabilities.some(
            (sta) => sta.serviceType.id === st.id
          ) && !st.deletedAt
      )
    : []

  const onCreateServiceTypeAvailability = (
    serviceType: ServiceTypeFragmentFragment
  ) => {
    if (!scheduleId) return
    const id = uuid()
    // If provider has only one team we select it by default when creating a service type availability
    const teamId = provider.teams.length === 1 ? provider.teams[0].id : null

    createServiceTypeAvailability({
      variables: {
        input: {
          id,
          scheduleId: scheduleId,
          serviceTypeId: serviceType.id,
          teamId: teamId,
        },
      },
      onCompleted: () => {
        navigate(
          generatePath(RouteStrings.calendarSchedule, {
            providerId: provider.id,
            scheduleId: scheduleId,
            serviceId: id,
          })
        )
      },
    })
  }

  const menuOptions = filteredServiceTypes.map((st) => {
    return {
      label: st.name,
      value: st.id,
      onSelect: () => {
        onCreateServiceTypeAvailability(st)
      },
    }
  })

  useEffect(() => {
    if (scheduleId) {
      selectScheduleStore.setValue(scheduleId)
    }
  }, [scheduleId])

  const isDraft = schedule?.status === AvailabilityScheduleStatus.Draft

  const hasMenuOptions = menuOptions.length > 0

  return (
    <aside className={styles.sidebar}>
      <div className={styles.headingContainer}>
        <Heading size="large">{t("Availability schedule")}</Heading>
        {schedule && (
          <Tag color={isDraft ? "neutral" : "levBlue"} className={styles.tag}>
            {tEnums(`AvailabilityScheduleStatus.${schedule.status}`)}
          </Tag>
        )}
      </div>

      {!schedule && (
        <Panel className={styles.panel}>
          <div className={styles.panelInfo}>
            <Heading>{t("Start by adding schedule periods")}</Heading>
            {t("You can set up a separate schedule for each period")}
            <Button
              icon={<Icon name="add-line" />}
              className={styles.addPeriodButton}
              onClick={() => setShowCreateAvailabilitySchedule(true)}
            >
              {t("Add Schedule Period")}
            </Button>
          </div>
        </Panel>
      )}
      {scheduleOptions.length > 0 && (
        <>
          <div className={styles.scheduleSelectWrapper}>
            <Select
              label={t("Schedule Period")}
              selectStore={selectScheduleStore}
              options={scheduleOptions}
              hideMessage
              onSelectChange={(value) => {
                if (typeof value === "string") {
                  navigate(
                    generatePath(RouteStrings.calendarSchedule, {
                      providerId: provider.id,
                      scheduleId: value,
                    })
                  )

                  return
                }

                captureException(new Error(`Value ${value} is not a string`))
              }}
            />
            <Button
              icon={<Icon name="settings-line" />}
              variant="clear"
              className={styles.scheduleSettings}
              onClick={() => setShowSchedulePeriodModal(true)}
            />
          </div>
          <hr />
        </>
      )}

      {schedule && (
        <>
          <div className={styles.servicesHeading}>
            <Heading>{t("Services")}</Heading>
            {isDraft && (
              <Restricted to={PermissionKey.CalendarExternalServiceTypeEdit}>
                {hasMenuOptions && (
                  <FilterableMenu
                    menuStore={menuStore}
                    options={menuOptions}
                    menuButton={
                      <MenuButton
                        variant="clear"
                        icon={<Icon name="add-line" />}
                      >
                        {t("Add Service")}
                      </MenuButton>
                    }
                    onSelect={(option: {
                      label: string
                      value: string
                      onSelect?: () => void
                    }) => {
                      menuStore.setOpen(false)

                      option?.onSelect?.()
                    }}
                    label={t("Add Service")}
                  />
                )}
              </Restricted>
            )}
          </div>

          {schedule.serviceTypeAvailabilities.length === 0 && (
            <Restricted to={PermissionKey.CalendarExternalServiceTypeEdit}>
              <Panel status="info" className={styles.panel}>
                <div className={styles.panelInfo}>
                  <Heading>
                    {hasMenuOptions
                      ? t("Add services to set schedules for them")
                      : "No services available"}
                  </Heading>
                  {t(
                    hasMenuOptions
                      ? "You can set up a separate schedule for each service"
                      : "Services are the tasks or treatments that providers offer. They must be added to the system before setting up schedules for appointments related to them."
                  )}
                </div>

                {!hasMenuOptions && (
                  <div className={styles.redirectServiceButtonWrap}>
                    <Button
                      icon={<Icon name="add-line" />}
                      className={styles.addPeriodButton}
                      onClick={() => setShowAddServiceModal(true)}
                      as={Link}
                      to={RouteStrings.createServiceType}
                    >
                      {t("Add Service")}
                    </Button>
                  </div>
                )}
              </Panel>
            </Restricted>
          )}
          {schedule.serviceTypeAvailabilities
            .filter((sta) => !sta.isDeleted)
            .map((sta) => {
              return (
                <AvailabilityServiceType
                  key={sta.id}
                  canEdit={isDraft}
                  serviceTypeAvailability={sta}
                  expanded={sta.id === activeServiceTypeAvailabilityId}
                  provider={provider}
                />
              )
            })}

          <div className={styles.confirmationButtons}>
            <Button
              status="error"
              onClick={() => {
                if (!scheduleId) return
                setScheduleToDelete(scheduleId)
              }}
            >
              {t("doDelete")}
            </Button>
            {schedule.status === AvailabilityScheduleStatus.Draft && (
              <Button
                variant="filled"
                onClick={() => {
                  setShowPublishAvailabilityScheduleDialog(true)
                }}
              >
                {t("publish")}
              </Button>
            )}
          </div>
        </>
      )}
      <ServiceTypeModal
        serviceType={selectedServiceType}
        showModal={showAddServiceModal !== false}
        closeModal={() => {
          setShowAddServiceModal(false)
          setSelectedServiceType(null)
        }}
        onCreate={(serviceType: ServiceTypeFragmentFragment) => {
          onCreateServiceTypeAvailability(serviceType)
        }}
      />
      <AvailabilityScheduleModal
        showModal={showCreateAvailabilitySchedule}
        closeModal={() => setShowCreateAvailabilitySchedule(false)}
        providerId={provider.id}
        allowProviderSelect={false}
        refetchSchedules
      />
      <SchedulePeriodModal
        showModal={showSchedulePeriodModal}
        onClose={() => setShowSchedulePeriodModal(false)}
        schedules={schedules}
        providerId={provider.id}
        setShowCreateScheduleModal={(show) =>
          setShowCreateAvailabilitySchedule(show)
        }
      />
      <DeleteAvailabilityScheduleDialog
        isOpen={!!scheduleToDelete}
        scheduleId={scheduleToDelete || ""}
        providerId={provider.id}
        onClose={() => setScheduleToDelete(null)}
        schedules={schedules}
      />
      <PublishAvailabilityScheduleDialog
        scheduleId={scheduleId || ""}
        isOpen={showPublishAvailabilityScheduleDialog}
        onClose={() => setShowPublishAvailabilityScheduleDialog(false)}
      />
    </aside>
  )
}

export default AvailabilityScheduleSidebar
