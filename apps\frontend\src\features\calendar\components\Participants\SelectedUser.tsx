import { addYears } from "date-fns"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import { formatPersonaId } from "@leviosa/utils"

import { getEventProviderName } from "utils/getEventProviderName"

import {
  ParticipantAttendeeSource,
  useGetUpcomingEventQuery,
} from "generated/graphql"

import { UserInfo } from "../UserInfo/UserInfo"
import { Participant } from "./Participants"
import styles from "./SelectedUser.module.css"

type SelectedUserProps = {
  user: Participant
  onRemove: () => void
  readOnly?: boolean
  eventToDate?: string
}

export const SelectedUser = ({
  onRemove,
  user: {
    participantType,
    userId,
    rsvpStatus,
    participantId,
    name,
    isAvailable,
    personaId,
    phoneNumber,
    specialty,
  },
  readOnly = false,
  eventToDate,
}: SelectedUserProps) => {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })

  const isSubject = participantType === ParticipantAttendeeSource.Subject

  const inputFilter = useMemo(() => {
    const dateFilter = eventToDate ? new Date(eventToDate) : new Date()

    return {
      participantSubjectId: [userId],
      fromDate: dateFilter,
      toDate: addYears(new Date(dateFilter), 1),
      includeCanceled: false,
    }
  }, [userId])

  const { data: eventsData } = useGetUpcomingEventQuery({
    variables: { inputFilter },
    skip: !isSubject,
  })

  const providerSpecialty = specialty ? tEnum(specialty) : ""
  const upcomingEvent = eventsData?.eventInstances?.[0]

  const upcomingEventData = upcomingEvent
    ? {
        eventName: upcomingEvent.serviceType?.name || upcomingEvent.title,
        provider: getEventProviderName(upcomingEvent.participants),
        date: upcomingEvent.fromDate,
      }
    : undefined

  return (
    <li className={styles.wrap}>
      <input type="hidden" name="userId" value={userId} />
      <input type="hidden" name="participantType" value={participantType} />
      <input type="hidden" name="eventParticipantId" value={participantId} />
      <UserInfo
        key={userId}
        id={userId}
        name={name}
        description={isSubject ? formatPersonaId(personaId) : providerSpecialty}
        onRemove={onRemove}
        rsvpStatus={rsvpStatus}
        isSubject={isSubject}
        readOnly={readOnly}
        isAvailable={isAvailable}
        noPhoneNumber={!phoneNumber && isSubject}
        upcomingEvent={upcomingEventData}
      />
    </li>
  )
}
