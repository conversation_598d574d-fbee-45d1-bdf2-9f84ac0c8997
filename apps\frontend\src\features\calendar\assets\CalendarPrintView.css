@media print {
  .rbc-current-time-indicator {
    display: none;
  }

  .rbc-time-header {
    margin-right: 0 !important;
    border: 0 !important;
  }

  .eventSubjectInfo,
  .calendarOwnerSubject {
    display: block;
  }

  .calendarOwnerSubject {
    margin-bottom: 12px;
  }

  .hide {
    display: none;
  }

  .hideWeekendOnPrint .rbc-header:nth-last-child(-n + 2),
  .hideWeekendOnPrint .rbc-allday-cell .rbc-day-bg:nth-last-child(-n + 2),
  .hideWeekendOnPrint .rbc-month-row .rbc-day-bg:nth-last-child(-n + 2),
  .hideWeekendOnPrint .rbc-month-row .rbc-date-cell:nth-last-child(-n + 2),
  .hideWeekendOnPrint .rbc-time-column:nth-last-child(-n + 2) {
    display: none;
  }

  .rbc-event.rbc-event {
    padding-top: 0;
    padding-bottom: 0;
    top: var(--top) !important;
    height: var(--height) !important;
  }

  .hideWeekendOnPrint .rbc-row-segment {
    flex-basis: 20% !important;
    max-width: 20% !important;
  }

  .rbc-month-row .rbc-row-segment .rbc-event {
    border: 1px solid var(--color-lev-blue);
  }

  .rbc-calendar > div:first-child {
    visibility: hidden;
  }

  .rbc-calendar > div:first-child > div:nth-child(2) {
    visibility: visible;
  }

  .rbc-time-column .rbc-timeslot-group {
    height: 50px !important;
    background-color: red;
  }

  .rbc-timeslot-group {
    min-height: 50px;
    height: 50px !important;
  }

  .rbc-time-slot {
    flex: none;
    height: 13px !important;
  }

  .rbc-event-label {
    display: none;
  }

  .rbc-event-content {
    font-size: 12px;
    color: black;
  }

  .rbc-button-link span {
    font-size: 13px;
    font-weight: bold;
    color: black;
  }

  .rbc-day-slot .rbc-event,
  .rbc-day-slot .rbc-background-event {
    border: 1px solid var(--color-lev-blue);
  }
}
