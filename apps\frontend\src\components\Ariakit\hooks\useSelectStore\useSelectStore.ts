import {
  SelectStoreProps as SelectStorePropsAriakit,
  useSelectStore as useSelectStoreAriakit,
} from "@ariakit/react"

export type SelectStoreProps<T> = SelectStorePropsAriakit & {
  defaultValue?: T
}

export const useSelectStore = <T>(props: SelectStoreProps<T>) => {
  const menuState = useSelectStoreAriakit({
    focusLoop: "vertical",
    animated: true,
    defaultValue: props.defaultValue || "",
    ...props,
  })

  return menuState
}

export default useSelectStore
