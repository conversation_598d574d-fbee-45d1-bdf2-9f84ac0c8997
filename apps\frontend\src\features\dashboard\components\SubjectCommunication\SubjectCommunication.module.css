.tableHeaderCell {
  width: 160px;
}

.wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
}
.wrap thead {
  font-size: 12px;
}
.wrap tbody {
  font-size: 14px;
}

.newMessageButton {
  width: fit-content;
}

.label {
  margin-bottom: 8px;
}

.phoneNumber {
  word-break: break-all;
}

.textMessage {
  margin-top: 16px;
}

.messageFormButtons {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  justify-content: flex-end;
}

.noMessages {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.noMessages > button {
  align-self: flex-end;
}

.noPhoneNumber {
  display: flex;
  flex-direction: column;
  width: fit-content;
  padding: 16px;
  gap: 8px;
}

.reminderToggle {
  display: flex;
  gap: 16px;
  align-items: center;
  width: 100%;
}

.infoPanel {
  width: 100%;
}

.messageContentCell {
  width: 26px;
}

.messageIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-lev-blue);
  padding: 6px;
  margin: -6px;
  margin-right: 0;
}

.messageIcon:hover {
  border-radius: 50%;
  background-color: var(--color-background-hover);
}

.messageIcon > svg {
  font-size: 16px;
}
