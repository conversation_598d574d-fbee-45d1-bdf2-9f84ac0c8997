import c from "classnames"
import { ReactNode } from "react"
import <PERSON><PERSON> from "react-lottie-player"

import <PERSON>osa<PERSON><PERSON> from "assets/leviosa/merki_hreyfing.json"
import { color } from "styles/colors"

import styles from "./PublicCard.module.css"

type PublicCardProps = {
  children?: ReactNode
}

export const PublicCard = ({ children }: PublicCardProps) => {
  return (
    <div className={styles.wrapper}>
      <div className={c(styles.content, color.light)}>
        <Lottie
          className={styles.logo}
          play
          loop
          animationData={LeviosaLogo}
        ></Lottie>
        {children}
      </div>
    </div>
  )
}
