[package]
name = "leviosa-infrastructure"
version = "0.1.0"
edition.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
leviosa-domain-contracts = { path = "../domain-contracts" }
leviosa-domain-types = { path = "../domain-types" }
dotenv.workspace = true
envy.workspace = true
serde.workspace = true
serde_json.workspace = true
jsonwebtoken.workspace = true
bcrypt.workspace = true
thiserror.workspace = true
async-trait.workspace = true
regex.workspace = true
anyhow.workspace = true
sentry.workspace = true
aws-config.workspace = true
aws-sdk-s3.workspace = true
sea-orm.workspace = true
sqlx.workspace = true
isahc.workspace = true
sha2.workspace = true
rand.workspace = true
base64.workspace = true
reqwest.workspace = true
tracing.workspace = true
lettre.workspace = true
prefixed-api-key.workspace = true
tracing-subscriber.workspace = true
sentry-tracing.workspace = true
futures.workspace = true
async-graphql.workspace = true
uuid.workspace = true
urlencoding.workspace = true
serde-xml-rs.workspace = true
chrono.workspace = true
mockall.workspace = true
serde_with.workspace = true
quick-xml = {version="0.29.0", features=["serialize"] }
aes-gcm-siv.workspace = true



[dev-dependencies]
leviosa-testing = { path = "../testing" }
tokio.workspace = true #needed for tests
mockall.workspace = true 
insta.workspace = true
tokio-shared-rt.workspace = true
