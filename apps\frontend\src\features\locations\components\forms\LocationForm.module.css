.formHeading {
  grid-column: 1 / center-start;
  justify-self: self-end;
  padding-right: 16px;
}
.form {
  position: relative;
}
.form::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: calc(-1 * var(--grid-gap) - 2px);
  width: 2px;
  background-color: var(--color-lev-blue);
  transition:
    width 0.2s ease-in-out,
    left 0.2s ease-in-out;
}
.form:focus-within::after {
  width: 4px;
  left: calc(-1 * var(--grid-gap) - 4px);
}

.span1 {
  grid-column: span 1;
}
.span2 {
  grid-column: span 2;
}
.span3 {
  grid-column: span 3;
}
.errorPanel {
  margin-bottom: 12px;
}

.table {
  margin: 0 auto;
  grid-column: 2 / -2;
  width: 100%;
}

.submitRow {
  display: flex;
  justify-content: flex-end;
  gap: var(--grid-gap);
}

.footer {
  display: flex;
  gap: var(--grid-gap);
  justify-content: flex-end;
  margin-top: var(--grid-gap);
}

.panelFrom {
  max-width: 100%;
  margin: 2rem auto;
}
