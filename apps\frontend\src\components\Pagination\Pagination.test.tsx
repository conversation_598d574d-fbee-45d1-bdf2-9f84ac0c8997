import { getPaginationRange } from "./Pagination"

describe("getPaginationRange", () => {
  it("returns an empty array when totalPages is 0", () => {
    const currentPage = 1
    const totalPages = 0

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([])
  })

  it("returns an array with a single page number when totalPages is 1", () => {
    const currentPage = 1
    const totalPages = 1

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([1])
  })

  it("returns an array with page numbers within the range of currentPage - 2 to currentPage + 2", () => {
    const currentPage = 5
    const totalPages = 10

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([1, "...", 4, 5, 6, "...", 10])
  })

  it("returns an array with page numbers within the range of 1 to totalPages when currentPage is near beginning  of edge ", () => {
    const currentPage = 1
    const totalPages = 10

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([1, 2, 3, 4, 5, "...", 10])
  })

  it("returns an array with page numbers within the range of 1 to totalPages when currentPage is near end of edge", () => {
    const currentPage = 10
    const totalPages = 10

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([1, "...", 6, 7, 8, 9, 10])
  })

  it("returns an array with page numbers within the range of 1 to totalPages when totalPages is less than 5", () => {
    const currentPage = 3
    const totalPages = 4

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([1, 2, 3, 4])
  })

  it("returns an array with page numbers within the range of 1 to totalPages when totalPages 10. When current page is 4", () => {
    const currentPage = 4
    const totalPages = 10

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([1, 2, 3, 4, 5, "...", 10])
  })

  it("returns an array with page numbers within the range of 1 to totalPages when totalPages 10. When current page is 7", () => {
    const currentPage = 7
    const totalPages = 10

    const result = getPaginationRange(currentPage, totalPages)

    expect(result).toEqual([1, "...", 6, 7, 8, 9, 10])
  })
})
