use super::{InvoiceIssuerIdGQL, InvoiceIssuerObject, InvoiceLineIdGQL, InvoiceLineObject};
use crate::{
    accounts::{
        ids::{ProviderIdGQL, SubjectIdGQL},
        subject::controllers::SubjectObject,
        user::controllers::ProviderObject,
    },
    billing::{
        enums::{BillingCodeTypeGQL, PaymentMethodGQL, PaymentStatusGQL},
        loaders::InvoiceLinesByInvoiceKey,
    },
    calendar::controllers::EventInstanceIdGQL,
    lib::{
        context::{LeviosaAuthContext, LeviosaContext, LeviosaLoaderContext},
        errors::Result,
        graphql::{gql_id, graphql_wrapper, IntoGQL},
    },
    subject_journal::{
        controllers::{EncounterIdGQL, EncounterObject},
        loaders::EncounterKey,
    },
};
use async_graphql::{Context, InputObject, Object};
use chrono::{DateTime, Utc};
use leviosa_domain::{
    billing::{
        CompleteOnlinePaymentCheckoutCommand, CompleteOnlinePaymentCheckoutCommandHandler,
        CreateCreditInvoiceCommand, CreateInvoiceDiscountForSubjectCommand,
        CreateInvoiceForEncounterCommand, CreateOnlinePaymentCheckoutCommand,
        CreateOnlinePaymentCheckoutCommandHandler, DeleteInvoiceCommand, GetInvoiceQuery,
        GetInvoicesQuery, Invoice, InvoiceId, IssueInvoiceCommand, IssueInvoiceHandler,
        NhiInvoiceTotalCalculator, RetryIssueInvoiceHandler, RetrySendInvoicesToNhiCommand,
        SendInvoiceCommand, SendInvoiceHandler, SetNhiPaysFullAmountCommand, UpdateInvoiceCommand,
        UpdateInvoiceLineCommand, UpdateInvoicePaymentMethodCommand, UpdatePaymentStatusCommand,
    },
    command::{CommandPipeline, InvoiceTotalCalculationHandler},
    nhi_service::SubjectInsuranceStatus,
    online_payment_service::PaymentCheckoutResponse,
    query::QueryPipeline,
};
use uuid::Uuid;
use leviosa_infrastructure::online_payment::EmptyPaymentService as EmptyOnlinePaymentService;

gql_id!(InvoiceId);
graphql_wrapper!(Invoice);

#[derive(Default)]
pub struct InvoiceQueries;

#[Object]
impl InvoiceQueries {
    async fn invoice(&self, ctx: &Context<'_>, id: InvoiceIdGQL) -> Result<InvoiceObject> {
        let invoice = QueryPipeline::execute(
            ctx.repo_query_handler()?,
            &ctx.auth_data()?,
            GetInvoiceQuery { id: id.into() },
        )
        .await?;

        Ok(invoice.into_gql())
    }

    async fn invoices(
        &self,
        ctx: &Context<'_>,
        filter: InvoiceFilterInput,
    ) -> Result<Vec<InvoiceObject>> {
        let invoices = QueryPipeline::execute(
            ctx.repo_query_handler()?,
            &ctx.auth_data()?,
            GetInvoicesQuery {
                subject_id: filter.subject_id.map(Into::into),
                provider_id: filter.provider_id.map(Into::into),
                issuer_id: filter.issuer_id.map(Into::into),
                from_date: filter.from_date,
                to_date: filter.to_date,
                issued: filter.issued,
                payment_status: filter.payment_status.map(Into::into),
                payment_method: filter.payment_method.map(Into::into),
                invoice_number: filter.invoice_number.clone(),
                limit: filter.limit,
                from_treatment_date: filter.from_treatment_date,
                to_treatment_date: filter.to_treatment_date,
            },
        )
        .await?;

        Ok(invoices.into_gql())
    }

    async fn subject_insurance_status(
        &self,
        ctx: &Context<'_>,
        subject_id: SubjectIdGQL,
    ) -> Result<SubjectInsuranceStatusObject> {
        // TODO: Implement GetSubjectPaymentStatusQuery through contracts
        // For now, return a default status
        let payment_status = SubjectInsuranceStatus::Uninsured;

        Ok(payment_status.into_gql())
    }
}

graphql_wrapper!(SubjectInsuranceStatus);
#[Object(name = "SubjectInsuranceStatus")]
impl SubjectInsuranceStatusObject {
    async fn subject_status(&self) -> &str {
        match &self.0 {
            SubjectInsuranceStatus::Uninsured => "UNINSURED",
            SubjectInsuranceStatus::Insured {
                insurance_category, ..
            } => insurance_category,
        }
    }

    async fn payment_status(&self) -> f64 {
        match self.0 {
            SubjectInsuranceStatus::Uninsured => 0.0,
            SubjectInsuranceStatus::Insured {
                maximum_payable_by_subject,
                ..
            } => maximum_payable_by_subject,
        }
    }

    async fn is_insured(&self) -> bool {
        match self.0 {
            SubjectInsuranceStatus::Uninsured => false,
            SubjectInsuranceStatus::Insured { .. } => true,
        }
    }
}

#[derive(Default)]
pub struct InvoiceMutations;

#[Object]
impl InvoiceMutations {
    // The first step in billing is creating an Invoice.
    async fn create_invoice(
        &self,
        ctx: &Context<'_>,
        input: CreateInvoiceInput,
    ) -> Result<InvoiceObject> {
        let command = CreateInvoiceForEncounterCommand {
            id: input.id.map(Into::into),
            encounter_id: input.encounter_id.into(),
            reference: input.reference,
        };

        let handler = InvoiceTotalCalculationHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            NhiInvoiceTotalCalculator::new_with_contracts(ctx.get()?.nhi_service(), ctx.auth_data()?.token()?),
        );

        let invoice = CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(invoice.into_gql())
    }

    // Used if an invoice was wrong and has been issued to give money back to the patient.
    async fn create_credit_invoice(
        &self,
        ctx: &Context<'_>,
        invoice_id: InvoiceIdGQL,
    ) -> Result<InvoiceObject> {
        let command = CreateCreditInvoiceCommand {
            invoice_id: invoice_id.into(),
        };

        let invoice =
            CommandPipeline::handle(ctx.repo_command_handler()?, &ctx.auth_data()?, command)
                .await?;

        Ok(invoice.into_gql())
    }

    async fn edit_invoice(
        &self,
        ctx: &Context<'_>,
        input: EditInvoiceInput,
    ) -> Result<InvoiceObject> {
        let command = UpdateInvoiceCommand {
            id: input.id.into(),
            issuer_id: input.issuer_id.map(Into::into),
            provider_id: input.provider_id.map(Into::into),
            payment_date: input.payment_date,
            due_date: input.due_date,
            treatment_date: input.treatment_date,
            total_discount: None,
            nhi_pays_all: None,
            total_vat: None,
            total: None,
            total_payable_by_subject: None,
            total_payable_by_insurance: None,
            reference: input.reference,
            payer_id: input.payer_id,
            payer_name: input.payer_name,
            payer_email: input.payer_email,
            comment: input.comment,
            send_invoice_mail: input.send_invoice_mail,
            print_invoice: input.print_invoice,
            payment_method: input.payment_method.map(Into::into),
            subject_discount: None,
        };

        let invoice =
            CommandPipeline::handle(ctx.repo_command_handler()?, &ctx.auth_data()?, command)
                .await?;

        Ok(invoice.into_gql())
    }

    async fn delete_invoice(
        &self,
        ctx: &Context<'_>,
        input: DeleteInvoiceInput,
    ) -> Result<InvoiceObject> {
        let command = DeleteInvoiceCommand {
            id: input.id.into(),
        };

        let invoice =
            CommandPipeline::handle(ctx.repo_command_handler()?, &ctx.auth_data()?, command)
                .await?;

        Ok(invoice.into_gql())
    }

    async fn issue_invoice(&self, ctx: &Context<'_>, id: InvoiceIdGQL) -> Result<InvoiceObject> {
        let app_config = ctx.app_config().await?;
        // TODO: Implement NHI service through contracts
        // For now, use a placeholder
        let handler = IssueInvoiceHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            ctx.auth_data()?.token()?,
            app_config.ui_language_id(),
            // TODO: Add NHI service parameter
            ctx.file_repo(),
            ctx.get()?.pdf_generator(),
            ctx.get()?.email_sender(),
        );
        let invoice = CommandPipeline::handle(
            handler,
            &ctx.auth_data()?,
            IssueInvoiceCommand { id: id.into() },
        )
        .await?;

        Ok(invoice.into_gql())
    }

    async fn retry_send_invoices_to_nhi(
        &self,
        ctx: &Context<'_>,
        ids: Vec<InvoiceIdGQL>,
    ) -> Result<bool> {
        // TODO: Implement NHI service through contracts
        // For now, use a placeholder
        let handler = RetryIssueInvoiceHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            ctx.auth_data()?.token()?,
            // TODO: Add NHI service parameter
        );

        CommandPipeline::handle(
            handler,
            &ctx.auth_data()?,
            RetrySendInvoicesToNhiCommand {
                ids: ids.into_iter().map(Into::into).collect(),
            },
        )
        .await?;

        Ok(true)
    }

    async fn update_invoice_payment_status(
        &self,
        ctx: &Context<'_>,
        input: UpdateInvoicePaymentStatusInput,
    ) -> Result<InvoiceObject> {
        let invoice_line = CommandPipeline::handle(
            ctx.repo_command_handler()?,
            &ctx.auth_data()?,
            UpdatePaymentStatusCommand {
                id: input.id.into(),
                payment_status: input.payment_status.into(),
            },
        )
        .await?;

        Ok(invoice_line.into_gql())
    }

    async fn update_invoice_payment_method(
        &self,
        ctx: &Context<'_>,
        input: UpdateInvoicePaymentMethodInput,
    ) -> Result<InvoiceObject> {
        let invoice_line = CommandPipeline::handle(
            ctx.repo_command_handler()?,
            &ctx.auth_data()?,
            UpdateInvoicePaymentMethodCommand {
                id: input.id.into(),
                payment_method: input.payment_method.into(),
            },
        )
        .await?;

        Ok(invoice_line.into_gql())
    }

    async fn edit_invoice_line(
        &self,
        ctx: &Context<'_>,
        input: EditInvoiceLineInput,
    ) -> Result<InvoiceLineObject> {
        let command = UpdateInvoiceLineCommand {
            id: input.invoice_line_id.into(),
            quantity: input.quantity,
            billable_quantity: input.billable_quantity,
            unit_price: input.unit_price,
            units: input.units,
            vat: input.vat,
            discount: input.discount,
        };

        let handler = InvoiceTotalCalculationHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            NhiInvoiceTotalCalculator::new_with_contracts(ctx.get()?.nhi_service(), ctx.auth_data()?.token()?),
        );

        let invoice_line = CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(invoice_line.into_gql())
    }

    async fn create_checkout_for_online_payment(
        &self,
        ctx: &Context<'_>,
        event_instance_id: EventInstanceIdGQL,
    ) -> Result<PaymentCheckoutResponseObject> {
        let command = CreateOnlinePaymentCheckoutCommand {
            event_instance_id: event_instance_id.into(),
        };

        let handler = CreateOnlinePaymentCheckoutCommandHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            // TODO: Fix OnlinePaymentService architecture mismatch
            // For now, disable online payment functionality
            &EmptyOnlinePaymentService,
        );

        let online_payment_checkout =
            CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(online_payment_checkout.into_gql())
    }

    async fn complete_checkout_for_online_payment(
        &self,
        ctx: &Context<'_>,
        checkout_id: Uuid,
    ) -> Result<InvoiceObject> {
        let command = CompleteOnlinePaymentCheckoutCommand { checkout_id };

        let handler = CompleteOnlinePaymentCheckoutCommandHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            // TODO: Fix OnlinePaymentService architecture mismatch
            // For now, disable online payment functionality
            &EmptyOnlinePaymentService,
        );

        let complete_checkout =
            CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(complete_checkout.into_gql())
    }

    async fn create_invoice_discount_for_subject(
        &self,
        ctx: &Context<'_>,
        invoice_id: InvoiceIdGQL,
        discount: f64,
    ) -> Result<InvoiceObject> {
        let command = CreateInvoiceDiscountForSubjectCommand {
            invoice_id: invoice_id.into(),
            subject_discount: discount,
        };

        let handler = InvoiceTotalCalculationHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            NhiInvoiceTotalCalculator::new_with_contracts(ctx.get()?.nhi_service(), ctx.auth_data()?.token()?),
        );

        let invoice = CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(invoice.into_gql())
    }

    async fn send_invoice_email(
        &self,
        ctx: &Context<'_>,
        invoice_id: InvoiceIdGQL,
        email: Option<String>,
        update_subject_email: Option<bool>,
    ) -> Result<InvoiceObject> {
        let mut command = SendInvoiceCommand::new(invoice_id.into());
        command.email = email;
        command.update_subject_email = update_subject_email.unwrap_or(false);

        let app_config = ctx.app_config().await?;
        let handler = SendInvoiceHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            ctx.auth_data()?.token()?,
            app_config.ui_language_id(),
            ctx.file_repo(),
            ctx.get()?.pdf_generator(),
            ctx.get()?.email_sender(),
        );

        let invoice = CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(invoice.into_gql())
    }

    async fn set_nhi_pays_full_invoice_amount(
        &self,
        ctx: &Context<'_>,
        invoice_id: InvoiceIdGQL,
        nhi_pays_all: bool,
    ) -> Result<InvoiceObject> {
        let command = SetNhiPaysFullAmountCommand {
            invoice_id: invoice_id.into(),
            nhi_pays_all,
        };

        let handler = InvoiceTotalCalculationHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            NhiInvoiceTotalCalculator::new_with_contracts(ctx.get()?.nhi_service(), ctx.auth_data()?.token()?),
        );

        let invoice = CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(invoice.into_gql())
    }
}

graphql_wrapper!(PaymentCheckoutResponse);
#[Object(name = "PaymentCheckoutResponse")]
impl PaymentCheckoutResponseObject {
    async fn id(&self) -> &Uuid {
        &self.0.id
    }
    async fn url(&self) -> &str {
        &self.0.url
    }
}

#[Object(name = "Invoice")]
impl InvoiceObject {
    pub async fn id(&self) -> InvoiceIdGQL {
        self.0.id().into()
    }

    pub async fn invoice_number(&self) -> String {
        self.0.invoice_number().clone()
    }

    pub async fn invoice_lines(&self, ctx: &Context<'_>) -> Result<Vec<InvoiceLineObject>> {
        let lines = ctx
            .db_loader()?
            .load_one(InvoiceLinesByInvoiceKey(self.0.id()))
            .await?;

        if let Some(lines) = lines {
            Ok(lines.into_gql())
        } else {
            Ok(vec![])
        }
    }

    pub async fn provider(&self, ctx: &Context<'_>) -> Result<Option<ProviderObject>> {
        if let Some(provider_id) = self.0.provider_id() {
            let provider = ctx
                .db_loader()?
                .load_one(provider_id)
                .await?
                .ok_or_else(|| anyhow::anyhow!("Provider not found"))?;
            Ok(Some(provider.into_gql()))
        } else {
            Ok(None)
        }
    }

    // Organizations can define Issuers, so that they know which provider should receive payment.
    pub async fn issuer(&self, ctx: &Context<'_>) -> Result<Option<InvoiceIssuerObject>> {
        if let Some(issuer_id) = self.0.issuer_id() {
            let issuer = ctx
                .db_loader()?
                .load_one(issuer_id)
                .await?
                .ok_or_else(|| anyhow::anyhow!("Issuer not found"))?;
            Ok(Some(issuer.into_gql()))
        } else {
            Ok(None)
        }
    }

    pub async fn subject(&self, ctx: &Context<'_>) -> Result<SubjectObject> {
        let subject = ctx
            .db_loader()?
            .load_one(self.0.subject_id())
            .await?
            .ok_or_else(|| anyhow::anyhow!("Subject not found"))?;
        Ok(subject.into_gql())
    }

    pub async fn encounter(&self, ctx: &Context<'_>) -> Result<Option<EncounterObject>> {
        if self.0.encounter_id().is_some() {
            let encounter = ctx
                .db_loader()?
                .load_one(EncounterKey(self.0.encounter_id().unwrap()))
                .await?
                .ok_or_else(|| {
                    anyhow::anyhow!("Encounter not found {:?}", self.0.encounter_id())
                })?;
            Ok(Some(encounter.into_gql()))
        } else {
            Ok(None)
        }
    }

    // Date when payment was received
    pub async fn payment_date(&self) -> Option<DateTime<Utc>> {
        self.0.payment_date()
    }

    // The date when the service provided
    pub async fn treatment_date(&self) -> Option<DateTime<Utc>> {
        self.0.treatment_date()
    }

    pub async fn due_date(&self) -> Option<DateTime<Utc>> {
        self.0.due_date()
    }

    pub async fn total_line_items_discount(&self) -> Option<f64> {
        self.0.total_discount()
    }

    pub async fn total_discount(&self) -> Option<f64> {
        let line_items = self.0.total_discount().unwrap_or(0.0);
        let nhi_discount = self.0.subject_discount().unwrap_or(0.0);
        let total = line_items + nhi_discount;
        if total == 0.0 {
            Some(0.0)
        } else {
            Some(total)
        }
    }

    pub async fn subject_discount(&self) -> Option<f64> {
        self.0.subject_discount()
    }

    pub async fn total_vat(&self) -> Option<f64> {
        self.0.total_vat()
    }

    pub async fn total(&self) -> Option<f64> {
        self.0.total()
    }

    pub async fn total_without_vat(&self) -> Option<f64> {
        match (self.0.total(), self.0.total_vat()) {
            (Some(total), Some(vat)) => Some(total - vat),
            (Some(total), None) => Some(total),
            (None, _) => None,
        }
    }

    pub async fn total_payable_by_subject(&self) -> Option<f64> {
        self.0.total_payable_by_subject()
    }

    pub async fn total_payable_by_insurance(&self) -> Option<f64> {
        self.0.total_payable_by_insurance()
    }

    pub async fn issued(&self) -> bool {
        self.0.issued()
    }

    pub async fn issued_at(&self) -> Option<DateTime<Utc>> {
        self.0.issued_at()
    }

    pub async fn payment_status(&self) -> PaymentStatusGQL {
        self.0.payment_status().into()
    }

    // An optional reference that the customers can set
    pub async fn reference(&self) -> Option<String> {
        self.0.reference().clone()
    }

    // Find out what this is
    pub async fn payer_id(&self) -> Option<String> {
        self.0.payer_id().clone()
    }

    pub async fn payer_name(&self) -> Option<String> {
        self.0.payer_name().clone()
    }

    pub async fn payer_email(&self) -> Option<String> {
        self.0.payer_email().clone()
    }

    pub async fn comment(&self) -> Option<String> {
        self.0.comment().clone()
    }

    // This is an email
    pub async fn send_invoice_mail(&self) -> Option<bool> {
        self.0.send_invoice_mail()
    }

    pub async fn print_invoice(&self) -> Option<bool> {
        self.0.print_invoice()
    }

    pub async fn payment_method(&self) -> Option<PaymentMethodGQL> {
        self.0.payment_method().map(Into::into)
    }

    pub async fn created_by(&self, ctx: &Context<'_>) -> Result<ProviderObject> {
        let provider = ctx
            .db_loader()?
            .load_one(self.0.created_by_id())
            .await?
            .ok_or_else(|| anyhow::anyhow!("Provider not found"))?;
        Ok(provider.into_gql())
    }

    pub async fn created_at(&self) -> DateTime<Utc> {
        self.0.created_at()
    }

    pub async fn updated_by(&self, ctx: &Context<'_>) -> Result<ProviderObject> {
        let provider = ctx
            .db_loader()?
            .load_one(self.0.updated_by_id())
            .await?
            .ok_or_else(|| anyhow::anyhow!("Provider not found"))?;
        Ok(provider.into_gql())
    }

    pub async fn updated_at(&self) -> DateTime<Utc> {
        self.0.updated_at()
    }

    pub async fn deleted_by(&self, ctx: &Context<'_>) -> Result<Option<ProviderObject>> {
        if let Some(provider_id) = self.0.deleted_by_id() {
            let provider = ctx
                .db_loader()?
                .load_one(provider_id)
                .await?
                .ok_or_else(|| anyhow::anyhow!("Provider not found"))?;
            Ok(Some(provider.into_gql()))
        } else {
            Ok(None)
        }
    }

    pub async fn deleted_at(&self) -> Option<DateTime<Utc>> {
        self.0.deleted_at()
    }

    pub async fn contains_nhi_items(&self) -> bool {
        self.0.contains_nhi_items().unwrap_or(false)
    }

    pub async fn nhi_payable_by_subject(&self) -> Option<f64> {
        self.0.nhi_payable_by_subject()
    }

    pub async fn clinic_payable_by_subject(&self) -> Option<f64> {
        self.0.clinic_payable_by_subject()
    }

    pub async fn total_payable_by_subject_before_discount(&self) -> Option<f64> {
        self.0.total_payable_by_subject_before_discount()
    }

    pub async fn nhi_pays_all(&self) -> bool {
        self.0.nhi_pays_all()
    }
}

#[derive(InputObject)]
pub struct CreateInvoiceInput {
    id: Option<InvoiceIdGQL>,
    encounter_id: EncounterIdGQL,
    subject_id: Option<SubjectIdGQL>,
    reference: Option<String>,
}

#[derive(InputObject)]
pub struct EditInvoiceInput {
    id: InvoiceIdGQL,
    issuer_id: Option<InvoiceIssuerIdGQL>,
    provider_id: Option<ProviderIdGQL>,
    treatment_date: Option<DateTime<Utc>>,
    payment_date: Option<DateTime<Utc>>,
    due_date: Option<DateTime<Utc>>,
    reference: Option<String>,
    payer_id: Option<String>,
    payer_name: Option<String>,
    payer_email: Option<String>,
    comment: Option<String>,
    send_invoice_mail: Option<bool>,
    print_invoice: Option<bool>,
    payment_method: Option<PaymentMethodGQL>,
}

#[derive(InputObject)]
struct DeleteInvoiceInput {
    id: InvoiceIdGQL,
}

#[derive(InputObject)]
pub struct EditInvoiceLineInput {
    invoice_line_id: InvoiceLineIdGQL,
    quantity: Option<f64>,
    billable_quantity: Option<f64>,
    units: Option<f64>,
    unit_price: Option<f64>,
    vat: Option<f64>,
    discount: Option<f64>,
}

#[derive(InputObject)]
pub struct InvoiceFilterInput {
    subject_id: Option<SubjectIdGQL>,
    provider_id: Option<ProviderIdGQL>,
    issuer_id: Option<InvoiceIssuerIdGQL>,
    from_date: Option<DateTime<Utc>>,
    to_date: Option<DateTime<Utc>>,
    issued: Option<bool>,
    payment_status: Option<PaymentStatusGQL>,
    payment_method: Option<PaymentMethodGQL>,
    invoice_number: Option<String>,
    limit: Option<u64>,
    from_treatment_date: Option<DateTime<Utc>>,
    to_treatment_date: Option<DateTime<Utc>>,
}

#[derive(InputObject)]
pub struct AddInvoiceLineToInvoiceInput {
    encounter_id: EncounterIdGQL,
    invoice_id: InvoiceIdGQL,
    billing_code_id: Uuid,
    billing_code_type: BillingCodeTypeGQL,
    quantity: f64,
}

#[derive(InputObject)]
pub struct UpdateInvoicePaymentStatusInput {
    id: InvoiceIdGQL,
    payment_status: PaymentStatusGQL,
}

#[derive(InputObject)]
pub struct UpdateInvoicePaymentMethodInput {
    id: InvoiceIdGQL,
    payment_method: PaymentMethodGQL,
}
