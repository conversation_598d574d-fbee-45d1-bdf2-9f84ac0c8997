import { ChangeEvent, FocusEvent } from "react"
import { useTranslation } from "react-i18next"

import styles from "../../DateInput.module.css"

type DayInputProps = {
  onDayChange: (value: string) => void
  value?: string
  defaultValue?: string
  disabled?: boolean
  required?: boolean
  readOnly?: boolean
}

const validDayRegex = /^(0?[1-9]|[1-2][0-9]|3[0-1])$/

export const DayInput = ({
  value,
  defaultValue,
  onDayChange,
  disabled,
  required,
  readOnly,
}: DayInputProps) => {
  const { t } = useTranslation()

  return (
    <input
      aria-label="Day"
      placeholder={t("DD")}
      className={`${styles.inputs} ${styles.day}`}
      type="tel"
      min={1}
      max={31}
      value={value}
      defaultValue={defaultValue}
      onChange={(event: ChangeEvent<HTMLInputElement>) => {
        const { value } = event.target
        if (validDayRegex.test(value) || value === "" || value === "0") {
          onDayChange(value)
        }
      }}
      onBlur={(event: FocusEvent<HTMLInputElement>) => {
        const { value } = event.target
        if (value.length === 1 && value !== "0") {
          onDayChange(`0${value}`)
        }

        if (value === "0") {
          onDayChange("")
        }
      }}
      disabled={disabled}
      required={required}
      readOnly={readOnly}
    />
  )
}
