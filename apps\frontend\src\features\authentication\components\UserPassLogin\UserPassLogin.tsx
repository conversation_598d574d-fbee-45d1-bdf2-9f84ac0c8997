import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"

import Panel from "components/Panel/Panel"
import { useAuth } from "features/authentication/AuthProvider"
import { FormGrid } from "ui"
import Button from "ui/components/Button/Button"
import { LoadingIcon } from "ui/components/Loading/LoadingIcon"

import { SessionData, useUserPassLoginMutation } from "generated/graphql"

import styles from "../../Authentication.module.css"
import PasswordOnlyLoginForm from "./PasswordOnlyLoginForm"
import UserPassLoginForm from "./UserPassLoginForm"

type UserPassLoginProps = {
  session: SessionData | null
}
export default function UserPassLogin({ session }: UserPassLoginProps) {
  // store session in state so we know what the initial value was
  // this prevents the form from being reset i.e. flickering when the session
  // changes before isAuthorized is set
  const [showExistingSessionForm] = useState(!!session)

  const { t } = useTranslation()
  const { authenticate } = useAuth()
  const [login, { loading, error }] = useUserPassLoginMutation()

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.target as HTMLFormElement)

    const email = session?.email || (formData.get("email") as string)
    const password = formData.get("password") as string

    if (
      !email ||
      !password ||
      typeof email !== "string" ||
      typeof password !== "string"
    ) {
      return
    }

    const { data } = await login({
      variables: { email, password },
    })

    if (!data) return

    const { accessToken, refreshToken } = data.logIn.tokens
    authenticate({ accessToken, refreshToken })
  }

  const commonChildren = (
    <>
      {error && (
        <Panel status="error" className={styles.panel}>
          {error.message}
        </Panel>
      )}
      <Button
        className={styles.submitButton}
        type="submit"
        size="large"
        variant="filled"
        loading={loading}
        {...(loading && { iconEnd: <LoadingIcon /> })}
      >
        {t("routes:auth.doLogin")}
      </Button>
    </>
  )

  return (
    <FormGrid onSubmit={handleSubmit} className={styles.wrap}>
      {showExistingSessionForm && session ? (
        <PasswordOnlyLoginForm {...session}>
          {commonChildren}
        </PasswordOnlyLoginForm>
      ) : (
        <UserPassLoginForm>{commonChildren}</UserPassLoginForm>
      )}
    </FormGrid>
  )
}
