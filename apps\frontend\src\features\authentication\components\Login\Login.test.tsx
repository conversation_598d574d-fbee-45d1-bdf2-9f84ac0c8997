import {
  screen,
  waitFor,
  waitForElementToBeRemoved,
} from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { Suspense } from "react"
import * as reactRouter from "react-router-dom"
import { batchedGraphQLQuery } from "test/mocks/batchedGraphQLQuery"
import {
  getPasswordAuthMethodNoSession,
  getPasswordAuthMethodWithSession,
  userPassLoginMutationMock,
  getElectronicIdAuthMethodNoSession,
  getElectronicIdAuthMethodWithSession,
  electronicIdLoginMutationMock,
  userPassLoginMutationMockError,
  removeSessionCookieMutationMock,
} from "test/mocks/handlers/loginHandlers"
import { server } from "test/mocks/node"
import { render } from "test/testUtils"

import * as tokenStorage from "features/authentication/utils/tokenStorage"

import Login from "./Login"

vi.mock(import("react-router-dom"), async (importOriginal) => {
  const original = await importOriginal()

  return {
    ...original,
    useNavigate: original.useNavigate,
  }
})

// Helper component to display current location for testing
const LocationDisplay = () => {
  const location = reactRouter.useLocation()
  return <div data-testid="location-display">{location.pathname}</div>
}

describe("<Login />", () => {
  afterEach(() => {
    vi.restoreAllMocks()
  })

  it("should successfully authenticate with email and password", async () => {
    server.use(
      batchedGraphQLQuery("/graphql", [
        getPasswordAuthMethodNoSession,
        userPassLoginMutationMock,
      ])
    )

    const user = userEvent.setup()

    render(
      <Suspense fallback={<></>}>
        <Login />
        <LocationDisplay />
      </Suspense>,
      { initialEntries: ["/login"] }
    )

    expect(screen.getByTestId("location-display")).toHaveTextContent("/login")

    // still loading - user doesn't see anything
    expect(screen.queryByText(/log in/i)).toBeNull()

    expect(screen.getByTestId("login-loading")).toBeInTheDocument()

    await waitForElementToBeRemoved(
      () => screen.queryByTestId("login-loading"),
      // timeout needed, to prevent flaky test on first run
      { timeout: 6000 }
    )

    const userInfo = { email: "<EMAIL>", password: "doe" }

    const emailField = await screen.findByLabelText(/email/i)
    // need to clear the inputs first, because of the default values
    await user.clear(emailField)
    await user.type(emailField, userInfo.email)
    expect(emailField).toHaveValue(userInfo.email)

    const passwordField = screen.getByLabelText(/password/i)
    await user.clear(passwordField)
    await user.type(passwordField, userInfo.password)
    expect(passwordField).toHaveValue(userInfo.password)

    const submitButton = screen.getByRole("button", { name: /log in/i })
    expect(submitButton).not.toBeDisabled()

    await user.click(submitButton)

    await waitFor(() => {
      expect(submitButton).toBeDisabled()
    })

    await waitFor(() => {
      expect(submitButton).not.toBeDisabled()
    })

    expect(screen.getByTestId("location-display")).not.toHaveTextContent(
      "/login"
    )
    // add timeout for CI
  }, 10000)

  it("should successfully authenticate with electronic ID", async () => {
    // Mock getTokens to ensure a clean auth state, since clearing localStorage/sessionStorage is not sufficient between tests
    vi.spyOn(tokenStorage, "getTokens").mockReturnValue(null)

    server.use(
      batchedGraphQLQuery("/graphql", [
        getElectronicIdAuthMethodNoSession,
        electronicIdLoginMutationMock,
      ])
    )

    const user = userEvent.setup()

    render(
      <Suspense fallback={<></>}>
        <Login />
        <LocationDisplay />
      </Suspense>,
      { initialEntries: ["/login"] }
    )

    expect(screen.getByTestId("location-display")).toHaveTextContent("/login")
    expect(screen.queryByText(/log in/i)).toBeNull()
    expect(screen.getByTestId("login-loading")).toBeInTheDocument()

    const userInfo = { phoneNumber: "222-5678" }

    const phoneField = await screen.findByLabelText(/phone/i)
    await user.clear(phoneField)
    await user.type(phoneField, userInfo.phoneNumber)
    expect(phoneField).toHaveValue("2225678")

    const submitButton = screen.getByRole("button", { name: /log in/i })
    expect(submitButton).not.toBeDisabled()

    await user.click(submitButton)
    expect(submitButton).toBeDisabled()

    await waitFor(() => {
      expect(submitButton).not.toBeDisabled()
    })

    expect(screen.getByTestId("location-display")).not.toHaveTextContent(
      "/login"
    )
  })

  it("should display user not found error for invalid credentials", async () => {
    server.use(
      batchedGraphQLQuery("/graphql", [
        getPasswordAuthMethodNoSession,
        userPassLoginMutationMockError,
      ])
    )

    const user = userEvent.setup()

    render(
      <Suspense fallback={<></>}>
        <Login />
        <LocationDisplay />
      </Suspense>
    )

    const emailField = await screen.findByLabelText(/email/i)
    await user.clear(emailField)
    await user.type(emailField, "<EMAIL>")
    expect(emailField).toHaveValue("<EMAIL>")

    const passwordField = screen.getByLabelText(/password/i)
    await user.clear(passwordField)
    await user.type(passwordField, "irrelevant")
    expect(passwordField).toHaveValue("irrelevant")

    const submitButton = screen.getByRole("button", { name: /log in/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(
        screen.getByText(/user with "x@leviosa\.is" was not found/i)
      ).toBeInTheDocument()
    })
  })

  describe("Returning User Experience", () => {
    it("should display welcome back interface for existing password session", async () => {
      server.use(
        batchedGraphQLQuery("/graphql", [
          getPasswordAuthMethodWithSession,
          userPassLoginMutationMock,
        ])
      )

      const user = userEvent.setup()

      render(
        <Suspense fallback={<></>}>
          <Login />
          <LocationDisplay />
        </Suspense>,
        { initialEntries: ["/login"] }
      )

      expect(await screen.findByText(/welcome back/i)).toBeTruthy()
      expect(screen.getByText("Mock Provider")).toBeInTheDocument()

      expect(screen.queryByLabelText(/email/i)).not.toBeInTheDocument()

      const passwordField = screen.getByLabelText(/password/i)
      expect(passwordField).toBeInTheDocument()
      expect(passwordField).toHaveFocus()

      const userInfo = { password: "123" }

      await user.clear(passwordField)
      await user.type(passwordField, userInfo.password)
      expect(passwordField).toHaveValue(userInfo.password)

      const submitButton = screen.getByRole("button", { name: "Log In" })
      expect(submitButton).not.toBeDisabled()

      await user.click(submitButton)

      expect(submitButton).toBeDisabled()

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled()
      })

      expect(screen.getByTestId("location-display")).not.toHaveTextContent(
        "/login"
      )
    })

    it("should display welcome back interface for existing electronic ID session", async () => {
      vi.spyOn(tokenStorage, "getTokens").mockReturnValue(null)

      server.use(
        batchedGraphQLQuery("/graphql", [
          getElectronicIdAuthMethodWithSession,
          electronicIdLoginMutationMock,
        ])
      )

      const user = userEvent.setup()

      render(
        <Suspense fallback={<></>}>
          <Login />
          <LocationDisplay />
        </Suspense>,
        { initialEntries: ["/login"] }
      )

      expect(await screen.findByText(/welcome back/i)).toBeTruthy()
      expect(screen.getByText("Mock ElectronicId User")).toBeInTheDocument()

      expect(screen.queryByLabelText(/phone/i)).not.toBeInTheDocument()

      const submitButton = screen.getByRole("button", {
        name: /log in with electronic id/i,
      })

      await user.click(submitButton)
      expect(submitButton).toBeDisabled()

      expect(
        screen.getByText(/please confirm authentication/i)
      ).toBeInTheDocument()
      expect(screen.getByText(/on your phone/i)).toBeInTheDocument()

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled()
      })

      expect(screen.getByTestId("location-display")).not.toHaveTextContent(
        "/login"
      )
    })
  })

  describe("Session Management", () => {
    it("should clear existing password session when switching users", async () => {
      const mockNavigate = vi.fn()
      vi.spyOn(reactRouter, "useNavigate").mockReturnValue(mockNavigate)

      server.use(
        batchedGraphQLQuery("/graphql", [
          getPasswordAuthMethodWithSession,
          removeSessionCookieMutationMock,
        ])
      )

      const user = userEvent.setup()

      render(
        <Suspense fallback={<></>}>
          <Login />
          <LocationDisplay />
        </Suspense>
      )

      const removeSessionButton = await screen.findByRole("button", {
        name: /log in as someone else/i,
      })
      await user.click(removeSessionButton)

      expect(removeSessionButton).toBeDisabled()

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith(0)
      })
    })

    it("should clear existing electronic ID session when switching users", async () => {
      const mockNavigate = vi.fn()
      vi.spyOn(reactRouter, "useNavigate").mockReturnValue(mockNavigate)

      server.use(
        batchedGraphQLQuery("/graphql", [
          getElectronicIdAuthMethodWithSession,
          removeSessionCookieMutationMock,
        ])
      )

      const user = userEvent.setup()

      render(
        <Suspense fallback={<></>}>
          <Login />
          <LocationDisplay />
        </Suspense>
      )

      await waitForElementToBeRemoved(
        () => screen.queryByTestId("login-loading"),
        { timeout: 2000 }
      )

      const removeSessionButton = await screen.findByRole("button", {
        name: /log in as someone else/i,
      })
      await user.click(removeSessionButton)

      expect(removeSessionButton).toBeDisabled()

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith(0)
      })
    })
  })
})
