import { test, expect } from "@playwright/test"

import { openSubjectJournalForSubject } from "../utils/subjectJournalTestUtils"
import {
  CheckUrl,
  modifierKeys,
  simulateKeyCombination,
} from "../utils/testUtils"

test.describe("Power Menu", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("http://localhost:3000/")
    await page.locator('[data-testid="subject-search-button"]').focus()
  })

  test("should open and close power-menu using shortcuts", async ({ page }) => {
    // Open power menu
    await simulateKeyCombination(page, "K", [modifierKeys[0]])

    // Check if power menu is open
    await expect(page.locator("[data-testid=power-menu]")).toBeVisible()

    // Close power menu
    await page.keyboard.press("Escape")

    // Wait for power menu to disappear
    await page.waitForSelector('[data-testid="power-menu"]', {
      state: "hidden",
    })

    // Check if power menu is closed
    await expect(page.locator('[data-testid="power-menu"]')).toBeHidden()
  })

  test("should open and close power-menu using mouse", async ({ page }) => {
    // Open power menu
    await page.locator('[data-testid="power-menu-button"]').click()

    // Wait for power menu to appear
    await page.waitForSelector("[data-testid=power-menu]")

    // Check if power menu is open
    await expect(page.locator("[data-testid=power-menu]")).toBeVisible()

    // Close power menu
    await page.keyboard.press("Escape")

    // Wait for power menu to disappear
    await page.waitForSelector('[data-testid="power-menu"]', {
      state: "hidden",
    })

    // Check if power menu is closed
    await expect(page.locator('[data-testid="power-menu"]')).toBeHidden()
  })

  test("should perform a successful search in the power-menu", async ({
    page,
  }) => {
    // Open power menu
    await page.locator('[data-testid="power-menu-button"]').click()

    // Wait for power menu to appear
    await page.waitForSelector("[data-testid=power-menu]")

    // Check if power menu is open
    await expect(page.locator("[data-testid=power-menu]")).toBeVisible()

    // Search for "teams"
    await page.fill('[data-testid="search-input"]', "teams")

    // Check if search results are visible
    await expect(
      page.locator('[data-testid="power-menu-item"]', { hasText: "Teams" })
    ).toBeVisible()

    // Close power menu
    await page.keyboard.press("Escape")

    // Wait for power menu to disappear
    await page.waitForSelector('[data-testid="power-menu"]', {
      state: "hidden",
    })

    // Check if power menu is closed
    await expect(page.locator('[data-testid="power-menu"]')).toBeHidden()
  })

  test("should perform a select of the searched item in the power-menu", async ({
    page,
  }) => {
    // Open power menu
    await page.locator('[data-testid="power-menu-button"]').click()

    // Wait for power menu to appear
    await page.waitForSelector("[data-testid=power-menu]")

    // Check if power menu is open
    await expect(page.locator("[data-testid=power-menu]")).toBeVisible()

    // Search for "worklist"
    await page.fill('[data-testid="search-input"]', "worklist")

    // Check if search results are visible
    await expect(
      page.locator('[data-testid="power-menu-item"]', { hasText: "Worklist" })
    ).toBeVisible()

    // Click on the search result
    await page.click('[data-testid="power-menu-item"] >> text="Worklist"')

    // Wait for the page to finish loading
    await page.waitForLoadState()

    // Check if the page is redirected to the worklist
    await CheckUrl(page, /\/worklist/)
  })

  // Open the provider worklist using the Ctrl+Alt+3 shortcut.
  test("should open the worklist using the Ctrl+Alt+3 shortcut", async ({
    page,
  }) => {
    // Arrange
    // Act
    // Open worklist with shortcut
    await simulateKeyCombination(page, "3", [modifierKeys[0], modifierKeys[1]])
    // Wait for the page to load and To do button to be visible
    await page.waitForSelector('button:has-text("To do")', { timeout: 10000 })

    // Assert
    // Check if the page is redirected to the worklist
    await CheckUrl(page, /worklist/)
  })

  // Open the dashboard using the Ctrl+Alt+2 shortcut.
  test("should open the dashboard using the Ctrl+Alt+2 shortcut", async ({
    page,
  }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "Rudy Gainsburg")

    // Act
    // Open Dashboard with shortcut
    await simulateKeyCombination(page, "2", [modifierKeys[0], modifierKeys[1]])
    await page.waitForLoadState()

    // Assert
    // Check if the page is redirected to the dashboard
    await CheckUrl(page, /team\/[a-f0-9-]+\/dashboard/)
  })

  // Open the subject journal using the Ctrl+Alt+1 shortcut.
  test("should open the subject journal using the Ctrl+Alt+1 shortcut", async ({
    page,
  }) => {
    // Arrange

    // Act
    // Open subject journal with shortcut
    await simulateKeyCombination(page, "1", [modifierKeys[0], modifierKeys[1]])
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(1000)

    // Assert
    // Check if the page is redirected to the subject journal
    await CheckUrl(page, /subject\/[a-f0-9-]+\/journal/)
  })
})
