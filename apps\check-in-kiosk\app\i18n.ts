import i18next from "i18next"
import { initReactI18next } from "react-i18next"

import GB_generic from "./locales/EN_GB/generic.json"
import IS_generic from "./locales/IS/generic.json"

export const i18nInstance = i18next.use(initReactI18next)
i18nInstance.init({
  lng: "IS",
  fallbackLng: ["EN_GB", "IS"],
  ns: ["generic"],
  fallbackNS: "generic",
  defaultNS: "generic",
  debug: false,
  resources: {
    EN_GB: {
      generic: GB_generic,
    },
    IS: {
      generic: IS_generic,
    },
  },
  interpolation: {
    escapeValue: true,
  },
})
