import { <PERSON><PERSON>, <PERSON> } from "@storybook/react-vite"

import { MenuButton } from "components/Ariakit"
import Icon from "components/Icon/Icon"

import { useComboboxStore, useMenuStore } from "../Ariakit/hooks"
import { FilterableMenu, FilterableMenuProps } from "./FilterableMenu"

export default {
  title: "Components/Filtrable Menu",
  component: FilterableMenu,
} as Meta

const menuOptions = [
  {
    label: "Option 1",
    value: "Option 1",
    onSelect: () => {
      console.log("Option 1 clicked")
    },
  },
  {
    label: "Option 2",
    value: "Option 2",
    onSelect: () => {
      console.log("Option 2 clicked")
    },
  },
  {
    label: "Option 3",
    value: "Option 3",
    onSelect: () => {
      console.log("Option 3 clicked")
    },
  },
]

export const FiltrableMenuDefault: Story<FilterableMenuProps> = (args) => {
  const combobox = useComboboxStore()
  const menuStore = useMenuStore(combobox)

  return (
    <FilterableMenu
      {...args}
      menuStore={menuStore}
      options={menuOptions}
      onSelect={(option: {
        label: string
        value: string
        onSelect?: () => void
      }) => {
        menuStore.setOpen(false)
        option.onSelect?.()
      }}
      label={"Click to open menu"}
    />
  )
}

export const FiltrableMenuCustomButton: Story<FilterableMenuProps> = (args) => {
  const combobox = useComboboxStore()
  const menuStore = useMenuStore(combobox)

  return (
    <FilterableMenu
      {...args}
      menuStore={menuStore}
      options={menuOptions}
      onSelect={(option: {
        label: string
        value: string
        onSelect?: () => void
      }) => {
        menuStore.setOpen(false)
        option.onSelect?.()
      }}
      menuButton={
        <MenuButton variant="outline" icon={<Icon name="add-line" />}>
          Add Option
        </MenuButton>
      }
      label={"Click to open menu"}
    />
  )
}

export const FiltrableMenuWithAlwaysVisibleOption: Story<
  FilterableMenuProps
> = (args) => {
  const combobox = useComboboxStore()
  const menuStore = useMenuStore(combobox)

  const alwaysVisibleMenuOption = {
    label: "+ Add new option",
    value: "add_new_option",
    onSelect: () => {
      console.log("Always Visible Option Clicked")
    },
  }

  return (
    <FilterableMenu
      {...args}
      menuStore={menuStore}
      options={menuOptions}
      onSelect={(option: {
        label: string
        value: string
        onSelect?: () => void
      }) => {
        menuStore.setOpen(false)
        option.onSelect?.()
      }}
      alwaysVisibleMenuOption={alwaysVisibleMenuOption}
      label={"Click to open menu"}
    />
  )
}
