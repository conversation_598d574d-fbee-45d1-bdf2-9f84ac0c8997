import { useBackgroundQuery, useReadQuery } from "@apollo/client"
import { PopoverStore } from "@ariakit/react"
import c from "classnames"
import { useRef } from "react"
import { useHotkeys } from "react-hotkeys-hook"
import { useTranslation } from "react-i18next"

import { Popover } from "components/Ariakit/Popover/Popover"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import { timeViewFormat } from "features/calendar/utils/timeViewFormat"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading, notification, Text } from "ui"

import {
  EventInstanceCreateInput,
  EventType,
  GetProvidersAndSubjectForAvailabilityQuery,
  GetProvidersAndSubjectForAvailabilityQueryVariables,
  namedOperations,
  ParticipantAttendanceRequest,
  ParticipantAttendeeSource,
  ParticipantEventRelationObject,
  ParticipantEventRelationSource,
  useCreateEventInstanceMutation,
  useSetEventParticipantsMutation,
} from "generated/graphql"

import { useCalendar } from "../Calendar/CalendarProvider"
import stylesEventInstance from "../EventInstance/EventInstance.module.css"
import { EventParticipantList } from "../EventInstance/EventParticipantList"
import {
  EventProviderParticipantListItem,
  EventSubjectParticipantListItem,
} from "../EventInstance/EventParticipantListItem"
import stylesViewEventInstanceHeader from "../EventInstance/ViewEvenInstanceHeader/ViewEventInstanceHeader.module.css"
import stylesViewEventInstance from "../EventInstance/ViewEventInstance.module.css"
import { ViewSection } from "../EventInstance/ViewSection/ViewSection"
import { ViewSectionLabel } from "../EventInstance/ViewSection/ViewSectionLabel"
import styles from "./EventWrapperAvailability.module.css"

type AvailabilitySlotQueryRef = NonNullable<
  ReturnType<
    typeof useBackgroundQuery<
      GetProvidersAndSubjectForAvailabilityQuery,
      GetProvidersAndSubjectForAvailabilityQueryVariables
    >
  >[0]
>

type CalendarAvailabilitySlotPopoverProps = {
  slotStartTime: Date
  slotEndTime: Date
  store: PopoverStore
  serviceTypeName: string
  serviceTypeId: string
  teamId?: string
  queryRef: AvailabilitySlotQueryRef
  providerId?: string
}

export default function CalendarAvailabilitySlotPopover({
  slotStartTime,
  slotEndTime,
  store,
  serviceTypeId,
  serviceTypeName,
  queryRef,
  teamId,
  providerId,
}: CalendarAvailabilitySlotPopoverProps) {
  const { t } = useTranslation()

  const navigateCalendar = useNavigateCalendar()
  const createButtonRef = useRef<HTMLButtonElement>(null)
  const editButtonRef = useRef<HTMLButtonElement>(null)

  const { globalData } = useGlobalState()

  const { allProviders } = useCalendar()

  const { data } = useReadQuery(queryRef)

  useHotkeys("e", () => editButtonRef.current?.click())

  const [createEventInstance, { loading: createEventInstanceLoading }] =
    useCreateEventInstanceMutation()

  const [setEventParticipant, { loading: setEventParticipantLoading }] =
    useSetEventParticipantsMutation({
      onCompleted: (data) => {
        if (data) {
          notification.create({
            message: t("Event created"),
            status: "success",
            maxWidth: "500px",
          })

          store.hide()
        }
      },
      // Remove when setEventParticipant return all event instances and cache is updated
      refetchQueries: [
        namedOperations.Query.EventInstances,
        namedOperations.Query.ProviderBox,
      ],
    })

  const formattedTime = timeViewFormat(
    new Date(slotStartTime),
    new Date(slotEndTime)
  )

  const lastInteractedSubject = globalData.actor?.selectedSubjects[0]?.subject

  const subjectParticipants = data?.subject
    ? [data.subject]
    : lastInteractedSubject
      ? [
          {
            ...lastInteractedSubject,
            isAvailable: true,
          },
        ]
      : []

  const providerParticipants =
    data?.providers ||
    allProviders
      .filter((provider) => providerId === provider.id)
      .map((provider) => ({
        ...provider,
        isAvailable: true,
      }))

  const isLoading = setEventParticipantLoading || createEventInstanceLoading

  const handleCreateEvent = async () => {
    const eventInstanceInput: EventInstanceCreateInput = {
      fromDate: slotStartTime,
      toDate: slotEndTime,
      title: serviceTypeName,
      serviceTypeId: serviceTypeId,
      eventType: EventType.Appointment,
      ownerId: globalData.actor.id,
      description: "",
      teamId: teamId,
    }

    const response = await createEventInstance({
      variables: {
        id: null,
        input: eventInstanceInput,
      },
    })

    const eventInstanceId = response.data?.createEventInstance?.id

    if (!eventInstanceId) return

    const eventRelationObject: ParticipantEventRelationObject = {
      objId: eventInstanceId,
      source: ParticipantEventRelationSource.EventInstance,
    }

    const subjectParticipantList = subjectParticipants.map((participant) => ({
      attendanceRequest: ParticipantAttendanceRequest.Optional,
      attendeeRelation: {
        objId: participant.id,
        source: ParticipantAttendeeSource.Provider,
      },
    }))

    const providerParticipantList = providerParticipants.map((participant) => ({
      attendanceRequest: ParticipantAttendanceRequest.Optional,
      attendeeRelation: {
        objId: participant.id,
        source: ParticipantAttendeeSource.Provider,
      },
    }))

    const participantList = [
      ...subjectParticipantList,
      ...providerParticipantList,
    ]

    await setEventParticipant({
      variables: {
        eventRelationObject,
        participantInputs: participantList,
      },
    })
  }

  return (
    <Popover
      store={store}
      className={stylesEventInstance.popover}
      portal={true}
      autoFocusOnHide={false}
      overlap
      unmountOnHide
      initialFocus={createButtonRef}
    >
      <header
        className={c(stylesViewEventInstanceHeader.header, {
          [stylesViewEventInstanceHeader.noArrivalIndicator]: true,
        })}
      >
        <Heading>{serviceTypeName}</Heading>

        <Button
          variant="clear"
          icon={<Icon name={"close-line"} />}
          onClick={store.hide}
          aria-label="Close"
        />
      </header>
      <div className={stylesEventInstance.content}>
        <ViewSection iconName="group-line">
          <div className={stylesViewEventInstance.participants}>
            <ViewSectionLabel>{t("Subjects")}</ViewSectionLabel>
            <EventParticipantList>
              {subjectParticipants.map(
                ({
                  id,
                  name,
                  isAvailable,
                  age,
                  gender,
                  personaId,
                  phoneNumber,
                }) => (
                  <EventSubjectParticipantListItem
                    key={id}
                    id={id}
                    name={name}
                    hasSchedulingConflict={!isAvailable}
                    subjectData={{ age, gender, personaId, phoneNumber }}
                  />
                )
              )}
              {subjectParticipants.length === 0 && (
                <Text className={stylesViewEventInstance.noInfo}>
                  {t("No subjects")}
                </Text>
              )}
            </EventParticipantList>
            <ViewSectionLabel>{t("Providers")}</ViewSectionLabel>
            <EventParticipantList>
              {providerParticipants.map((participant) => (
                <EventProviderParticipantListItem
                  key={participant.id}
                  name={participant.name}
                  hasSchedulingConflict={!participant.isAvailable}
                  type={participant.__typename}
                />
              ))}
              {providerParticipants.length === 0 && (
                <Text className={stylesViewEventInstance.noInfo}>
                  {t("No providers")}
                </Text>
              )}
            </EventParticipantList>
          </div>
        </ViewSection>
        <ViewSection iconName="time-line">{formattedTime}</ViewSection>
        <footer className={styles.availabilitySlotFooter}>
          <Tooltip tooltipContent="Or press 'e' to edit" timeout={1000}>
            <Button
              variant="clear"
              ref={editButtonRef}
              onClick={() => {
                navigateCalendar(RouteStrings.calendarCreateEventInstance, {
                  state: {
                    start: slotStartTime,
                    end: slotEndTime,
                    serviceTypeId,
                    teamId,
                  },
                })

                store.hide()
              }}
            >
              {t("Edit details")}
            </Button>
          </Tooltip>
          <Tooltip tooltipContent="Or press enter to confirm" timeout={1000}>
            <Button
              ref={createButtonRef}
              variant="filled"
              onClick={handleCreateEvent}
              disabled={isLoading}
            >
              {t("Create event")}
            </Button>
          </Tooltip>
        </footer>
      </div>
    </Popover>
  )
}
