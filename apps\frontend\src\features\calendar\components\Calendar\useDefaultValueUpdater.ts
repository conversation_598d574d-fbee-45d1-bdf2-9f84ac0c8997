import { SelectStore } from "@ariakit/react"
import { isEqual } from "lodash"
import { useState } from "react"

export const useDefaultValueUpdater = (
  defaultValue: string[],
  store: SelectStore
) => {
  const [prevSubjectValue, setPrevSubjectValue] = useState(defaultValue)

  if (!isEqual(prevSubjectValue, defaultValue)) {
    setPrevSubjectValue(defaultValue)
    store.setValue(defaultValue)
  }
}
