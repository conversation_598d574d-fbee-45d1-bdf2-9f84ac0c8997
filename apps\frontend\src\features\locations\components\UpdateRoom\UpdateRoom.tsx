import { useTranslation } from "react-i18next"

import { Modal, notification } from "ui"

import { useUpdateRoomMutation } from "generated/graphql"

import { Building } from "../BuildingOverview/BuildingOverview"
import { RoomForm } from "../forms/RoomForm/RoomForm"

type UpdateRoomProps = {
  room?: Building["rooms"]["rooms"][0]
  roomId: string | null
  onCancel: () => void
}

export const UpdateRoom = ({ room, roomId, onCancel }: UpdateRoomProps) => {
  const { t } = useTranslation()

  const [updateRoom, { loading: updateRoomLoading, error: updateRoomError }] =
    useUpdateRoomMutation({
      onCompleted: (data) => {
        if (data) {
          notification.create({
            message: t("Room has been updated"),
            status: "success",
            maxWidth: "500px",
          })
        }

        onCancel()
      },
    })

  if (roomId === null) return null

  return (
    <Modal onClose={() => onCancel()} title={t("Edit Room")} isOpen={true}>
      <RoomForm
        onSubmit={({ label, capacity }) => {
          updateRoom({
            variables: {
              input: {
                id: roomId,
                label: label,
                capacity: {
                  set: capacity,
                },
              },
            },
          })
        }}
        onCancel={() => onCancel()}
        loading={updateRoomLoading}
        formData={room}
        error={updateRoomError}
      />
    </Modal>
  )
}
