{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "jsx": "react-jsx", "types": ["@remix-run/node", "vite/client"], "rootDirs": ["."], "resolveJsonModule": true, "baseUrl": ".", "paths": {"app/*": ["app/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/.server/**/*.ts", "**/.server/**/*.tsx", "**/.client/**/*.ts", "**/.client/**/*.tsx"], "exclude": ["out-tsc", "dist", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx", "src/**/*.spec.js", "src/**/*.test.js", "src/**/*.spec.jsx", "src/**/*.test.jsx", "vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs", "**/*.stories.tsx"]}