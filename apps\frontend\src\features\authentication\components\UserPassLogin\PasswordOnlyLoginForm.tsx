import React from "react"
import { useTranslation } from "react-i18next"
import { <PERSON> } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { Input } from "ui"
import { Button } from "ui"

import { SessionData } from "generated/graphql"

import { RemoveSessionButton } from "../RemoveSessionButton/RemoveSessionButton"
import { WelcomeBackHeading } from "../WelcomeBackHeading/WelcomeBackHeading"
import styles from "./UserPassLogin.module.css"

type PasswordOnlyLoginFormProps = SessionData & {
  children: React.ReactNode
}

export default function PasswordOnlyLoginForm({
  children,
  email,
  name,
}: PasswordOnlyLoginFormProps) {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "auth" })

  return (
    <>
      <WelcomeBackHeading name={name} />

      <input type="hidden" name="email" value={email} autoComplete="username" />

      <Input
        id="login-password"
        label={tRoutes("password")}
        name="password"
        type="password"
        autoComplete="current-password"
        autoFocus
        secondaryLabel={
          <Button
            as={Link}
            to={RouteStrings.forgetPassword}
            className={styles.forgotPasswordButton}
            variant="clear"
            size="small"
          >
            {tRoutes("forgotPassword")}
          </Button>
        }
      />

      {children}

      <RemoveSessionButton />
    </>
  )
}
