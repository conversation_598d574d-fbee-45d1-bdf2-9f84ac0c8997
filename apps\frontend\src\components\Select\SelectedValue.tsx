import { ReactNode, useDeferredValue, useEffect } from "react"

import MenuListItem from "components/MenuListItem/MenuListItem"
import { Pill } from "ui/components/Pill/Pill"

import { Option } from "../Ariakit"
import { useSelectStore } from "../Ariakit/hooks"
import styles from "./Select.module.css"

type SelectedValueProps<T extends string> = {
  options: Option<T>[]
  placeholder: ReactNode
  selectStore: ReturnType<typeof useSelectStore>
  minValues?: number
  handleUnselectValue?: (optionValue: string) => void
}

export const SelectedValue = <T extends string>({
  placeholder = <>&nbsp;</>,
  options,
  selectStore,
  minValues,
  handleUnselectValue,
}: SelectedValueProps<T>) => {
  const { setValue, show } = selectStore

  const { value } = selectStore.useState()
  const deferredValue = useDeferredValue(value)
  const isMinValue = minValues && deferredValue.length === minValues

  const hasDefaultValueNotInOptions =
    options.length &&
    !Array.isArray(value) &&
    value &&
    !options.some((el) => el.value === value)

  useEffect(() => {
    if (hasDefaultValueNotInOptions) {
      selectStore.setValue("")
    }
  }, [hasDefaultValueNotInOptions, selectStore])

  if (!Array.isArray(deferredValue)) {
    const selectedOption = options.find((el) => el.value === value)
    if (value && selectedOption) {
      const subContent =
        !selectedOption.hideSubContentWhenSelected && selectedOption.subContent

      return (
        <MenuListItem
          subContent={subContent}
          direction={selectedOption.direction}
          className={styles.selectedValueItem}
        >
          {selectedOption.label}
        </MenuListItem>
      )
    }

    return <span className={styles.placeholder}>{placeholder}</span>
  }

  if (Array.isArray(deferredValue) && !deferredValue.length) {
    return <span className={styles.placeholder}>{placeholder}</span>
  }

  const onRemove = (optionValue: string) => {
    const filteredValues = deferredValue.filter(
      (value) => value !== optionValue
    )
    setValue(filteredValues)

    // hack stopPropagation doesn't stops from opening and closing Options Menu
    // keep opening menu while deleting options
    show()
    handleUnselectValue?.(optionValue)
  }

  return (
    <div className={styles.selectedValues}>
      {deferredValue.map((selectedValue) => {
        return (
          <Pill
            key={selectedValue}
            onRemove={!isMinValue ? () => onRemove(selectedValue) : undefined}
          >
            {options.find((el) => el.value === selectedValue)?.label}
          </Pill>
        )
      })}
    </div>
  )
}
