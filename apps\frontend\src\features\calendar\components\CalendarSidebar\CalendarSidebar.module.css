.aside {
  display: grid;
  grid-template-rows: min-content auto 1fr auto;
  gap: var(--grid-gap);
  background-color: var(--color-lev-blue-200);
  margin: -36px 0 0 -36px;
  padding: 36px;
  max-height: calc(100vh - var(--header-height));
}

.scrollable {
  min-height: 0;
  overflow: auto;
}

.dayPicker {
  justify-self: stretch;
  align-self: end;
  grid-row: 4;
  flex-shrink: 0;

  --color-background-hover: var(--color-lev-blue-300);
  --color-background-active: var(--color-lev-blue-400);
  --color-selected-background: var(--color-main);
  --color-selected-text: var(--color-white);
  --color-selected-background-hover: var(--color-700);
  --color-selected-background-active: var(--color-800);
  --color-current-background: var(--color-lev-blue-300);
  --color-current-text: var(--color-lev-blue-900);
  --color-current-background-hover: var(--color-lev-blue-400);
  --color-current-background-active: var(--color-lev-blue-500);
}

.showCancelledEventsToggle {
  display: flex;
  align-self: start;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}
