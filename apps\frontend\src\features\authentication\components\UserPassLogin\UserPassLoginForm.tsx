import { useTranslation } from "react-i18next"
import { <PERSON> } from "react-router-dom"

import useFocusOnMount from "hooks/useFocusOnMount"
import { RouteStrings } from "routes/RouteStrings"
import { Input } from "ui"
import isDev from "utils/isDev"

import styles from "../../Authentication.module.css"

const emails = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
]
const random = Math.floor(Math.random() * emails.length)
const defaultEmail = isDev ? emails[random] : undefined
const defaultPass = isDev ? "somepassword" : undefined

type UserPassLoginProps = {
  children: React.ReactNode
}
export default function UserPassLoginForm({ children }: UserPassLoginProps) {
  const { t } = useTranslation()
  const firstInputRef = useFocusOnMount<HTMLInputElement>()

  return (
    <>
      <Input
        id="login-email"
        label={t("routes:auth.email")}
        ref={firstInputRef}
        name="email"
        type="email"
        defaultValue={defaultEmail}
        autoComplete="username"
      />

      <Input
        id="login-password"
        label={t("routes:auth.password")}
        name="password"
        type="password"
        defaultValue={defaultPass}
        autoComplete="current-password"
      />

      {children}

      <Link to={RouteStrings.forgetPassword} className={styles.link}>
        {t("routes:auth.forgotPassword")}
      </Link>
    </>
  )
}
