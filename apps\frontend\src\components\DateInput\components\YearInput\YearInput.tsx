import { ChangeEvent, forwardRef } from "react"
import { useTranslation } from "react-i18next"

import styles from "../../DateInput.module.css"

type YearInputProps = {
  onChange: (value: string) => void
  value?: string
  disabled?: boolean
  required?: boolean
  readOnly?: boolean
}

export const YearInput = forwardRef<HTMLInputElement, YearInputProps>(
  ({ value, onChange, disabled, required, readOnly }, ref) => {
    const { t } = useTranslation()

    return (
      <input
        aria-label="Year"
        ref={ref}
        value={value}
        placeholder={t("YYYY")}
        type="tel"
        className={`${styles.inputs} ${styles.year}`}
        onChange={(event: ChangeEvent<HTMLInputElement>) => {
          const { value } = event.target
          onChange(value)
        }}
        disabled={disabled}
        required={required}
        readOnly={readOnly}
      />
    )
  }
)
