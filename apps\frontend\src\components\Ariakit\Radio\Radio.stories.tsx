import { Meta } from "@storybook/react-vite"

import { Radio } from "../Radio/Radio"
import { useRadioStore } from "../hooks/useRadioStore/useRadioStore"
import { RadioGroup } from "./RadioGroup/RadioGroup"

export default {
  title: "Ariakit/Radio",
  component: Radio,
} as Meta

export const RadioExample = (args) => {
  const radioStore = useRadioStore()

  return (
    <RadioGroup store={radioStore} {...args}>
      <Radio label="Apple" value="apple" />
      <Radio label="Orange" value="orange" />
      <Radio label="Watermelon" value="watermelon" />
    </RadioGroup>
  )
}
