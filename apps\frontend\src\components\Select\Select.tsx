import c from "classnames"
import { ReactNode, useEffect, useId } from "react"

import Icon from "components/Icon/Icon"
import { Button, Label, Text } from "ui"
import { LabelWithInlineGuide } from "ui/components/LabelWithInlineGuide/LabelWithInlineGuide"

import {
  Option,
  Select as SelectAriaKit,
  SelectLabel,
  SelectPopover,
} from "../Ariakit"
import { useSelectStore } from "../Ariakit/hooks"
import { Options } from "./Options"
import styles from "./Select.module.css"
import { SelectIcon } from "./SelectIcon"
import { SelectedValue } from "./SelectedValue"

type Status = "default" | "error" | "warning" | "success"
export type Size = "default" | "large" | "small"

export type SelectProps<T extends string | string[]> = {
  label: string
  status?: Status
  message?: ReactNode
  size?: Size
  disabled?: boolean
  readOnly?: boolean
  required?: boolean
  isLoading?: boolean
  hideLabel?: boolean
  options: Option<T extends string ? T : T[number]>[]
  selectStore: ReturnType<typeof useSelectStore<T>>
  placeholder?: string
  noOptionsPlaceholder?: string
  icon?: ReactNode
  name?: string
  variant?: "default" | "clear"
  minValues?: number
  onSelectChange?: (value: T | null) => void
  className?: string
  selectClassName?: string
  iconClassName?: string
  isClearable?: boolean
  portal?: boolean
  hideMessage?: boolean
  inlineGuide?: string
  sameWidth?: boolean
}

export const Select = <T extends string | string[] = string>({
  message,
  status = "default",
  size = "default",
  label,
  isLoading = false,
  disabled = false,
  hideLabel = false,
  readOnly = false,
  className = "",
  icon,
  options = [],
  placeholder = "Select Option",
  selectStore,
  noOptionsPlaceholder = "No Options Found",
  name,
  variant = "default",
  selectClassName = "",
  onSelectChange,
  required = false,
  minValues,
  isClearable = false,
  portal = false,
  hideMessage = false,
  inlineGuide,
  sameWidth,
  ...rest
}: SelectProps<T> & JSX.IntrinsicElements["div"]) => {
  const id = useId()

  const { value: selectValue } = selectStore.useState()

  const items = selectStore.useState("items")

  const isOpen = selectStore.useState("open")

  const isMulti = Array.isArray(selectValue)

  const hasValue = isMulti ? selectValue.length > 0 : !!selectValue

  const hasClear = isClearable && hasValue

  // Set the active id to the first option if the select value is empty
  useEffect(() => {
    if (!selectValue && isOpen) {
      selectStore.setActiveId(items[1]?.id)
    }
  }, [selectValue, items, isOpen])

  const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    const key = event.key.toLowerCase()

    const matchingOption = options.find((option) =>
      option.label.toLowerCase().startsWith(key)
    )

    if (matchingOption) {
      Array.isArray(selectValue)
        ? onSelectChange?.([...selectValue, matchingOption.value] as T)
        : onSelectChange?.(matchingOption.value as T)

      selectStore.setValue(matchingOption.value)
      selectStore.setOpen(false)
    }
  }

  return (
    <div
      className={`${styles.wrap} ${className}`}
      data-size={size}
      data-disabled={disabled}
      data-read-only={readOnly}
      data-status={status}
      data-is-open={isOpen}
      data-no-options={!options.length}
      data-variant={variant}
      data-is-selected={!!selectValue}
      data-is-multi={Array.isArray(selectValue)}
      data-has-clear-icon={hasClear}
      {...rest}
    >
      <LabelWithInlineGuide inlineGuide={inlineGuide}>
        <Label
          as={SelectLabel}
          className={c(styles.label, { labelIsRequired: required })}
          store={selectStore}
          visuallyHidden={hideLabel}
        >
          {label}
        </Label>
      </LabelWithInlineGuide>

      <div className={styles.selectWrapper}>
        <SelectAriaKit
          name={name}
          store={selectStore}
          className={`${styles.select} ${selectClassName}`}
          required={required}
          aria-describedby={message ? id : undefined}
          disabled={disabled}
          onKeyDown={handleKeyDown}
        >
          <SelectedValue
            placeholder={placeholder}
            options={options}
            selectStore={selectStore}
            minValues={minValues}
            handleUnselectValue={(optionValue) => {
              // handleUnselectValue is only called when the value is an array
              if (typeof selectValue === "string") return

              const options = selectValue.filter(
                (value) => value !== optionValue
              )
              onSelectChange?.(options as T)
            }}
          />
          <span className={styles.icon}>
            <SelectIcon isLoading={isLoading} icon={icon} />
          </span>
        </SelectAriaKit>
        {hasClear && (
          <Button
            icon={<Icon name="close-line" />}
            className={styles.clearButton}
            onClick={() => {
              selectStore.setOpen(false)
              if (isMulti) {
                selectStore.setValue([])
                onSelectChange?.([] as unknown as T)
                return
              }

              onSelectChange?.(null)
              selectStore.setValue("")
            }}
          />
        )}
      </div>

      <SelectPopover store={selectStore} portal={portal} sameWidth={sameWidth}>
        <Options
          isLoading={isLoading}
          value={selectValue}
          options={options}
          noOptionsPlaceholder={noOptionsPlaceholder}
          isClearable={isClearable}
          onClick={(value) => {
            if (Array.isArray(selectValue)) {
              onSelectChange?.([...selectValue, value] as T)

              return
            }

            onSelectChange?.(value as T)
          }}
        />
      </SelectPopover>

      <Text
        size="small"
        id={id}
        className={styles.message}
        visuallyHidden={hideMessage}
      >
        {message ?? <>&nbsp;</>}
      </Text>
    </div>
  )
}

export default Select
