import { ApolloError } from "@apollo/client"
import _ from "lodash"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import useCountries from "hooks/useCountries"
import { Button, FormGrid, Heading, Input } from "ui"

import { Building } from "../../BuildingOverview/BuildingOverview"
import styles from "../LocationForm.module.css"

const baseSchema = z.object({
  label: z.string(),
  addressLine1: z.string(),
  addressLine2: z.string().optional(),
  city: z.string(),
  postalCode: z.string(),
  region: z.string().optional(),
  country: z.string(),
})

type LocationBuildingFormProps = {
  onSubmit: (data: z.infer<typeof baseSchema>) => void
  onCancel: () => void
  error?: ApolloError
  loading: boolean
  formData: Partial<Building>
}

export const LocationBuildingForm = ({
  onSubmit,
  onCancel,
  loading,
  error,
  formData,
}: LocationBuildingFormProps) => {
  const { t } = useTranslation()
  const { countries } = useCountries()
  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const selectCountryStore = useSelectStore({
    defaultValue: formData.address?.country || "IS",
  })

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setValidationError(null)

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = baseSchema.safeParse(data)

    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    onSubmit?.(validatedInput.data)
  }

  return (
    <Panel as="form" onSubmit={handleSubmit} className={styles.panelForm}>
      <Heading as="h3" size="large">
        {_.isEmpty(formData) ? t("Add Building") : t("Edit Building")}
      </Heading>

      <FormGrid as="div" colSpan={4}>
        <Input
          defaultValue={formData.label}
          label="Label"
          name="label"
          type="text"
          required={true}
        />
        <Input
          defaultValue={formData.address?.addressLine1}
          label={t("addressOne")}
          name="addressLine1"
          type="text"
          required
        />
        <Input
          defaultValue={formData.address?.addressLine2 || ""}
          label={t("addressTwo")}
          name="addressLine2"
          type="text"
          placeholder="Optional"
        />
        <Input
          defaultValue={formData.address?.city}
          label={t("city")}
          name="city"
          type="text"
          required
          className={styles.span2}
        />
        <Input
          defaultValue={formData.address?.postalCode}
          label={t("postalCode")}
          name="postalCode"
          type="text"
          required
          className={styles.span2}
        />
        <Input
          defaultValue={formData.address?.region || ""}
          label={t("region")}
          name="region"
          type="text"
          placeholder="Optional"
          className={styles.span2}
        />
        <Select
          label={t("country")}
          name="country"
          required
          selectStore={selectCountryStore}
          options={countries.map((c) => c)}
          isClearable
          className={styles.span2}
        />
      </FormGrid>
      {(error || validationError) && (
        <Panel status="error">
          {validationError?.message}
          {error?.message}
        </Panel>
      )}

      <div className={styles.footer}>
        <Button disabled={loading} onClick={onCancel}>
          {t("Cancel")}
        </Button>
        <Button type="submit" variant="filled" disabled={loading}>
          {_.isEmpty(formData) ? t("Save") : t("Update")}
        </Button>
      </div>
    </Panel>
  )
}
