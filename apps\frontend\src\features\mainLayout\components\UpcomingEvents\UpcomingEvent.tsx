import c from "classnames"
import { <PERSON> } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import ArrivalIndicator from "features/calendar/components/ArrivalIndicator/ArrivalIndicator"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"
import { calendarColorMap, color } from "styles/colors"
import { Text } from "ui"
import { getEventTitle } from "utils/getEventTitle"
import { isTypename } from "utils/isTypename"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  CalendarColor,
  ParticipantAttendanceState,
  ProviderBoxQuery,
} from "generated/graphql"

import { Duration } from "./Duration"
import styles from "./UpcomingEvents.module.css"

type Event = ProviderBoxQuery["events"][number]

type ProviderEventBadgeProps = {
  event: Event
}

const getCheckedInTime = (participants: Event["participants"]) => {
  const subjectParticipants = participants.filter(
    isTypename("ParticipantSubject")
  )

  // Get the checked in times for all participants (who are checked in)
  const checkedInTimes = subjectParticipants.map((participant) =>
    participant.attendanceState?.state === ParticipantAttendanceState.True
      ? participant.attendanceState?.date
      : null
  )

  // Get the time when first participant checked in
  const firstCheckedInTime = checkedInTimes.sort((a, b) =>
    a && b ? new Date(a).getTime() - new Date(b).getTime() : 0
  )?.[0]

  return firstCheckedInTime ? firstCheckedInTime : null
}

export default function UpcomingEvent({ event }: ProviderEventBadgeProps) {
  const formatTime = useTimeFormatter()
  const { globalData } = useGlobalState()
  const { actor } = globalData
  if (!event) return null

  const { id, title, fromDate: _fromDate, serviceType } = event

  const subjectParticipants = event.participants.filter(
    isTypename("ParticipantSubject")
  )

  let eventLabel = getEventTitle(title, serviceType?.name || "")
  if (subjectParticipants.length > 0) {
    eventLabel = subjectParticipants[0].subject.name

    if (subjectParticipants.length > 1) {
      eventLabel += ` +${subjectParticipants.length - 1}`
    }
  }

  const checkedInDate = getCheckedInTime(subjectParticipants)

  const fromDate = new Date(_fromDate)

  const getCalendarPath = useGetCalendarPathObject()

  const eventColor = serviceType
    ? calendarColorMap[serviceType.color]
    : calendarColorMap[CalendarColor.LevBlue]

  return (
    <li className={c(styles.eventWrapper, eventColor, color.light)}>
      <Text
        size="small"
        as={Link}
        className={styles.eventContent}
        to={getCalendarPath(RouteStrings.calendarViewEventInstance, {
          eventId: id,
          search: { provider: actor.id },
        })}
      >
        <time dateTime={fromDate.toISOString()}>
          {formatTime(new Date(fromDate))}
        </time>
        <PiiSensitive className={styles.eventLabel} title={eventLabel}>
          {eventLabel}
        </PiiSensitive>
      </Text>

      <ArrivalIndicator subjectParticipants={subjectParticipants} />
      {checkedInDate && (
        <Duration date={checkedInDate} className={styles.duration} />
      )}
    </li>
  )
}
