import { test, expect } from "@playwright/test"

// Annotate file as serial so that journal templates that are created don't interfere with different test cases
test.describe.configure({ mode: "serial" })

test.skip("Journal template tests are disabled due to maintenance", async ({
  page,
}) => {
  test.describe("ManageJournalTemplates", () => {
    test.beforeEach(async ({ page }) => {
      await page.goto("/")
      // Open power menu
      await page.click('[data-testid="power-menu-button"]')
      // Wait for power menu to appear
      await page.waitForSelector("[data-testid=power-menu]")
      // Check if power menu is open
      await expect(page.locator("[data-testid=power-menu]")).toBeVisible()
      // Search for "Journal Templates"
      await page.fill('[data-testid="search-input"]', "journal templates")
      // Check if search results are visible
      await expect(
        page.locator('[data-testid="power-menu-item"]', {
          hasText: "Journal Templates",
        })
      ).toBeVisible()
      // Click on the search result
      await page.click(
        '[data-testid="power-menu-item"] >> text="Journal Templates"'
      )
      // Wait for the page to finish loading
      await page.waitForLoadState()
    })

    test("Should load existing journal templates", async ({ page }) => {
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      const templateCount = await page
        .locator('[data-testid="journal-template-card"]')
        .count()
      expect(templateCount).toBeGreaterThan(0)
    })

    test("Create new journal template in Draft mode", async ({ page }) => {
      // Arrange

      // Act
      await page.getByRole("button", { name: "New template" }).click()
      await page.getByLabel("Name").fill("The new template name")
      await page.getByLabel("Description").fill("Description of new template")
      await page.getByLabel("Reference").fill("101")
      await page.getByTestId("intervention-period-submit").click()
      // Select the new template - for some reason this is needed when running Chromium browser
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      await page.waitForLoadState()
      await page
        .locator("div")
        .filter({ hasText: "The new template name" })
        .first()
        .click({ force: true })
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      await page.waitForLoadState()
      // Assert
      const title = await page.getByTestId("journal-template-title").innerText()
      expect(title).toBe("The new template name")

      const description = await page
        .getByTestId("template-description")
        .innerText()
      expect(description).toBe("Description of new template")

      const reference = await page.getByTestId("template-reference").innerText()
      expect(reference).toBe("101")
    })

    test("Publish an existing journal template", async ({ page }) => {
      // Arrange
      await page.getByRole("button", { name: "New template" }).click()
      await page.getByLabel("Name").fill("Template to publish")
      await page
        .getByLabel("Description")
        .fill("Description of template that should be published")
      await page.getByLabel("Reference").fill("102")
      await page.getByTestId("intervention-period-submit").click()
      // Select the new template - for some reason this is needed when running Chromium browser
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      await page.waitForLoadState()
      await page
        .locator("div")
        .filter({ hasText: "Template to publish" })
        .first()
        .click({ force: true })
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      await page.waitForLoadState()
      // Act
      await page.getByRole("button", { name: "Publish" }).click()
      await page.waitForTimeout(2000)
      await page.waitForLoadState()
      // Assert
      const status = await page
        .locator('[class*="JournalTemplate_status"]')
        .innerText()
      expect(status).toBe("Published")
    })

    test("Delete a journal template", async ({ page }) => {
      // Arrange
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      let templateCountBefore = await page
        .locator('[data-testid="journal-template-card"]')
        .count()

      if (templateCountBefore === 0) {
        // Create a new template
        await page.getByRole("button", { name: "New template" }).click()
        await page.getByLabel("Name").fill("Template to delete")
        await page
          .getByLabel("Description")
          .fill("Description of template that should be deleted")
        await page.getByTestId("intervention-period-submit").click()
        // Select the new template - for some reason this is needed when running Chromium browser
        // eslint-disable-next-line playwright/no-wait-for-timeout
        await page.waitForTimeout(2000)
        await page.waitForLoadState()
        templateCountBefore = await page
          .locator('[data-testid="journal-template-card"]')
          .count()
      }
      // Act
      await page
        .locator('[class*="JournalTemplateCard_menuButton"]')
        .first()
        .click()
      await page
        .locator('[class*="JournalTemplateCard_menuItemDelete"]')
        .first()
        .click()

      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      await page.waitForLoadState()
      // Assert
      const templateCountAfter = await page
        .locator('[data-testid="journal-template-card"]')
        .count()

      expect(templateCountAfter).toBe(templateCountBefore - 1)
    })
  })
})
