import { useTranslation } from "react-i18next"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  Menu,
  MenuButton,
  MenuButtonProps,
} from "../Ariakit"
import { useComboboxStore, useMenuStore } from "../Ariakit/hooks"
import { Option } from "../Ariakit/types/Option"

export type FiltrableButtonMenuProps<T extends string> = {
  onSelect?: (value: T) => void
  menuStore: ReturnType<typeof useMenuStore>
  comboboxStore: ReturnType<typeof useComboboxStore>
  options: Option<T>[]
  renderItem?: (value: T) => React.ReactNode
  placeholder?: string
} & Omit<MenuButtonProps, "store" | "ref" | "onSelect">

export const FiltrableButtonMenu = <T extends string>({
  onSelect,
  menuStore,
  comboboxStore,
  renderItem,
  options,
  placeholder,
  icon,
  ...rest
}: FiltrableButtonMenuProps<T>) => {
  const { t } = useTranslation()

  return (
    <>
      <MenuButton store={menuStore} icon={icon} {...rest} />
      <Menu store={menuStore} composite={false}>
        <Combobox
          store={comboboxStore}
          autoSelect
          placeholder={placeholder ?? t("Search ...")}
        />

        <ComboboxList store={comboboxStore}>
          {options.length > 0 ? (
            options.map(({ value, label }) =>
              renderItem ? (
                renderItem(value)
              ) : (
                <ComboboxItem
                  key={value}
                  value={value}
                  focusOnHover
                  onClick={() => {
                    onSelect?.(value)
                    menuStore.setOpen(false)
                  }}
                  setValueOnClick={false}
                >
                  {label}
                </ComboboxItem>
              )
            )
          ) : (
            <ComboboxItem>{t("No results Found")}</ComboboxItem>
          )}
        </ComboboxList>
      </Menu>
    </>
  )
}
