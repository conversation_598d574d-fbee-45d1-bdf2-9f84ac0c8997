import { EventWrapperProps } from "react-big-calendar"

import type { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"

import { EventWrapperAvailability } from "../EventWrapperAvalibility/EventWrapperAvailability"
import { EventWrapperEventInstance } from "../EventWrapperEventInstance/EventWrapperEventInstance"
import { EventWrapperHoliday } from "../EventWrapperHoliday/EventWrapperHoliday"

export const CalendarEventWrapper = ({
  event,
  children,
  ...rest
}: EventWrapperProps<CalendarEvent> & {
  children?: React.ReactNode
}) => {
  if (event.resource.type === "eventInstance") {
    return (
      <EventWrapperEventInstance event={event}>
        {children}
      </EventWrapperEventInstance>
    )
  }

  if (event.resource.type === "availability")
    return (
      <EventWrapperAvailability
        event={event}
        allowCreateEvent
        eventInfo={{ event, ...rest }}
      />
    )

  if (event.resource.type === "holiday") {
    return <EventWrapperHoliday event={event} />
  }
  return <>{children}</>
}
