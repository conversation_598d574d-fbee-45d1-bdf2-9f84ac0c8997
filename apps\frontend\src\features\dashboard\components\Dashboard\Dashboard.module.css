.accessPanel {
  grid-column: 1 / -1;
  margin-bottom: var(--grid-gap);
}

.filters {
  grid-column: 1 / -1;
  display: flex;
  gap: var(--grid-gap);
  margin-bottom: var(--grid-gap);
}

.dashboard {
  border-collapse: collapse;
  border-spacing: 0;
  grid-column: 1 / -1;
  /* Dashboard wants as much horizontal space as possible */
}

.dashboard th button {
  padding-left: 0;
}

.dashboard th :is(button, :hover) {
  background-color: transparent;
  color: var(--color-white);
}

.dashboard td,
.dashboard th {
  padding: 8px 8px;
  vertical-align: center;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dashboard th {
  padding: 17px 8px;
}

.dashboard td:first-child,
.dashboard th:first-child {
  padding-left: 16px;
}
.dashboard td:last-child,
.dashboard th:last-child {
  padding-right: 16px;
}

.dashboard tr th button span[data-icon="default"] {
  opacity: 0;
  transition: opacity 0.15s ease-in-out;
}

.dashboard tr th button:hover span[data-icon="default"] {
  opacity: 1;
}

.dashboard tbody tr {
  cursor: pointer;
}

.rowButton {
  width: 100%;
  padding: 0;
}

.rowButton[data-icon-only="true"] {
  display: flex;
  justify-content: flex-end;
}

.priority.priority {
  padding-left: 2px;
}

.dashboard td {
  /* the general rule is that content does not break line, then we apply exceptions for certain tds */
  white-space: nowrap;
}

.dashboard thead th {
  /* sort arrow (icons) must not break from label */
  white-space: nowrap;
  display: table-cell;
}

.dashboard thead th span {
  line-height: 1.2;
}

.arrivalPre {
  width: 65px;
}

/* Note column must be wider */
.note {
  width: 400px;
}

.gender {
  padding-right: 0;
}

.statusFilter {
  width: 180px;
}

.loading tbody {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(210, 210, 210, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.4;
  }
}

.subjectSummaryModal {
  padding: 0;
  border-width: 0;
  border-radius: 8px;

  > div {
    padding: 0;
  }
}

.popover {
  z-index: calc(1 + var(--z-index-header));
}

.statistics {
  margin-left: auto;
}
