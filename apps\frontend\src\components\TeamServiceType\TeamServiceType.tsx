import { useTranslation } from "react-i18next"

import { Tag } from "ui/components/Tag/Tag"

import { ServiceType } from "generated/graphql"

type Props = {
  serviceType: ServiceType | null
}

export const TeamServiceType = ({ serviceType }: Props) => {
  const serviceTypeValue = Object.values(ServiceType).find(
    (id) => id === serviceType
  )
  const { t } = useTranslation()

  return (
    <Tag>
      {t(
        serviceTypeValue !== undefined
          ? `enums:Team_ServiceType.${serviceTypeValue}`
          : `routes:manageTeam.serviceTypeUnset`
      )}
    </Tag>
  )
}
