import { useTranslation } from "react-i18next"

import { IconName } from "@leviosa/assets"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"

import { EventInstancesQuery, PaymentStatus } from "generated/graphql"

import styles from "../Event/Event.module.css"
import { getInvoiceWithLowestStatus } from "./getInvoiceWithLowestStatus"

export type Invoices = NonNullable<
  EventInstancesQuery["eventInstances"][0]["encounter"]
>["invoices"]

type EventInvoiceIndicatorProps = {
  invoices?: Invoices
}

export const EventInvoiceIndicator = ({
  invoices,
}: EventInvoiceIndicatorProps) => {
  const { t } = useTranslation()
  if (!invoices || invoices.length === 0) return null

  const tooltipValues = {
    draft: t("Draft invoice"),
    [PaymentStatus.Unpaid]: t("Unpaid invoice"),
    [PaymentStatus.Paid]: t("Invoice paid"),
    [PaymentStatus.ClaimCreated]: t("Claim created"),
  }

  const paymentIconNames: {
    [key in PaymentStatus | "draft"]: IconName
  } = {
    draft: "money-dollar-circle-line",
    [PaymentStatus.Unpaid]: "money-dollar-circle-line",
    [PaymentStatus.Paid]: "money-dollar-circle-fill",
    [PaymentStatus.ClaimCreated]: "money-dollar-circle-fill",
  }
  const invoiceStatus = getInvoiceWithLowestStatus(invoices)

  if (!invoiceStatus) return null

  return (
    <Tooltip
      placement="top-start"
      tooltipContent={
        <div style={{ maxWidth: 250 }}>{tooltipValues[invoiceStatus]}</div>
      }
    >
      <Icon
        name={paymentIconNames[invoiceStatus]}
        className={styles.invoiceStatusIcon}
      />
    </Tooltip>
  )
}
