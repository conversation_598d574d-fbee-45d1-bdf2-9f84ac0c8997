import { screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { render } from "test/testUtils"

import { PersonaIdInput } from "./PersonaIdInput"

describe("PersonaIdInput", () => {
  it("should display success status and success icon when valid persona ID is entered and blurred", async () => {
    const user = userEvent.setup()
    // id is needed here, since R<PERSON> fails this test otherwise, because of
    // SyntaxError: 'input#:r3:._input_44bed1 button,a' is not a valid selector
    render(<PersonaIdInput label="personaId" id="personaId" />)
    const input = screen.getByLabelText("personaId")

    await user.type(input, "111125-2879")

    expect(input).toHaveValue("111125-2879")
    await userEvent.click(document.body) // blur

    // should be re-written since it doesn't follow react-testing-library best practices i.e. eslint-plugin-testing-library complains
    // because of direct Node access
    // eslint-disable-next-line testing-library/no-node-access
    expect(input.closest("div")).toHaveAttribute("data-status", "success")
  })

  it("should display error status and validation message when invalid persona ID is entered and blurred", async () => {
    const user = userEvent.setup()
    render(<PersonaIdInput label="personaId" id="personaId" />)
    const input = screen.getByLabelText("personaId")

    await user.type(input, "123456-7890")
    await userEvent.click(document.body)

    // eslint-disable-next-line testing-library/no-node-access
    expect(input.closest("div")).toHaveAttribute("data-status", "error")
    expect(input).toBeInvalid()
    expect(screen.getByText(/Persona Id is invalid/)).toBeInTheDocument()
  })
})
