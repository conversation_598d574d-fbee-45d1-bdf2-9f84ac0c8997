use crate::{
    lib::{auth::AuthData, context::ContextData, errors::Error as GQLErro<PERSON>},
    resolvers::{Mutation, Query},
};
use async_graphql::{
    BatchRequest, BatchResponse, EmptySubscription, Error, ObjectType, Pos, Response, Schema,
    ServerError, SubscriptionType,
};
use async_graphql_axum::{GraphQLBatchRequest, GraphQLResponse};
use axum::{
    extract::{ConnectInfo, Extension},
    http::{HeaderMap, Uri},
};
use futures;
use leviosa_domain::{
    accounts::{AppAuthenticator, AuthRepo},
    audit_logger::AuditLogger,
    auth::JwtEncoder,
    calendar_notification_service::CalendarNotificationService,
    doctor_letter_and_referral_api::DoctorLetterAndReferralApi,
    external_organisation_integration::ExternalOrganisationIntegration,
    file_repo::IFileRepo,
    national_registry_integration::DomainNationalRegistry,
    notification_service_integration::DomainNotificationService,
    oracle_api::OracleApi,
    prescription_api::PrescriptionApi,
};
use leviosa_domain_contracts::{
    auth::{ElectronicId, PasswordHasher},
    services::{
        CustomerService, EmailSender, NhiService, OnlinePaymentService, PdfGenerator,
    },
};
use leviosa_infrastructure::observability::{ObservabilityExtension, with_authentication_span};
use sea_orm::DatabaseConnection;
use std::net::{IpAddr, SocketAddr};
use std::sync::Arc;

pub struct ExecutionResult {
    pub response: BatchResponse,
    pub context: ContextData,
}

pub struct App<TQuery, TMutation, TSubscription>
where
    TQuery: ObjectType + Sized + 'static,
    TMutation: ObjectType + Sized + 'static,
    TSubscription: SubscriptionType + Sized + 'static,
{
    schema: Schema<TQuery, TMutation, TSubscription>,
    context_base: ContextBase,
}

impl<TQuery, TMutation, TSubscription> Clone for App<TQuery, TMutation, TSubscription>
where
    TQuery: ObjectType + Sized + 'static,
    TMutation: ObjectType + Sized + 'static,
    TSubscription: SubscriptionType + Sized + 'static,
{
    fn clone(&self) -> Self {
        Self {
            schema: self.schema.clone(),
            context_base: self.context_base.clone(),
        }
    }
}

#[derive(Clone)]
pub struct ContextBase {
    pub sea_database: DatabaseConnection,
    pub email_sender: Arc<dyn EmailSender>,
    pub audit_logger: Arc<dyn AuditLogger>,
    pub admin_key: String,
    pub refresh_token_expiration_time: u32,
    pub token_lifetime_seconds: u32,
    pub jwt: Arc<dyn JwtEncoder>,
    pub password_hasher: Arc<dyn PasswordHasher>,
    pub electronic_id: Arc<dyn ElectronicId>,
    pub customer_service: Arc<dyn CustomerService>,
    pub oracle_api: Arc<dyn OracleApi>,
    pub prescription_api: Arc<dyn PrescriptionApi>,
    pub pdf_generator: Arc<dyn PdfGenerator>,
    pub nhi_service: Arc<dyn NhiService>,
    pub notification_service_integration: Arc<dyn DomainNotificationService>,
    pub calendar_notification_service_integration: Arc<dyn CalendarNotificationService>,
    pub file_repo: Arc<dyn IFileRepo>,
    pub national_registry: Arc<dyn DomainNationalRegistry>,
    pub online_payment_service: Arc<dyn OnlinePaymentService>,
    pub doctor_letter_and_referral_api: Arc<dyn DoctorLetterAndReferralApi>,
    pub external_organisation_integration: Arc<dyn ExternalOrganisationIntegration>,
    pub cookie_key: cookie::Key,
    pub cookie_session_minutes: u32,
    pub app_environment: String,
}

impl ContextBase {
    pub fn pdf_generator(&self) -> Arc<dyn PdfGenerator> {
        self.pdf_generator.clone()
    }
    pub fn nhi_service(&self) -> Arc<dyn NhiService> {
        self.nhi_service.clone()
    }
}

impl App<Query, Mutation, EmptySubscription> {
    pub fn new(context_base: ContextBase) -> Self {
        let schema = Schema::build(Query::default(), Mutation::default(), EmptySubscription)
            .extension(ObservabilityExtension)
            .finish();

        App {
            schema,
            context_base,
        }
    }
}

#[allow(clippy::type_complexity)]
impl<TQuery, TMutation, TSubscription> App<TQuery, TMutation, TSubscription>
where
    TQuery: ObjectType + Sized + 'static,
    TMutation: ObjectType + Sized + 'static,
    TSubscription: SubscriptionType + Sized + 'static,
{
    pub fn sdl() -> String {
        Schema::build(Query::default(), Mutation::default(), EmptySubscription)
            .finish()
            .sdl()
    }

    pub async fn execute(
        &self,
        headers: HeaderMap,
        ip_address: Option<IpAddr>,
        req: BatchRequest,
    ) -> ExecutionResult {
        let auth_data = authenticate(&headers, &self.context_base).await;

        match auth_data {
            Ok(auth_data) => {
                let host_header = headers
                    .get("host")
                    .and_then(|v| v.to_str().ok())
                    .and_then(|h| h.parse::<Uri>().ok())
                    .and_then(|h| h.host().map(Into::into));

                with_authentication_span(
                    auth_data.clone(),
                    host_header.clone(),
                    ip_address,
                    async move {
                        let cookie_header = headers.get("cookie").and_then(|v| v.to_str().ok());

                        let context = ContextData::new(
                            self.context_base.clone(),
                            auth_data,
                            host_header,
                            cookie_header,
                        );

                        let req_with_context = req.data(context.clone());

                        let response = self.schema.execute_batch(req_with_context).await;

                        ExecutionResult { response, context }
                    },
                )
                .await
            }
            Err(err) => {
                let server_error: ServerError = Into::<Error>::into(GQLError::from(err))
                    .into_server_error(Pos { line: 1, column: 1 });

                let response = match req {
                    BatchRequest::Single(_) => {
                        async_graphql::BatchResponse::Single(Response::from_errors(vec![
                            server_error,
                        ]))
                    }
                    BatchRequest::Batch(requests) => async_graphql::BatchResponse::Batch(
                        requests
                            .into_iter()
                            .map(|_| Response::from_errors(vec![server_error.clone()]))
                            .collect(),
                    ),
                };

                let context =
                    ContextData::new(self.context_base.clone(), AuthData::Anon, None, None);

                ExecutionResult { response, context }
            }
        }
    }
}

async fn authenticate(
    headers: &HeaderMap,
    ctx: &ContextBase,
) -> leviosa_domain_contracts::errors::Result<AuthData> {
    if let Some(app_token) = headers
        .get("app-authorization")
        .and_then(|v| v.to_str().ok())
    {
        let repo = AuthRepo::new(&ctx.sea_database);
        let app_authenticator =
            AppAuthenticator::new(&repo, ctx.password_hasher.as_ref(), ctx.jwt.as_ref());

        app_authenticator.authenticate_app_user(app_token).await
    } else {
        let access_token = headers.get("authorization").and_then(|v| v.to_str().ok());

        AuthData::from_header(access_token, ctx.jwt.as_ref())
    }
}

struct CustomGraphQLResponse {
    response: GraphQLResponse,
    context: ContextData,
}

impl axum::response::IntoResponse for CustomGraphQLResponse {
    fn into_response(self) -> axum::response::Response {
        let mut response = self.response.into_response();
        let cookies = futures::executor::block_on(self.context.response_cookies().take());
        for cookie in cookies {
            response
                .headers_mut()
                .append("Set-Cookie", cookie.to_string().parse().unwrap());
        }
        response
    }
}

pub async fn graphql_handler<TQuery, TMutation, TSubscription>(
    ConnectInfo(socket_addr): ConnectInfo<SocketAddr>,
    Extension(app): Extension<App<TQuery, TMutation, TSubscription>>,
    headers: HeaderMap,
    req: GraphQLBatchRequest,
) -> impl axum::response::IntoResponse
where
    TQuery: ObjectType + Sized + 'static,
    TMutation: ObjectType + Sized + 'static,
    TSubscription: SubscriptionType + Sized + 'static,
{
    let execution_result = app
        .execute(headers, Some(socket_addr.ip()), req.into_inner())
        .await;

    let response = GraphQLResponse::from(execution_result.response);

    let context_with_cookies = execution_result.context;

    CustomGraphQLResponse {
        response,
        context: context_with_cookies,
    }
}
