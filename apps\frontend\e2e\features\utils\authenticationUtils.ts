import { Page } from "@playwright/test"

export const login = async (page: Page) => {
  // const loginbutton = page.locator('button:has-text("Log In")')
  // if ((await loginbutton.count()) === 0) {
  //   return
  // }
  // // Fill in the login form with valid credentials
  // await page.fill("[name='email']", "<EMAIL>")
  // await page.fill("[name='password']", "somepassword")
  // await page.click('button:has-text("Log In")')
  // // eslint-disable-next-line playwright/no-wait-for-timeout
  // await page.waitForTimeout(1200)
}
