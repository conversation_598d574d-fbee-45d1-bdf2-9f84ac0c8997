import { test, expect } from "@playwright/test"

import { login } from "../utils/authenticationUtils"
import {
  createCompletedJournalEntry,
  createEncounter,
  openSubjectJournalForSubject,
} from "../utils/subjectJournalTestUtils"

test.describe.configure({ mode: "serial" })

/* eslint-disable playwright/no-wait-for-timeout */
/* eslint-disable playwright/no-useless-await */
/* eslint-disable playwright/no-force-option */

test.describe("Encounter status", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("/")
    await page.waitForTimeout(2000)
    login(page)
  })

  test("Planned Encounter can be transitioned only to Check in or Delete", async ({
    page,
  }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "Rudy Gainsburg")
    await createEncounter(page, "test Planned encounter transitions")

    // Act
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="PLANNED"]'
      )
      .first()
      .click({ force: true }) // Open action menu for encounter status
    await page.waitForTimeout(500)

    // Assert
    const statusMenu = await page.locator("[data-enter]")
    await expect(await statusMenu.count()).toBeGreaterThan(0)
    const transitionMenuItem = await statusMenu.locator(
      '[data-testid="encounter-status-transition-menu-item"]'
    )
    // Planned encounter can be transitioned to Check-in and Delete
    await expect(transitionMenuItem).toHaveCount(2)
    await expect(transitionMenuItem.first()).toHaveText("Check-in")
    await expect(transitionMenuItem.nth(1)).toHaveText("Delete")
  })

  test("Planned Encounter with clinical data can be transitioned only to Check in or Cancel", async ({
    page,
  }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "Rudy Gainsburg")
    await createEncounter(page, "test Planned with data encounter transitions")
    await createCompletedJournalEntry(page)

    // Act
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="PLANNED"]'
      )
      .first()
      .click({ force: true }) // Open action menu for encounter status
    await page.waitForTimeout(500)

    // Assert
    const statusMenu = await page.locator("[data-enter]")
    const transitionMenuItem = await statusMenu.locator(
      '[data-testid="encounter-status-transition-menu-item"]'
    )
    await expect(await statusMenu.count()).toBeGreaterThan(0)
    // Planned encounter can be transitioned to Check-in and Delete
    await expect(transitionMenuItem).toHaveCount(2)
    await expect(transitionMenuItem.first()).toHaveText("Check-in")
    await expect(transitionMenuItem.nth(1)).toHaveText("Cancel")
  })

  test("In progress Encounter with clinical data can be transitioned correctly", async ({
    page,
  }) => {
    // Arrange
    //this test case needs extra time to load the page for some reason.
    await page.waitForTimeout(2000)
    await openSubjectJournalForSubject(page, "Dana Morgan")

    // Act
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="IN_PROGRESS"]'
      )
      .first()
      .click({ force: true }) // Open action menu for encounter status
    await page.waitForTimeout(500)

    // Assert
    const statusMenu = await page.locator("[data-enter]")
    await expect(await statusMenu.count()).toBeGreaterThan(0) //Status menu expanded

    const transitionMenuItem = await statusMenu.locator(
      '[data-testid="encounter-status-transition-menu-item"]'
    )
    // In progress encounter can be transitioned to Check-out, Conclude and Cancel, and
    // disposition status can be set
    await expect(transitionMenuItem).toHaveCount(4)
    await expect(transitionMenuItem.nth(0)).toHaveText("Check-out")
    await expect(transitionMenuItem.nth(1)).toHaveText("Conclude")
    await expect(transitionMenuItem.nth(2)).toHaveText("Set")
    await expect(transitionMenuItem.nth(3)).toHaveText("Cancel")
  })

  test("In progress Encounter with no data can be transitioned correctly", async ({
    page,
  }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "Ivan Hoe")

    // Act
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="IN_PROGRESS"]'
      )
      .first()
      .click() // Open action menu for encounter status
    await page.waitForTimeout(500)

    // Assert
    const statusMenu = await page.locator("[data-enter]")
    await expect(await statusMenu.count()).toBeGreaterThan(0) // Status menu expanded
    const transitionMenuItem = statusMenu.locator(
      '[data-testid="encounter-status-transition-menu-item"]'
    )
    // In progress encounter can be transitioned to Check-out, Conclude and Delete, and
    // disposition status can be set
    await expect(transitionMenuItem).toHaveCount(3)
    await expect(await transitionMenuItem.nth(0)).toHaveText("Check-out")
    await expect(await transitionMenuItem.nth(1)).toHaveText("Set")
    await expect(await transitionMenuItem.nth(2)).toHaveText("Delete")
  })

  test("Checked out Encounter can be transitioned only to Concluded", async ({
    page,
  }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "David Davis")

    // Act
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="CHECKED_OUT"]'
      )
      .first()
      .click({ force: true }) // Open action menu for encounter status

    await page.waitForTimeout(500)

    // Assert
    const statusMenu = await page.locator("[data-enter]")
    await expect(await statusMenu.count()).toBeGreaterThan(0) // Status menu expanded

    const transitionMenuItem = await statusMenu.locator(
      '[data-testid="encounter-status-transition-menu-item"]'
    )
    await expect(transitionMenuItem).toHaveCount(1) // Only one option available
    await expect(transitionMenuItem).toHaveText("Conclude") // Option is Conclude encounter
  })

  test("Cancelled Encounter can not be transitioned", async ({ page }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "Dana Morgan")

    // Set status of encounter to Cancelled
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="IN_PROGRESS"]'
      )
      .first()
      .click({ force: true }) // Open action menu for encounter status
    const statusMenu = await page.locator("[data-enter]")
    await statusMenu
      .locator('[data-testid="encounter-status-transition-menu-item"]')
      .nth(3)
      .click() // Cancel encounter
    await page.waitForTimeout(400)

    // Act
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="CANCELLED"]'
      )
      .first()
      .click() // No action menu for encounter status

    // Assert
    await expect(page.locator("[data-enter]")).toBeHidden() // No expanded options
  })
})

test.describe("Delete Encounter", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("/")
    await page.waitForTimeout(2000)
    login(page)
  })

  test("Can delete new Encounter with no data", async ({ page }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "Ivan Hoe")

    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="IN_PROGRESS"]'
      )
      .first()
      .click({ force: true }) // Open action menu for encounter status
    await page.waitForTimeout(500)

    // Act
    const statusMenu = await page.locator("[data-enter]")
    await expect(await statusMenu.count()).toBeGreaterThan(0) // Status menu expanded

    const transitionMenuItem = await statusMenu.locator(
      '[data-testid="encounter-status-transition-menu-item"]'
    )
    await expect(transitionMenuItem).toHaveCount(3) // Last option is Delete
    await expect(transitionMenuItem.nth(2)).toHaveText("Delete")

    await page.waitForLoadState()
    await page
      .locator('[data-testid="encounter-status-transition-menu-item"]', {
        hasText: "Delete",
      })
      .click()
    await page.waitForTimeout(500)
    await page.getByRole("button", { name: "Delete" }).click()
    await page.waitForTimeout(500)

    // Assert
    // Notification that encounter was deleted
    await expect(
      await page
        .locator('[class*="Notification_content"]')
        .filter({ hasText: "Encounter has been deleted" })
    ).toBeVisible()
  })

  test("Cannot delete Encounter with completed journal entry", async ({
    page,
  }) => {
    // Arrange
    await openSubjectJournalForSubject(page, "Ruby McElveen")

    // Act
    // Open action menu for encounter status
    await page
      .locator(
        '[data-testid="encounter-status-transition-button"][data-status="IN_PROGRESS"]'
      )
      .first()
      .click({ force: true })
    await page.waitForTimeout(400)
    const statusMenu = await page.locator("[data-enter]")
    const transitionMenuItem = await statusMenu.locator(
      '[data-testid="encounter-status-transition-menu-item"]'
    )

    // Assert
    await expect(await statusMenu.count()).toBeGreaterThan(0) // Status menu expanded
    await expect(transitionMenuItem).toHaveCount(4)
    await expect(transitionMenuItem.first()).toHaveText("Check-out")
    await expect(transitionMenuItem.nth(1)).toHaveText("Conclude")
    await expect(transitionMenuItem.nth(2)).toHaveText("Set")
    await expect(transitionMenuItem.nth(3)).toHaveText("Cancel")
    // Delete option is not available
  })
})
