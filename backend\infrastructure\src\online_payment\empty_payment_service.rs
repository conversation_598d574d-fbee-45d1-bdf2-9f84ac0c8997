use async_trait::async_trait;
use leviosa_domain::{
    errors::Error,
    online_payment_service::{
        CheckoutStatus, CreateCustomerResponse, Customer, OnlinePaymentService, PaymentCheckout,
        PaymentCheckoutResponse, PaymentRequest, PaymentResponse, PaymentStatus,
    },
};
use uuid::Uuid;

pub struct EmptyPaymentService;

#[async_trait]
impl OnlinePaymentService for EmptyPaymentService {
    async fn api_auth_header(&self) -> Result<String, Error> {
        Err(anyhow::anyhow!("Online Payment service is not configured").into())
    }

    async fn create_checkout(
        &self,
        _payment_checkout: PaymentCheckout,
    ) -> Result<PaymentCheckoutResponse, Error> {
        Err(anyhow::anyhow!("Online Payment service is not configured").into())
    }

    async fn get_checkout(&self, _checkout_id: Uuid) -> Result<CheckoutStatus, Error> {
        Err(anyhow::anyhow!("Online Payment service is not configured").into())
    }

    async fn create_customer(&self, _customer: Customer) -> Result<CreateCustomerResponse, Error> {
        Err(anyhow::anyhow!("Online Payment service is not configured").into())
    }

    async fn create_payment(&self, _request: PaymentRequest) -> Result<PaymentResponse, Error> {
        Err(anyhow::anyhow!("Online Payment service is not configured").into())
    }

    async fn get_payment_status(&self, _payment_id: &str) -> Result<PaymentStatus, Error> {
        Err(anyhow::anyhow!("Online Payment service is not configured").into())
    }

    async fn process_webhook(&self, _payload: &str, _signature: &str) -> Result<PaymentStatus, Error> {
        Err(anyhow::anyhow!("Online Payment service is not configured").into())
    }
}
