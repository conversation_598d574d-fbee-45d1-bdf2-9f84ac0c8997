.noInfo {
  color: var(--color-blue-gray-violet-dark);
}

.participants {
  display: grid;
  grid-row-gap: 8px;
  grid-column-gap: 16px;
  grid-template-columns: 1fr 1fr;
  grid-template-areas:
    "first third"
    "second fourth";
  width: 100%;
}

.participants > :nth-child(1) {
  grid-area: first;
}

.participants > :nth-child(2) {
  grid-area: second;
}

.participants > :nth-child(3) {
  grid-area: third;
}

.participants > :nth-child(4) {
  grid-area: fourth;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding-bottom: var(--grid-gap);
}

.createInvoiceButton {
  margin: -4px 0 0 -17px;
}

.participantsList {
  display: grid;
  grid-row-gap: 4px;
  align-items: start;
  align-content: start;
}

.participant {
  display: flex;
  align-items: center;
}

.openJournalButton {
  margin-left: auto;
}
