import Fuse from "fuse.js"
import { useMemo } from "react"

import { fuseSearch } from "./fuseSearch"

type UseStateFilterParams<T> = {
  value: string
  defaultItems: T[]
  fuseOption?: Fuse.IFuseOptions<T>
  title?: string
  description?: string
}

/**
 * @deprecated This hook is deprecated and will be removed in future versions.
 */
export const useFilter = <T>({
  defaultItems,
  value,
  fuseOption,
}: UseStateFilterParams<T>) => {
  const filteredList = useMemo(
    () => fuseSearch(defaultItems, value, fuseOption),
    [value, defaultItems, fuseOption]
  )

  return { value, filteredList }
}
