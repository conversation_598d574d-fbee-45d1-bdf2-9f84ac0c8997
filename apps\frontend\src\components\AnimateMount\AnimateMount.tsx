// Reworked from https://czaplinski.io/blog/super-easy-animation-with-react-hooks/
import React, { CSSProperties, useEffect, useState } from "react"

import styles from "./AnimateMount.module.css"

export type AnimateMountProps = {
  animation?: "fade" | "fadeUp" | "manual"
  animationDuration?: number
  show: boolean
  children?: React.ReactNode
}

const useAnimateMount = <E extends HTMLDivElement | HTMLDialogElement>(
  show: boolean,
  onAnimationEnd?: (event: React.AnimationEvent<E>) => void
) => {
  const [shouldRender, setRender] = useState(show)

  useEffect(() => {
    if (show) setRender(true)
  }, [show])

  const oAE = (event: React.AnimationEvent<E>) => {
    onAnimationEnd?.(event)
    if (!show) setRender(false)
  }

  return { shouldRender, onAnimationEnd: oAE }
}

export const AnimateMount = React.forwardRef<
  HTMLDivElement,
  AnimateMountProps & JSX.IntrinsicElements["div"]
>(
  (
    {
      show,
      animation = "fade",
      animationDuration = 200,
      className = "",
      style = {},
      ...rest
    },
    ref
  ) => {
    const { shouldRender, onAnimationEnd } = useAnimateMount(
      show,
      rest.onAnimationEnd
    )

    // We need to check both should render and show, since
    // shouldRender updates in the tick after show is updated.
    // This can cause errors if there parent component expects
    // the child to be rendered when show is true. (Since that's
    // not the case in the tick after show is updated)
    return shouldRender || show ? (
      <div
        ref={ref}
        data-enter={show ? true : undefined}
        data-leave={!show ? true : undefined}
        data-animation={animation}
        className={`${className} ${styles.wrap}`}
        style={
          {
            "--animation-duration": `${animationDuration}ms`,
            ...style,
          } as CSSProperties
        }
        {...rest}
        onAnimationEnd={onAnimationEnd}
      />
    ) : null
  }
)

export const AnimatedDialog = React.forwardRef<
  HTMLDialogElement,
  AnimateMountProps & JSX.IntrinsicElements["dialog"]
>(
  (
    {
      show,
      animation = "fade",
      animationDuration = 200,
      className = "",
      style = {},
      ...rest
    },
    ref
  ) => {
    const { shouldRender, onAnimationEnd } = useAnimateMount(
      show,
      rest.onAnimationEnd
    )

    // We need to check both should render and show, since
    // shouldRender updates in the tick after show is updated.
    // This can cause errors if there parent component expects
    // the child to be rendered when show is true. (Since that's
    // not the case in the tick after show is updated)
    return shouldRender || show ? (
      <dialog
        ref={ref}
        data-enter={show ? true : undefined}
        data-leave={!show ? true : undefined}
        data-animation={animation}
        className={`${className} ${styles.wrap}`}
        style={
          {
            "--animation-duration": `${animationDuration}ms`,
            ...style,
          } as CSSProperties
        }
        {...rest}
        onAnimationEnd={onAnimationEnd}
      />
    ) : null
  }
)

export default AnimateMount
