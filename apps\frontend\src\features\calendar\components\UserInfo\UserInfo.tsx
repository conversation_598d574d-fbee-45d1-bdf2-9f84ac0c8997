import { useTranslation } from "react-i18next"
import { <PERSON> } from "react-router-dom"

import { Toolt<PERSON> } from "components/Ariakit/Tooltip/Tooltip"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { ButtonText, IconButton, Text } from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import { ParticipantRsvpStatus } from "generated/graphql"

import styles from "./UserInfo.module.css"

type UserInfoProps = {
  id: string
  name: string
  rsvpStatus?: ParticipantRsvpStatus | null
  onRemove: () => void
  readOnly?: boolean
  isSubject?: boolean
  description?: string
  isAvailable?: boolean
  upcomingEvent?: {
    eventName: string
    provider: string
    date: string
  }
  noPhoneNumber?: boolean
}

export const UserInfo = ({
  id,
  name,
  isSubject = false,
  readOnly = false,
  onRemove,
  description,
  noPhoneNumber,
  upcomingEvent,
  isAvailable,
}: UserInfoProps) => {
  const { t } = useTranslation()
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()
  const {
    globalState: { isNnMode },
  } = useGlobalState()

  const eventDate = upcomingEvent ? new Date(upcomingEvent.date) : null
  const formattedDate = eventDate
    ? dateFormat(eventDate, { dateStyle: "medium" })
    : ""
  const formattedTime = eventDate ? timeFormat(eventDate) : ""

  return (
    <div className={styles.wrap}>
      {!readOnly && (
        <IconButton
          iconName="close-line"
          onClick={onRemove}
          size="small"
          className={styles.removeButton}
        />
      )}

      <div className={styles.userInfo}>
        {isSubject ? (
          <PiiSensitive>
            <ButtonText
              as={Link}
              to={getRecordPagePath(RouteStrings.subjectJournal, id)}
            >
              {name}
            </ButtonText>
            <Text className={styles.description} size="small" secondary>
              {description}
            </Text>
          </PiiSensitive>
        ) : (
          <>
            <ButtonText>{name}</ButtonText>
            <Text className={styles.description} size="small" secondary>
              {description}
            </Text>
          </>
        )}
      </div>
      {noPhoneNumber && (
        <Tooltip
          tooltipContent={
            "Phone number is not set, so we're unable to send event reminders."
          }
          status="warning"
          tooltipClassName={styles.tooltip}
          className={styles.info}
          portal={false}
        >
          <Icon
            name="notification-off-line"
            className={styles.missingInformationIcon}
          />
        </Tooltip>
      )}
      {!isAvailable && (
        <Tooltip
          tooltipContent={`${
            isNnMode && isSubject ? "-" : name
          } has a scheduling conflict`}
          status="error"
          tooltipClassName={styles.tooltip}
          className={styles.info}
          portal={false}
        >
          <Icon name="information-line" className={styles.conflictIcon} />
        </Tooltip>
      )}
      {upcomingEvent && (
        <Tooltip
          tooltipContent={
            <>
              {`${t("Upcoming")} `}
              <b>{upcomingEvent.eventName}</b>
              {` ${t("with")} `}
              <b>{upcomingEvent.provider}</b>
              {` ${t("on")} `}
              <b>{`${formattedDate}, ${formattedTime}`}</b>
            </>
          }
          tooltipClassName={styles.infoTooltip}
          className={styles.info}
          portal={false}
        >
          <Icon name="information-line" className={styles.infoIcon} />
        </Tooltip>
      )}
    </div>
  )
}
