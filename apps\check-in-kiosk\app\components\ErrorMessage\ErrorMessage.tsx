import { useNavigate } from "@remix-run/react"
import { useTranslation } from "react-i18next"

import StethoscopeIllustration from "@leviosa/assets/illustrations/stethoscope.svg?react"
import { Button } from "@leviosa/components"

import styles from "./ErrorMessage.module.css"

type ErrorMessageProps = {
  message?: string
}

const ErrorMessage = ({ message }: ErrorMessageProps) => {
  const navigate = useNavigate()
  const { t } = useTranslation()

  return (
    <div className={styles.container}>
      <StethoscopeIllustration />
      <h2>{t("somethingWentWrong")}</h2>
      <p className={styles.message}>{message || t("pleaseTryAgain")}</p>
      <Button onClick={() => navigate(-1)}>{t("goBackToMainScreen")}</Button>
    </div>
  )
}

export default ErrorMessage
