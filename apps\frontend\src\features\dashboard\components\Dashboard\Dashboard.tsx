import { Popover, usePopoverStore } from "@ariakit/react"
import c from "classnames"
import { matchSorter } from "match-sorter"
import { startTransition, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { useImmer } from "use-immer"

import useRadioStore from "components/Ariakit/hooks/useRadioStore/useRadioStore"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import TeamStatistics from "components/TeamStatistics/TeamStatistics"
import DashboardStatusPicker from "features/dashboard/components/Dashboard/DashboardStatusPicker"
import SubjectSummary from "features/dashboard/components/SubjectSummary/SubjectSummary"
import filterDashboardByTag from "features/dashboard/utils/filterDashboardByTags"
import getTagsFromNote from "features/dashboard/utils/getTagsFromNote"
import { Button, Grid, Input, Table, Text } from "ui"

import { EncounterStatus, useDashboardQuery } from "generated/graphql"

import styles from "./Dashboard.module.css"
import { DashboardRow } from "./DashboardRow/DashboardRow"
import { DashboardSortIcon } from "./DashboardSortIcon/DashboardSortIcon"
import { DashboardTags } from "./DashboardTags/DashboardTags"
import type { DashboardSelectedTagMap } from "./DashboardTags/DashboardTags"
import { sortDashboardData } from "./sortDashboardData"

const filterableValues = [
  "subject.*.name",
  "subject.*.personaId",
  "reason",
  "responsibleProviders.*.name",
  "responsibleProviders.*.nameInitials",
] as const

// Keys from dashboard query data
export type SearchSettingsColumnType =
  | "subject.location"
  | "subject.name"
  | "priority"
  | "subject.age"
  | "reason"
  | "fromDate" // field for calculation of duration

export type SearchSettingsType = {
  order: "asc" | "desc" | "default"
  column: SearchSettingsColumnType
}

export const dashboardPollInterval = 60000

type DashboardProps = {
  actorId: string
}

const Dashboard = ({ actorId }: DashboardProps) => {
  const [searchValue, setSearchValue] = useState("")

  const [selectedTags, setSelectedTags] = useImmer<DashboardSelectedTagMap>({})

  const [selectedSubjectId, setSelectedSubjectId] = useState<string | null>(
    null
  )

  const [searchSettings, setSearchSettings] = useImmer<SearchSettingsType>({
    order:
      (localStorage.getItem("order") as SearchSettingsType["order"]) ||
      "default",
    column:
      (localStorage.getItem("column") as SearchSettingsType["column"]) ||
      "subject.location",
  })

  const { teamId } = useParams()
  if (teamId === undefined) throw new Error("teamId is undefined")

  const { t } = useTranslation("routes")
  const popoverStore = usePopoverStore()

  const radioStore = useRadioStore({
    defaultValue: EncounterStatus.InProgress,
  })
  const statusFilterValue = (radioStore.useState().value ||
    EncounterStatus.InProgress) as EncounterStatus

  const { data, previousData, loading, error } = useDashboardQuery({
    variables: {
      teamId,
      status: statusFilterValue || EncounterStatus.InProgress,
    },
    pollInterval: dashboardPollInterval,
  })

  const dashboardData = loading ? previousData : data

  const isActorInTeam = dashboardData?.team.members.some(
    ({ id }) => id === actorId
  )

  const allTags = [
    ...new Set(
      dashboardData?.team.rows.flatMap(({ note }) => getTagsFromNote(note))
    ),
  ]

  const filteredList = useMemo(() => {
    const options = (dashboardData?.team.rows || []).map((row) => {
      return {
        ...row,
        tags: getTagsFromNote(row.note),
      }
    })

    const rawList = matchSorter(options, searchValue, {
      keys: filterableValues,
      threshold: matchSorter.rankings.CONTAINS,
    })

    const filteredTaggedItems = rawList.filter(
      filterDashboardByTag(selectedTags)
    )

    return sortDashboardData(filteredTaggedItems, searchSettings)
  }, [dashboardData?.team.rows, searchValue, searchSettings, selectedTags])

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    startTransition(() => {
      setSearchValue(e.target.value)
    })
  }

  const handleSortChange = (column: SearchSettingsType["column"]) => {
    setSearchSettings((draft) => {
      if (draft.column === column) {
        draft.order =
          draft.order === "default"
            ? "asc"
            : draft.order === "asc"
              ? "desc"
              : "default"

        localStorage.setItem("order", draft.order)

        return
      }

      draft.column = column
      draft.order = "asc"

      localStorage.setItem("column", column)
      localStorage.setItem("order", draft.order)
    })
  }

  const selectedSubject = selectedSubjectId
    ? dashboardData?.team.rows.find(
        (row) => row.subject.id === selectedSubjectId
      )?.subject
    : null

  return (
    <>
      <Grid>
        {!loading && !isActorInTeam && !error && (
          <Panel className={styles.accessPanel} status="warning">
            {t("dashboard.accessRestricted_notTeamMember")}
          </Panel>
        )}
        {error && (
          <Panel className={styles.accessPanel} status="warning">
            {t("Could not load data")}: {error.message}
          </Panel>
        )}
        <div className={styles.filters}>
          <Input
            label={t("dashboard.filterDashboard")}
            hideLabel
            hideMessage
            name="search2"
            placeholder={t("dashboard.filterDashboard")}
            value={searchValue}
            onChange={handleFilterChange}
            icon={<Icon name="search-line" />}
            clearable
          />

          <DashboardStatusPicker store={radioStore} teamId={teamId} />

          <DashboardTags
            allTags={allTags}
            selectedTags={selectedTags}
            onIncludeTag={(tag) => {
              setSelectedTags((draft) => {
                draft[tag] = 1
              })
            }}
            onExcludeTag={(tag) => {
              setSelectedTags((draft) => {
                draft[tag] = -1
              })
            }}
            onRemoveTag={(tag) => {
              setSelectedTags((draft) => {
                // Delete it rather than set it to 0
                // so that if it is added again it will be added last
                delete draft[tag]
              })
            }}
          />

          <TeamStatistics id={teamId} className={styles.statistics} />
        </div>

        <Table
          className={c({
            [styles.dashboard]: 1,
            [styles.loading]: loading,
          })}
        >
          <thead>
            <tr>
              <th key={"location"}>
                <Button
                  onClick={() => handleSortChange("subject.location")}
                  className={styles.rowButton}
                  iconEnd={
                    <DashboardSortIcon
                      displayColumn="subject.location"
                      searchSettings={searchSettings}
                    />
                  }
                >
                  {t("subjectFields.location")}
                </Button>
              </th>
              <th key={"age"}>
                <Button
                  onClick={() => handleSortChange("subject.age")}
                  className={styles.rowButton}
                  data-testid="sort-by-age"
                  data-icon-only={true}
                  iconEnd={
                    <DashboardSortIcon
                      displayColumn="subject.age"
                      searchSettings={searchSettings}
                    />
                  }
                ></Button>
              </th>
              <th key={"gender"}></th>
              <th key={"subject"}>
                <Button
                  onClick={() => handleSortChange("subject.name")}
                  className={styles.rowButton}
                  data-testid="sort-by-name"
                  iconEnd={
                    <DashboardSortIcon
                      displayColumn="subject.name"
                      searchSettings={searchSettings}
                    />
                  }
                >
                  {t("subjectFields.name")}
                </Button>
              </th>

              <th>
                <Button
                  onClick={() => handleSortChange("priority")}
                  className={`${styles.rowButton} ${styles.priority}`}
                  data-testid="sort-by-priority"
                  data-icon-only={true}
                  iconEnd={
                    <DashboardSortIcon
                      displayColumn="priority"
                      searchSettings={searchSettings}
                    />
                  }
                ></Button>
              </th>

              <th>
                <Button
                  onClick={() => handleSortChange("fromDate")}
                  className={styles.rowButton}
                  data-testid="sort-by-fromDate"
                  data-icon-only={true}
                  iconEnd={
                    <DashboardSortIcon
                      displayColumn="fromDate"
                      searchSettings={searchSettings}
                    />
                  }
                ></Button>
              </th>

              <th>
                <Button
                  onClick={() => handleSortChange("reason")}
                  className={styles.rowButton}
                  iconEnd={
                    <DashboardSortIcon
                      displayColumn="reason"
                      searchSettings={searchSettings}
                    />
                  }
                >
                  {t("encounterFields.arrival")}
                </Button>
              </th>

              <th>
                <Text as="span" weight="bold">
                  {t("encounterFields.dietaryAllowance")}
                </Text>
              </th>

              <th>
                <Text as="span" weight="bold">
                  {t("editableNote.note")}
                </Text>
              </th>

              <th>
                <Text as="span" weight="bold">
                  {t("encounterFields.responsibleProviders")}
                </Text>
              </th>
              <th>
                <Text as="span" weight="bold">
                  {t("encounterFields.journalEntries")}
                </Text>
              </th>
            </tr>
          </thead>

          <tbody>
            {filteredList.map((row) => (
              <DashboardRow
                key={row.id}
                row={row}
                popoverStore={popoverStore}
                onSubjectSelected={(subjectId) =>
                  setSelectedSubjectId(subjectId)
                }
                onProviderSelected={(name) => {
                  // empty filter if Provider already used
                  setSearchValue(searchValue === name ? "" : name)
                }}
              />
            ))}
          </tbody>
        </Table>
      </Grid>

      <Popover
        open={!!selectedSubjectId}
        hideOnEscape
        hideOnInteractOutside
        unmountOnHide
        store={popoverStore}
        onClose={() => setSelectedSubjectId(null)}
        className={styles.popover}
        autoFocusOnHide={false}
        portal
      >
        {selectedSubject && (
          <SubjectSummary
            {...selectedSubject}
            onRequestClose={() => {
              popoverStore.hide()
              setSelectedSubjectId(null)
            }}
          />
        )}
      </Popover>
    </>
  )
}

export default Dashboard
