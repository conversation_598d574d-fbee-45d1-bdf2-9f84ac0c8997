import { Meta } from "@storybook/react-vite"

import { Combobox, ComboboxProvider } from "./Combobox"
import { ComboboxItem } from "./ComboboxItem/ComboboxItem"
import { ComboboxPopover } from "./ComboboxPopover/ComboboxPopover"

export default {
  title: "Ariakit/Combobox",
  component: Combobox,
} as Meta

export const ComboboxExample = () => {
  return (
    <ComboboxProvider>
      <label className="label">
        Your favorite fruit
        <Combobox placeholder="e.g., Apple" className="combobox" />
      </label>
      <ComboboxPopover gutter={4} sameWidth className="popover">
        <ComboboxItem className="combobox-item" value="Apple">
          🍎 Apple
        </ComboboxItem>
        <ComboboxItem className="combobox-item" value="Grape">
          🍇 Grape
        </ComboboxItem>
        <ComboboxItem className="combobox-item" value="Orange">
          🍊 Orange
        </ComboboxItem>
        <ComboboxItem className="combobox-item" value="Strawberry">
          🍓 Strawberry
        </ComboboxItem>
        <ComboboxItem className="combobox-item" value="Watermelon">
          🍉 Watermelon
        </ComboboxItem>
      </ComboboxPopover>
    </ComboboxProvider>
  )
}
