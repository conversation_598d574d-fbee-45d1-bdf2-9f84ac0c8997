import { createContext, ReactNode, useContext } from "react"
import "react-big-calendar/lib/addons/dragAndDrop/styles.css"

import {
  GetAllProvidersQuery,
  useGetAllProvidersQuery,
} from "generated/graphql"

import "../../assets/Calendar.css"
import "../../assets/CalendarDragAndDrop.css"

type CalendarContextType = {
  allProviders: GetAllProvidersQuery["providers"]
}

const CalendarContext = createContext<CalendarContextType>({
  allProviders: [],
})

export const useCalendar = () => {
  return useContext(CalendarContext)
}

export const CalendarProvider = ({ children }: { children: ReactNode }) => {
  const { data } = useGetAllProvidersQuery()

  return (
    <CalendarContext.Provider
      value={{
        allProviders: data?.providers ?? [],
      }}
    >
      {children}
    </CalendarContext.Provider>
  )
}
