//! Repository interfaces - Contracts for inverse dependency
//!
//! This module provides contract interfaces that achieve inverse dependency principle.
//! Other crates can depend on these contracts instead of directly on domain implementations.
//!
//! **IMPORTANT**: This is a contracts-only crate that provides interfaces.
//! The actual implementations are provided by the domain crate.
//! This enables parallel compilation and clean architecture.

use crate::auth::AuthenticatedUser;
use crate::errors::Result;
use async_trait::async_trait;
use leviosa_domain_types::*;

/// Contract for repository collection
/// This provides access to all repository interfaces
#[async_trait]
pub trait IReposContract<'a>: Send + Sync {
    // Account repositories
    fn organisation_repo(&self) -> Box<dyn IOrganisationRepoContract + 'a>;
    fn provider_repo(&self) -> Box<dyn IProviderRepoContract + 'a>;
    fn subject_repo(&self) -> Box<dyn ISubjectRepoContract + 'a>;
    fn address_repo(&self) -> Box<dyn IAddressRepoContract + 'a>;
    fn department_repo(&self) -> Box<dyn IDepartmentRepoContract + 'a>;

    // Team repositories
    fn team_repo(&self) -> Box<dyn ITeamRepoContract + 'a>;

    // Billing repositories
    fn billing_invoice_repo(&self) -> Box<dyn IBillingInvoiceRepoContract + 'a>;
    fn billing_invoice_line_repo(&self) -> Box<dyn IBillingInvoiceLineRepoContract + 'a>;
    fn billing_invoice_issuer_repo(&self) -> Box<dyn IBillingInvoiceIssuerRepoContract + 'a>;
    fn billing_code_nhi_repo(&self) -> Box<dyn IBillingCodeNhiRepoContract + 'a>;
    fn billing_code_clinic_specific_repo(&self) -> Box<dyn IBillingCodeClinicSpecificRepoContract + 'a>;
    fn nhi_config_repo(&self) -> Box<dyn INhiConfigRepoContract + 'a>;
    fn nhi_log_repo(&self) -> Box<dyn INhiLogRepoContract + 'a>;
    fn online_payment_config_repo(&self) -> Box<dyn IOnlinePaymentConfigRepoContract + 'a>;

    // Calendar repositories
    fn event_instance_repo(&self) -> Box<dyn IEventInstanceRepoContract + 'a>;
    fn participant_repo(&self) -> Box<dyn IParticipantRepoContract + 'a>;
    fn availability_schedule_repo(&self) -> Box<dyn IAvailabilityScheduleRepoContract + 'a>;
    fn calendar_config_repo(&self) -> Box<dyn ICalendarConfigRepoContract + 'a>;
    fn event_recurrence_repo(&self) -> Box<dyn IEventRecurrenceRepoContract + 'a>;
    fn external_service_type_repo(&self) -> Box<dyn IExternalServiceTypeRepoContract + 'a>;

    // Subject Journal repositories
    fn journal_entry_repo(&self) -> Box<dyn IJournalEntryRepoContract + 'a>;
    fn drug_prescription_repo(&self) -> Box<dyn IDrugPrescriptionRepoContract + 'a>;
    fn intervention_period_repo(&self) -> Box<dyn IInterventionPeriodRepoContract + 'a>;
    fn encounter_repo(&self) -> Box<dyn IEncounterRepoContract + 'a>;
    fn journal_entry_block_repo(&self) -> Box<dyn IJournalEntryBlockRepoContract + 'a>;
    fn clinical_coding_repo(&self) -> Box<dyn IClinicalCodingRepoContract + 'a>;
    fn note_repo(&self) -> Box<dyn INoteRepoContract + 'a>;
    fn outbound_referral_repo(&self) -> Box<dyn IOutboundReferralRepoContract + 'a>;
    fn outbound_doctors_letter_repo(&self) -> Box<dyn IOutboundDoctorsLetterRepoContract + 'a>;
    fn medical_certificate_repo(&self) -> Box<dyn IMedicalCertificateRepoContract + 'a>;
    fn subject_health_profile_repo(&self) -> Box<dyn ISubjectHealthProfileRepoContract + 'a>;
    fn subject_static_data_repo(&self) -> Box<dyn ISubjectStaticDataRepoContract + 'a>;
    fn snippet_repo(&self) -> Box<dyn ISnippetRepoContract + 'a>;
    fn journal_focus_log_repo(&self) -> Box<dyn IJournalFocusLogRepoContract + 'a>;
    fn journal_entry_attachment_repo(&self) -> Box<dyn IJournalEntryAttachmentRepoContract + 'a>;

    // Template repositories
    fn journal_template_repo(&self) -> Box<dyn IJournalTemplateRepoContract + 'a>;
    fn journal_block_template_repo(&self) -> Box<dyn IJournalBlockTemplateRepoContract + 'a>;
    fn journal_template_section_repo(&self) -> Box<dyn IJournalTemplateSectionRepoContract + 'a>;
    fn drug_prescription_template_repo(&self) -> Box<dyn IDrugPrescriptionTemplateRepoContract + 'a>;
    fn clinical_coding_template_repo(&self) -> Box<dyn IClinicalCodingTemplateRepoContract + 'a>;

    // List repositories
    fn appointment_request_repo(&self) -> Box<dyn IAppointmentRequestRepoContract + 'a>;
    fn referral_item_repo(&self) -> Box<dyn IReferralItemRepoContract + 'a>;
    fn doctor_letter_item_repo(&self) -> Box<dyn IDoctorLetterItemRepoContract + 'a>;
    fn list_item_repo(&self) -> Box<dyn IListItemRepoContract + 'a>;
    fn waiting_list_repo(&self) -> Box<dyn IWaitingListRepoContract + 'a>;

    // Entity event repository
    fn entity_event_repo(&self) -> Box<dyn IEntityEventRepoContract + 'a>;

    // Notification repositories
    fn subject_notifications_config_repo(&self) -> Box<dyn ISubjectNotificationsConfigRepoContract + 'a>;

    // Inbound data repositories
    fn inbound_data_repo(&self) -> Box<dyn IInboundDataRepoContract + 'a>;
    fn inbound_entry_repo(&self) -> Box<dyn IInboundEntryRepoContract + 'a>;
}

/// Marker trait for repository contracts
#[async_trait]
pub trait RepositoryContract: Send + Sync {}

// Account repository contracts
#[async_trait]
pub trait IOrganisationRepoContract: RepositoryContract {}

#[async_trait]
pub trait IProviderRepoContract: RepositoryContract {}

#[async_trait]
pub trait ISubjectRepoContract: RepositoryContract {}

#[async_trait]
pub trait IAddressRepoContract: RepositoryContract {}

#[async_trait]
pub trait IDepartmentRepoContract: RepositoryContract {}

// Team repository contracts
#[async_trait]
pub trait ITeamRepoContract: RepositoryContract {}

// Billing repository contracts
#[async_trait]
pub trait IBillingInvoiceRepoContract: RepositoryContract {}

#[async_trait]
pub trait IBillingInvoiceLineRepoContract: RepositoryContract {}

#[async_trait]
pub trait IBillingInvoiceIssuerRepoContract: RepositoryContract {}

#[async_trait]
pub trait IBillingCodeNhiRepoContract: RepositoryContract {}

#[async_trait]
pub trait IBillingCodeClinicSpecificRepoContract: RepositoryContract {}

#[async_trait]
pub trait INhiConfigRepoContract: RepositoryContract {}

#[async_trait]
pub trait INhiLogRepoContract: RepositoryContract {}

#[async_trait]
pub trait IOnlinePaymentConfigRepoContract: RepositoryContract {}

// Subject Journal repository contracts
#[async_trait]
pub trait IJournalEntryRepoContract: RepositoryContract {}

#[async_trait]
pub trait IDrugPrescriptionRepoContract: RepositoryContract {}

#[async_trait]
pub trait IInterventionPeriodRepoContract: RepositoryContract {}

#[async_trait]
pub trait IEncounterRepoContract: RepositoryContract {}

#[async_trait]
pub trait IJournalEntryBlockRepoContract: RepositoryContract {}

#[async_trait]
pub trait IClinicalCodingRepoContract: RepositoryContract {}

#[async_trait]
pub trait INoteRepoContract: RepositoryContract {}

#[async_trait]
pub trait IOutboundReferralRepoContract: RepositoryContract {}

#[async_trait]
pub trait IOutboundDoctorsLetterRepoContract: RepositoryContract {}

#[async_trait]
pub trait IMedicalCertificateRepoContract: RepositoryContract {}

#[async_trait]
pub trait ISubjectHealthProfileRepoContract: RepositoryContract {}

#[async_trait]
pub trait IJournalEntryAttachmentRepoContract: RepositoryContract {}

#[async_trait]
pub trait ISubjectStaticDataRepoContract: RepositoryContract {}

#[async_trait]
pub trait ISnippetRepoContract: RepositoryContract {}

#[async_trait]
pub trait IJournalFocusLogRepoContract: RepositoryContract {}

#[async_trait]
pub trait IInboundDataRepoContract: RepositoryContract {}

#[async_trait]
pub trait IInboundEntryRepoContract: RepositoryContract {}

// Template repository contracts
#[async_trait]
pub trait IJournalTemplateRepoContract: RepositoryContract {}

#[async_trait]
pub trait IJournalBlockTemplateRepoContract: RepositoryContract {}

#[async_trait]
pub trait IJournalTemplateSectionRepoContract: RepositoryContract {}

#[async_trait]
pub trait IDrugPrescriptionTemplateRepoContract: RepositoryContract {}

#[async_trait]
pub trait IClinicalCodingTemplateRepoContract: RepositoryContract {}

// Calendar repository contracts
#[async_trait]
pub trait ICalendarConfigRepoContract: RepositoryContract {}

#[async_trait]
pub trait IEventInstanceRepoContract: RepositoryContract {}

#[async_trait]
pub trait IParticipantRepoContract: RepositoryContract {}

#[async_trait]
pub trait IExternalServiceTypeRepoContract: RepositoryContract {}

#[async_trait]
pub trait IAvailabilityScheduleRepoContract: RepositoryContract {}

#[async_trait]
pub trait IEventRecurrenceRepoContract: RepositoryContract {}

// Entity event repository contracts
#[async_trait]
pub trait IEntityEventRepoContract: RepositoryContract {}

// Notification repository contracts
#[async_trait]
pub trait ISubjectNotificationsConfigRepoContract: RepositoryContract {}

// List repository contracts
#[async_trait]
pub trait IAppointmentRequestRepoContract: RepositoryContract {}

#[async_trait]
pub trait IReferralItemRepoContract: RepositoryContract {}

#[async_trait]
pub trait IDoctorLetterItemRepoContract: RepositoryContract {}

#[async_trait]
pub trait IListItemRepoContract: RepositoryContract {}

#[async_trait]
pub trait IWaitingListRepoContract: RepositoryContract {}
