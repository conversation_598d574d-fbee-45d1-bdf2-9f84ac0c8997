import * as Ariakit from "@ariakit/react"
import c from "classnames"
import { ElementType, ReactNode } from "react"

import styles from "./Tooltip.module.css"

export type TooltipStatus = "info" | "success" | "warning" | "error"

// This type is copied over from Ariakit, where it has `any`
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type As<P = any> = ElementType<P>

/**
 * Props for the Tooltip component.
 *
 * @template T - The element type of the Tooltip component.
 */
export type TooltipProps<T extends As = "div"> = Omit<
  Ariakit.TooltipProps<T>,
  "store"
> & {
  tooltipContent?: ReactNode
  children: ReactNode
  tooltipClassName?: string
  placement?: Ariakit.TooltipStoreState["placement"]
  open?: boolean
  status?: TooltipStatus
  portal?: boolean
  timeout?: number
  unmountOnHide?: boolean
  className?: string
}

export function Tooltip<T extends As = "div">({
  tooltipContent,
  children,
  tooltipClassName = "",
  placement = "bottom",
  open,
  portal,
  status = "info",
  timeout,
  unmountOnHide = true,
  className = "",
  ...rest
}: TooltipProps<T>) {
  const tooltip = Ariakit.useTooltipStore({ placement, timeout })

  return (
    <>
      <Ariakit.TooltipAnchor
        store={tooltip}
        className={c(className, styles.anchor)}
        {...rest}
      >
        {children}
      </Ariakit.TooltipAnchor>
      <Ariakit.Tooltip
        store={tooltip}
        gutter={0}
        portal={portal}
        className={c(styles.tooltip, tooltipClassName, styles[status])}
        arrowPadding={12}
        open={!!tooltipContent && open}
        unmountOnHide={unmountOnHide}
      >
        <Ariakit.TooltipArrow />
        {tooltipContent}
      </Ariakit.Tooltip>
    </>
  )
}
