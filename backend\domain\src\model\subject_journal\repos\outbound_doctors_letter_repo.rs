use super::SaveBlock;
use crate::{
    auth::{AuthenticatedUser, CheckTenant},
    entity_event::SaveEvent,
    errors::{NotFoundError, Result},
    subject_journal::{
        JournalEntryBlockStatus, JournalEntryBlockType, JournalEntryId, OutboundDoctorsLetter,
        OutboundDoctorsLetterEvent, OutboundDoctorsLetterId, OutboundReceiver, OutboundReceiverId,
        entities::{
            journal_entry_block::{
                Column as JournalEntryBlockColumn, Entity as JournalEntryBlockEntity,
            },
            outbound_doctors_letter::{
                ActiveModel as OutboundDoctorsLetterActiveModel,
                Entity as OutboundDoctorsLetterEntity,
            },
            outbound_receiver::{
                ActiveModel as OutboundReceiverActiveModel, Column as OutboundReceiverColumn,
                Entity as OutboundReceiverEntity,
            },
        },
    },
};
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, Entity<PERSON>rait, Query<PERSON>ilter, Set,
    sea_query::OnConflict,
};

#[async_trait::async_trait]
pub trait IOutboundDoctorsLetterRepo: Send + Sync {
    async fn save(
        &self,
        model: OutboundDoctorsLetter,
        events: Vec<OutboundDoctorsLetterEvent>,
    ) -> Result<OutboundDoctorsLetter>;
    async fn get(&self, id: OutboundDoctorsLetterId) -> Result<OutboundDoctorsLetter>;
    async fn get_receivers(&self, ids: Vec<OutboundReceiverId>) -> Result<Vec<OutboundReceiver>>;
    async fn save_receivers(&self, models: Vec<OutboundReceiver>) -> Result<Vec<OutboundReceiver>>;
    async fn get_receiver(&self, id: OutboundReceiverId) -> Result<OutboundReceiver>;
    async fn save_receiver(&self, models: OutboundReceiver) -> Result<OutboundReceiver>;
    async fn get_by_journal_entry_ids(
        &self,
        ids: &[JournalEntryId],
    ) -> Result<Vec<OutboundDoctorsLetter>>;
}

pub struct OutboundDoctorsLetterRepo<'a, C: ConnectionTrait> {
    pub connection: &'a C,
    pub user: &'a AuthenticatedUser,
}

impl<C: ConnectionTrait> SaveEvent for OutboundDoctorsLetterRepo<'_, C> {
    type Event = OutboundDoctorsLetterEvent;
}

impl<C: ConnectionTrait> SaveBlock for OutboundDoctorsLetterRepo<'_, C> {}

impl<'a, C: ConnectionTrait> OutboundDoctorsLetterRepo<'a, C> {
    pub fn new(connection: &'a C, user: &'a AuthenticatedUser) -> Self {
        Self { connection, user }
    }
}

#[async_trait::async_trait]
impl<C: ConnectionTrait> IOutboundDoctorsLetterRepo for OutboundDoctorsLetterRepo<'_, C> {
    async fn get(&self, id: OutboundDoctorsLetterId) -> Result<OutboundDoctorsLetter> {
        let (block, doctors_letter) = JournalEntryBlockEntity::find_by_id(id.0)
            .find_also_related(OutboundDoctorsLetterEntity)
            .one(self.connection)
            .await?
            .ok_or_else(|| NotFoundError::new::<OutboundDoctorsLetter>(id))?;

        self.user.check_tenant(&block.organisation_id)?;

        let doctors_letter =
            doctors_letter.ok_or_else(|| NotFoundError::new::<OutboundDoctorsLetter>(id))?;

        let receiver = self.get_receiver(doctors_letter.receiver_id.into()).await?;

        Ok((doctors_letter, block.into(), receiver).into())
    }

    async fn save(
        &self,
        model: OutboundDoctorsLetter,
        events: Vec<OutboundDoctorsLetterEvent>,
    ) -> Result<OutboundDoctorsLetter> {
        let active_model = OutboundDoctorsLetterActiveModel {
            id: Set(model.id().into()),
            block_type: Set(model.block_type()),
            content: Set(model.content().as_ref().map(Into::into)),
            receiver_id: Set(model.receiver().id().0),
        };

        self.save_events(self.connection, self.user, &events)
            .await?;

        let receiver = self.save_receiver(model.receiver().clone()).await?;
        let block = Self::save_block(self, self.connection, model.block().clone()).await?;

        let doctors_letter = match OutboundDoctorsLetterEntity::find_by_id(model.id().0)
            .one(self.connection)
            .await?
        {
            Some(_) => active_model.update(self.connection).await?,
            None => active_model.insert(self.connection).await?,
        };

        Ok((doctors_letter, block, receiver).into())
    }

    async fn get_receivers(&self, ids: Vec<OutboundReceiverId>) -> Result<Vec<OutboundReceiver>> {
        let receivers = OutboundReceiverEntity::find()
            .filter(OutboundReceiverColumn::Id.is_in(ids.iter().map(|id| id.0)))
            .all(self.connection)
            .await?;

        Ok(receivers.into_iter().map(Into::into).collect())
    }

    async fn get_by_journal_entry_ids(
        &self,
        ids: &[JournalEntryId],
    ) -> Result<Vec<OutboundDoctorsLetter>> {
        let result = JournalEntryBlockEntity::find()
            .filter(
                JournalEntryBlockColumn::JournalEntryId
                    .is_in(ids.iter().map(|id| id.0))
                    .and(JournalEntryBlockColumn::Status.ne(JournalEntryBlockStatus::Deleted))
                    .and(
                        JournalEntryBlockColumn::BlockType
                            .eq(JournalEntryBlockType::OutboundDoctorsLetter),
                    ),
            )
            .find_also_related(OutboundDoctorsLetterEntity)
            .all(self.connection)
            .await?;

        let receivers = OutboundReceiverEntity::find()
            .filter(
                OutboundReceiverColumn::Id.is_in(result.iter().filter_map(|r| {
                    r.1.as_ref()
                        .map(|doctors_letter| doctors_letter.receiver_id)
                })),
            )
            .all(self.connection)
            .await?;

        // assign each reciever to the corresponding doctors_letter using the result from the previous query
        let mut doctors_letter_list = Vec::new();

        for (block, doctors_letter) in result {
            self.user.check_tenant(&block.organisation_id)?;

            let doctors_letter = doctors_letter.ok_or_else(|| {
                NotFoundError::by_key::<OutboundDoctorsLetter>(
                    &block.journal_entry_id.to_string(),
                    "journal_entry_id",
                )
            })?;

            let associated_receiver = receivers
                .iter()
                .find(|r| r.id == doctors_letter.receiver_id)
                .cloned()
                .ok_or_else(|| {
                    NotFoundError::by_key::<OutboundReceiver>(
                        &doctors_letter.id.to_string(),
                        "outbound_doctors_letter_id",
                    )
                })?;

            doctors_letter_list
                .push((doctors_letter, block.into(), associated_receiver.into()).into());
        }

        Ok(doctors_letter_list)
    }

    async fn save_receivers(&self, models: Vec<OutboundReceiver>) -> Result<Vec<OutboundReceiver>> {
        let ids = models.iter().map(|m| m.id()).collect();
        // Upsert (insert or update) the incoming models
        OutboundReceiverEntity::insert_many(models.into_iter().map(|model| {
            OutboundReceiverActiveModel {
                id: Set(model.id().into()),
                communication_status: Set(model.communication_status()),
                communication_response: Set(model.communication_response().clone()),
                send_error: Set(model.send_error().clone()),
                external_organisation_id: Set(model.external_organisation_id().map(|id| id.0)),
                external_provider_name: Set(model.external_provider_name().clone()),
            }
        }))
        .on_conflict(
            OnConflict::columns(vec![OutboundReceiverColumn::Id])
                .update_column(OutboundReceiverColumn::ExternalOrganisationId)
                .update_column(OutboundReceiverColumn::ExternalProviderName)
                .update_column(OutboundReceiverColumn::SendError)
                .update_column(OutboundReceiverColumn::CommunicationStatus)
                .update_column(OutboundReceiverColumn::CommunicationResponse)
                .clone(),
        )
        .exec(self.connection)
        .await?;

        let receivers = self.get_receivers(ids).await?;

        Ok(receivers)
    }

    async fn get_receiver(&self, id: OutboundReceiverId) -> Result<OutboundReceiver> {
        let receiver = OutboundReceiverEntity::find_by_id(id)
            .one(self.connection)
            .await?
            .ok_or_else(|| NotFoundError::new::<OutboundReceiver>(id))?;

        Ok(receiver.into())
    }

    async fn save_receiver(&self, model: OutboundReceiver) -> Result<OutboundReceiver> {
        OutboundReceiverEntity::insert(OutboundReceiverActiveModel {
            id: Set(model.id().into()),
            communication_status: Set(model.communication_status()),
            communication_response: Set(model.communication_response().clone()),
            send_error: Set(model.send_error().clone()),
            external_organisation_id: Set(model.external_organisation_id().map(|id| id.0)),
            external_provider_name: Set(model.external_provider_name().clone()),
        })
        .on_conflict(
            OnConflict::columns(vec![OutboundReceiverColumn::Id])
                .update_column(OutboundReceiverColumn::ExternalOrganisationId)
                .update_column(OutboundReceiverColumn::ExternalProviderName)
                .update_column(OutboundReceiverColumn::SendError)
                .update_column(OutboundReceiverColumn::CommunicationStatus)
                .update_column(OutboundReceiverColumn::CommunicationResponse)
                .clone(),
        )
        .exec(self.connection)
        .await?;

        self.get_receiver(model.id()).await
    }
}
