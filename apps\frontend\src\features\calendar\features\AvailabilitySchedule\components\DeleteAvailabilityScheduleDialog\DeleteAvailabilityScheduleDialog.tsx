import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { But<PERSON>, notification } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"

import {
  AvailabilityScheduleFragmentFragment,
  namedOperations,
  useDeleteAvailabilityScheduleMutation,
} from "generated/graphql"

type DeleteAvailabilityScheduleDialogProps = {
  scheduleId: string
  providerId: string
  isOpen: boolean
  onClose: () => void
  schedules: AvailabilityScheduleFragmentFragment[]
}

const DeleteAvailabilityScheduleDialog = ({
  scheduleId,
  providerId,
  isOpen,
  onClose,
  schedules,
}: DeleteAvailabilityScheduleDialogProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [deleteSchedule] = useDeleteAvailabilityScheduleMutation()
  return (
    <Dialog
      title={t("Are you sure you want to delete this schedule?")}
      isOpen={isOpen}
      onClose={onClose}
      actions={
        <>
          <Button onClick={onClose}>{t("Cancel")}</Button>
          <Button
            onClick={() => {
              deleteSchedule({
                variables: {
                  input: {
                    id: scheduleId,
                  },
                },
                onCompleted: () => {
                  onClose()

                  // Redirect to the first schedule (the schedules list has been sorted previously)
                  const nextScheduleId: string | undefined = schedules.filter(
                    (s) => s.id !== scheduleId
                  )[0]?.id

                  navigate(
                    generatePath(RouteStrings.calendarSchedule, {
                      providerId,
                      scheduleId: nextScheduleId,
                    })
                  )
                },
                onError: (e) => {
                  notification.create({
                    status: "error",
                    message: e.message,
                  })
                },
                refetchQueries: [
                  namedOperations.Query.GetAvailabilitySchedules,
                ],
              })
            }}
            variant="filled"
            status="error"
          >
            {t("Delete")}
          </Button>
        </>
      }
    >
      {t("This action cannot be undone")}
    </Dialog>
  )
}

export default DeleteAvailabilityScheduleDialog
