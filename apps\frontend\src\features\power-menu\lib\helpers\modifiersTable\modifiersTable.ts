import { Tabbable } from "../../enums/Tabbable.enum"
import { Modifier } from "../../types/Modifier.type"

export const modifiersTable: { [key in Tabbable]: Partial<Modifier> } = {
  ClinicalCodingICD10: {},
  ClinicalCodingNCSP: {},
  ViewUser: { recentlyOpened: true },
  SelectSubject: { favorite: true, recentlyOpened: true },
  FormsContainer: {
    selfIsOwner: true,
    favorite: true,
    favoriteModelGlobal: true,
    recentlyOpened: true,
  },
  Forms: {
    selfIsOwner: true,
    favorite: true,
    favoriteModelGlobal: true,
    recentlyOpened: true,
  },
}
