import { useHotkeys } from "react-hotkeys-hook"

import { HotKeys } from "../hotkeys"
import { PowerMenuGroup } from "../types"

export const usePowerMenuHotKeys = (
  powerMenuGroups: PowerMenuGroup[],
  powerMenu: {
    isOpen: boolean
    open: () => void
    close: () => void
  }
) => {
  const { open, close, isOpen } = powerMenu

  // An object containing combo: execute fn
  const shortcutHandlers: { [combination: string]: () => void } = {
    [HotKeys.openPowerMenu]: open,
  }

  // Loop through each item of each group and add the execute function of each item
  // with a shortcut combo to the shortcutHandlers map
  powerMenuGroups
    .map((group) => group.items.map((item) => ({ ...item, groupId: group.id })))
    .flat()
    .forEach(({ shortcutCombination, execute }) => {
      if (shortcutCombination) shortcutHandlers[shortcutCombination] = execute
    })

  const shortcuts = Object.keys(shortcutHandlers).join(",")

  useHotkeys(
    shortcuts,
    (event, handler) => {
      event.preventDefault()
      shortcutHandlers[handler.key]?.()
      if (isOpen) close()
    },
    {
      enableOnTags: ["TEXTAREA", "INPUT", "SELECT"],
      enableOnContentEditable: true,
    },
    [shortcutHandlers]
  )
}
