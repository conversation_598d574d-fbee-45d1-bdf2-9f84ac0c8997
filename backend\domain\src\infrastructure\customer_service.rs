// Re-export from contracts
pub use leviosa_domain_contracts::services::{CustomerService, FeedbackData, FeedbackUser};

use std::fmt::{Display, Formatter};
use crate::errors::Error;
use serde::{Deserialize, Serialize};

pub mod mock;

// Legacy types for backward compatibility
#[derive(Debug, Serialize, Deserialize, Clone, Copy, PartialEq, Eq)]
pub enum FeedbackValues {
    POSITIVE,
    NEUTRAL,
    NEGATIVE,
}

impl Display for FeedbackValues {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            FeedbackValues::POSITIVE => write!(f, "Positive"),
            FeedbackValues::NEUTRAL => write!(f, "Neutral"),
            FeedbackValues::NEGATIVE => write!(f, "Negative"),
        }
    }
}

/// Legacy FeedbackData for backward compatibility
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct LegacyFeedbackData {
    pub user: LegacyFeedbackUser,
    pub feedback: FeedbackValues,
    pub comment: Option<String>,
    pub os: Option<String>,
    pub browser: Option<String>,
    pub browser_version: Option<String>,
    pub app_version: f64,
}

/// Legacy FeedbackUser for backward compatibility
#[derive(Debug, Clone, Serialize)]
pub struct LegacyFeedbackUser {
    pub user_id: crate::accounts::ProviderId,
    pub user_name: String,
    pub user_email: String,
    pub organization_id: crate::accounts::OrganisationId,
    pub organization_name: String,
}

impl Display for LegacyFeedbackData {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "Feedback: {}\n", self.feedback)?;

        writeln!(
            f,
            "Comment: \n{}\n",
            self.comment
                .as_deref()
                .unwrap_or("User did not leave a comment")
        )?;

        writeln!(f, "User:")?;
        writeln!(f, "id: {}", self.user.user_id)?;
        writeln!(f, "name: {}", self.user.user_name)?;
        writeln!(f, "email: {}\n", self.user.user_email)?;

        writeln!(f, "Organisation:")?;
        writeln!(f, "id: {}", self.user.organization_id)?;
        writeln!(f, "name: {}\n", self.user.organization_name)?;

        writeln!(f, "Environment:")?;
        writeln!(f, "os: {}", self.os.as_deref().unwrap_or("Unknown"))?;
        writeln!(
            f,
            "browser: {}",
            self.browser.as_deref().unwrap_or("Unknown")
        )?;
        writeln!(
            f,
            "browser version: {}",
            self.browser_version.as_deref().unwrap_or("Unknown")
        )?;
        write!(f, "app version: {}", self.app_version)
    }
}

/// Helper for converting between domain and contracts types
pub struct CustomerServiceAdapter;

impl CustomerServiceAdapter {
    /// Convert contracts result to domain result
    pub fn convert_result<T>(result: leviosa_domain_contracts::errors::Result<T>) -> Result<T, Error> {
        result.map_err(|e| e.into())
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use insta::assert_snapshot;
    use uuid::Uuid;

    #[test]
    fn feedback_display_test() {
        // arrange
        let feedback = LegacyFeedbackData {
            user: LegacyFeedbackUser {
                user_id: Uuid::parse_str("476bf939-1672-4d96-99b4-332c4f281d56")
                    .unwrap()
                    .into(),
                user_name: String::from("Test User"),
                user_email: String::from("Test Email"),
                organization_id: Uuid::parse_str("f6e4156c-ea55-4304-b433-1da84f6db5d6")
                    .unwrap()
                    .into(),
                organization_name: String::from("Test Organisation"),
            },
            feedback: FeedbackValues::POSITIVE,
            comment: Some(String::from("Test Comment")),
            os: Some(String::from("Test OS")),
            browser: Some(String::from("Test Browser")),
            browser_version: Some(String::from("Test Browser Version")),
            app_version: 0.0,
        };

        // act
        let result = feedback.to_string();

        // assert
        assert_snapshot!(result, @r###"
        Feedback: Positive

        Comment: 
        Test Comment

        User:
        id: 476bf939-1672-4d96-99b4-332c4f281d56
        name: Test User
        email: Test Email

        Organisation:
        id: f6e4156c-ea55-4304-b433-1da84f6db5d6
        name: Test Organisation

        Environment:
        os: Test OS
        browser: Test Browser
        browser version: Test Browser Version
        app version: 0
        "###);
    }

    #[test]
    fn feedback_display_test_only_required() {
        // arrange
        let feedback = LegacyFeedbackData {
            user: LegacyFeedbackUser {
                user_id: Uuid::parse_str("476bf939-1672-4d96-99b4-332c4f281d56")
                    .unwrap()
                    .into(),
                user_name: String::from("Test User"),
                user_email: String::from("Test Email"),
                organization_id: Uuid::parse_str("f6e4156c-ea55-4304-b433-1da84f6db5d6")
                    .unwrap()
                    .into(),
                organization_name: String::from("Test Organisation"),
            },
            feedback: FeedbackValues::POSITIVE,
            comment: None,
            os: None,
            browser: None,
            browser_version: None,
            app_version: 0.0,
        };

        // act
        let result = feedback.to_string();

        // assert
        assert_snapshot!(result, @r###"
        Feedback: Positive

        Comment: 
        User did not leave a comment

        User:
        id: 476bf939-1672-4d96-99b4-332c4f281d56
        name: Test User
        email: Test Email

        Organisation:
        id: f6e4156c-ea55-4304-b433-1da84f6db5d6
        name: Test Organisation

        Environment:
        os: Unknown
        browser: Unknown
        browser version: Unknown
        app version: 0
        "###);
    }
}
