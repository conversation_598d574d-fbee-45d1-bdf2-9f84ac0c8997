import { json, MetaFunction, redirect } from "@remix-run/node"
import { Outlet, useLoaderData } from "@remix-run/react"
import * as Sentry from "@sentry/remix"

import Header from "app/components/Header/Header"
import { getSession } from "app/sessions"

export const meta: MetaFunction = () => {
  return [{ title: "Leviosa Check-in" }]
}

export async function loader({ request }: { request: Request }) {
  try {
    const session = await getSession(request.headers.get("Cookie"))

    const accessToken = session.get("accessToken")
    const logoUrl = session.get("logoUrl")

    if (!accessToken) {
      return redirect("/")
    }

    return json({ logoUrl })
  } catch (error) {
    Sentry.addBreadcrumb({
      level: "error",
      message: "Could not load session",
      data: {
        error,
      },
    })
    throw new Error("Something went wrong logging in")
  }
}

export default function Kiosk() {
  const data = useLoaderData<typeof loader>()

  return (
    <>
      <Header logoUrl={data.logoUrl} />
      <Outlet />
    </>
  )
}
