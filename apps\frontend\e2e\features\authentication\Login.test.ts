import { test } from "@playwright/test"

import { CheckUrl } from "../utils/testUtils"

test.describe("Login Page", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the login page
    await page.goto("http://localhost:3000/login")
    // Log out before each test case (this is easier than manipulating the auth.setup and playwright config to prevent the initial log in)
    const powerMenu = page.locator('[data-testid="power-menu-button"]')
    if (powerMenu) {
      // Open power menu
      await page.click('[data-testid="power-menu-button"]')

      // Click the logout button
      await page.click("#LogOut")

      // Check if the URL is correct after logging out
      await CheckUrl(page, "login")
    }
  })

  test("should allow valid users to log in", async ({ page }) => {
    // Fill in the login form with valid credentials
    await page.fill("[name='email']", "<EMAIL>")
    await page.fill("[name='password']", "somepassword")
    await page.click('button:has-text("Log In")')

    // Check if the URL is correct after logging in
    await CheckUrl(page, "")
  })

  test("should not allow invalid users to log in", async ({ page }) => {
    // Fill in the login form with invalid credentials
    await page.fill("[name='email']", "   ") // Empty email
    await page.fill("[name='password']", "somepassword")
    await page.click('button:has-text("Log In")')

    // Check if the URL is correct after logging in
    await CheckUrl(page, "login")
  })

  // test for logout
  test("should allow users to log out", async ({ page }) => {
    // Arrange
    // Must log in before testing the logout function
    await page.fill("[name='email']", "<EMAIL>")
    await page.fill("[name='password']", "somepassword")
    await page.click('button[type="submit"]')

    // Check if the URL is correct after logging in
    await CheckUrl(page, "")

    // Act
    // Open power menu
    await page.click('[data-testid="power-menu-button"]')

    // Click the logout button
    await page.click("#LogOut")

    // Assert
    // Check if the URL is correct after logging out
    await CheckUrl(page, "login")
  })
})
