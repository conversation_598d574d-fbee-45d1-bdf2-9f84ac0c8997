import {
  AvailabilityScheduleFragmentFragment,
  AvailabilityScheduleStatus,
} from "generated/graphql"

import { sortSchedules } from "./sortSchedules"

const mockSchedule: AvailabilityScheduleFragmentFragment = {
  __typename: "AvailabilitySchedule",
  id: "1",
  fromDate: "2022-01-01",
  toDate: "2022-01-02",
  provider: {
    __typename: "Provider",
    id: "1",
    name: "<PERSON>",
  },
  serviceTypeAvailabilities: [],
  status: AvailabilityScheduleStatus.Draft,
}

beforeEach(() => {
  vi.useFakeTimers()
  const date = new Date("2024-12-24")
  vi.setSystemTime(date)
})

afterEach(() => {
  // Put things back the way you found it.
  vi.useRealTimers()
})

describe("sortSchedules", () => {
  test("returns a negative value when dateA is before dateB", () => {
    const dateA = "2022-01-01"
    const dateB = "2022-02-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeLessThan(0)
  })

  test("returns a positive value when dateA is after dateB", () => {
    const dateA = "2022-02-01"
    const dateB = "2022-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeGreaterThan(0)
  })

  test("returns 0 when dateA is equal to dateB", () => {
    const dateA = "2022-01-01"
    const dateB = "2022-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBe(0)
  })

  test("returns a positive value when dateA is in the past and dateB is in the future", () => {
    const dateA = "2021-01-01"
    const dateB = "2027-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeGreaterThan(0)
  })

  test("returns a negative value when dateA is in the future and dateB is in the past", () => {
    const dateA = "2027-01-01"
    const dateB = "2021-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeLessThan(0)
  })

  test("returns a negative value when both dates are in the past and dateA is before dateB", () => {
    const dateA = "2021-01-01"
    const dateB = "2022-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeLessThan(0)
  })

  test("returns a positive value when both dates are in the past and dateA is after dateB", () => {
    const dateA = "2022-01-01"
    const dateB = "2021-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeGreaterThan(0)
  })

  test("returns a positive value when both dates are in the future and dateA is after dateB", () => {
    const dateA = "2029-01-01"
    const dateB = "2028-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeGreaterThan(0)
  })

  test("returns a negative value when both dates are in the future and dateA is before dateB", () => {
    const dateA = "2028-01-01"
    const dateB = "2029-01-01"

    const scheduleA = { ...mockSchedule, toDate: dateA }
    const scheduleB = { ...mockSchedule, toDate: dateB }

    expect(sortSchedules(scheduleA, scheduleB)).toBeLessThan(0)
  })
})
