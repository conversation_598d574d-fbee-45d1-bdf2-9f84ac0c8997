import { ComboboxStore } from "@ariakit/react"
import { useEffect } from "react"

import { roundUpToNearestQuarterMinutes } from "./roundUpToNearestQuarterMinutes"

export const useScrollComboboxItem = (
  combobox: ComboboxStore,
  idString: string
) => {
  const comboboxValue = combobox.useState("value")
  const isOpen = combobox.useState("open")

  useEffect(() => {
    if (!isOpen) return

    const roundedFromTime = roundUpToNearestQuarterMinutes(comboboxValue)

    if (!roundedFromTime) return

    const fromTimeId = `${idString}${roundedFromTime}`

    combobox.setActiveId(fromTimeId)

    const item = document.getElementById(fromTimeId)
    item?.scrollIntoView()
  }, [isOpen, comboboxValue])
}
