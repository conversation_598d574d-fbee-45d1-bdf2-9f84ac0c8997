import { ApolloError } from "@apollo/client"
import { VisuallyHidden } from "@ariakit/react"
import { useTranslation } from "react-i18next"

import { ComboboxItem, SelectItem } from "components/Ariakit"

type SelectPopoverFallbackProps = {
  children: React.ReactNode
  loading: boolean
  error?: ApolloError
  noMatchesNoSearch: boolean
  hasNoMatches: boolean | ""
}

export const SelectPopoverFallback = ({
  children,
  loading,
  error,
  noMatchesNoSearch,
  hasNoMatches,
}: SelectPopoverFallbackProps) => {
  const { t } = useTranslation()

  return (
    <>
      <SelectItem
        value=""
        render={<ComboboxItem render={<VisuallyHidden />}></ComboboxItem>}
      />
      {loading && (
        <SelectItem value="loading" disabled>
          {t("loading")}
        </SelectItem>
      )}
      {error && (
        <SelectItem value="error" disabled>
          {t("Failed to load data. Please reload to try again.")}
        </SelectItem>
      )}
      {noMatchesNoSearch && (
        <SelectItem value="no-results" disabled>
          {t("Type to search…")}
        </SelectItem>
      )}
      {hasNoMatches && (
        <SelectItem value="no-results" disabled>
          {t("No results found")}
        </SelectItem>
      )}
      {children}
    </>
  )
}
