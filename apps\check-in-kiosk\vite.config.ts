import { vitePlugin as remix } from "@remix-run/dev"
import { installGlobals } from "@remix-run/node"
import crypto from "crypto"
import { defineConfig } from "vite"
import svgr from "vite-plugin-svgr"
import tsconfigPaths from "vite-tsconfig-paths"

installGlobals()

export default defineConfig({
  plugins: [
    remix({
      appDirectory: __dirname + "/app",
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
      },
    }),
    tsconfigPaths(),
    svgr(),
  ],
  css: {
    modules: {
      localsConvention: "camelCase",
      generateScopedName: (name, filename, css) => {
        const componentName = filename
          .replace(/\.\w+$/, "")
          .replace(".module", "")
          .split("/")
          .pop()

        // Generate hash
        const hash = crypto
          .createHash("md5")
          .update(css)
          .digest("base64")
          .substring(0, 5)
          .replace(/[^a-z,A-Z,0-9]/g, "_")

        return `${componentName}_${name}_${hash}`
      },
    },
  },
})
