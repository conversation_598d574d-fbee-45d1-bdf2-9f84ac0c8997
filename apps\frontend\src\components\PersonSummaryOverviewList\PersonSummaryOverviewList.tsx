import { useTranslation } from "react-i18next"

import { genderIconNames } from "components/Icon/GenderIcon"
import Icon from "components/Icon/Icon"
import { useFetchSubjectLocation } from "hooks/useFetchSubjectLocation"
import { PersonSummaryOverview, PersonSummaryOverviewItem } from "ui"
import { PersonSummarySelectionItem } from "ui/components/PersonSummary/PersonSummary"
import parseAgeText from "utils/parseAgeText"

import {
  DietaryAllowanceId,
  GenderId,
  namedOperations,
  Subject,
  useCreateSubjectHealthProfileMutation,
  useUpdateEncounterMutation,
  useUpdateSubjectHealthProfileMutation,
} from "generated/graphql"

import styles from "./PersonSummaryOverviewList.module.css"

type PersonSummaryOverviewListProps = Pick<
  Subject,
  "age" | "birthDate" | "gender"
> & {
  dietaryAllowanceId?: DietaryAllowanceId
  healthProfileId?: string
  locationId?: string
  className?: string
  encounterId?: string
}

const genderAgeText = {
  [GenderId.Female]: "Female, age:",
  [GenderId.Male]: "Male, age:",
  [GenderId.Unknown]: "Unknown gender, age:",
  [GenderId.Other]: "Other gender, age:",
}

export const PersonSummaryOverviewList = ({
  age,
  birthDate,
  dietaryAllowanceId,
  gender,
  healthProfileId,
  locationId,
  className = "",
  encounterId,
}: PersonSummaryOverviewListProps) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "DietaryAllowance",
  })

  const ageString = parseAgeText(age)

  const { data: locationsList } = useFetchSubjectLocation()

  const [updateEncounter] = useUpdateEncounterMutation({
    refetchQueries: [namedOperations.Query.GetSubjectSummary],
  })

  const [createSubjectHealthProfile] = useCreateSubjectHealthProfileMutation()
  const [updateSubjectHealthProfile] = useUpdateSubjectHealthProfileMutation()

  // Reverse the order of the dietary restrictions to have None at the top
  const dietaryRestrictions = Object.values(DietaryAllowanceId).reverse()

  const dietaryRestrictionsOptions = dietaryRestrictions.map((restriction) => {
    if (restriction === DietaryAllowanceId.Unset) {
      return {
        label: "None",
        value: restriction.toString(),
      }
    }
    return {
      label: tEnum(restriction),
      value: restriction.toString(),
    }
  })

  const onUpdateSubjectLocation = (updatedLocationId: string) => {
    if (!encounterId) return

    updateEncounter({
      variables: {
        id: encounterId,
        input: {
          locationId: updatedLocationId,
        },
      },
    })
  }

  const onUpdateDietaryAllowance = (
    updatedDietaryAllowance: DietaryAllowanceId
  ) => {
    // Since we are using the enum values to create the options for the dropdown we can be sure that the value is a valid enum value

    if (!encounterId) return

    if (healthProfileId) {
      updateSubjectHealthProfile({
        variables: {
          input: {
            encounterId,
            dietaryAllowanceId: updatedDietaryAllowance,
          },
        },
      })

      return
    }

    createSubjectHealthProfile({
      variables: {
        input: {
          encounterId,
          dietaryAllowanceId: updatedDietaryAllowance,
        },
      },
    })
  }

  return (
    <PersonSummaryOverview className={className}>
      <PersonSummaryOverviewItem
        title={new Date(birthDate).toDateString()}
        icon={
          <Icon
            name={genderIconNames[gender]}
            className={styles.overviewItemIcon}
          />
        }
        label={genderAgeText[gender]}
      >
        {t(...ageString)}
      </PersonSummaryOverviewItem>
      <PersonSummaryOverviewItem
        icon={
          <Icon name={"scales-2-line"} className={styles.overviewItemIcon} />
        }
        label="Weight"
      >
        –&thinsp;kg
      </PersonSummaryOverviewItem>
      <PersonSummaryOverviewItem
        icon={<Icon name={"ruler-line"} className={styles.overviewItemIcon} />}
        label="Height"
      >
        –&thinsp;cm
      </PersonSummaryOverviewItem>

      {encounterId && (
        <PersonSummarySelectionItem
          icon="hospital-line"
          label="Location"
          options={locationsList}
          value={locationId}
          onUpdate={(value) => {
            onUpdateSubjectLocation(value as string)
          }}
        />
      )}

      {encounterId && (
        <PersonSummarySelectionItem
          icon="restaurant-line"
          label="Dietary Restrictions"
          value={
            dietaryAllowanceId === DietaryAllowanceId.Unset
              ? undefined
              : dietaryAllowanceId
          }
          options={dietaryRestrictionsOptions}
          onUpdate={(value) => {
            onUpdateDietaryAllowance(value as DietaryAllowanceId)
          }}
        />
      )}
    </PersonSummaryOverview>
  )
}
