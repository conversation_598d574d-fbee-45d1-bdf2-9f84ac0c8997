//! Text message integration service contract

use async_trait::async_trait;
use leviosa_domain_types::SubjectId;
use crate::errors::Result;

/// Contract for text message integration service
#[async_trait]
#[mockall::automock]
pub trait TextMessageIntegration: Send + Sync {
    /// Send a text message to a subject
    async fn send_message(
        &self,
        subject_id: SubjectId,
        message: &str,
        sender_name: Option<&str>,
    ) -> Result<()>;

    /// Send a text message to a phone number
    async fn send_message_to_phone(
        &self,
        phone_number: &str,
        message: &str,
        sender_name: Option<&str>,
    ) -> Result<()>;

    /// Check if text messaging is available for a subject
    async fn is_available_for_subject(&self, subject_id: SubjectId) -> Result<bool>;
}

/// Simple mock implementation for testing
pub struct SimpleTextMessageMock;

impl SimpleTextMessageMock {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl TextMessageIntegration for SimpleTextMessageMock {
    async fn send_message(
        &self,
        _subject_id: SubjectId,
        _message: &str,
        _sender_name: Option<&str>,
    ) -> Result<()> {
        // Mock implementation - just log and return success
        tracing::info!("Mock: Sending text message to subject");
        Ok(())
    }

    async fn send_message_to_phone(
        &self,
        _phone_number: &str,
        _message: &str,
        _sender_name: Option<&str>,
    ) -> Result<()> {
        // Mock implementation - just log and return success
        tracing::info!("Mock: Sending text message to phone");
        Ok(())
    }

    async fn is_available_for_subject(&self, _subject_id: SubjectId) -> Result<bool> {
        // Mock implementation - always available
        Ok(true)
    }
}

/// Empty implementation that does nothing
pub struct EmptyTextMessageIntegration;

impl EmptyTextMessageIntegration {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl TextMessageIntegration for EmptyTextMessageIntegration {
    async fn send_message(
        &self,
        _subject_id: SubjectId,
        _message: &str,
        _sender_name: Option<&str>,
    ) -> Result<()> {
        // Empty implementation - do nothing
        Ok(())
    }

    async fn send_message_to_phone(
        &self,
        _phone_number: &str,
        _message: &str,
        _sender_name: Option<&str>,
    ) -> Result<()> {
        // Empty implementation - do nothing
        Ok(())
    }

    async fn is_available_for_subject(&self, _subject_id: SubjectId) -> Result<bool> {
        // Empty implementation - not available
        Ok(false)
    }
}
