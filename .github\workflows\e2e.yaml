name: E2E
on:
  schedule:
    - cron: "0 6 * * *"
  workflow_dispatch:

jobs:
  e2e:
    name: E2E
    timeout-minutes: 60
    runs-on: ubuntu-latest
    env:
      CARGO_INCREMENTAL: 0
    services:
      database:
        image: postgres:15.1
        env:
          POSTGRES_PASSWORD: leviosa
          POSTGRES_DB: leviosa
        ports:
          - 5432:5432
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: 1.87.0
          override: true

      - name: Set up cargo cache
        uses: actions/cache/restore@v3
        continue-on-error: false
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-e2e-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: ${{ runner.os }}-cargo-e2e-

      - name: Run Db Migrations
        env:
          DATABASE_URL: postgres://postgres:leviosa@localhost:5432/leviosa
          LEVIOSA_USER_PASSWORD: leviosa
        run: cargo run -p migration -- up

      - name: Build Backend
        run: cargo build -p leviosa-api

      - name: Run Backend
        env:
          PORT: 4001
          PG_HOST: localhost
          PG_PORT: 5432
          PG_USER: leviosa
          PG_PASSWORD: leviosa
          PG_DB: leviosa
          PG_MIGRATION_USER: postgres
          PG_MIGRATION_PASSWORD: leviosa
          LEVIOSA_USER_PASSWORD: leviosa
          NHI_SERVICE_DB_PASSWORD: nhi-service
          TOKEN_SECRET: secret
          ADMIN_KEY: key
          EMIT_SCHEMA_FILE: true
          DATABASE_URL: postgres://postgres:leviosa@localhost:5432/leviosa
          LOGS: true
          SALT_COST: 4
          S3_ENDPOINT: http://localhost:9000
          FILE_STORAGE_BUCKET: leviosa
          S3_REGION: eu-west-1
          REFRESH_TOKEN_EXPIRATION_TIME: 1800
          JWT_EXPIRATION_TIME: 300
          JWT_PRIVATE_KEY_PATH: backend/api/dev.private.key
          JWT_PUBLIC_KEY_PATH: backend/api/dev.public.pem
          SENTRY_DSN: mock
          SENTRY_ENVIRONMENT: ci_tests
          SENTRY_SAMPLE_RATE: 0.0
          SENTRY_TRACES_SAMPLE_RATE: 0.0
          SENTRY_PROFILES_SAMPLE_RATE: 0.0
          JIRA_HOST: mock
          EMAIL_SENDER: mock
          PRESCRIPTION_API: mock
          ORACLE_API: mock
          ORACLE_API_ENDPOINT: http://localhost:3200
          LOG_LEVEL: info
          LOGS_STRUCTURED: false
          ELECTRONIC_ID_BASE_URL: mock
          NOTIFICATION_SERVICE_INTEGRATION: mock
          EXTERNAL_ORGANISATION_INTEGRATION: mock
        run: cargo run -p leviosa-api &

      - name: Wait for Backend
        run: sleep 2

      - name: Set up mss.localhost
        run: |
          cp /etc/hosts ./hosts.bak
          echo '127.0.0.1       mss.localhost' >> ./hosts.bak
          sudo mv ./hosts.bak /etc/hosts
          sudo cat /etc/hosts

      - name: Healthcheck Backend
        run: |
          printf "curl 127.0.0.1:4001/health:\n" 
          curl 127.0.0.1:4001/health
          printf "\ncurl localhost:4001/health:\n" 
          curl localhost:4001/health
          printf "\ncurl mss.localhost:4001/health:\n" 
          curl mss.localhost:4001/health

      - name: NPM Install
        run: npm ci

      - name: NPM Run Seed for end to end tests
        run: npm run seedE2E

      - name: Start Frontend
        run: npm run start:fe &

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps chromium

      - name: Run Playwright tests
        run: |
          cd apps/frontend
          npx playwright test --config=playwright.config.ts --project=chromium --timeout=45000

      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: e2e
          path: |
            apps/frontend/e2e/.reports/index.html            
            apps/frontend/e2e/.reports/data/
            apps/frontend/e2e/.reports/trace/
          retention-days: 10
