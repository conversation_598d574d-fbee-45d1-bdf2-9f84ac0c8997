import { ApolloError } from "@apollo/client"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"

import Panel from "components/Panel/Panel"
import { useAuth } from "features/authentication/AuthProvider"
import { FormGrid } from "ui"
import Button from "ui/components/Button/Button"
import { LoadingIcon } from "ui/components/Loading/LoadingIcon"

import {
  SessionData,
  useLogInWithElectronicIdMutation,
} from "generated/graphql"

import styles from "../../Authentication.module.css"
import ElectronicIdLoginExistingSession from "./ElectronicIdLoginExistingSession"
import ElectronicIdLoginForm from "./ElectronicIdLoginForm"

const getErrorMessage = (error?: ApolloError) => {
  if (!error) return null

  const firstError = error.graphQLErrors[0]

  if (!firstError) return null

  switch (firstError.extensions?.code) {
    case "ERR_NOT_FOUND":
      return (
        <>
          No user found with this number. <br /> If you believe this is a
          mistake, please contact your organisation's administrator.
        </>
      )
    default:
      return (
        <>
          We encountered an error in the authentication process. <br /> Please
          try again. If the problem persists, please contact support at{" "}
          <a href="mailto:<EMAIL>" className={styles.link}>
            <EMAIL>
          </a>
        </>
      )
  }
}
type ElectronicIdLoginProps = {
  session: SessionData | null
}
export default function ElectronicIdLogin({ session }: ElectronicIdLoginProps) {
  // store session in state so we know what the initial value was
  // this prevents the form from being reset i.e. flickering when the session
  // changes before isAuthorized is set
  const [showExistingSessionForm] = useState(!!session)

  const { authenticate } = useAuth()
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "auth" })

  const [logInWithElectronicId, { loading, error }] =
    useLogInWithElectronicIdMutation()

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.target as HTMLFormElement)
    const phoneNumber =
      session?.phoneNumber || (formData.get("phone") as string)

    if (!phoneNumber) {
      return
    }

    const { data } = await logInWithElectronicId({
      variables: {
        input: {
          phoneNumber,
        },
      },
    })

    if (!data) return

    const { accessToken, refreshToken } = data.logInWithElectronicId.tokens
    authenticate({ accessToken, refreshToken })
  }

  const errorMessage = getErrorMessage(error)

  const commonChildren = (
    <>
      {loading && (
        <Panel status="info" className={styles.panel}>
          Please confirm authentication <br /> on your phone
        </Panel>
      )}

      {error && (
        <Panel status="error" className={styles.panel}>
          {errorMessage}
        </Panel>
      )}

      <Button
        className={styles.submitButton}
        type="submit"
        size="large"
        variant="filled"
        loading={loading}
        {...(loading && { iconEnd: <LoadingIcon /> })}
      >
        {tRoutes("doLoginWithElectronicId")}
      </Button>
    </>
  )

  return (
    <FormGrid onSubmit={handleSubmit} className={styles.wrap}>
      {showExistingSessionForm && session ? (
        <ElectronicIdLoginExistingSession {...session}>
          {commonChildren}
        </ElectronicIdLoginExistingSession>
      ) : (
        <ElectronicIdLoginForm>{commonChildren}</ElectronicIdLoginForm>
      )}
    </FormGrid>
  )
}
