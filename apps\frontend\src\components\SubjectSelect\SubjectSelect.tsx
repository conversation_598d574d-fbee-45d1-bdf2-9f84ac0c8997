import { startTransition, useState, useEffect } from "react"
import { useTranslation } from "react-i18next"

import { formatPersonaId } from "@leviosa/utils"

import { ComboboxProvider, SelectArrow, SelectLabel } from "components/Ariakit"
import { Select, SelectProvider } from "components/Ariakit/Select/Select"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import MenuListItem from "components/MenuListItem/MenuListItem"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { Button, Text } from "ui"

import { useGetSubjectQuery } from "generated/graphql"

import styles from "./SubjectSelect.module.css"
import { SubjectSelectPopover } from "./SubjectSelectPopover"

export type SubjectSelectOption = {
  id: string
  name: string
  personaId: string
}

export type SubjectSelectProps = {
  required?: boolean
  name?: string
  isClearable?: boolean
  placeholder?: string
  selectedSubjectId?: string
  onChange?: (value: string | null) => void
}

export default function SubjectSelect({
  required,
  name = "subject",
  isClearable = false,
  placeholder = "Select subject",
  selectedSubjectId: subjectId,
  onChange,
}: SubjectSelectProps) {
  const { t } = useTranslation()
  const [searchValue, setSearchValue] = useState("")
  const { globalData } = useGlobalState()
  const { recentSubjectInteractions } = globalData.actor
  const [selectedSubjectId, setSelectedSubjectId] = useState<
    string | undefined
  >(subjectId)

  useEffect(() => {
    setSelectedSubjectId(subjectId)
  }, [subjectId])

  const { data: subjectData } = useGetSubjectQuery({
    variables: selectedSubjectId ? { id: selectedSubjectId } : undefined,
    skip: !selectedSubjectId,
  })

  const selectedSubject = subjectData?.subject
    ? {
        id: subjectData.subject.id,
        name: subjectData.subject.name,
        personaId: subjectData.subject.personaId,
      }
    : undefined

  const recentSubjectOptions: SubjectSelectOption[] =
    recentSubjectInteractions.map((interaction) => ({
      id: interaction.subject.id,
      name: interaction.subject.name,
      personaId: interaction.subject.personaId,
    }))

  const handleSelectValue = (value: string) => {
    if (!value && !required) {
      setSelectedSubjectId(undefined)
      onChange?.(null)
      return
    }

    setSelectedSubjectId(value)
    onChange?.(value)
  }

  const handleClear = () => {
    setSelectedSubjectId(undefined)
    onChange?.(null)
  }

  const hasValue = !!selectedSubject
  const showClearButton = isClearable && hasValue

  return (
    <ComboboxProvider
      resetValueOnHide
      setValue={(value) => {
        startTransition(() => {
          setSearchValue(value)
        })
      }}
    >
      <SelectProvider value={selectedSubjectId} setValue={handleSelectValue}>
        <SelectLabel>
          {t("Subject")}
          {required && " *"}
        </SelectLabel>
        <div className={styles.selectContainer}>
          <Select required={required} name={name}>
            <MenuListItem
              className={styles.menuListItem}
              subContent={
                selectedSubject ? (
                  <PiiSensitive>
                    {formatPersonaId(selectedSubject.personaId)}
                  </PiiSensitive>
                ) : undefined
              }
              subContentClassName={styles.personaId}
            >
              {selectedSubject ? (
                <PiiSensitive>{selectedSubject.name}</PiiSensitive>
              ) : (
                <Text secondary>{placeholder}</Text>
              )}
            </MenuListItem>
            <SelectArrow />
          </Select>
          {showClearButton && (
            <Button
              icon={<Icon name="close-line" />}
              className={styles.clearButton}
              onClick={handleClear}
              variant="clear"
            />
          )}
        </div>

        <SubjectSelectPopover
          searchValue={searchValue}
          selectedSubjectId={selectedSubjectId}
          recentSubjectOptions={recentSubjectOptions}
        />
      </SelectProvider>
    </ComboboxProvider>
  )
}
