//! PDF generator service contract

use crate::errors::Result;
use async_trait::async_trait;
use leviosa_domain_types::*;

/// Contract for PDF generation service
#[async_trait]
#[mockall::automock]
pub trait PdfGenerator: Send + Sync {
    /// Generate a medical certificate PDF
    async fn medical_certificate(
        &self,
        id: MedicalCertificateId,
        token: &str,
    ) -> Result<Vec<u8>>;

    /// Generate a free text document PDF
    async fn free_text_document(
        &self,
        id: MedicalCertificateId,
        token: &str,
    ) -> Result<Vec<u8>>;

    /// Generate an invoice PDF
    async fn invoice(&self, id: InvoiceId, token: &str) -> Result<Vec<u8>>;
}
