.wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: start;
  column-gap: 16px;
}

.timePickers {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 16px;
  margin-bottom: 17px;
}

.timePickers[data-is-edit="true"] {
  margin-bottom: 17px;
}

.minutesInput.minutesInput {
  height: 44px;
}

.intervalWrap {
  display: grid;
  grid-template-columns: 193px auto;
  align-items: flex-start;
  column-gap: 15px;
}

.intervalWrap[data-has-interval="true"] {
  grid-template-columns: 193px 193px auto;
}

.intervalWrap[data-is-edit="true"] {
  grid-template-columns: auto;
}

.hiddenInput {
  visibility: hidden;
  height: 0;
}
