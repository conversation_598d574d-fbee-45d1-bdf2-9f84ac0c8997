import { ComboboxStore } from "@ariakit/react"
import { createContext, ReactNode, useContext } from "react"

import { useEventInstanceState } from "./useEventInstanceState"

const EventInstanceContext = createContext<
  ReturnType<typeof useEventInstanceState>
>({
  /* eslint-disable-next-line @typescript-eslint/no-empty-function */
  setFromDateState: () => {},
  /* eslint-disable-next-line @typescript-eslint/no-empty-function */
  setToDateState: () => {},
  fromDateState: "",
  toDateState: "",
  comboboxFromTime: {} as ComboboxStore,
  comboboxToTime: {} as ComboboxStore,
  // Input values are set to 1970-01-01T00:00:00.000Z and 1970-01-01T00:01:00.000Z to avoid
  // backend error of invalid isAvailable property. Ideally backend should handle null values for this values
  fromDateTimeLocal: "1970-01-01T00:00:00.000Z",
  toDateTimeLocal: "1970-01-01T00:01:00.000Z",
})

export const useEventInstance = () => {
  return useContext(EventInstanceContext)
}

type EventInstanceProviderProps = {
  children: ReactNode
  fromDate?: string
  toDate?: string
}

export const EventInstanceProvider = ({
  children,
  fromDate,
  toDate,
}: EventInstanceProviderProps) => {
  const eventInstanceForm = useEventInstanceState(fromDate, toDate)

  return (
    <EventInstanceContext.Provider value={eventInstanceForm}>
      {children}
    </EventInstanceContext.Provider>
  )
}
