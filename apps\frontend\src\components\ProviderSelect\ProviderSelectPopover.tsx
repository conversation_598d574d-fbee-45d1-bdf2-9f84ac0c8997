import { matchSorter } from "match-sorter"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  SelectItem,
  SelectPopover,
} from "components/Ariakit"
import { SelectPopoverFallback } from "components/SelectPopoverFallback/SelectPopoverFallback"

import { useGetProvidersQuery } from "generated/graphql"

type ProviderSelectPopoverProps = {
  searchValue: string
} & React.ComponentProps<typeof SelectPopover>

export const ProviderSelectPopover = ({
  searchValue,
  ...rest
}: ProviderSelectPopoverProps) => {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })

  const { data, loading, error } = useGetProvidersQuery()

  const allOptions = data?.providers || []

  const matches = useMemo(() => {
    return matchSorter(allOptions, searchValue, {
      keys: ["name", "specialty", "nameInitials"],
    })
  }, [searchValue, allOptions])

  const noMatchesNoSearch = !searchValue && !matches.length && !loading

  const hasNoMatches = !matches.length && searchValue && !loading

  const { t } = useTranslation()

  return (
    <SelectPopover {...rest}>
      <Combobox autoSelect placeholder={t("Search...")} />
      <ComboboxList>
        <SelectPopoverFallback
          loading={loading}
          noMatchesNoSearch={noMatchesNoSearch}
          hasNoMatches={hasNoMatches}
          error={error}
        >
          {matches.map(({ id, name, specialty }) => (
            <SelectItem
              key={id}
              value={id}
              render={
                <ComboboxItem subContent={tEnum(specialty)}>
                  {name}
                </ComboboxItem>
              }
            />
          ))}
        </SelectPopoverFallback>
      </ComboboxList>
    </SelectPopover>
  )
}
