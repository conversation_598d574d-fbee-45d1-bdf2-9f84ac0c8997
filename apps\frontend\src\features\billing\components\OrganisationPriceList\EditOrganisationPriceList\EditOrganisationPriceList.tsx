import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link, useNavigate } from "react-router-dom"
import { v4 as uuid } from "uuid"
import { z } from "zod"

import Icon from "components/Icon/Icon"
import { logException } from "lib/sentry/sentry"
import { RouteStrings } from "routes/RouteStrings"
import { Button, notification } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import { getValueWithoutThousandSeparator } from "utils/getValueWithoutThousandSeparator"

import {
  BillingCodeType,
  useCreateBillingCodeClinicSpecificMutation,
  useDeleteBillingCodeClinicSpecificMutation,
  useUpdateBillingCodeClinicSpecificMutation,
} from "generated/graphql"

import { CenteredLayout } from "../../CenteredLayout/CenteredLayout"
import { SearchInput } from "../../SearchInput/SearchInput"
import {
  PriceList,
  PriceListTypeValues,
  useOrganisationPriceList,
} from "../OrganisationPriceList.context"
import {
  OrganisationPriceListEditTable,
  priceItemKeys,
} from "../OrganisationPriceListEditTable/OrganisationPriceListEditTable"
import { OrganisationPriceListHeader } from "../OrganisationPriceListHeader/OrganisationPriceListHeader"
import priceListStyles from "../PriceList.module.css"
import styles from "./EditOrganisationPriceList.module.css"

const FormTypeSchema = z
  .object({
    id: z.string(),
    code: z.string(),
    title: z.string(),
    units: z.string(),
    unitPrice: z.string(),
    currency: z.string().optional(),
    vat: z.string(),
  })
  .transform((data) => ({
    ...data,
    units: getValueWithoutThousandSeparator(data.units),
    unitPrice: getValueWithoutThousandSeparator(data.unitPrice),

    // COMEBACK set separators depending on language
    vat: parseFloat(data.vat.replace(",", ".")) || 0,
  }))

const EditOrganisationPriceList = () => {
  const { t } = useTranslation()
  const [showCancelModal, setShowCancelModal] = useState(false)

  const [priceListForCreate, setPriceListForCreate] = useState<PriceList>([])
  const [selectedPriceItemIds, setSelectedPriceItemIds] = useState<string[]>([])
  const [archivedPriceItemIds, setArchivedPriceItemIds] = useState<string[]>([])

  const {
    priceList,
    // currentPage,
    // totalPages,
    // loading,
    resetPriceList,
    refetchPriceList,
  } = useOrganisationPriceList()

  const navigate = useNavigate()

  const [createBillingCodeClinicSpecific] =
    useCreateBillingCodeClinicSpecificMutation()

  const [updateBillingCodeClinicSpecific] =
    useUpdateBillingCodeClinicSpecificMutation()

  const [deleteBillingCodeClinicSpecific] =
    useDeleteBillingCodeClinicSpecificMutation()

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    const formData = new FormData(event.currentTarget)

    //getting all types. Each element in table has type value
    const getAllTypes = formData.getAll("type") as PriceListTypeValues[]

    const priceItemsForCreate = []
    const priceItemsForEdit = []

    const convertToFormType = (data: { [key: string]: FormDataEntryValue }) => {
      try {
        const formType = FormTypeSchema.parse(data)
        return formType
      } catch (error) {
        logException(error)
        return null
      }
    }

    for (let i = 0; i < getAllTypes.length; i++) {
      if (getAllTypes[i] !== "CREATE" && getAllTypes[i] !== "EDIT") continue

      const item: { [key: string]: FormDataEntryValue } = {}
      for (const key of priceItemKeys) {
        item[key] = formData.getAll(key)[i]
      }

      const formItem = convertToFormType(item)

      if (!formItem) return

      if (getAllTypes[i] === "CREATE") {
        priceItemsForCreate.push(formItem)
      }

      if (getAllTypes[i] === "EDIT") {
        priceItemsForEdit.push(formItem)
      }
    }

    if (
      priceItemsForCreate.some((item) => item.title === "") ||
      priceItemsForEdit.some((item) => item.title === "")
    ) {
      notification.create({
        message: t("Item Name cannot be empty"),
        status: "error",
      })

      return
    }

    // id value is not used for create billing codes
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const createPromises = priceItemsForCreate.map(({ id, ...rest }) =>
      createBillingCodeClinicSpecific({
        variables: {
          input: {
            ...rest,
          },
        },
      })
    )

    const updatePromises = priceItemsForEdit.map((item) =>
      updateBillingCodeClinicSpecific({
        variables: {
          input: {
            ...item,
          },
        },
      })
    )

    const deletePromises = archivedPriceItemIds.map((id) =>
      deleteBillingCodeClinicSpecific({
        variables: {
          deleteBillingCodeClinicSpecificId: id,
        },
      })
    )

    await Promise.all([...createPromises, ...updatePromises, ...deletePromises])

    refetchPriceList()

    navigate(generatePath(RouteStrings.organisationPriceList))
  }

  const handleAddNewLine = () => {
    setPriceListForCreate((prev) => [
      ...prev,
      {
        id: uuid(),
        code: "",
        title: "",
        units: 0,
        unitPrice: 0,
        currency: "",
        vat: 0,
        category: "",
        billingCodeType: BillingCodeType.ClinicSpecific,
        type: "CREATE",
        __typename: "BillingCodeClinicSpecific",
      },
    ])
  }

  const allPriceList = [...priceListForCreate, ...priceList].filter(
    (item) => !archivedPriceItemIds.includes(item.id)
  )

  return (
    <>
      <CenteredLayout>
        <form onSubmit={handleSubmit}>
          <OrganisationPriceListHeader>
            <div className={styles.formButtons}>
              <Button onClick={() => setShowCancelModal(true)}>
                {t("Cancel")}
              </Button>
              <Button type="submit" variant="filled">
                {t("Save")}
              </Button>
            </div>
          </OrganisationPriceListHeader>

          <div className={styles.searchWrap}>
            <SearchInput
              onChange={() => resetPriceList()}
              label={t("Search Price Item")}
              className={priceListStyles.input}
            />

            <Button
              variant="clear"
              onClick={handleAddNewLine}
              icon={<Icon name="add-line" />}
            >
              {t("Add new line")}
            </Button>
          </div>

          <OrganisationPriceListEditTable
            priceList={allPriceList}
            onArchive={() => {
              const filteredPriceListItemIds = selectedPriceItemIds.filter(
                (id) => !priceListForCreate.find((item) => item.id === id)
              )

              const filteredPriceListForCreate = priceListForCreate.filter(
                (item) => !selectedPriceItemIds.includes(item.id)
              )

              setArchivedPriceItemIds((prev) => {
                return [...prev, ...filteredPriceListItemIds]
              })

              setPriceListForCreate(filteredPriceListForCreate)

              setSelectedPriceItemIds([])
            }}
            onToolbarClose={() => setSelectedPriceItemIds([])}
            selectedPriceItemIds={selectedPriceItemIds}
            onCheckPriceItem={(id) => {
              setSelectedPriceItemIds((prev) => {
                if (prev.includes(id)) {
                  return prev.filter((itemId) => itemId !== id)
                }

                return [...prev, id]
              })
            }}
          />
        </form>
      </CenteredLayout>

      <Dialog
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        title={t("Are you sure you want to cancel?")}
        actions={
          <>
            <Button
              onClick={() => {
                setShowCancelModal(false)
              }}
            >
              {t("No, keep editing")}
            </Button>
            <Button
              as={Link}
              variant="filled"
              to={generatePath(RouteStrings.organisationPriceList)}
            >
              {t("Yes, discard changes")}
            </Button>
          </>
        }
      ></Dialog>
    </>
  )
}

export default EditOrganisationPriceList
