import { InlineGuideId, inlineGuides } from "assets/inlineGuides"
import {
  Hovercard,
  HovercardAnchor,
  useHovercardStore,
} from "components/Ariakit/Hovercard/Hovercard"
import { PopoverDescription } from "components/Ariakit/Popover/Popover"
import Icon from "components/Icon/Icon"
import { Markdown } from "components/Markdown/Markdown"

import styles from "./InlineGuide.module.css"

type Props = {
  id?: keyof typeof InlineGuideId
  content?: string
}

export const InlineGuidePopover = ({ id, content = "" }: Props) => {
  const inlineGuideContent = id ? inlineGuides[id].content : content

  const hovercardStore = useHovercardStore({
    placement: "right",
    animated: true,
  })

  return (
    <>
      <HovercardAnchor
        store={hovercardStore}
        aria-label="Open inline guide"
        render={(props) => (
          <Icon
            name={"information-line"}
            className={styles.hoverable}
            {...props}
          />
        )}
      />

      <Hovercard store={hovercardStore}>
        <PopoverDescription className={styles.popover} as="div">
          <Markdown children={inlineGuideContent} />
        </PopoverDescription>
      </Hovercard>
    </>
  )
}
