.row {
  /* needed to get all tds to be aligned. COMEBACK doesn't place items in middle of tr still? */
  vertical-align: baseline;
}

.disposition {
  margin-right: 8px;
}
.row:nth-child(2n + 1) .disposition {
  background-color: var(--color-background-hover);
}
.row:nth-child(2n + 1):hover .disposition {
  background-color: var(--color-background);
}

.primaryText {
  pointer-events: none;
}

.secondaryText {
  color: var(--color-text-secondary);
  font-variant-numeric: tabular-nums;
  gap: 5px;
  justify-content: flex-end;
  text-align: right;
}

.note {
  min-width: 160px;
  color: var(--color-text-secondary);
}

.journalEntries {
  display: inline-flex;
  gap: 5px;
  margin: 0;
  padding: 0;
  align-items: center;
}

.journalEntries li {
  list-style-type: none;
}

.countCircled {
  min-height: 24px;
  min-width: 24px;
  font-size: 14px;
  width: fit-content;
  pointer-events: none;
}

.subjectNameRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.personaIdCopyButtonIcon.personaIdCopyButtonIcon {
  opacity: 1;
}

.subjectNameRow .personaIdCopyButton.personaIdCopyButton {
  opacity: 0;
  transition: opacity 150ms;
}

.subjectNameRow:hover .personaIdCopyButton {
  opacity: 1;
}

.responsibleProviders {
  display: grid;
  grid-auto-flow: column;
  gap: 2px;
  width: fit-content;
}

.dashboardNote {
  max-width: 500px;
}
