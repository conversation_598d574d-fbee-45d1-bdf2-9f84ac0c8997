import { SessionData } from "generated/graphql"

import { RemoveSessionButton } from "../RemoveSessionButton/RemoveSessionButton"
import { WelcomeBackHeading } from "../WelcomeBackHeading/WelcomeBackHeading"

type ElectronicIdLoginExistingSessionProps = SessionData & {
  children: React.ReactNode
}

export default function ElectronicIdLoginExistingSession({
  children,
  name,
}: ElectronicIdLoginExistingSessionProps) {
  return (
    <>
      <WelcomeBackHeading name={name} />

      {children}

      <RemoveSessionButton />
    </>
  )
}
