.customiseButton {
  padding: 0 16px;
  border: 0;
  border-radius: 0 var(--radius-button-half);
  background-color: var(--color-blue-primary-on-white-active);
  color: var(--color-brand-primary-blue);
  z-index: 1;
}

.customiseButton:hover {
  background-color: var(--color-lev-blue-hover);
  color: var(--color-brand-primary-blue);
}

.customiseButton:active {
  background-color: var(--color-lev-blue-light-active);
  color: var(--color-brand-primary-blue);
}

.addSectionWrapper {
  width: 100%;
  display: flex;
}

.addSectionButton {
  align-self: flex-start;
  margin-top: -16px;
}

.ruler {
  padding: 0;
  flex-grow: 1;
  margin: 0 8px;
}

.ruler::after {
  background-color: var(--color-blue-primary-on-white-active);
}

.addSectionModal {
  width: 500px;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
