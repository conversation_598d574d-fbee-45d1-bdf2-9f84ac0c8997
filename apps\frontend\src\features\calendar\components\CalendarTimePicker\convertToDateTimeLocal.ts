import { formatISO, parse } from "date-fns"

import { isValid24HourFormat } from "./isValid24HourFormat"

export const convertToDateTimeLocal = (dateInMonth: string, time: string) => {
  if (!isValid24HourFormat(time)) {
    // Hack untill backend supports fromTime and toTime values to be null
    const currentDateTime = new Date()
    const isoDateTimeLocal = formatISO(currentDateTime, { format: "extended" })

    return isoDateTimeLocal
  }

  const dateTime = parse(
    `${dateInMonth} ${time}`,
    "yyyy-MM-dd HH:mm",
    new Date()
  )
  const isoDateTimeLocal = formatISO(dateTime, { format: "extended" })

  return isoDateTimeLocal
}
