---
mode: "agent"
tools: ["jira_create_issue", "jira_search_fields"]
description: "A prompt to make creating jira issues easier."
---

# Goal

The output of this workflow is a Jira ticket. _Do not do any code changes unless prompted to do so._

# Workflow

## Verify and map arguments

This MCP function needs multiple parameters which user may easily forget so we are going to assist him.

If no arguments are passed, return expected arguments as documented below.

The following arguments are accepted from prompt:

- title: string representing issue title (or "summary"). It may be skipped where you are expected to derive it from description.
- type: `Bug`, `Improvement`, `Story`, `Task`
- team: `Front end` or `Back end`. It is passed to MCP tool as `customfield_10120` and as an array of values.
- area: this is the most complicated parameter because it must map to a list of unique values. User may skip it where you may estimate it from title and description. It is passed to MCP tool as `customfield_10121` and is one of the following:
  1. `Infrastructure and Security`
  2. `Navigation (Header/PM)`
  3. `Codesets`
  4. `Calendar/Scheduling`
  5. `Subject Summary`
  6. `Subject journal (IP/EC/SJE/SJEB)`
  7. `Drug prescriptions`
  8. `Subject Journal Templates`
  9. `Dashboard`
  10. `Billing`
  11. `Task management`
  12. `Referrals & Clinical correspondence`
  13. `Waiting list`
  14. `AppConfiguration (Org/Dep)`
  15. `Authentication, Users and Accounts`
  16. `Authorization and access control`
  17. `Patient Portal`
  18. `Kiosk`
  19. `Guides/Localisation`
  20. `Customer Support`
  21. `Public API`
  22. `Integration`
  23. `LITE specific`
- description: has one or multiple paragraphs of text.

To utilise AI which can smartly derive parameters

If parameters cannot be parsed from the prompt you should fail and ask user to complete, providing information about correct parameter syntax.

## Create ticket

Now use `#jira_create_ticket` to create ticket using the arguments we received from AI prompt. Following parameters must be passed to MCP:

- `summary` (from `title` argument)
- `customfield_10120` (from `team` argument)
- `customfield_10121` (from `area` argument)
- `description`
- `project_key` is always set to `DEV`.

# Finally

Return message: "✅Ticket is ready to be created, please confirm our mapping of parameters: [list your computed mapping of arguments to parameters (shorten text of `description` parameter to not bloat output)]"
