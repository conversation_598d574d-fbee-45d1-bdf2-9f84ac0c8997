/* eslint-disable playwright/no-useless-await */
/* eslint-disable playwright/no-wait-for-timeout */
import { test, expect } from "@playwright/test"

import { openSubjectJournalForSubject } from "../utils/subjectJournalTestUtils"

test.describe("ClinicalCoding", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("/", { waitUntil: "commit" })
    await page.waitForLoadState()
  })

  test.describe("Context Note", () => {
    test("should be able to create a new CC and add context note", async ({
      page,
    }) => {
      // Arrange
      await openSubjectJournalForSubject(page, "Jenny Ingram")
      // Click on a template button
      await page.getByTestId("journal-template-tile").first().click()
      await page.waitForTimeout(400)

      // Act
      const editor = page.locator("#drag-file-element").getByRole("textbox")
      editor.fill("#diag")
      await page.getByRole("button", { name: "Diagnosis" }).click()
      await page.locator('[class*="Popover_menuItemButton"]').click()
      await page.locator('[data-name="clinicalCoding"]').click()
      await page.getByRole("combobox", { name: "Search..." }).fill("tannarbrot")
      await page.waitForTimeout(1000)
      await page.getByRole("option", { name: "S02.5 Tannarbrot" }).click()
      await page.waitForTimeout(400)
      await page.getByTestId("new-clinical-coding-submit").click()

      // Assert
      await page
        .locator("section")
        .filter({ hasText: "S02.5 Tannarbrot" })
        .getByLabel("Expand")
        .click()

      // Act
      await page
        .locator("[data-testid='clinical-coding-description-empty']")
        .dispatchEvent("click")
      await page.waitForTimeout(1000)
      await page
        .getByTestId("clinical-coding-description")
        .fill("Front tooth is broken")
      await page.getByRole("button", { name: "Save" }).click()

      // Assert
      await expect(
        page.getByText(
          "S02.5 TannarbrotCloseICD10Albus Dumbledore • todayFront tooth is brokenEdit"
        )
      ).toHaveCount(1)
    })

    test("should be able to create a new CC with context note", async ({
      page,
    }) => {
      // Arrange
      await openSubjectJournalForSubject(page, "Ian Ingram")
      // Click on a template button
      await page.getByTestId("journal-template-tile").first().click()
      await page.waitForTimeout(400)

      // Act
      const editor = page.locator("#drag-file-element").getByRole("textbox")
      editor.fill("#diag")
      await page.getByRole("button", { name: "Diagnosis" }).click()
      await page.locator('[class*="Popover_menuItemButton"]').click()
      await page.locator('[data-name="clinicalCoding"]').click()
      await page.getByRole("combobox", { name: "Search..." }).fill("adenósín")
      await page.waitForTimeout(1000)
      await page
        .getByRole("option", { name: "D81.3 Adenósín-deamínasaskortur" })
        .click()
      await page.waitForTimeout(400)
      await page.getByLabel("Description").fill("Skortur á alls konar")
      await page.getByTestId("new-clinical-coding-submit").click()

      // Assert
      await page
        .locator("section")
        .filter({ hasText: "D81.3 Adenósín-deamínasaskortur" })
        .getByLabel("Expand")
        .click()

      // Assert
      await expect(
        page.getByText(
          "D81.3 Adenósín-deamínasaskorturCloseICD10Albus Dumbledore • todaySkortur á alls konarEdit"
        )
      ).toHaveCount(1)
    })
  })
})
