import { FocusEventHandler, forwardRef, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { getFormattedBirthDate, isPersonaIdValid } from "@leviosa/utils"

import { Input, InputProps, Status } from "ui"

export type PersonaIdInputProps = InputProps & {
  allowAlternatePersonaId?: boolean
}
export const PersonaIdInput = forwardRef<HTMLInputElement, PersonaIdInputProps>(
  (
    {
      allowAlternatePersonaId = false,
      label,
      placeholder,
      onChange,
      onKeyDown,
      onBlur,
      onFocus,
      ...restProps
    },
    ref
  ) => {
    const { t } = useTranslation()
    const inputRef = useRef<HTMLInputElement | null>(null)
    const [inputStatus, setInputStatus] = useState<Status>("default")
    const [inputMessage, setInputMessage] = useState<string>("")

    const handleOnFocus: FocusEventHandler<HTMLInputElement> = (e) => {
      setInputMessage("")
      setInputStatus("default")
      onFocus?.(e)
    }

    const handleOnBlur: FocusEventHandler<HTMLInputElement> = (e) => {
      const value = e.currentTarget.value
      const isValid = isPersonaIdValid(value, allowAlternatePersonaId)

      if (isValid) {
        setInputStatus("success")
        e.target.setCustomValidity("")
      }

      if (value.length > 0 && !isValid) {
        setInputMessage(t("invalidPersonaId"))
        setInputStatus("error")
        e.target.setCustomValidity("invalidPersonaId")
      }
      onBlur?.(e)
    }

    return (
      <Input
        status={inputStatus}
        ref={(node: HTMLInputElement) => {
          inputRef.current = node
          if (typeof ref === "function") {
            ref(node)
          } else if (ref) {
            ref.current = node
          }
        }}
        label={label || t("personaId")}
        placeholder={placeholder || t("personaId")}
        message={inputMessage}
        onChange={(e) => {
          onChange?.(e)
        }}
        onBlur={handleOnBlur}
        onFocus={handleOnFocus}
        onClick={(e) => e.preventDefault()}
        onKeyDown={(e) => {
          onKeyDown?.(e)
        }}
        inputProps={{ "data-1p-ignore": true }}
        {...restProps}
      />
    )
  }
)

export default PersonaIdInput
