import { useState } from "react"
import { useTranslation } from "react-i18next"

import { Text } from "ui"

import styles from "./DropTarget.module.css"

export type DropTargetProps = {
  onDropFile: (file: FileList) => void
  isLoading?: boolean
  canUpload?: boolean
  className?: string
  children?: React.ReactNode
}

export function DropTarget({
  onDropFile,
  isLoading = false,
  canUpload,
  children,
  ...rest
}: DropTargetProps): JSX.Element {
  const [isDragActive, setIsDragActive] = useState(false)

  const { t } = useTranslation()

  const handleDrag = (e: React.DragEvent) => {
    if (canUpload) {
      return
    }
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setIsDragActive(true)

      return
    }

    setIsDragActive(false)
  }

  const handleDrop = async (e: React.DragEvent) => {
    if (canUpload) {
      return
    }
    e.preventDefault()
    e.stopPropagation()
    const files = e.dataTransfer.files
    setIsDragActive(false)
    if (files.length) {
      onDropFile(files)
    }
  }

  const renderChildren = () => {
    if (isLoading) {
      return <Text className={styles.dropTarget}>{t("Uploading...")}</Text>
    }

    if (isDragActive) {
      return <Text className={styles.dropTarget}>{t("Drop files here")}</Text>
    }

    return children
  }

  return (
    <div
      data-is-drag-active={isDragActive}
      id="drag-file-element"
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
      {...rest}
    >
      {renderChildren()}
    </div>
  )
}
