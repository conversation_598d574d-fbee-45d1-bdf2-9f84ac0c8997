//! Domain contracts crate
//!
//! This crate contains the public interfaces (contracts) that define the boundaries
//! between the domain layer and other layers in the application.
//!
//! By separating these interfaces into their own crate, we can apply the
//! Dependency Inversion Principle, allowing other crates to depend on these
//! interfaces without having to depend on the entire domain crate.

pub mod auth;
pub mod command;
pub mod db;
pub mod entity_event;
pub mod errors;
pub mod permissions;
pub mod query;
pub mod repo_connection;
pub mod repos;
pub mod resource;
pub mod services;

// Re-export commonly used types
pub use auth::AuthData;
pub use auth::AuthenticatedUser;
pub use command::CommandHandler;
pub use entity_event::Entity;
pub use entity_event::EntityEvent;
pub use query::QueryHandler;
pub use repo_connection::IRepoConnection;
pub use repo_connection::IRepoTransaction;
pub use repo_connection::ITransactionManager;
pub use repos::IReposContract;

// Re-export service contracts
pub use services::*;
