import { test, expect, Page } from "@playwright/test"

import { login } from "../utils/authenticationUtils"
import { openSubjectJournalForSubject } from "../utils/subjectJournalTestUtils"

/* eslint-disable playwright/no-wait-for-timeout */

test.describe("Subject Summary tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/")
    await page.waitForTimeout(2000)
    await login(page)
  })

  const arrange = async (page: Page) => {
    await openSubjectJournalForSubject(page, "Rudy Gainsburg")
    await page.click('[class*="SubjectSummaryButton"]')
    await page.waitForSelector('button:has-text("Contact")', {
      state: "visible",
      timeout: 10000,
    })
  }

  test("Icon in Subject Box is red because of critical code", async ({
    page,
  }) => {
    // Arrange
    await arrange(page)
    // Act
    const foundRedSSButtonsCount = await page.locator(
      '[aria-label="Show subject summary"]'
    )
    // Assert
    await expect(foundRedSSButtonsCount).toHaveClass(/.*colors_critical.*/)
  })

  test("Popover opens from Subject Box", async ({ page }) => {
    // Arrange
    await arrange(page)
    // Act
    // Assert
    await expect(
      page.getByRole("dialog").getByText("Rudy Gainsburg251130")
    ).toBeVisible()
  })

  test("Shows Subject Name and Persona Id", async ({ page }) => {
    // Arrange
    await arrange(page)
    const expectedSubjectName = "Rudy Gainsburg"

    // Act
    const subjectName =
      // eslint-disable-next-line playwright/no-conditional-in-test
      (await page
        .locator('[class*="PersonSummary_headingWrapper"]')
        .first()
        .textContent()) || ""
    // first fourteen characters of the subjectName
    const trimmedSubjectName = subjectName.slice(0, 14)

    // Assert
    await expect(trimmedSubjectName).toBe(expectedSubjectName)
  })

  test("Subject Summary shows Sex and Age", async ({ page }) => {
    // Arrange
    await arrange(page)
    const expectedSex = "Female, age:"
    const expectedAge = "94Y"

    // Act
    const sex = await page
      .locator('[class*="PersonSummary_overviewItemLabel"]')
      .first()
      .getAttribute("aria-label")
    const age = await page
      .locator('[class*="PersonSummary_overviewItem"]')
      .nth(1)
      .textContent()
      .then((text) => text?.replace(/\s/g, "")) //remove any escape characters

    // Assert
    await expect(sex).toBe(expectedSex)
    await expect(age).toBe(expectedAge)
  })

  test("Contact Card shows Email and Phone", async ({ page }) => {
    // Arrange
    await arrange(page)
    const expectedEmail = "<EMAIL>"
    //const expectedPhone = "+354 8548805"

    // Act
    // Assert
    await expect(page.getByTestId("Email")).toHaveText(expectedEmail, {
      timeout: 5000,
    })
    await expect(page.getByTestId("Phone")).not.toBe(null)
    await expect(page.getByTestId("Phone")).not.toBe("")
  })

  test("Clinical Summary Card shows codings list", async ({ page }) => {
    // Arrange
    await arrange(page)
    // Act
    await page.click('button:has-text("Clinical summary")')
    await page.waitForTimeout(400)

    // Assert
    await expect(page.locator('[class*="ClinicalCodingList"]')).toBeVisible()
  })

  test("Clinical Summary Card shows all codings", async ({ page }) => {
    // Arrange
    await arrange(page)
    // Act
    await page.click('button:has-text("Clinical summary")')
    await page.waitForTimeout(800)

    // Assert
    const listItemCount = await page
      .locator('div[class^="ClinicalCodingList_wrap"] ul li')
      .count()
    expect(listItemCount).toBe(2)
  })

  test("Encounters Card shows all encounters", async ({ page }) => {
    // Arrange
    await arrange(page)
    // Act
    await page.click('button:has-text("Encounters")')
    await page.waitForTimeout(400)

    // Assert
    await expect(page.locator('[class*="EncounterCard_wrap"]')).toHaveCount(2)
  })
})
