import {
  Tab as <PERSON>ki<PERSON><PERSON>ab,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>t<PERSON>ab<PERSON><PERSON>,
  TabListOptions,
  TabOptions,
  TabPanel,
  TabProvider,
} from "@ariakit/react"
import { forwardRef, ReactNode, Ref } from "react"
import { Link, LinkProps } from "react-router-dom"

import { Button, ButtonGroup, ButtonGroupProps, ButtonProps } from "ui"

const Tab = ({
  icon,
  iconEnd,
  status,
  variant,
  block,
  size,
  readOnly,
  ...tabProps
}: Omit<TabOptions<"button">, "render"> &
  Omit<ButtonProps<"button">, "children" | "ref"> & {
    children: ReactNode
  }) => {
  const buttonProps = { icon, iconEnd, variant, status, block, size, readOnly }
  return (
    <AriakitTab
      {...tabProps}
      render={(p) => (
        <Button {...buttonProps} {...p} ref={p.ref as Ref<HTMLButtonElement>} />
      )}
    />
  )
}

const TabLink = forwardRef<HTMLButtonElement, LinkProps>(
  function Tab(props, ref) {
    return (
      <AriakitTab
        id={props.id}
        ref={ref}
        className="tab"
        render={<Button as={Link} {...props} />}
      />
    )
  }
)

function TabList({
  size,
  variant,
  direction,
  children,
  ...tabListProps
}: TabListOptions<"div"> & ButtonGroupProps & { children: ReactNode }) {
  const buttonGroupProps = { size, variant, direction }
  return (
    <AriakitTabList
      {...tabListProps}
      render={(p) => (
        <ButtonGroup {...buttonGroupProps} {...p}>
          {children}
        </ButtonGroup>
      )}
    />
  )
}

export { Tab, TabLink, TabList, TabPanel, TabProvider }
