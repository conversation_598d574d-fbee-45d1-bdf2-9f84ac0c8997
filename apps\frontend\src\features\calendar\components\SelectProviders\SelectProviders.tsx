import { matchSorter } from "match-sorter"
import { useEffect, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { useLocation, useSearchParams } from "react-router-dom"
import { z } from "zod"

import { ComboboxItem } from "components/Ariakit/Combobox/ComboboxItem/ComboboxItem"
import { useComboboxStore } from "components/Ariakit/hooks"

import {
  ActivationStatus,
  ParticipantAttendanceRequest,
  ParticipantAttendeeSource,
  useGetAvailableProvidersQuery,
} from "generated/graphql"

import { Participant } from "../Participants/Participants"
import { UserFilter } from "../Participants/UserFilter"
import { useEventInstance } from "../forms/EventInstanceForm/EventInstance.context"

const LocationState = z.object({
  providerId: z.string().optional(),
})

type Props = {
  readOnly?: boolean
  onSelect: (option: Participant) => void
  selectedParticipants: Participant[]
  formType?: "Create" | "Edit"
}

export const SelectProviders = ({
  readOnly,
  onSelect,
  selectedParticipants,
  formType,
}: Props) => {
  const { t } = useTranslation("features", { keyPrefix: "calendar" })
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })
  const [searchParams] = useSearchParams()
  const [hasSaved, setHasSaved] = useState(false)
  const providerSearchParam = searchParams.get("provider")
  const { state } = useLocation()
  const parsedState = LocationState.safeParse(state)
  // The location state clears after the first render, so we need to store the provider
  const [providerFromCalendar] = useState(
    parsedState.success ? parsedState.data.providerId : undefined
  )

  const providersCombobox = useComboboxStore({
    resetValueOnHide: true,
  })

  const providerValue = providersCombobox.useState("value")

  const { fromDateTimeLocal, toDateTimeLocal } = useEventInstance()

  const { data: providersData, loading: providerLoading } =
    useGetAvailableProvidersQuery({
      variables: {
        fromTime: new Date(fromDateTimeLocal),
        toTime: new Date(toDateTimeLocal),
        inputFilter: {
          name: providerValue,
          activationStatus: ActivationStatus.Active,
        },
      },
    })

  const providersOptions = useMemo(
    () =>
      providersData?.providers.map(({ id, name, isAvailable, specialty }) => ({
        userId: id,
        name: name,
        attendanceRequest: ParticipantAttendanceRequest.Optional,
        participantType: ParticipantAttendeeSource.Provider,
        rsvpStatus: null,
        attendance: null,
        isAvailable: isAvailable,
        specialty: specialty,
      })) || [],
    [providersData]
  )

  useEffect(() => {
    if (providersOptions.length && !hasSaved && formType === "Create") {
      // Data has been loaded, we can now set the value

      if (providerFromCalendar) {
        // When event is created by dragging a time slot in day view with
        // multiple providers selected, we must use this provider from
        // the calendar's "resource" property to set the value, rather
        // than the providers from the search params.
        const selectedProvider = providersOptions.find(
          (provider) => provider.userId === providerFromCalendar
        )
        if (selectedProvider) {
          onSelect(selectedProvider)
        }
        setHasSaved(true)
      } else if (providerSearchParam) {
        const decodedParams = providerSearchParam
          ? decodeURIComponent(providerSearchParam)?.split(",")
          : []

        const preselectedProviders = providersOptions.filter((provider) =>
          decodedParams.includes(provider.userId)
        )

        preselectedProviders.forEach(onSelect)
        setHasSaved(true)
      }
    }
  }, [
    providersOptions,
    hasSaved,
    setHasSaved,
    providerSearchParam,
    formType,
    providerFromCalendar,
  ])

  const filteredProviders = useMemo(
    () =>
      providersOptions.filter(
        (option) =>
          !selectedParticipants.some(
            (selectedUser) => selectedUser.userId === option.userId
          )
      ),
    [providersOptions, selectedParticipants]
  )

  const sortedOptions = matchSorter(filteredProviders, providerValue, {
    keys: ["name"],
  })
  return (
    <UserFilter
      readOnly={readOnly}
      placeholder={t("selectProviderPlaceholder")}
      combobox={providersCombobox}
      label={t("selectProviderLabel")}
      isLoading={providerLoading}
    >
      {filteredProviders.length ? (
        sortedOptions.map((option) => (
          <ComboboxItem
            key={option.userId}
            value={option.userId}
            disabled={readOnly}
            onClick={() => {
              onSelect(option)
              providersCombobox.hide()
            }}
            subContent={tEnum(option.specialty || "")}
            direction="vertical"
          >
            {option.name}
          </ComboboxItem>
        ))
      ) : (
        <ComboboxItem key="no-users">
          {t("selectProviderNoResults")}
        </ComboboxItem>
      )}
    </UserFilter>
  )
}
