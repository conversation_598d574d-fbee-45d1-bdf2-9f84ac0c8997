import { LeviosaKindId } from "generated/graphql"

import { Config, PowerMenu } from "../../PowerMenu"
import { usePowerMenu } from "../../PowerMenu.context"
import { PowerMenuGroup } from "../../lib/types"
import { usePowerMenuGroups } from "../../usePowerMenuGroups"
import { testGroups } from "../lib/powerMenuData"

const config: Config = {
  leviosaKindId: LeviosaKindId.Mss,
  appVersion: "0.0.1",
  build: "0.0.1",
}

type Props = {
  newGroups?: PowerMenuGroup[]
}

export const PowerMenuTestWrapper = ({ newGroups = [] }: Props) => {
  const { open } = usePowerMenu()

  usePowerMenuGroups([...testGroups, ...newGroups], [])

  return (
    <div>
      <PowerMenu config={config} />
      <button onClick={open}>Open Power Menu</button>
    </div>
  )
}
