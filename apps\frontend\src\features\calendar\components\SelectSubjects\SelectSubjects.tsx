import { matchSorter } from "match-sorter"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDebounce } from "use-debounce"

import { formatPersonaId } from "@leviosa/utils"

import {
  ComboboxGroup,
  ComboboxGroupLabel,
} from "components/Ariakit/Combobox/ComboboxGroup/ComboboxGroup"
import { ComboboxItem } from "components/Ariakit/Combobox/ComboboxItem/ComboboxItem"
import { useComboboxStore } from "components/Ariakit/hooks"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"

import {
  ParticipantAttendanceRequest,
  ParticipantAttendeeSource,
  useGetSubjectsQuery,
} from "generated/graphql"

import { Participant } from "../Participants/Participants"
import { UserFilter } from "../Participants/UserFilter"
import { useEventInstance } from "../forms/EventInstanceForm/EventInstance.context"

type Props = {
  readOnly?: boolean
  onSelect: (option: Participant) => void
  selectedParticipants: Participant[]
  formType?: "Create" | "Edit"
}

export const SelectSubjects = ({
  readOnly = false,
  onSelect,
  selectedParticipants,
  formType,
}: Props) => {
  const { t } = useTranslation("features", { keyPrefix: "calendar" })
  const [hasSaved, setHasSaved] = useState(false)

  const subjectsCombobox = useComboboxStore({
    resetValueOnHide: true,
  })
  const { globalData } = useGlobalState()
  const { actor } = globalData
  const fallbackSubjects =
    actor.recentSubjectInteractions.map(({ subject }) => ({
      userId: subject.id,
      name: subject.name,
      attendanceRequest: ParticipantAttendanceRequest.Optional,
      participantType: ParticipantAttendeeSource.Subject,
      rsvpStatus: null,
      attendance: null,
      isAvailable: true,
      personaId: subject.personaId,
      phoneNumber: subject.phoneNumber,
    })) || []

  const subjectValue = subjectsCombobox.useState("value")

  const [useDebouncedValue] = useDebounce(subjectValue, 100)

  const { fromDateTimeLocal, toDateTimeLocal } = useEventInstance()

  const {
    data: subjectsData,
    loading: subjectLoading,
    previousData,
  } = useGetSubjectsQuery({
    variables: {
      fromTime: new Date(fromDateTimeLocal),
      toTime: new Date(toDateTimeLocal),
      searchQuery: useDebouncedValue,
    },
    skip: !useDebouncedValue,
  })

  const subjectsOptions = (
    subjectsData?.searchForSubjects.subjects ||
    previousData?.searchForSubjects.subjects ||
    []
  ).map(({ id, name, isAvailable, personaId, phoneNumber }) => ({
    userId: id,
    name: name,
    attendanceRequest: ParticipantAttendanceRequest.Optional,
    participantType: ParticipantAttendeeSource.Subject,
    rsvpStatus: null,
    attendance: null,
    isAvailable: isAvailable,
    personaId: personaId,
    phoneNumber: phoneNumber,
  }))

  const lastSubjectInteractionId = actor?.lastSubjectInteraction?.subject.id

  useEffect(() => {
    if (recentSubjects.length && !hasSaved && formType === "Create") {
      const preselectedSubjects = recentSubjects.filter((subject) => {
        const isActorSubjectInAvailabilitySlot =
          subject.userId === lastSubjectInteractionId
        return isActorSubjectInAvailabilitySlot
      })

      preselectedSubjects.forEach(onSelect)
      setHasSaved(true)
    }
  }, [hasSaved, setHasSaved, lastSubjectInteractionId])

  const recentSubjects = fallbackSubjects.filter(
    (option) =>
      !selectedParticipants.some(
        (selectedUser) => selectedUser.userId === option.userId
      )
  )
  const sortedRecentSubjects = subjectValue
    ? matchSorter(recentSubjects, subjectValue, {
        keys: ["name", "personaId"],
      })
    : recentSubjects
  const filteredSubjects = subjectsOptions
    .filter(
      ({ userId }) =>
        !selectedParticipants.some(
          (selectedUser) => selectedUser.userId === userId
        ) && !sortedRecentSubjects.some((user) => user.userId === userId)
    )
    .slice(0, 30)

  return (
    <UserFilter
      label={t("selectSubjectsLabel")}
      readOnly={readOnly}
      placeholder={t("selectSubjectPlaceholder")}
      combobox={subjectsCombobox}
      isLoading={subjectLoading}
    >
      {!!sortedRecentSubjects.length && (
        <ComboboxGroup>
          <ComboboxGroupLabel>
            {t("selectSubjectRecentSubjects")}
          </ComboboxGroupLabel>
          {sortedRecentSubjects.map((option) => (
            <ComboboxItem
              key={option.userId}
              value={option.userId}
              disabled={readOnly}
              onClick={() => {
                onSelect(option)
                subjectsCombobox.hide()
              }}
              subContent={
                <PiiSensitive>{formatPersonaId(option.personaId)}</PiiSensitive>
              }
              direction="vertical"
            >
              <PiiSensitive>{option.name}</PiiSensitive>
            </ComboboxItem>
          ))}
        </ComboboxGroup>
      )}
      {!!filteredSubjects.length && (
        <ComboboxGroup>
          <ComboboxGroupLabel>
            {t("selectSubjectSearchResults")}
          </ComboboxGroupLabel>
          {filteredSubjects.map((option) => (
            <ComboboxItem
              key={option.userId}
              value={option.userId}
              disabled={readOnly}
              onClick={() => {
                onSelect(option)
                subjectsCombobox.hide()
              }}
              subContent={
                <PiiSensitive>{formatPersonaId(option.personaId)}</PiiSensitive>
              }
              direction="vertical"
            >
              <PiiSensitive>{option.name}</PiiSensitive>
            </ComboboxItem>
          ))}
        </ComboboxGroup>
      )}
      {!filteredSubjects.length && !sortedRecentSubjects.length && (
        <ComboboxItem disabled={readOnly} className="no-options">
          {t("selectSubjectNoResults")}
        </ComboboxItem>
      )}
    </UserFilter>
  )
}
