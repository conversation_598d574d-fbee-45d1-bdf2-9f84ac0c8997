.wrap {
  --color-border-disabled: var(--color-gray-20);
  position: relative;

  width: 100%;
  display: grid;
  grid-template-rows: min-content min-content min-content;
}

.label.label {
  margin-bottom: 8px;
}

.selectWrapper {
  display: flex;
  align-items: center;
}

.select {
  padding-right: 48px;
  transition:
    box-shadow 200ms,
    border 100ms,
    background-color 100ms;
  position: relative;
}
.wrap .select {
  /* icn-width (16) + icn-right (8) + 8 = 32 */
  /* padding-right: 32px; */
}
.wrap[data-has-clear-icon="true"] .select {
  padding-right: 46px;
}

.wrap[data-is-multi="true"] .select {
  padding-top: 6px;
  padding-bottom: 6px;
}

.wrap[data-variant="clear"] .select {
  border-color: transparent;
  background-color: inherit;
}

.wrap[data-variant="clear"] .select:hover {
  background-color: var(--color-lev-blue-10);
}

.select[aria-expanded="true"],
.select:focus-visible,
.wrap[data-variant="clear"]:focus-within .select {
  border-color: var(--color-lev-blue);
  box-shadow:
    0 0 4px rgba(13, 16, 57, 0.1),
    0 0 20px rgba(13, 16, 57, 0.2);
  outline: none;
  background-color: var(--color-white);
}

.wrap[data-variant="clear"] .select[aria-expanded="true"] {
  background-color: var(--color-lev-blue-10);
}

.selectedValues {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin: 4px 0;
}

.selectedValueItem.selectedValueItem {
  padding: 0;
}

.selectedValueItem.selectedValueItem:hover {
  background-color: transparent;
}

.wrap[data-disabled="true"] :is(.select, .selectLabel, .message) {
  color: var(--color-disabled);
  border-color: var(--color-border-disabled);
  pointer-events: none;
}

.wrap[data-disabled="true"] .select {
  background: white;
  color: var(--color-disabled);
}

.wrap[data-read-only="true"] {
  pointer-events: none;
}

.wrap[data-status="error"] .select {
  border-color: var(--color-critical);
}

.wrap[data-status="warning"] .select {
  background-color: var(--color-white);
  border-color: var(--color-warning);
}

.wrap[data-status="success"] .select {
  border-color: var(--color-success);
}

.icon {
  position: absolute;
  right: 24px;
  width: 16px;
  height: 16px;
  pointer-events: none;
  /* prevent from shrinking when select value is large 
  flex: 0 0 16px; */
}

.clearButton.clearButton {
  padding: 0;
  border: none;
  position: absolute;
  right: 34px;
}

.wrap[data-size="small"] .icon {
  width: 10px;
  height: 10px;
}

.wrap[data-is-open="true"] .arrowIcon {
  transform: rotateX(180deg);
}

/* Is there a reason why we need to disable the select when the status is in error/warning state? */
/* .wrap[data-status="error"],
.wrap[data-status="warning"] .select {
  pointer-events: none;
} */

.wrap[data-status="error"] .message {
  color: var(--color-critical);
}

.wrap[data-status="warning"] .message {
  color: var(--color-warning);
}

.wrap[data-status="success"] .message {
  color: var(--color-success);
}

.wrap[data-size="small"] .select {
  font-size: 12px;
}

.optionsContent {
  padding: 0 16px;
  font-size: 16px;
  line-height: 40px;
}

.wrap[data-size="small"] .option {
  font-size: 12px;
}

.placeholder {
  color: var(--color-text-secondary);
}

.message {
  grid-row: 3;
}
