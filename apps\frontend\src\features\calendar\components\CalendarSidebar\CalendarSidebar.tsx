import c from "classnames"
import { useTranslation } from "react-i18next"

import ProviderPicker from "features/calendar/components/CalendarSidebar/ProviderPicker"
import ServiceTypes, {
  ServiceTypesProps,
} from "features/calendar/components/ServiceTypes/ServiceTypes"
import { color } from "styles/colors"
import { Text } from "ui"
import { DayPicker, DayPickerProps } from "ui/components/DayPicker/DayPicker"
import Switch from "ui/components/Switch/Switch"
import { useBreakpoint } from "ui/lib/useBreakpoint"

import styles from "./CalendarSidebar.module.css"

type CalendarSidebarProps = {
  serviceTypes: ServiceTypesProps["serviceTypes"]
  date: Required<DayPickerProps>["selectedDate"]
  selectedProviders: ServiceTypesProps["providers"]
  showCancelledEvents: boolean
  onSelectDate: DayPickerProps["onDateSelect"]
  onToggleCancelledEvents: () => void
}

export default function CalendarSidebar({
  serviceTypes,
  selectedProviders,
  showCancelledEvents,
  date,
  onSelectDate,
  onToggleCancelledEvents,
}: CalendarSidebarProps) {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "calendar",
  })
  const { sm } = useBreakpoint()

  return (
    <aside className={c(styles.aside, color.levBlue.light)}>
      <ProviderPicker />

      <ServiceTypes
        className={styles.scrollable}
        providers={selectedProviders}
        serviceTypes={serviceTypes}
      />

      <div className={styles.showCancelledEventsToggle}>
        <Switch
          id="show-cancelled-events-toggle"
          checked={showCancelledEvents}
          onToggle={onToggleCancelledEvents}
        />
        <Text as="label" htmlFor="show-cancelled-events-toggle">
          {tRoutes("showCancelledEvents")}
        </Text>
      </div>

      {sm && (
        <DayPicker
          key={date.toString()}
          selectedDate={date}
          onDateSelect={onSelectDate}
          className={styles.dayPicker}
        />
      )}
    </aside>
  )
}
