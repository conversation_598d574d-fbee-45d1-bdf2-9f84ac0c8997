import { Preview } from "@storybook/react-vite"
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"
import { I18nextProvider } from "react-i18next"
import { MemoryRouter } from "react-router-dom"

import "@leviosa/assets/styles/000_default.css"
import "@leviosa/assets/styles/colors.css"
import "@leviosa/assets/styles/reset.css"
import "@leviosa/assets/styles/variables.css"

import "../public/fonts/fonts.css"
import { i18nInstance } from "../src/i18n"

dayjs.extend(localizedFormat)

const preview: Preview = {
  parameters: {
    options: {
      storySort: {
        order: ["Components", "Form Components", "Ariakit"],
      },
    },
    layout: "centered",
    grid: { cellSize: 16 },
    controls: {
      // disable various props that have no meaning in Storybook (e.g. id, className). They are also hidden in master ((Sheets doc) b/c don't need testing
      // disable icon globally b/c impossible to set via StoryBook
      // disable value globally b/c different object type and each Story-component includes state to test it
      exclude: /^id|className|^kind|^icon|^value|^filter|^options/,
      // expanded: true
    },
  },

  decorators: [
    (Story: React.FC) => (
      <MemoryRouter>
        <I18nextProvider i18n={i18nInstance}>
          <Story />
        </I18nextProvider>
      </MemoryRouter>
    ),
  ],

  argTypes: {
    title: { defaultValue: "some title" },

    // Set default values b/c looks weird otherwise
    size: { control: "inline-radio", defaultValue: "NORMAL" },
    disabled: { defaultValue: false },
  },

  // i18n change locale
  globalTypes: {
    locale: {
      name: "Locale",
      description: "Internationalization locale",
      defaultValue: "EN_GB",
      toolbar: {
        icon: "globe",
        items: [
          { value: "EN_GB", title: "English-GB" },
          { value: "IS", title: "Icelandic" },
        ],
      },
    },
  },

  tags: ["autodocs"],
}

export default preview
