import c from "classnames"
import { useTranslation } from "react-i18next"

import ComputerIllustration from "@leviosa/assets/illustrations/computer2.svg?react"

import { Heading } from "ui"

import styles from "./JournalTemplate.module.css"

export const NoTemplateAvailable = () => {
  const { t } = useTranslation()
  return (
    <div className={c(styles.wrap, styles.emptyState)}>
      <ComputerIllustration />
      <Heading size="large">{t("No template found")}</Heading>
    </div>
  )
}
