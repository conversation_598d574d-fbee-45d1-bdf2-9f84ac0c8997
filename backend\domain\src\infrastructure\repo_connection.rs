use crate::{
    auth::AuthenticatedUser,
    errors::Result,
    repos::{I<PERSON><PERSON>os, Repos},
};
use sea_orm::{ConnectionTrait, DatabaseConnection, DatabaseTransaction, TransactionTrait};

pub trait IRepoConnection: Send + Sync {
    fn connection(&self) -> &impl ConnectionTrait;

    fn repos<'a>(&'a self) -> Box<dyn IRepos<'a> + 'a>;
}

pub struct RepoConnection {
    connection: DatabaseConnection,
    user: AuthenticatedUser,
}

impl RepoConnection {
    pub fn new(connection: DatabaseConnection, user: AuthenticatedUser) -> Self {
        Self { connection, user }
    }
}

impl IRepoConnection for RepoConnection {
    fn connection(&self) -> &impl ConnectionTrait {
        &self.connection
    }

    fn repos<'a>(&'a self) -> Box<dyn IRepos<'a> + 'a> {
        Box::new(Repos {
            connection: &self.connection,
            user: &self.user,
        })
    }
}

#[async_trait::async_trait]
pub trait IRepoTransaction: IRepoConnection + Send + Sync {
    async fn commit(self) -> Result<()>;
    async fn rollback(self) -> Result<()>;
}

pub struct RepoTransaction {
    connection: DatabaseTransaction,
    user: AuthenticatedUser,
}

impl IRepoConnection for RepoTransaction {
    fn connection(&self) -> &impl ConnectionTrait {
        &self.connection
    }

    fn repos<'a>(&'a self) -> Box<dyn IRepos<'a> + 'a> {
        Box::new(Repos {
            connection: &self.connection,
            user: &self.user,
        })
    }
}

#[async_trait::async_trait]
impl IRepoTransaction for RepoTransaction {
    async fn commit(self) -> Result<()> {
        self.connection.commit().await?;
        Ok(())
    }

    async fn rollback(self) -> Result<()> {
        self.connection.rollback().await?;
        Ok(())
    }
}

pub struct TransactionManager {
    connection: DatabaseConnection,
    user: AuthenticatedUser,
}

impl TransactionManager {
    pub fn new(connection: DatabaseConnection, user: AuthenticatedUser) -> Self {
        Self { connection, user }
    }
}

#[async_trait::async_trait]
pub trait ITransactionManager: Send + Sync {
    type Transaction: IRepoTransaction;

    async fn transaction(&self) -> Result<Self::Transaction>;
}

#[async_trait::async_trait]
impl ITransactionManager for TransactionManager {
    type Transaction = RepoTransaction;

    async fn transaction(&self) -> Result<Self::Transaction> {
        let tx = self.connection.begin().await?;

        Ok(RepoTransaction {
            connection: tx,
            user: self.user.clone(),
        })
    }
}
