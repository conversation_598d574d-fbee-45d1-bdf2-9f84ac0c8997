import { Page } from "@playwright/test"

/* eslint-disable playwright/no-wait-for-timeout */

/**
 * Helper functions for testing the subject journal and creating entities in it.
 */

// Helper function to create an encounter.
// Parameters:
//   page: The page object
//   title: The title of the encounter
export const createEncounter = async (
  page: Page,
  title: string,
  assignToInterventionPeriod = false
) => {
  await page.locator('button:text("Add encounter")').click()
  await page.waitForTimeout(300)
  await page.getByLabel("reason").fill(title)
  // Select clinical team as responsible team
  await page.getByRole("combobox", { name: "Team" }).click()
  await page
    .getByTestId("primary-team-select")
    .locator("[data-testid*=select-option]")
    .getByText("Medical team")
    .first()
    .click()
  if (assignToInterventionPeriod) {
    // Select intervention period
    await page.getByRole("combobox", { name: "Intervention Period" }).click()
    await page.waitForTimeout(300)
    await page.getByRole("button", { name: "select option" }).nth(1).click()
    await page.waitForTimeout(300)
  }
  await page.locator('button:text("Create encounter")').click()
  await page.waitForTimeout(600)
  // wait for notification wrapper to be visible
  await page.waitForSelector('[class*="Notification_wrapper"]')
  await page.waitForTimeout(500)
}

// Helper function to create a journal entry that has some text and is completed.
export const createCompletedJournalEntry = async (page: Page) => {
  await page.getByTestId("journal-template-tile").first().click()
  await page
    .locator("[data-lexical-editor='true']")
    .first()
    .fill("Journal entry text")
  await page.waitForTimeout(4000)
  await page.getByRole("button", { name: "Complete Entry" }).first().click()
  await page.waitForTimeout(2000)
}

// Helper function to open a subject journal using subject search.
// Parameters:
//   page: The page object
//   subjectName: Name of the subject (case sensitive, exact match)
export const openSubjectJournalForSubject = async (
  page: Page,
  subjectName: string
) => {
  // Open the subject search
  await page.getByTestId("subject-search-button").click()
  await page.waitForTimeout(400)
  // find by placeholder text
  await page.fill('input[placeholder="Search by name or ID"]', subjectName)
  await page.waitForTimeout(1000)
  // click on open journal button for the first item that has class SubjectSearch_subjectLink
  await page
    .locator('[class*="SubjectSearch_subjectCard"]')
    .first()
    .getByTestId("open-subject-journal-button")
    .click()
  await page
    .getByRole("button", { name: "Add encounter" })
    .waitFor({ state: "visible", timeout: 18000 })
}
