import c from "classnames"
import { useTranslation } from "react-i18next"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import DuplicateEventMenu from "features/calendar/components/DuplicateEventMenu/DuplicateEventMenu"
import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import { replaceHotKeyChar } from "features/power-menu/components/PowerMenuItem/replaceHotkeyChar"
import modifier from "features/power-menu/lib/hotkeys/hotkeyModifier"
import { Heading, IconButton, Label } from "ui"
import { getEventTitle } from "utils/getEventTitle"
import { isTypename } from "utils/isTypename"
import useDateFormatter from "utils/useDateFormatter"

import { UserType } from "generated/graphql"

import ArrivalIndicator from "../../ArrivalIndicator/ArrivalIndicator"
import styles from "./ViewEventInstanceHeader.module.css"

type CreatedBy = { userType: string; name: string }
type ViewEventInstanceHeaderProps = {
  event: CalendarEvent<"eventInstance">
  createdAt?: string
  createdBy?: CreatedBy
  onEditClick: () => void
  onClose: () => void
}

type BookingInfoProps = {
  createdAt?: string
  createdBy?: CreatedBy
}

const BookingInfo = ({ createdBy, createdAt }: BookingInfoProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "calendar.popover",
  })
  const dateFormat = useDateFormatter()
  const createdAtDate = dateFormat(new Date(createdAt ?? new Date()), {
    dateStyle: "long",
  })

  return (
    <span
      className={c({ [styles.bookingInfoLoading]: !createdAt })}
      aria-hidden={!createdAt}
    >
      {createdBy?.userType === UserType.Booking
        ? tRoutes("eventBookedOnline", { createdAtDate })
        : tRoutes("eventBookedByOnDate", {
            name: createdBy?.name,
            createdAtDate,
          })}
    </span>
  )
}

export const ViewEventInstanceHeader = ({
  event,
  createdAt,
  createdBy,
  onEditClick,
  onClose,
}: ViewEventInstanceHeaderProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "calendar",
  })
  const { title, participants, fromDate, serviceType, canceledAt } =
    event.resource

  const isEventCancelled = !!canceledAt

  const subjectParticipants = participants.filter(
    isTypename("ParticipantSubject")
  )

  const providerParticipants = participants.filter(
    isTypename("ParticipantProvider")
  )

  const providerId = providerParticipants[0]?.provider.id

  const eventTitle = getEventTitle(title, serviceType?.name)

  return (
    <div
      className={c(styles.header, {
        [styles.noArrivalIndicator]:
          subjectParticipants.length === 0 && !isEventCancelled,
      })}
    >
      {isEventCancelled ? (
        <Tooltip
          placement="top"
          tooltipContent={
            <div style={{ maxWidth: 250 }}>
              {tRoutes("eventHasBeenCancelled")}
            </div>
          }
        >
          <Icon className={styles.cancelledIcon} name="prohibited-2-line" />
        </Tooltip>
      ) : (
        <ArrivalIndicator
          subjectParticipants={subjectParticipants}
          wrapperClassName={styles.arrivalIndicatorWrapper}
          className={styles.arrivalIndicator}
          placement="bottom-start"
        />
      )}

      <div className={styles.headingContainer}>
        <Heading className={styles.title} title={eventTitle}>
          {eventTitle}
        </Heading>
        <Label secondary>
          <BookingInfo createdAt={createdAt} createdBy={createdBy} />
        </Label>
      </div>

      <div className={styles.actions}>
        {serviceType &&
          providerId &&
          subjectParticipants.length === 1 &&
          !isEventCancelled && (
            <DuplicateEventMenu
              eventDate={fromDate}
              serviceTypeId={serviceType?.id}
              providerId={providerId}
              subjectId={subjectParticipants[0].subject.id}
            />
          )}

        {!isEventCancelled && (
          <Tooltip
            tooltipContent={`Edit event (${replaceHotKeyChar(
              `${modifier}+E`
            )})`}
          >
            <IconButton
              variant="clear"
              aria-label="Edit Event"
              size="xlarge"
              iconName="pencil-line"
              onClick={onEditClick}
            />
          </Tooltip>
        )}

        <IconButton
          variant="clear"
          size="xlarge"
          iconName="close-line"
          onClick={onClose}
          aria-label="Close"
        />
      </div>
    </div>
  )
}
