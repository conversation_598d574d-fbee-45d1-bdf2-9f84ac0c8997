import { Fragment } from "react"
import { useTranslation } from "react-i18next"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import { Button, notification, Table } from "ui"

import { RoomType, useDeleteBedForRoomMutation } from "generated/graphql"

import { Building } from "../BuildingOverview/BuildingOverview"
import tableStyles from "../LocationTable.module.css"
import styles from "./RoomTable.module.css"

type RoomTableProps = {
  rooms: Building["rooms"]["rooms"]
  onEditRoomClick?: (roomId: string) => void
  onAddBedClick?: (roomId: string) => void
}

export const RoomTable = ({
  rooms,
  onEditRoomClick,
  onAddBedClick,
}: RoomTableProps) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")

  const [deleteBedForRoom] = useDeleteBedForRoomMutation({
    onCompleted: (data) => {
      if (data) {
        notification.create({
          message: t("Bed has been deleted"),
          status: "success",
          maxWidth: "500px",
        })
      }
    },
    // refetchQueries: [namedOperations.Query.GetBuildings],
  })

  if (rooms.length === 0) return null

  return (
    <>
      <Table className={tableStyles.table}>
        <thead>
          <tr>
            <th>{t("Room")}</th>
            <th>{t("Bed")}</th>
            <th>{t("Type")}</th>
            <th>{t("Edit")}</th>
          </tr>
        </thead>
        <tbody>
          {rooms.map((room) => {
            const { beds } = room.beds
            return (
              <Fragment key={room.id}>
                <tr key={room.id}>
                  <td>{room.label}</td>
                  <td>
                    {room.roomType === RoomType.PatientRoom ? beds.length : ""}
                  </td>
                  <td>{tEnum(room.roomType)}</td>
                  <td>
                    <div className={styles.iconsWrap}>
                      <Tooltip tooltipContent={t("Edit Room")}>
                        <Button
                          icon={<Icon name="pencil-line" />}
                          onClick={() => onEditRoomClick?.(room.id)}
                          variant="clear"
                        />
                      </Tooltip>

                      {onAddBedClick &&
                        room.roomType === RoomType.PatientRoom && (
                          <Tooltip tooltipContent={t("Add Beds")}>
                            <Button
                              icon={<Icon name="add-circle-line" />}
                              onClick={() => onAddBedClick(room.id)}
                              variant="clear"
                            />
                          </Tooltip>
                        )}
                    </div>
                  </td>
                </tr>
                {beds.map((bed) => (
                  <tr key={bed.id}>
                    <td></td>
                    <td>{bed.label}</td>
                    <td>{bed.bedType}</td>
                    <td>
                      <Tooltip
                        tooltipContent={t("Remove Bed")}
                        placement="bottom"
                        className={styles.tooltip}
                      >
                        <Button
                          variant="clear"
                          icon={<Icon name="delete-bin-line" />}
                          onClick={() =>
                            deleteBedForRoom?.({
                              variables: {
                                input: {
                                  locationId: bed.id,
                                },
                              },
                            })
                          }
                        />
                      </Tooltip>
                    </td>
                  </tr>
                ))}
              </Fragment>
            )
          })}
        </tbody>
      </Table>
      <br />
    </>
  )
}
