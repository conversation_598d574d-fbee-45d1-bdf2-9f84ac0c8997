use async_trait::async_trait;
use leviosa_domain_types::SubjectId;
use leviosa_domain::{MedicalBill, NhiService, NhiSubmissionResult, SubjectInsuranceStatus};

pub struct EmptyNhiService;

#[async_trait]
impl NhiService for EmptyNhiService {
    async fn get_subject_payment_status(
        &self,
        _: SubjectId,
        _: &str,
    ) -> leviosa_domain::errors::Result<SubjectInsuranceStatus> {
        Err(anyhow::anyhow!("NHI service is not configured").into())
    }

    async fn submit_invoice(
        &self,
        _: MedicalBill,
        _: &str,
        _: &str,
    ) -> leviosa_domain::errors::Result<NhiSubmissionResult> {
        Err(anyhow::anyhow!("NHI service is not configured").into())
    }
}
