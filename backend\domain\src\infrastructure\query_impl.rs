//! Implementation of the query interfaces from domain-contracts

use crate::infrastructure::{repo_connection::IRepoConnection, repos::IRepos};

/// Query pipeline for executing queries
pub struct QueryPipeline;

/// Implementation of the RepoQueryHandler for domain-specific query handlers
pub struct RepoQueryHandler<R: IRepoConnection> {
    connection: R,
}

impl<R: IRepoConnection> RepoQueryHandler<R> {
    pub fn new(connection: R) -> Self {
        Self { connection }
    }

    pub fn repos(&self) -> Box<dyn IRepos<'_> + '_> {
        self.connection.repos()
    }
}
