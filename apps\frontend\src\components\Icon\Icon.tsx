import c from "classnames"
import { forwardRef } from "react"

import { IconName } from "@leviosa/assets"
import Icons from "@leviosa/assets/generated/icon-sprite.svg"

import styles from "./Icon.module.css"

export type IconProps = {
  name: IconName
  spin?: boolean
} & React.SVGProps<SVGSVGElement>

export default forwardRef<SVGSVGElement, IconProps>(function Icon(
  { name, spin, className = "", ...rest },
  ref
) {
  const isBold = name.includes("Bold")
  return (
    <svg
      ref={ref}
      className={c([
        styles.icon,
        spin && styles.spin,
        isBold && styles.bold,
        className,
      ])}
      {...rest}
    >
      <use href={Icons + `#${name}`} />
    </svg>
  )
})
