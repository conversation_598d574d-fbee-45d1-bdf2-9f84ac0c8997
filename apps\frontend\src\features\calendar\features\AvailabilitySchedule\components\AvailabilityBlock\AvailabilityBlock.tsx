import { useEffect, useRef } from "react"
import { useTranslation } from "react-i18next"

import { useComboboxStore } from "components/Ariakit/hooks"
import { ErrorBoundary } from "components/ErrorBoundary/ErrorBoundary"
import Icon from "components/Icon/Icon"
import { TimePicker } from "components/TimePicker/TimePicker"
import { Button } from "ui"
import { isValidTime, naiveTimeToSimpleTime } from "utils/timeUtils"

import {
  AvailabilityBlockRuleFragmentFragment,
  CreateAvailabilityBlockInput,
  useCreateAvailabilityBlockMutation,
  useDeleteAvailabilityBlockMutation,
  useUpdateAvailabilityBlockMutation,
} from "generated/graphql"

import styles from "./AvailabilityBlock.module.css"

export function AddAvailabilityBlockButton({
  scheduleId,
  serviceTypeAvailabilityId,
  weekday,
}: CreateAvailabilityBlockInput) {
  const [createAvailabilityBlock] = useCreateAvailabilityBlockMutation()

  return (
    <Button
      variant="clear"
      icon={<Icon name={"add-line"} />}
      onClick={() => {
        createAvailabilityBlock({
          variables: {
            input: {
              scheduleId,
              serviceTypeAvailabilityId,
              weekday,
            },
          },
        })
      }}
    />
  )
}

type AvailabilityBlockProps = {
  scheduleId: string
  serviceTypeAvailabilityId: string
  blockRule: AvailabilityBlockRuleFragmentFragment
  minuteStep?: number
  allowedStartTime: string
  allowedEndTime: string
  showAddBlockButton?: boolean
  disabled?: boolean
}

const AvailabilityBlock = ({
  scheduleId,
  serviceTypeAvailabilityId,
  blockRule,
  minuteStep = 15,
  allowedStartTime,
  allowedEndTime,
  showAddBlockButton,
  disabled = false,
}: AvailabilityBlockProps) => {
  const { t } = useTranslation()
  const [updateAvailabilityBlock] = useUpdateAvailabilityBlockMutation()
  const [deleteAvailabilityBlock] = useDeleteAvailabilityBlockMutation()

  const lastValidStartTime = useRef(allowedStartTime)

  const fromTime = blockRule.fromTime
    ? naiveTimeToSimpleTime(blockRule.fromTime)
    : ""
  const toTime = blockRule.toTime ? naiveTimeToSimpleTime(blockRule.toTime) : ""

  const fromTimeStore = useComboboxStore({
    defaultItems: [],
    defaultValue: fromTime,
  })

  const toTimeStore = useComboboxStore({
    defaultItems: [],
    defaultValue: toTime,
  })
  const fromTimeValue = fromTimeStore.useState().value
  const toTimeValue = toTimeStore.useState().value

  useEffect(() => {
    // Call mutation when the time changes
    if (isValidTime(fromTimeValue)) {
      updateAvailabilityBlock({
        variables: {
          input: {
            id: blockRule.id,
            fromTime: `${fromTimeValue}:00`,
          },
        },
      })
    }
  }, [fromTimeValue, blockRule.id, updateAvailabilityBlock])

  useEffect(() => {
    // Call mutation when the time changes
    if (isValidTime(toTimeValue)) {
      updateAvailabilityBlock({
        variables: {
          input: {
            id: blockRule.id,
            toTime: `${toTimeValue}:00`,
          },
        },
      })
    }
  }, [toTimeValue, blockRule.id, updateAvailabilityBlock])

  // Make sure that we're passing a valid time to TimePicker
  let validStartTime = lastValidStartTime.current
  if (isValidTime(fromTimeValue)) {
    validStartTime = fromTimeValue
    lastValidStartTime.current = fromTimeValue
  }

  let fromPickerError: string | undefined
  if (fromTimeValue && !isValidTime(fromTimeValue)) {
    fromPickerError = t("Invalid time")
  } else if (fromTimeValue && fromTimeValue < allowedStartTime) {
    fromPickerError = t("Block cannot start before the previous one has ended")
  }

  let toPickerError: string | undefined
  if (toTimeValue && !isValidTime(toTimeValue)) {
    toPickerError = t("Invalid time")
  } else if (fromTimeValue && toTimeValue && fromTimeValue === toTimeValue) {
    toPickerError = t("Block cannot end at the same time it starts")
  } else if (fromTimeValue && toTimeValue && fromTimeValue > toTimeValue) {
    toPickerError = t("Block cannot end before it has started")
  }

  return (
    <ErrorBoundary fallback={<td colSpan={4}>Error</td>}>
      <td>
        <TimePicker
          className={styles.fromTimePicker}
          store={fromTimeStore}
          minuteStep={15}
          startTime={validStartTime}
          error={fromPickerError}
          disabled={disabled}
          scrollToTime={
            allowedStartTime === "00:00" ? "08:00" : allowedStartTime
          }
        />
      </td>
      <td>
        <TimePicker
          className={styles.toTimePicker}
          store={toTimeStore}
          minuteStep={minuteStep}
          startTime={validStartTime}
          scrollToTime={validStartTime || allowedStartTime}
          pickEndOfStep
          endTime={allowedEndTime}
          error={toPickerError}
          disabled={disabled}
        />
      </td>
      <td>
        {blockRule && !disabled && (
          <Button
            variant="clear"
            icon={<Icon name={"delete-bin-line"} />}
            onClick={() => {
              deleteAvailabilityBlock({
                variables: {
                  input: {
                    id: blockRule.id,
                  },
                },
              })
            }}
          />
        )}
      </td>
      <td>
        {showAddBlockButton && !disabled && (
          <AddAvailabilityBlockButton
            scheduleId={scheduleId}
            serviceTypeAvailabilityId={serviceTypeAvailabilityId}
            weekday={blockRule.weekday}
          />
        )}
      </td>
    </ErrorBoundary>
  )
}

export default AvailabilityBlock
