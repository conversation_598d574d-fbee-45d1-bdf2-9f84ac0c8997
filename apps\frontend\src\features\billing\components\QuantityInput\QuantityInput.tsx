import c from "classnames"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"

import { ErrorTooltip } from "components/Ariakit/Tooltip/ErrorTooltip"
import Icon from "components/Icon/Icon"
import { Input } from "ui"

import styles from "./QuantityInput.module.css"

type QuantityInputProps = {
  name: string
  quantity: number
  error?: string
  onBlur: (value: number) => void | Promise<unknown>
  className?: string
  allowFloat?: boolean
  disabled?: boolean
}

export const QuantityInput = ({
  name,
  quantity,
  error,
  onBlur,
  className,
  allowFloat = false,
  disabled,
}: QuantityInputProps) => {
  const { t } = useTranslation()

  const [quantityError, setQuantityError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const [quantityValue, setQuantityValue] = useState<number>(quantity)
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const parsedValue = parseFloat(value)
    if (isNaN(parsedValue)) {
      setQuantityValue(0)
    } else {
      setQuantityValue(parsedValue)
    }
  }

  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const inputQuantity = parseFloat(e.target.value)
    if (inputQuantity === quantity) return

    if (!allowFloat && !Number.isInteger(inputQuantity)) {
      setQuantityError(t("Quantity must be an integer"))

      return
    }

    if (inputQuantity < 0 || inputQuantity > 999) {
      setQuantityError(t("Quantity must be between 0 and 999"))

      return
    }

    setQuantityError(null)
    setIsLoading(true)
    try {
      await onBlur(inputQuantity)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (quantity !== quantityValue) {
      setQuantityValue(quantity)
    }
  }, [quantity])

  return (
    <ErrorTooltip error={error || quantityError} focusable={false}>
      <Input
        name={name}
        className={c(styles.inputWrap, isLoading && styles.loading, className)}
        value={quantityValue}
        hideLabel
        hideMessage
        hideStatusIcon
        iconStart={isLoading ? <Icon name="loader-4-line" spin /> : undefined}
        type="number"
        label={name}
        status={error || quantityError ? "error" : undefined}
        min={0}
        max={999}
        disabled={disabled}
        step={allowFloat ? "any" : 1}
        onBlur={handleBlur}
        onChange={handleChange}
      />
    </ErrorTooltip>
  )
}
