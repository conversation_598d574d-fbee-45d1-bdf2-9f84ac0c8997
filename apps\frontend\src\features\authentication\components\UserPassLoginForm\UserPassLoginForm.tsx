import { FormEvent } from "react"
import { useTranslation } from "react-i18next"
import { Link } from "react-router-dom"

import Panel from "components/Panel/Panel"
import { useAuth } from "features/authentication/AuthProvider"
import useFocusOnMount from "hooks/useFocusOnMount"
import { RouteStrings } from "routes/RouteStrings"
import { FormGrid, Input } from "ui"
import Button from "ui/components/Button/Button"
import isDev from "utils/isDev"

import { useUserPassLoginMutation } from "generated/graphql"

import styles from "../../Authentication.module.css"

const emails = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
]
const random = Math.floor(Math.random() * emails.length)
const defaultEmail = isDev ? emails[random] : undefined
const defaultPass = isDev ? "somepassword" : undefined

export default function UserPassLoginForm() {
  const { t } = useTranslation()
  const { authenticate } = useAuth()
  const [login, { loading, error }] = useUserPassLoginMutation()

  const firstInputRef = useFocusOnMount<HTMLInputElement>()

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.target as HTMLFormElement)

    const { email, password } = Object.fromEntries(formData.entries())

    if (
      !email ||
      !password ||
      typeof email !== "string" ||
      typeof password !== "string"
    ) {
      return
    }

    const { data } = await login({
      variables: { email: email, password: password },
    })

    if (!data) return

    const { accessToken, refreshToken } = data.logIn.tokens
    authenticate({ accessToken, refreshToken })
  }

  return (
    <FormGrid onSubmit={handleSubmit} className={styles.wrap}>
      <Input
        id="login-email"
        label={t("routes:auth.email")}
        ref={firstInputRef}
        name="email"
        type="email"
        defaultValue={defaultEmail}
        autoComplete="username"
      />

      <Input
        id="login-password"
        label={t("routes:auth.password")}
        name="password"
        type="password"
        defaultValue={defaultPass}
        autoComplete="current-password"
      />

      {error && (
        <Panel status="error" className={styles.panel}>
          {error.message}
        </Panel>
      )}

      <Button
        className={styles.submitButton}
        disabled={loading}
        type="submit"
        size="large"
        variant="filled"
      >
        {t("routes:auth.doLogin")}
      </Button>
      <Link to={RouteStrings.forgetPassword} className={styles.link}>
        {t("routes:auth.forgotPassword")}
      </Link>
    </FormGrid>
  )
}
