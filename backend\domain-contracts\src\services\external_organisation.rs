//! External organisation integration contract

use crate::errors::Result;
use async_trait::async_trait;

use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ExternalOrganisation {
    pub id: String,
    pub name: String,
    pub contact_info: Option<ContactInfo>,
    pub integration_type: IntegrationType,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct ContactInfo {
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum IntegrationType {
    Ehr,
    Laboratory,
    Pharmacy,
    Insurance,
    Other(String),
}

/// Contract for external organisation integration
#[async_trait]
#[mockall::automock]
pub trait ExternalOrganisationIntegration: Send + Sync {
    /// Get external organisation information
    async fn get_organisation(
        &self,
        external_id: &str,
    ) -> Result<Option<ExternalOrganisation>>;

    /// Search for external organisations
    async fn search_organisations(
        &self,
        query: &str,
        integration_type: Option<IntegrationType>,
    ) -> Result<Vec<ExternalOrganisation>>;
}
