.wrap:not([data-animation="manual"]) {
  animation-duration: var(--animation-duration);
  animation-fill-mode: both;
}

:where(.wrap[data-animation="fade"])[data-enter] {
  animation-name: fadeIn;
  animation-timing-function: linear;
}

:where(.wrap[data-animation="fade"])[data-leave] {
  animation-name: fadeOut;
  animation-timing-function: linear;
}
:where(.wrap[data-animation="fadeUp"])[data-enter] {
  animation-name: fadeInUp;
  animation-timing-function: linear;
}

:where(.wrap[data-animation="fadeUp"])[data-leave] {
  animation-name: fadeOutDown;
  animation-timing-function: linear;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
  }
}
