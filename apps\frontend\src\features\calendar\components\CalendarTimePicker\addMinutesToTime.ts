export const addMinutesToTime = (
  timeString: string,
  minutesToAdd: number
): string => {
  const [hours, minutes] = timeString.split(":")
  const date = new Date()
  date.setHours(parseInt(hours), parseInt(minutes), 0)
  date.setMinutes(date.getMinutes() + minutesToAdd)

  const newHours = date.getHours().toString().padStart(2, "0")
  const newMinutes = date.getMinutes().toString().padStart(2, "0")
  const newTimeString = `${newHours}:${newMinutes}`

  return newTimeString
}
