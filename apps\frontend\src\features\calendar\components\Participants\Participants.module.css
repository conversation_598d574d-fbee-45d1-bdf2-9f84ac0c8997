.selectParticipantsWrap {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--grid-gap);
  width: 100%;
  margin-bottom: 16px;
}

.inputWrap {
  position: relative;
  margin-top: 8px;
}

.input {
  margin: 0;
  margin-bottom: 16px;
}

.loading {
  position: absolute;
  top: 35%;
  right: 12px;
  translate: 0 -50%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.1s;
}

.loading[data-loading="true"] {
  opacity: 1;
}

.participantsList {
  display: grid;
  gap: 16px;
}
