import { Page } from "@playwright/test"

import { simulateKeyCombination, modifierKeys } from "./testUtils"

/* eslint-disable playwright/no-wait-for-timeout */

/**
 * Helper functions for testing the Dashboard.
 */

// Helper function to open a subject journal from dashboard.
// Parameters:
//   page: The page object
//   subjectName: Name of the subject (case sensitive, exact match)
export const openSubjectJournalForSubjectViaDashboard = async (
  page: Page,
  subjectName: string
) => {
  await openDashboardPage(page)
  await page.waitForTimeout(1000)
  await page.getByRole("cell", { name: subjectName }).first().click()
  await page.waitForTimeout(500)
  await page
    .locator('[class*="EncounterCard_openJournalButton"]')
    .nth(0)
    .click()
  await page.waitForLoadState()
}

// Helper function to open the dashboard page using keyboard shortcuts.
export const openDashboardPage = async (page: Page) => {
  // Open subject journal with shortcut
  await simulateKeyCombination(page, "2", [modifierKeys[0], modifierKeys[1]])
}

// Helper function to open the dashboard for the test team
// Parameters:
//   page: The page object
export const openDashboardPageViaPowerMenu = async (page: Page) => {
  // Open clinical team dashboard page
  await page.click('[data-testid="power-menu-button"]')
  await page.waitForSelector("[data-testid=power-menu]")
  await page.fill('[data-testid="search-input"]', "my teams")
  await page.click('[data-testid="power-menu-item"] >> text="My Teams"')
  await page.fill('[data-testid="search-input"]', "dashboard ")
  await page.waitForTimeout(200)
  await page.locator('[data-testid="power-menu-item"]').first().click()
  await page.waitForTimeout(800)
}
