import { Composite, useCompositeStore } from "@ariakit/react"
import { useEffect } from "react"

import { Table } from "ui"

import { EditOrganisationPriceListToolbar } from "../EditOrganisationPriceListToolbar/EditOrganisationPriceListToolbar"
import { EmptyOrganisationPriceListRow } from "../EmptyOrganisationPriceListRow/EmptyOrganisationPriceListRow"
import { PriceList } from "../OrganisationPriceList.context"
import { OrganisationPriceListTableRowHeader } from "../OrganisationPriceListTableRowHeader/OrganisationPriceListTableRowHeader"
import styles from "./OrganisationPriceListEditTable.module.css"
import { OrganisationPriceListForEditRow } from "./OrganisationPriceListForEditRow/OrganisationPriceListForEditRow"

type OrganisationPriceListEditTableProps = {
  priceList: PriceList
  selectedPriceItemIds: string[]

  onArchive?: () => void
  onCheckPriceItem?: (id: string) => void
  onToolbarClose?: () => void
}

export type PriceListItemKeys = {
  id: string
  code: string
  title: string
  units: number
  unitPrice: number
  currency: string
  vat: number
  type?: string
}

export const priceItemKeys: Array<keyof PriceListItemKeys> = [
  "id",
  "code",
  "title",
  "units",
  "unitPrice",
  "currency",
  "vat",
  "type",
]

export const OrganisationPriceListEditTable = ({
  priceList,
  selectedPriceItemIds,

  onArchive,
  onToolbarClose,
  onCheckPriceItem,
}: OrganisationPriceListEditTableProps) => {
  const composite = useCompositeStore({
    focusLoop: "vertical",
    orientation: "vertical",
  })

  const items = composite.useState().items

  useEffect(() => {
    if (items.length === 0) return

    const activeElement = document.activeElement

    if (activeElement?.tagName !== "INPUT") {
      composite.move(composite.first())
    }
  }, [items, composite.first])

  return (
    <>
      <Table className={styles.table}>
        <thead>
          <tr>
            <th></th>
            <OrganisationPriceListTableRowHeader />
          </tr>
        </thead>

        <Composite store={composite} render={<tbody></tbody>}>
          {priceList.length === 0 && <EmptyOrganisationPriceListRow />}
          {priceList.map((priceListItem) => {
            return (
              <OrganisationPriceListForEditRow
                key={priceListItem.id}
                priceListItem={priceListItem}
                selectedPriceItemIds={selectedPriceItemIds}
                composite={composite}
                onCheck={() => onCheckPriceItem?.(priceListItem.id)}
              />
            )
          })}
        </Composite>
      </Table>
      <EditOrganisationPriceListToolbar
        onClose={() => onToolbarClose?.()}
        onArchive={() => onArchive?.()}
        selectedPriceItemIds={selectedPriceItemIds}
      />
    </>
  )
}
