import format from "date-fns/format"
import { Formats } from "react-big-calendar"

export const formats: Formats | undefined = {
  timeGutterFormat: (date) => format(date, "HH:mm"),
  eventTimeRangeFormat: ({ start, end }) =>
    `${format(start, "HH:mm")} - ${format(end, "HH:mm")}`,
  selectRangeFormat: ({ start, end }) =>
    `${format(start, "HH:mm")} - ${format(end, "HH:mm")}`,
  dayFormat: (date) => {
    const dayName = format(date, "EEEE")
    const dayOfMonth = format(date, "d")

    return `${dayOfMonth} ${dayName}`
  },
}
