import { ReactNode, useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSearchParams } from "react-router-dom"
import { useDebouncedCallback } from "use-debounce"

import Icon from "components/Icon/Icon"
import { Input } from "ui"

import styles from "./SearchInput.module.css"

type SearchInputProps = {
  label: ReactNode
  className?: string
  onChange?: (value: string) => void
}

export const SearchInput = ({
  label,
  className = "",
  onChange,
  ...rest
}: SearchInputProps) => {
  const { t } = useTranslation()
  const [searchParams, setSearchParams] = useSearchParams()
  const querySearchValue = searchParams.get("search") || ""
  const [searchValue, setSearchValue] = useState(querySearchValue)
  const refInput = useRef<HTMLInputElement>(null)

  const debouncedChange = useDebouncedCallback((value: string) => {
    onChange?.(value)

    setSearchParams((prevParams) => {
      if (!value) prevParams.delete("search")

      const allPreviousValues = Object.fromEntries(prevParams.entries())

      const searchParams = {
        ...prevParams,
        ...allPreviousValues,
      }

      if (value) {
        return {
          ...searchParams,
          search: value,
        }
      }

      return searchParams
    })
  }, 500)

  // Fix losing focus on query search params
  useEffect(() => {
    if (refInput.current) {
      refInput.current.focus()
    }
  }, [querySearchValue])

  return (
    <Input
      label={label}
      ref={refInput}
      clearable
      iconStart={<Icon name="search-line" className={styles.icon} />}
      hideLabel
      placeholder={t("Search")}
      hideMessage
      className={`${styles.inputWrap} ${className}`}
      value={searchValue}
      onChange={({ target: { value } }) => {
        setSearchValue(value)

        debouncedChange(value)
      }}
      {...rest}
    />
  )
}
