import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useParams, useSearchParams } from "react-router-dom"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  Select as SelectAriaKit,
  SelectItem,
  SelectLabel,
  SelectPopover,
} from "components/Ariakit"
import { useComboboxStore, useSelectStore } from "components/Ariakit/hooks"
import { fuseSearch } from "components/Ariakit/hooks/useFilter/fuseSearch"
import Icon from "components/Icon/Icon"
import { Button, ButtonText, Label, Text } from "ui"

import {
  LocationType,
  useGetLocationsWithAvailabilityQuery,
} from "generated/graphql"

import { useEventInstance } from "../forms/EventInstanceForm/EventInstance.context"
import styles from "./SelectLocation.module.css"

type SelectLocationProps = {
  className?: string
  readOnly: boolean
  locationId?: string
  visuallyHidden?: boolean
}

export const SelectLocation = ({
  className = "",
  readOnly = false,
  locationId,
  visuallyHidden = false,
}: SelectLocationProps) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const locationParams = searchParams.get("location")
  const locationParamsDecoded = locationParams
    ? decodeURIComponent(locationParams)?.split(",")
    : []

  const { eventId } = useParams()

  const combobox = useComboboxStore()

  const { fromDateTimeLocal, toDateTimeLocal } = useEventInstance()

  const select = useSelectStore({
    combobox: combobox,
    defaultValue:
      locationId ||
      (locationParamsDecoded.length === 1
        ? locationParamsDecoded[0]
        : undefined),
    focusLoop: "vertical",
  })

  const { value: comboboxValue } = combobox.useState()
  const { value: selectedValue } = select.useState()

  const { data, previousData } = useGetLocationsWithAvailabilityQuery({
    variables: {
      locationType: LocationType.Room,
      fromTime: new Date(fromDateTimeLocal),
      toTime: new Date(toDateTimeLocal),
      eventInstanceId: eventId || null,
    },
    fetchPolicy: "cache-and-network",
  })

  const locationsData = data?.locations || previousData?.locations || []

  const options =
    locationsData.map((location) => {
      if (location.__typename !== "Room") return {}

      const { id, label, capacity, isAvailable } = location

      return {
        value: id as string,
        label,
        capacity,
        isAvailable,
      }
    }) ?? []

  const selectedLocation = options.find(
    (option) => option.value === selectedValue
  )

  const filteredList = useMemo(
    () => fuseSearch(options, comboboxValue),
    [options, comboboxValue]
  )

  return (
    <>
      <div className={`${styles.wrap} ${className} `}>
        <Label store={select} as={SelectLabel} className={styles.label}>
          {t("Location")}
        </Label>

        <div className={styles.selectWrap}>
          <SelectAriaKit
            name={"locationId"}
            store={select}
            className={styles.select}
            disabled={readOnly}
          >
            <Text
              className={styles.selectedItem}
              data-is-available={selectedLocation?.isAvailable}
            >
              {selectedLocation?.label || t("Select a location")}
            </Text>

            <Icon name="arrow-down-s-line" className={styles.arrowIcon} />
          </SelectAriaKit>
          {selectedValue && (
            <Button
              icon={<Icon name="close-line" />}
              aria-label={t("Clear selection")}
              className={styles.clearButton}
              onClick={(e: { stopPropagation: () => void }) => {
                e.stopPropagation()

                select.setOpen(false)

                select.setValue("")
              }}
            />
          )}
        </div>
        <div className={styles.locationCapacityIndicator}>
          {selectedLocation?.capacity && (
            <>
              <Icon name="group-line" arial-label="Room capacity" />
              <Text>{selectedLocation?.capacity}</Text>
            </>
          )}
        </div>

        <SelectPopover store={select} composite={false} sameWidth>
          <Combobox
            store={combobox}
            autoSelect
            placeholder={"Search for a location"}
          />
          <ComboboxList store={combobox}>
            {/* Ariakit Select submits first option when non-selected value is submitted; default should be empty value */}
            <SelectItem hidden value="" />
            {filteredList.map(({ value, label, capacity, isAvailable }) => {
              return (
                <ComboboxItem
                  role="button"
                  aria-label={t("select option")}
                  key={value}
                  focusOnHover
                  render={(props) => (
                    <SelectItem
                      key={value}
                      className={styles.selectItem}
                      value={value}
                      direction="vertical"
                      subContent={
                        <ButtonText
                          className={styles.selectItemMessage}
                          size="small"
                        >
                          ({isAvailable ? t("Available") : t("Booked")}) -
                          <Icon name="group-line" />
                          <span>{capacity}</span>
                        </ButtonText>
                      }
                      {...props}
                    >
                      {label}
                    </SelectItem>
                  )}
                />
              )
            })}
          </ComboboxList>
        </SelectPopover>
      </div>

      {selectedLocation?.isAvailable === false && !visuallyHidden && (
        <div className={styles.errorMessage}>
          <Icon name="alert-line" className={styles.errorIcon} />
          <Text size="small" className={styles.errorText}>
            <b>{selectedLocation.label}</b>
            {t(" has previous bookings at this time. ")}
          </Text>
        </div>
      )}
    </>
  )
}
