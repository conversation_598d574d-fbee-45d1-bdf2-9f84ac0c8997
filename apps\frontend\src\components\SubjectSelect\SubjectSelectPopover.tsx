import { matchSorter } from "match-sorter"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useDebounce } from "use-debounce"

import { formatPersonaId } from "@leviosa/utils"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  SelectItem,
  SelectPopover,
} from "components/Ariakit"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { SelectPopoverFallback } from "components/SelectPopoverFallback/SelectPopoverFallback"
import isDefined from "utils/isDefined"

import {
  useGetSubjectQuery,
  useSearchForSubjectsQuery,
} from "generated/graphql"

import { SubjectSelectOption } from "./SubjectSelect"
import styles from "./SubjectSelect.module.css"

type SubjectSelectPopoverProps = {
  searchValue: string
  selectedSubjectId?: string
  recentSubjectOptions: SubjectSelectOption[]
} & React.ComponentProps<typeof SelectPopover>

export const SubjectSelectPopover = ({
  searchValue,
  selectedSubjectId,
  recentSubjectOptions,
  ...rest
}: SubjectSelectPopoverProps) => {
  const { t } = useTranslation()
  const [debouncedSearchValue] = useDebounce(searchValue, 200)

  const { data: subjectData } = useGetSubjectQuery({
    variables: selectedSubjectId ? { id: selectedSubjectId } : undefined,
    skip: !selectedSubjectId,
  })

  const selectedSubject = subjectData?.subject
    ? {
        id: subjectData.subject.id,
        name: subjectData.subject.name,
        personaId: subjectData.subject.personaId,
      }
    : undefined

  const {
    data: subjectsData,
    previousData: previousSubjectsData,
    loading,
    error,
  } = useSearchForSubjectsQuery({
    variables: { searchQuery: debouncedSearchValue },
    fetchPolicy: "network-only",
  })

  const searchSubjectOptions: SubjectSelectOption[] =
    (subjectsData || previousSubjectsData)?.searchForSubjects.subjects.map(
      (subject) => ({
        id: subject.id,
        name: subject.name,
        personaId: subject.personaId,
      })
    ) || []

  const allOptions = [selectedSubject]
    .concat(
      searchValue.length > 2 && searchSubjectOptions.length
        ? searchSubjectOptions.filter(({ id }) => id !== selectedSubject?.id)
        : recentSubjectOptions.filter(({ id }) => id !== selectedSubject?.id)
    )
    .filter(isDefined)

  const matches = useMemo(() => {
    return matchSorter(allOptions, searchValue, {
      keys: ["name", "personaId"],
    })
  }, [searchValue, allOptions])

  const noMatchesNoSearch = !searchValue && !matches.length && !loading
  const hasNoMatches = !matches.length && searchValue && !loading

  return (
    <SelectPopover sameWidth {...rest}>
      <Combobox placeholder={t("Search...")} autoSelect />
      <ComboboxList>
        <SelectPopoverFallback
          loading={loading}
          noMatchesNoSearch={noMatchesNoSearch}
          hasNoMatches={hasNoMatches}
          error={error}
        >
          {matches.map(({ id, name, personaId }) => (
            <SelectItem
              key={id}
              value={id}
              render={
                <ComboboxItem
                  subContent={
                    <PiiSensitive>{formatPersonaId(personaId)}</PiiSensitive>
                  }
                  subContentClassName={styles.personaId}
                >
                  <PiiSensitive>{name}</PiiSensitive>
                </ComboboxItem>
              }
            />
          ))}
        </SelectPopoverFallback>
      </ComboboxList>
    </SelectPopover>
  )
}
