import { ReactNode } from "react"

import { Combobox, ComboboxPopover } from "components/Ariakit"
import { useComboboxStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { Label } from "ui"

import styles from "./Participants.module.css"

type UserFilterProps = {
  combobox: ReturnType<typeof useComboboxStore>
  placeholder: string
  label: string
  isLoading: boolean
  readOnly?: boolean
  children: ReactNode
}

export const UserFilter = ({
  combobox,
  placeholder,
  label,
  isLoading,
  readOnly,
  children,
}: UserFilterProps) => {
  return (
    <div>
      {/* COMEBACK use Wrapper element form input */}
      <Label as={"label"}>{label}</Label>

      <div className={styles.inputWrap}>
        <Combobox
          readOnly={readOnly}
          className={styles.input}
          store={combobox}
          placeholder={placeholder}
        />

        <Icon
          name="loader-4-line"
          spin
          className={styles.loading}
          data-loading={isLoading}
        />
      </div>

      <ComboboxPopover store={combobox} sameWidth>
        {children}
      </ComboboxPopover>
    </div>
  )
}
