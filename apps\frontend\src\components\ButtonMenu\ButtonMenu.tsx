import { useTranslation } from "react-i18next"

import { Menu, MenuButton, MenuButtonProps } from "components/Ariakit"
import { MenuItem } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"

import { Option } from "../Ariakit/types/Option"

export type ButtonMenuProps = {
  onSelect?: (value: string) => void
  menuStore: ReturnType<typeof useMenuStore>
  options: Option<string>[]
  renderItem?: (value: string) => React.ReactNode
  menuClassName?: string
} & Omit<MenuButtonProps, "store" | "ref" | "onSelect">

export const ButtonMenu = ({
  onSelect,
  menuStore,
  options,
  renderItem,
  menuClassName,
  icon = <Icon name="add-circle-line" />,

  ...rest
}: ButtonMenuProps) => {
  const { t } = useTranslation()

  return (
    <>
      <MenuButton store={menuStore} icon={icon} {...rest} />

      <Menu store={menuStore} className={menuClassName}>
        {options.length > 0 ? (
          options.map(({ value, label }) =>
            renderItem ? (
              renderItem(value)
            ) : (
              <MenuItem
                key={value}
                focusOnHover
                onClick={() => {
                  onSelect?.(value)
                  menuStore.setOpen(false)
                }}
              >
                {label}
              </MenuItem>
            )
          )
        ) : (
          <MenuItem>{t("No results Found")}</MenuItem>
        )}
      </Menu>
    </>
  )
}
