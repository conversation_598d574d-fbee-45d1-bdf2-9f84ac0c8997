import { get, orderBy } from "lodash"

import { notification } from "ui"
import { Mutable } from "utils/mutableType"
import { ageToHours } from "utils/parseAgeToHours"

import { DashboardQuery } from "generated/graphql"

import { SearchSettingsType } from "./Dashboard"
import { getLocationText } from "./getLocationText"

// Icelandic collator for proper sorting of Icelandic characters
const icelandicCollator = new Intl.Collator("is-IS", { sensitivity: "base" })

const getValueForColumn = (
  row: DashboardQuery["team"]["rows"][0],
  column: SearchSettingsType["column"]
) => {
  const value = get(row, column)

  if (value === undefined) {
    notification.create({
      status: "error",
      message: `Column ${column} is not defined on DashboardQuery`,
    })
  }

  if (column === "subject.location") return getLocationText(row.subject)

  if (column === "subject.age") return ageToHours(row.subject.age)

  if (column === "priority")
    return row[column] === null ? Number.MAX_SAFE_INTEGER : row[column]

  return get(row, column)
}

export const sortDashboardData = (
  data: Mutable<DashboardQuery["team"]["rows"]>,
  searchSettings: SearchSettingsType
) => {
  const { order, column } = searchSettings

  if (order === "default") return data

  const direction = order === "asc" ? "asc" : "desc"

  // For string values that might contain Icelandic characters
  if (
    data.length > 0 &&
    typeof getValueForColumn(data[0], column) === "string"
  ) {
    // Use custom sorting with Icelandic collator
    return [...data].sort((a, b) => {
      const valueA = (getValueForColumn(a, column) as string) || ""
      const valueB = (getValueForColumn(b, column) as string) || ""

      // Compare using Icelandic collation rules
      const result = icelandicCollator.compare(valueA, valueB)
      return direction === "asc" ? result : -result
    })
  }

  // For non-string values, use the original lodash orderBy
  return orderBy(data, [(row) => getValueForColumn(row, column)], [direction])
}
