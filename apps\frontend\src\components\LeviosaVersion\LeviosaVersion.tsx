import { useState } from "react"
import {
  browser<PERSON>ame,
  browserVersion,
  osName,
  osVersion,
} from "react-device-detect"

import { PowerMenuGroupKey } from "features/power-menu/lib/enums/PowerMenuGroupKeys.enum"
import { usePowerMenuGroups } from "features/power-menu/usePowerMenuGroups"
import { Modal } from "ui/index"

type Props = {
  isOpen?: boolean
  onClose?: () => void
}

export const LeviosaVersion = ({ isOpen = false, onClose }: Props) => {
  const [isLeviosaVersionWindowOpen, setIsLeviosaVersionWindowOpen] =
    useState<boolean>(false)

  usePowerMenuGroups(
    [
      {
        id: PowerMenuGroupKey.moderator,
        items: [
          {
            id: "LeviosaAppVersion",
            title: "Leviosa app version",
            execute: () => setIsLeviosaVersionWindowOpen(true),
          },
        ],
      },
    ],
    []
  )

  const leviosaApp: Record<string, string> = {
    browser: browserName + ": " + browserVersion,
    os: osName + ": " + osVersion,
  }

  return (
    <Modal
      isOpen={isLeviosaVersionWindowOpen || isOpen}
      onClose={() => {
        setIsLeviosaVersionWindowOpen(false)
        onClose?.()
      }}
    >
      {Object.keys(leviosaApp).map((key) => (
        <p key={key}>{leviosaApp[key]}</p>
      ))}
    </Modal>
  )
}
