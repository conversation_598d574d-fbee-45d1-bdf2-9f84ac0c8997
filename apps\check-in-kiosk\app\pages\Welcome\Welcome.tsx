import { useNavigate } from "@remix-run/react"
import { useTranslation } from "react-i18next"

import ClockIllustration from "@leviosa/assets/illustrations/clock.svg?react"
import { Button } from "@leviosa/components"
import { useTimeout } from "@leviosa/utils"

import formatListToString from "app/utils/formatListToString"

import styles from "./Welcome.module.css"

interface WelcomeProps {
  checkInData: {
    fromTime: string | null
    subjectName: string | null
    providerNames: string[] | null
    locationLabel: string | null
  }
}

const Welcome = ({ checkInData }: WelcomeProps) => {
  const navigate = useNavigate()
  const { t } = useTranslation()

  // Navigate back to checkin screen after 20 seconds
  useTimeout(() => {
    navigate(-1)
  }, 20000)

  const { fromTime, subjectName, providerNames, locationLabel } = checkInData

  const formattedProviderNames = providerNames
    ? formatListToString(providerNames, t("and"))
    : ""

  if (!fromTime) {
    navigate("/error")
    return null
  }

  const currentTime = new Date()
  const fromDateTime = new Date(fromTime)
  const differenceInMilliseconds =
    fromDateTime.getTime() - currentTime.getTime()
  const differenceInMinutes = Math.round(differenceInMilliseconds / 1000 / 60)

  const AppointmentTime = () => {
    if (differenceInMinutes > 0) {
      return (
        <span className={styles.appointmentTime}>
          <p className={styles.appointmentTimeLabel}> {t("startsIn")}</p>
          <h2>
            {t("minutes", { minutes: differenceInMinutes, context: "future" })}
          </h2>
        </span>
      )
    } else {
      return (
        <span className={styles.appointmentTime}>
          <p className={styles.appointmentTimeLabel}>{t("started")}</p>
          <h2>
            {t("minutes", {
              minutes: Math.abs(differenceInMinutes),
              context: "past",
            })}
          </h2>
        </span>
      )
    }
  }

  return (
    <div className={styles.container}>
      <ClockIllustration />
      <h1>
        {t("welcome")} {subjectName}
      </h1>
      <p>
        {`${t("youHaveAnAppointment")} ${
          formattedProviderNames
            ? t("appointmentProvider", { name: formattedProviderNames }) + " "
            : ""
        } ${
          locationLabel
            ? t("appointmentLocation", { location: locationLabel })
            : ""
        }`}
      </p>

      <AppointmentTime />

      <Button onClick={() => navigate("/checkin")}>
        {t("goBackToMainScreen")}
      </Button>
    </div>
  )
}

export default Welcome
