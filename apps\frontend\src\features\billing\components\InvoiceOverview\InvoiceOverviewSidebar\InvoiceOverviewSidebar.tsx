import { format, isValid, parse, subWeeks } from "date-fns"
import { i18nInstance } from "i18n"
import { matchSorter } from "match-sorter"
import { useEffect } from "react"
import { Trans, useTranslation } from "react-i18next"
import {
  createSearchParams,
  generatePath,
  Link,
  useSearchParams,
} from "react-router-dom"

import { useComboboxStore, useSelectStore } from "components/Ariakit/hooks"
import DatePicker from "components/DatePicker/DatePicker"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import ProviderSelect from "components/ProviderSelect/ProviderSelect"
import Select from "components/Select/Select"
import SubjectSelect from "components/SubjectSelect/SubjectSelect"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { RouteStrings } from "routes/RouteStrings"
import { But<PERSON>, Heading, Text } from "ui"
import { InputWrap } from "ui/components/Layout/FormGrid"

import {
  PaymentMethod,
  PaymentStatus,
  PermissionKey,
  useGetIssuersForInvoiceQuery,
} from "generated/graphql"

import { INVOICES_LIMIT } from "../InvoiceOverview"
import { getInvoiceExportURL } from "../getInvoiceExportURL"
import styles from "./InvoiceOverviewSidebar.module.css"

export const paymentMethods = [
  {
    label: i18nInstance.t("enums:PaymentMethods.Card"),
    value: PaymentMethod.Card,
  },
  {
    label: i18nInstance.t("enums:PaymentMethods.Cash"),
    value: PaymentMethod.Cash,
  },
  {
    label: i18nInstance.t("enums:PaymentMethods.BankTransfer"),
    value: PaymentMethod.BankTransfer,
  },
  {
    label: i18nInstance.t("enums:PaymentMethods.Claim"),
    value: PaymentMethod.Claim,
  },
  {
    label: i18nInstance.t("enums:PaymentMethods.Other"),
    value: PaymentMethod.Other,
  },
]

const useValueToSearchParams = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  return (key: string) => ({
    setValue(value: string) {
      setSearchParams((searchParams) => {
        if (value === "") {
          searchParams.delete(key)
        } else {
          searchParams.set(key, value)
        }
        return searchParams
      })
    },
    value: searchParams.get(key) || "",
  })
}

const DATE_FORMAT = "yyyy-MM-dd"
function getFromAndToDefaultDates() {
  // only fetch invoices from the last week by default
  const fromDateDefault = format(subWeeks(new Date(), 1), DATE_FORMAT)
  const toDateDefault = format(new Date(), DATE_FORMAT)

  return { fromDateDefault, toDateDefault }
}

function isValidDate(date: string) {
  return isValid(parse(date, DATE_FORMAT, new Date()))
}

function getDateValue(date: string, isFromDate: boolean) {
  const { fromDateDefault, toDateDefault } = getFromAndToDefaultDates()
  const defaultDate = isFromDate ? fromDateDefault : toDateDefault

  return isValidDate(date) ? date : defaultDate
}

type InvoiceOverviewSidebarProps = {
  isPastLimit: boolean
}
export default function InvoiceOverviewSidebar({
  isPastLimit,
}: InvoiceOverviewSidebarProps) {
  const { t: tBFilter } = useTranslation("routes", {
    keyPrefix: "billing.filter",
  })
  const { t: tBExport } = useTranslation("routes", {
    keyPrefix: "billing.export",
  })
  const { t: tEnum } = useTranslation("enums")

  const [searchParams, setSearchParams] = useSearchParams()

  const valueToSearchParams = useValueToSearchParams()

  const { value: fromDate, setValue: setFromDate } =
    valueToSearchParams("fromDate")
  const { value: toDate, setValue: setToDate } = valueToSearchParams("toDate")
  const { value: subjectId, setValue: setSubjectId } =
    valueToSearchParams("subject")
  const { value: providerId, setValue: setProviderId } =
    valueToSearchParams("provider")

  const fromDateValue = getDateValue(fromDate, true)
  const toDateValue = getDateValue(toDate, false)

  useEffect(() => {
    // Set default values for fromDate and toDate
    // if they are not set in the URL
    if (!fromDate || !isValid(fromDate)) {
      setSearchParams((searchParams) => {
        searchParams.set("fromDate", fromDateValue)
        return searchParams
      })
    }
    if (!toDate || !isValid(toDate)) {
      setSearchParams((searchParams) => {
        searchParams.set("toDate", toDateValue)
        return searchParams
      })
    }
  }, [])

  const comboboxStoreIssuer = useComboboxStore({})
  const comboboxStoreStatus = useComboboxStore({})

  const selectStoreIssuer = useSelectStore({
    combobox: comboboxStoreIssuer,
    ...valueToSearchParams("invoiceIssuer"),
  })

  const selectStoreStatus = useSelectStore({
    combobox: comboboxStoreStatus,
    ...valueToSearchParams("status"),
  })

  const selectStorePaymentMethod = useSelectStore({
    ...valueToSearchParams("paymentMethod"),
    focusLoop: "vertical",
  })

  const selectedInvoiceIssuer = selectStoreIssuer.useState("value")
  const selectedStatus = selectStoreStatus.useState("value")
  const selectedPaymentMethod = selectStorePaymentMethod.useState("value")

  const { value: issuerValue } = comboboxStoreIssuer.useState()
  const { value: statusValue } = comboboxStoreStatus.useState()

  const {
    data: issuersData,
    loading: isIssuersLoading,
    previousData: issuersPreviousData,
  } = useGetIssuersForInvoiceQuery({
    variables: {
      filter: null,
    },
  })

  const issuersOptions =
    (issuersData?.invoiceIssuers || issuersPreviousData?.invoiceIssuers)?.map(
      ({ id, title }) => ({
        value: id,
        label: title,
      })
    ) || []

  const statusOptions = [
    {
      value: "DRAFT",
      label: tBFilter("statusDraft"),
    },
    ...Object.values(PaymentStatus)
      .reverse()
      .map((status) => ({
        value: status as string,
        label: tEnum(`PaymentStatus.${status}`),
      })),
  ]

  const filteredIssuerOptions = matchSorter(issuersOptions, issuerValue, {
    keys: ["label", "value"],
  })

  const filteredStatusOptions = matchSorter(statusOptions, statusValue, {
    keys: ["label", "value"],
  })

  const handleSubjectChange = (subjectId: string | null) => {
    setSubjectId(subjectId ?? "")
  }

  const handleProviderChange = (providerId: string | null) => {
    setProviderId(providerId ?? "")
  }

  const invoiceIssuerString =
    typeof selectedInvoiceIssuer === "string" ? selectedInvoiceIssuer : ""
  const statusString = typeof selectedStatus === "string" ? selectedStatus : ""
  const paymentMethodString =
    typeof selectedPaymentMethod === "string" ? selectedPaymentMethod : ""

  const downloadInvoiceURL = getInvoiceExportURL(
    searchParams.get("subject") || "",
    invoiceIssuerString,
    statusString,
    { fromDate: fromDateValue, toDate: toDateValue },
    searchParams.get("provider") || "",
    paymentMethodString
  )

  return (
    <div className={styles.wrap}>
      <form className={styles.form}>
        <div className={styles.headerContent}>
          <Heading>{tBFilter("header")}</Heading>
          <Link
            to={
              (generatePath(RouteStrings.invoiceOverview),
              {
                search: createSearchParams({
                  fromDate: fromDateValue,
                  toDate: toDateValue,
                }).toString(),
              })
            }
          >
            {tBFilter("resetAll")}
          </Link>
        </div>

        <InputWrap>
          <SubjectSelect
            name="subject"
            isClearable
            selectedSubjectId={subjectId}
            onChange={handleSubjectChange}
          />
        </InputWrap>

        <InputWrap>
          <ProviderSelect
            onChange={handleProviderChange}
            name="provider"
            providerId={providerId}
            isClearable
          />
        </InputWrap>

        <InputWrap>
          <FiltrableSelect
            label={tBFilter("issuers")}
            name="issuer"
            placeholder={tBFilter("selectIssuer")}
            comboboxStore={comboboxStoreIssuer}
            filteredOptions={filteredIssuerOptions}
            options={issuersOptions}
            isLoading={isIssuersLoading}
            selectStore={selectStoreIssuer}
            sameWidth
            hideMessage
            isClearable
          />
        </InputWrap>

        <InputWrap>
          <FiltrableSelect
            label={tBFilter("status")}
            name="status"
            placeholder={tBFilter("statusAny")}
            comboboxStore={comboboxStoreStatus}
            filteredOptions={filteredStatusOptions}
            options={statusOptions}
            selectStore={selectStoreStatus}
            sameWidth
            hideMessage
            isClearable
          />
        </InputWrap>

        <InputWrap>
          <Select
            selectStore={selectStorePaymentMethod}
            options={paymentMethods}
            placeholder={tBFilter("paymentMethodAny")}
            name="paymentMethod"
            label={tBFilter("paymentMethod")}
            hideMessage
            sameWidth
            isClearable
          />
        </InputWrap>

        <InputWrap>
          <DatePicker
            label={tBFilter("encounterFrom")}
            portal={true}
            value={fromDateValue}
            onChange={({ target: { value } }) =>
              // check if valid, because the date might be an empty string
              isValidDate(value) && setFromDate(value)
            }
            placeholder={tBFilter("encounterSelectDate")}
            className={styles.input}
            hideMessage
          />
        </InputWrap>

        <InputWrap>
          <DatePicker
            label={tBFilter("encounterTo")}
            value={toDateValue}
            onChange={({ target: { value } }) =>
              isValidDate(value) && setToDate(value)
            }
            portal={true}
            placeholder={tBFilter("encounterSelectDate")}
            className={styles.input}
            min={fromDate}
            hideMessage
          />
        </InputWrap>

        {isPastLimit && (
          <Panel>
            <Trans
              i18nKey={tBFilter("invoiceOverviewLimitReached")}
              values={{
                maxValue: INVOICES_LIMIT,
              }}
            />
          </Panel>
        )}
      </form>

      <Restricted to={PermissionKey.BillingInvoiceCreate}>
        <div className={styles.exportWrapper}>
          <Heading>{tBExport("header")}</Heading>
          <Text size="small">{tBExport("downloadCSVFile")}</Text>
          <Button
            as="a"
            download={`Invoices_${fromDateValue}_${toDateValue}.csv`}
            href={downloadInvoiceURL}
            size="large"
            iconEnd={<Icon name="download-2-line" />}
            className={styles.exportButton}
          >
            {tBExport("exportButton")}
          </Button>
        </div>
      </Restricted>
    </div>
  )
}
