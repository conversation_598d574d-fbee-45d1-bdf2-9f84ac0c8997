#!/bin/bash


if [ -z $1 ]; then
  echo "Please set db password";
  kubectl get secret
  exit 1
fi

if [ -z $2 ]; then
  echo "Please set variant";
  exit 1
fi


secret=$1
VARIANT=$2

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)

context=$(kubectl config current-context)


sql='
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;
'
echo "This executition will run on $pod in $context"

source "$SCRIPT_DIR/.sure.sh"


echo "Will execute '$sql' on the primary database after 5s"
sleep 5


echo $pod
kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$secret psql -d leviosa -U postgres -c '$sql' " 