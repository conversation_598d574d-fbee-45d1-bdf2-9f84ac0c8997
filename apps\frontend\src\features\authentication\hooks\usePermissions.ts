import { useCallback } from "react"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"

import { PermissionKey } from "generated/graphql"

export default function usePermissions() {
  // Get permissionKeys array from global context
  const { globalData } = useGlobalState()
  const permissionKeys = globalData.actor.permissions || []

  // Check if user has a specific permission
  const hasPermission = useCallback(
    (permission: PermissionKey) => permissionKeys.includes(permission),
    [permissionKeys]
  )

  // Check if all permissions are present
  const hasAllPermissions = useCallback(
    (keys: PermissionKey[]) => keys.every((key) => hasPermission(key)),
    [hasPermission]
  )

  // Check if any of the permissions are present
  const hasSomePermissions = useCallback(
    (keys: PermissionKey[]) => keys.some((key) => hasPermission(key)),
    [hasPermission]
  )

  return {
    hasPermission,
    hasAllPermissions,
    hasSomePermissions,
  }
}
