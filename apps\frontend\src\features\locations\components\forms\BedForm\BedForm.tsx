import { ApolloError } from "@apollo/client"
import _ from "lodash"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import { Button, FormGrid, Heading, Input } from "ui"
import { Center } from "ui/components/Layout/Center"
import { camelCaseToNormalWord } from "utils/camelCaseToNormal"

import { BedType } from "generated/graphql"

import { Building } from "../../BuildingOverview/BuildingOverview"
import styles from "../LocationForm.module.css"

const baseSchema = z.object({
  label: z.string(),
  bedType: z.nativeEnum(BedType),
})

type BedFormProps = {
  onSubmit: (data: z.infer<typeof baseSchema>) => void
  onCancel: () => void
  error?: ApolloError
  loading: boolean
  formData?: Building["rooms"]["rooms"][0]["beds"]["beds"][0]
}

export const BedForm = ({
  onSubmit,
  onCancel,
  error,
  loading,
  formData,
}: BedFormProps) => {
  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const { t } = useTranslation()
  const bedTypeOptions = Object.keys(BedType).map((key) => ({
    label: camelCaseToNormalWord(key),
    value: (BedType as Record<string, string>)[key],
  }))

  const selectRoomTypeStore = useSelectStore({
    defaultValue: formData?.bedType || bedTypeOptions[0].value,
  })

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setValidationError(null)

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = baseSchema.safeParse(data)

    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    onSubmit?.(validatedInput.data)
  }

  return (
    <Panel as="form" onSubmit={handleSubmit} className={styles.panelForm}>
      <Center as={Heading} size="display">
        {_.isEmpty(formData) ? t("Add Bed") : t("Edit Bed")}
      </Center>

      <br />
      <FormGrid as="div" colSpan={4}>
        <Input
          defaultValue={formData?.label}
          label={t("label")}
          name="label"
          type="text"
          required
          className={styles.span2}
        />
        <Select
          label={t("Type")}
          name="bedType"
          selectStore={selectRoomTypeStore}
          options={bedTypeOptions}
          className={styles.span2}
          disabled={!_.isEmpty(formData)}
        />
      </FormGrid>
      {(error || validationError) && (
        <Panel status="error">
          {validationError?.message}
          {error?.message}
        </Panel>
      )}

      <div className={styles.footer}>
        <Button disabled={loading} onClick={onCancel}>
          {t("Cancel")}
        </Button>
        <Button type="submit" variant="filled" disabled={loading}>
          {_.isEmpty(formData) ? t("Save") : t("Update")}
        </Button>
      </div>
    </Panel>
  )
}
