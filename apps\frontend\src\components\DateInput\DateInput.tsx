import {
  forwardRef,
  HTMLAttributes,
  InputHTMLAttributes,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from "react"

import { useSelectStore } from "components/Ariakit/hooks"
import { Label, Text } from "ui"

import styles from "./DateInput.module.css"
import { DayInput } from "./components/DayInput/DayInput"
import { MonthInput } from "./components/MonthInput/MonthInput"
import { YearInput } from "./components/YearInput/YearInput"

const getDay = (date?: string) => date?.slice(8, 10)
const getMonth = (date?: string) => date?.slice(5, 7)
const getYear = (date?: string) => date?.slice(0, 4)

const getValidIsoDate = (year?: string, month?: string, day?: string) => {
  if (!year || !month || !day || day === "0") return undefined
  const zeroPaddedYear = (year || "").padStart(4, "0")
  const zeroPaddedMonth = (month || "1").padStart(2, "0")
  const zeroPaddedDay = (day || "1").padStart(2, "0")
  return `${zeroPaddedYear}-${zeroPaddedMonth}-${zeroPaddedDay}`
}

export type Status = "default" | "error" | "warning" | "success"

export type NativeInputProps =
  | "required"
  | "min"
  | "max"
  | "autoComplete"
  | "disabled"
  | "readOnly"
  | "name"

export type DateInputProps = {
  label: string
  className?: string
  message?: ReactNode
  hideLabel?: boolean
  status?: Status
  disabled?: boolean
  value?: string
  defaultValue?: string
  onChange?: (value: string) => void
  variant?: "default" | "clear"
  inputProps?: Omit<
    InputHTMLAttributes<HTMLInputElement>,
    NativeInputProps | "value" | "defaultValue" | "onChange"
  >
} & Omit<HTMLAttributes<HTMLDivElement>, NativeInputProps | "onChange"> &
  Pick<InputHTMLAttributes<HTMLInputElement>, NativeInputProps>

const DateInput = forwardRef<HTMLInputElement, DateInputProps>(function (
  {
    label,
    className = "",
    hideLabel,
    message,
    status,
    variant,
    // Forwards to input
    onChange,
    disabled = false,
    value,
    defaultValue,
    name = "",
    required,
    min,
    max,
    autoComplete,
    readOnly,
    inputProps = {},

    // Forwards to div
    ...rest
  },
  ref
) {
  const parsedValue = value ? value.split("T")[0] : defaultValue || ""

  const [prevValue, setPrevValue] = useState(parsedValue)
  const [day, setDay] = useState<string | undefined>(getDay(parsedValue))
  const [month, setMonth] = useState<string | undefined>(
    getMonth(parsedValue) || "1"
  )

  const [year, setYear] = useState<string | undefined>(getYear(parsedValue))

  const yearRef = useRef<HTMLInputElement>(null)

  const selectMonth = useSelectStore({
    value: month ? month : undefined,
    defaultValue: defaultValue ? getMonth(defaultValue) : undefined,

    orientation: "vertical",
  })

  const validIsoDate = getValidIsoDate(year, month, day)

  useEffect(() => {
    if (
      value === undefined &&
      prevValue === undefined &&
      day &&
      year &&
      month
    ) {
      setDay(day)
      setMonth(month)
      setYear(year)

      return
    }

    if (value && validIsoDate !== value.slice(0, 10)) {
      if (value !== prevValue) {
        setDay(getDay(value))
        setMonth(getMonth(value))
        setYear(getYear(value))
        setPrevValue(value)
      } else if (validIsoDate) {
        onChange?.(validIsoDate || "")
      }
    }
  }, [value, validIsoDate, prevValue, day, year, month])

  const mergedInputProps = {
    disabled,
    name,
    required,
    min,
    max,
    autoComplete,
    readOnly,
    ...inputProps,
  }
  return (
    <div
      className={`${styles.wrap} ${className}`}
      data-status={status}
      data-variant={variant}
      data-disabled={disabled}
      {...rest}
    >
      <Label as="label" visuallyHidden={hideLabel}>
        {label}
      </Label>
      <input
        className="visuallyHidden"
        ref={ref}
        type="date"
        value={parsedValue || validIsoDate}
        {...mergedInputProps}
      />
      <div className={styles.inputsWrapper}>
        <DayInput
          value={day}
          onDayChange={(value) => {
            if (value.length === 2) {
              selectMonth.setOpen(true)
            }
            setDay(value)
          }}
          disabled={disabled}
          required={required}
          readOnly={readOnly}
        />
        <span style={{ alignSelf: "center" }}>.</span>
        <MonthInput
          selectMonth={selectMonth}
          onSelect={(value) => {
            yearRef.current?.focus()

            setMonth(value)
          }}
        />
        <YearInput
          ref={yearRef}
          onChange={(value) => {
            setYear(value)
          }}
          value={year || ""}
        />
      </div>
      <Text size="small" className={styles.message}>
        {message || <>&nbsp;</>}
      </Text>
    </div>
  )
})
export default DateInput
