const github = require("@actions/github")
const core = require("@actions/core")

const run = async () => {
  const token = core.getInput("token", { required: true })
  const repository = core.getInput("repository", { required: true })
  const currentRunId = core.getInput("current_run_id", { required: true })
  const branch = core.getInput("github_ref_name", { required: true })
  const [owner, repo] = repository.split("/")
  const octokit = github.getOctokit(token)
  const { data } = await octokit.actions.listWorkflowRunsForRepo({ owner, repo, branch, status: "in_progress" })
  if (
    data.workflow_runs.some((workflow_run) => {
      if (workflow_run.id.toString() === currentRunId) return false
      
      return workflow_run.name === "Publish" || workflow_run.name === "Release"
    })
  ) {
    core.setFailed("There is a Release or Publish workflow already running")
  }
}

run().catch((e) => core.setFailed(e.message))
