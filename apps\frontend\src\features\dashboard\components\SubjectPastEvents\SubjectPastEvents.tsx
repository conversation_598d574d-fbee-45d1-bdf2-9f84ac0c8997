import c from "classnames"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { Link } from "react-router-dom"

import { Icon } from "@leviosa/components"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Panel from "components/Panel/Panel"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"
import { calendarColorMap, color } from "styles/colors"
import { Heading, Text } from "ui"
import { isTypename } from "utils/isTypename"
import useDateFormatter from "utils/useDateFormatter"

import {
  UpcomingEventFragmentFragment,
  useGetSubjectEventsQuery,
} from "generated/graphql"

import styles from "./SubjectPastEvents.module.css"

type UpcomingEventsProps = {
  subjectId: string
}

const PastEvent = ({ event }: { event: UpcomingEventFragmentFragment }) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectSummary",
  })
  const getCalendarPath = useGetCalendarPathObject()
  const dateFormat = useDateFormatter()

  const providers =
    event.participants
      .filter(isTypename("ParticipantProvider"))
      .map((participant) => participant.provider) || []

  const firstProvider = providers[0] || null

  if (!firstProvider) {
    return null
  }

  const date = dateFormat(new Date(event.fromDate))
  const providerNames = providers.map((p) => p.name).join(", ")
  const eventTitle =
    event.serviceType?.name || (event.title?.trim() ? event.title : undefined)

  const cancellationReason = event.cancellationReason
    ? `: ${event.cancellationReason}`
    : ""

  const eventColor = event.serviceType?.color
    ? calendarColorMap[event.serviceType.color].light
    : color.levBlue.light

  return (
    <li>
      <Link
        key={event.id}
        to={getCalendarPath(RouteStrings.calendarViewEventInstance, {
          eventId: event.id,
          search: {
            provider: firstProvider.id,
            date: event.fromDate.split("T")[0],
          },
        })}
        className={c(styles.event, eventColor, {
          [styles.cancelledEvent]: event.canceledAt,
        })}
      >
        {event.canceledAt && (
          <Tooltip
            tooltipContent={`${tRoutes("eventCancelled")}${cancellationReason}`}
          >
            <Icon name="prohibited-2-line" className={styles.cancelledIcon} />
          </Tooltip>
        )}
        <Text>
          {date} • {providerNames} • {eventTitle}
        </Text>
      </Link>
    </li>
  )
}

export const SubjectPastEvents = ({ subjectId }: UpcomingEventsProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectSummary",
  })

  const inputFilter = useMemo(() => {
    return {
      participantSubjectId: [subjectId],
      fromDate: new Date(0), // Using Unix epoch (Jan 1, 1970) as fromDate
      toDate: new Date(),
      includeCanceled: true,
    }
  }, [subjectId])

  const { data, error } = useGetSubjectEventsQuery({
    variables: {
      inputFilter,
      limit: null,
    },
  })

  // Get the 5 most recent past events by taking the last 5 elements
  const pastEvents = useMemo(() => {
    if (!data?.eventInstances?.length) return []

    // The events are sorted by date (oldest first) so we want to take the last 5
    // If there are fewer than 5 events, this will return all available events
    const events = [...data.eventInstances]
    const recentEvents = events.slice(Math.max(events.length - 5, 0)).reverse()

    // Filter out events that are ongoing
    const filteredEvents = recentEvents.filter(
      (event) => new Date(event.toDate) < new Date()
    )

    return filteredEvents
  }, [data?.eventInstances])

  if (pastEvents.length === 0) {
    return null
  }

  return (
    <>
      <Heading>{tRoutes("recentEvents")}</Heading>
      <ul className={styles.eventList}>
        {error && (
          <Panel status="error">{tRoutes("errorLoadingRecentEvents")}</Panel>
        )}
        {pastEvents?.map((event) => <PastEvent event={event} key={event.id} />)}
      </ul>
    </>
  )
}
