//! Authentication and encryption types

use async_trait::async_trait;
use serde::{Deserialize, Serialize};

// Electronic ID types
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ElectronicId {
    pub user_id: String,
    pub name: String,
    pub national_id: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ElectronicIdUserInfo {
    pub user_id: String,
    pub name: String,
    pub national_id: String,
    pub mobile: Option<String>,
    pub email: Option<String>,
}

// Password types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PasswordError {
    HashingFailed(String),
    VerificationFailed(String),
    InvalidPassword(String),
}

impl std::fmt::Display for PasswordError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PasswordError::HashingFailed(msg) => write!(f, "Password hashing failed: {}", msg),
            PasswordError::VerificationFailed(msg) => write!(f, "Password verification failed: {}", msg),
            PasswordError::InvalidPassword(msg) => write!(f, "Invalid password: {}", msg),
        }
    }
}

impl std::error::Error for PasswordError {}

#[async_trait]
pub trait PasswordHasher: Send + Sync {
    async fn hash(&self, password: &str) -> Result<String, PasswordError>;
    async fn verify(&self, password: &str, hash: &str) -> Result<bool, PasswordError>;
}

// Encryption types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EncryptionError {
    EncryptionFailed(String),
    DecryptionFailed(String),
    InvalidKey(String),
}

impl std::fmt::Display for EncryptionError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            EncryptionError::EncryptionFailed(msg) => write!(f, "Encryption failed: {}", msg),
            EncryptionError::DecryptionFailed(msg) => write!(f, "Decryption failed: {}", msg),
            EncryptionError::InvalidKey(msg) => write!(f, "Invalid key: {}", msg),
        }
    }
}

impl std::error::Error for EncryptionError {}

pub trait DataEncryptor: Send + Sync {
    fn encrypt(&self, plaintext: &[u8]) -> Result<Vec<u8>, EncryptionError>;
    fn decrypt(&self, ciphertext: &[u8]) -> Result<Vec<u8>, EncryptionError>;
    fn encrypt_to_string(&self, plaintext: &[u8]) -> Result<String, EncryptionError>;
    fn decrypt_from_string(&self, encoded: &str) -> Result<Vec<u8>, EncryptionError>;
}
