import c from "classnames"

import { color } from "styles/colors"
import { Text, TextProps, TextWithIcon, TextWithIconProps } from "ui"
import { ElementTypeAcceptingClassName } from "utils/polymorphicTypes"

import styles from "./Panel.module.css"

type Status = "default" | "success" | "error" | "warning" | "info"
type PanelVariant = "default" | "no-border"

const defaultElement = "div"

const getPanelClassName = ({
  status,
  variant,
  className,
}: {
  status: Status
  variant: PanelVariant
  className: string
}) =>
  c(
    className,
    styles.panel,
    color.light,
    variant === "no-border" && styles.noBorder,
    status === "success" && color.success,
    status === "warning" && color.warning,
    status === "error" && color.critical,
    status === "info" && color.blue
  )

export type PanelProps<T extends ElementTypeAcceptingClassName> = {
  status?: Status
  variant?: PanelVariant
} & TextProps<T>

export default function Panel<
  T extends ElementTypeAcceptingClassName = typeof defaultElement,
>({
  status = "default",
  variant = "default",
  className = "",
  ...rest
}: PanelProps<T>) {
  return (
    <Text
      className={getPanelClassName({ status, variant, className })}
      {...rest}
    />
  )
}

export type PanelWithIconProps<T extends ElementTypeAcceptingClassName> = {
  status?: Status
  variant?: PanelVariant
} & TextWithIconProps<T>

export function PanelWithIcon<
  T extends ElementTypeAcceptingClassName = typeof defaultElement,
>({
  status = "default",
  variant = "default",
  className = "",
  ...rest
}: PanelWithIconProps<T>) {
  return (
    <TextWithIcon
      className={getPanelClassName({ status, variant, className })}
      {...rest}
    />
  )
}
