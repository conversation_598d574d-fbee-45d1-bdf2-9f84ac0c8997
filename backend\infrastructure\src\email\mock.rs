
use async_trait::async_trait;
use leviosa_domain_contracts::services::{EmailSendInput, EmailSender, EmailSubmitInput};
use regex::Regex;

use std::{
    collections::HashMap,
    sync::{Arc, Mutex},
};

// Simple string interpolation utility
fn interpolate_template(template: String, params: &HashMap<String, String>) -> Result<String, anyhow::Error> {
    let rx = Regex::new("\\{([[:alpha:]_]+)\\}").map_err(|e| anyhow::anyhow!("Failed to compile regex: {}", e))?;

    let result = rx
        .replace_all(template.as_str(), |cap: &regex::Captures| {
            let key = cap.get(1).unwrap().as_str();
            params.get(key).unwrap_or_else(|| {
                panic!("failed to interpolate: unspecified parameter {key} in template")
            })
        })
        .to_string();

    Ok(result)
}

#[derive(Clone)]
pub struct OldMockEmailSender {
    pub history: MockEmailHistory,
    logs: bool,
}

impl OldMockEmailSender {
    pub fn new(logs: bool) -> (Self, MockEmailHistory) {
        let arc = Arc::new(Mutex::new(vec![]));
        (
            Self {
                history: arc.clone(),
                logs,
            },
            arc,
        )
    }
}

#[async_trait]
impl EmailSender for OldMockEmailSender {
    async fn send(&self, input: EmailSendInput) -> leviosa_domain::errors::Result<()> {
        let subject = interpolate_template(input.subject_template, &input.params)?;
        let body = interpolate_template(input.body_template, &input.params)?;
        let record = MockEmailRecord {
            to_email: input.to_email.clone(),
            to_name: input.to_name,
            subject,
            body,
            params: input
                .params
                .iter()
                .map(|(k, v)| ((*k).to_string(), (*v).to_string()))
                .collect(),
        };
        if self.logs {
            tracing::info!(?record, "Sending email");
        }
        self.history.lock().unwrap().push(record);

        if input.to_email == "<EMAIL>" {
            Err(anyhow::anyhow!("mock sender error").into())
        } else {
            Ok(())
        }
    }

    async fn submit(&self, input: EmailSubmitInput) -> leviosa_domain::errors::Result<()> {
        let record = MockEmailRecord {
            to_email: input.to_email.clone(),
            to_name: input.to_name,
            subject: input.subject,
            body: input.body,
            params: HashMap::new(),
        };
        if self.logs {
            tracing::info!(?record, "Sending email");
        }
        self.history.lock().unwrap().push(record);

        if input.to_email == "<EMAIL>" {
            Err(anyhow::anyhow!("mock sender error").into())
        } else {
            Ok(())
        }
    }
}

// TODO Kit: Refactor the API
pub type MockEmailHistory = Arc<Mutex<Vec<MockEmailRecord>>>;

#[derive(Debug)]
pub struct MockEmailRecord {
    pub to_email: String,
    pub to_name: String,
    pub subject: String,
    pub body: String,
    pub params: HashMap<String, String>,
}
