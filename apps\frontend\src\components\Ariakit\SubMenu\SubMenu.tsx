import { MenuButton, MenuButtonProps, useMenuStore } from "@ariakit/react"
import * as React from "react"

import { Menu, MenuProvider } from "components/Ariakit/Menu/Menu"
import Icon from "components/Icon/Icon"

import { MenuItem } from "../Menu/MenuItem/MenuItem"
import styles from "./SubMenu.module.css"

export interface SubMenuProps extends MenuButtonProps {
  label: React.ReactNode
}

const SubMenu = React.forwardRef<HTMLDivElement, SubMenuProps>(
  ({ label, children, className, ...props }, ref) => {
    const menu = useMenuStore()
    return (
      <MenuProvider store={menu}>
        <MenuButton
          {...props}
          ref={ref}
          render={props.render || <MenuItem />}
          className={styles.labelWrap}
        >
          <span>{label}</span>
          <Icon className={styles.iconArrowRight} name="arrow-right-s-line" />
        </MenuButton>
        <Menu gutter={4} shift={-8} hasSubmenu portal className={className}>
          {children}
        </Menu>
      </MenuProvider>
    )
  }
)

export { SubMenu }
