use super::context::LoaderError;
use async_graphql::{Error as AsyncGQLError, ErrorExtensions};
use leviosa_domain::{
    auth::{EncryptionError, PasswordError},
    doctor_letter_and_referral_api::DoctorLetterAndReferralApiError,
    errors::{
        AuthorizationError, CrossOrganisationError, CustomError, Error as DomainError, InputError,
        NotConfiguredError, NotFoundError, NotificationIntegrationError, PermissionsError, RequiredRole,
    },
    prescription_api::PrescriptionApiError,
    service_communicator::ServiceCommunicatorError,
    text_message_integration::TextMessageIntegrationError,
};
use sea_orm::DbErr;

pub type Result<T> = std::result::Result<T, Error>;

#[derive(Debug)]
pub enum Error {
    Authorization(AuthorizationError),
    Password(PasswordError),
    Input(InputError),
    NotFound(NotFoundError),
    Permissions(PermissionsError),
    CrossOrganization(CrossOrganisationError),
    Custom(CustomError),
    Internal(anyhow::Error),
    Database(DbErr),
    PrescriptionApi(PrescriptionApiError),
    ServiceCommunicator(ServiceCommunicatorError),
    NotificationService(NotificationIntegrationError),
    TextMessageIntegrationError(TextMessageIntegrationError),
    DoctorLetterAndReferralApi(DoctorLetterAndReferralApiError),
    Encryptor(EncryptionError),
    NotConfigured(NotConfiguredError),
}

impl From<DomainError> for Error {
    fn from(e: DomainError) -> Self {
        match e {
            DomainError::Authorization(e) => Self::Authorization(e),
            DomainError::Password(e) => Self::Password(e),
            DomainError::Input(e) => Self::Input(e),
            DomainError::NotFound(e) => Self::NotFound(e),
            DomainError::Permissions(e) => Self::Permissions(e),
            DomainError::CrossOrganization(e) => Self::CrossOrganization(e),
            DomainError::Custom(e) => Self::Custom(e),
            DomainError::Internal(e) => Self::Internal(e),
            DomainError::Database(e) => Self::Database(e),
            DomainError::PrescriptionApi(e) => Self::PrescriptionApi(e),
            DomainError::ServiceCommunicator(e) => Self::ServiceCommunicator(e),
            DomainError::NotificationService(e) => Self::NotificationService(e),
            DomainError::TextMessageIntegrationError(e) => Self::TextMessageIntegrationError(e),
            DomainError::DoctorLetterAndReferralApi(e) => Self::DoctorLetterAndReferralApi(e),
            DomainError::Encryptor(e) => Self::Encryptor(e),
            DomainError::NotConfigured(e) => Self::NotConfigured(e),
        }
    }
}

impl From<DbErr> for Error {
    fn from(e: DbErr) -> Self {
        Self::Database(e)
    }
}

impl From<LoaderError> for Error {
    fn from(e: LoaderError) -> Self {
        Self::Internal(anyhow::anyhow!(e.message()))
    }
}

impl From<Error> for AsyncGQLError {
    fn from(e: Error) -> Self {
        e.extend()
    }
}

impl From<anyhow::Error> for Error {
    fn from(e: anyhow::Error) -> Self {
        Self::Internal(e)
    }
}

impl From<CustomError> for Error {
    fn from(e: CustomError) -> Self {
        Self::Custom(e)
    }
}

impl From<NotFoundError> for Error {
    fn from(e: NotFoundError) -> Self {
        Self::NotFound(e)
    }
}

impl From<AuthorizationError> for Error {
    fn from(e: AuthorizationError) -> Self {
        Self::Authorization(e)
    }
}

impl From<sqlx::Error> for Error {
    fn from(e: sqlx::Error) -> Self {
        Self::Internal(e.into())
    }
}

impl From<InputError> for Error {
    fn from(e: InputError) -> Self {
        Self::Input(e)
    }
}

impl ErrorExtensions for Error {
    #[allow(clippy::too_many_lines)]
    fn extend(&self) -> AsyncGQLError {
        match self {
            Self::Authorization(ex) => match ex {
                AuthorizationError::NotAuthenticated => {
                    tracing::error!(error = ?ex);
                    AsyncGQLError::new(format!("{ex}")).extend_with(|_, e| {
                        e.set("code", "ERR_AUTH_NOT_AUTHENTICATED");
                    })
                }
                AuthorizationError::MustBeAdmin => {
                    tracing::error!(error = ?ex);
                    AsyncGQLError::new(format!("{ex}")).extend_with(|_, e| {
                        e.set("code", "ERR_AUTH_MUST_BE_ADMIN");
                    })
                }
                AuthorizationError::MustBeUser => {
                    tracing::error!(error = ?ex);
                    AsyncGQLError::new(format!("{ex}")).extend_with(|_, e| {
                        e.set("code", "ERR_AUTH_MUST_BE_USER");
                    })
                }
                AuthorizationError::MalformedToken(_) => AsyncGQLError::new(format!("{ex}"))
                    .extend_with(|_, e| {
                        e.set("code", "ERR_AUTH_MALFORMED_TOKEN");
                    }),
                AuthorizationError::Unauthorized => {
                    tracing::error!(error = ?ex);
                    AsyncGQLError::new(format!("{ex}")).extend_with(|_, e| {
                        e.set("code", "ERR_AUTH_UNAUTHORIZED");
                    })
                }
            },
            Self::Input(e) => {
                tracing::error!(error = ?e);
                let arg_name = e.arg_name();

                AsyncGQLError::new(format!("{e}")).extend_with(|_, e| {
                    e.set("code", "ERR_INPUT");
                    e.set("arg_name", arg_name);
                })
            }
            Self::Password(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}")).extend_with(|_, e| e.set("code", "ERR_PASSWORD"))
            }
            Self::Encryptor(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_ENCRYPTION"))
            }
            Self::NotFound(e) => {
                tracing::error!(error = ?e);
                let resource_kind = e.resource_kind();
                let resource_id = e.resource_id();

                AsyncGQLError::new(format!("{e}")).extend_with(|_, e| {
                    e.set("code", "ERR_NOT_FOUND");
                    e.set("resourceKind", resource_kind);
                    e.set("resourceId", resource_id);
                })
            }
            Self::Permissions(e) => {
                tracing::error!(error = ?e);
                let required_role = match e.required_role {
                    RequiredRole::MustBeOwner => "owner",
                    RequiredRole::MustBeMember => "member",
                    RequiredRole::MustHaveAccess => "access",
                };
                let resource_kind = e.resource_kind;
                let resource_id = e.resource_id.to_string();

                AsyncGQLError::new(format!("{e}")).extend_with(|_, e| {
                    e.set("code", "ERR_PERMISSIONS");
                    e.set("required_role", required_role);
                    e.set("resource_kind", resource_kind);
                    e.set("resource_id", resource_id);
                })
            }
            Self::CrossOrganization(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_CROSS_ORG"))
            }
            Self::Custom(e) => {
                tracing::error!(error = ?e);
                let arg_name = e.arg_name();
                AsyncGQLError::new(format!("{e}")).extend_with(|_, e| {
                    e.set("code", "ERR_OTHER");
                    if let Some(arg) = arg_name {
                        e.set("arg_name", arg);
                    } else {
                        e.set("arg_name", ());
                    }
                })
            }
            Self::Internal(e) => {
                // This is a hacky location for error handling, but due to specifics of `juniper`,
                // it is significantly harder to handle the error elsewhere, because it is cast
                // to GraphQLError that is impossible to extract data from.
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}")).extend_with(|_, e| e.set("code", "ERR_INTERNAL"))
            }
            Self::Database(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}")).extend_with(|_, e| e.set("code", "ERR_DB_ERROR"))
            }
            Self::PrescriptionApi(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_PRESCRIPTION"))
            }
            Self::ServiceCommunicator(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_COMMUNICATOR"))
            }
            Self::NotificationService(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_NOTIFICATION_SERVICE"))
            }
            Self::TextMessageIntegrationError(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_TEXT_MESSAGE_INTEGRATION"))
            }
            Self::DoctorLetterAndReferralApi(e) => {
                tracing::error!(error = ?e);
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_DOCTOR_LETTER_AND_REFERRAL_API"))
            }
            Self::NotConfigured(e) => {
                tracing::warn!(service = e.service_name(), "Service not configured");
                AsyncGQLError::new(format!("{e}"))
                    .extend_with(|_, e| e.set("code", "ERR_NOT_CONFIGURED"))
            }
        }
    }
}

impl From<NotConfiguredError> for Error {
    fn from(e: NotConfiguredError) -> Self {
        Self::NotConfigured(e)
    }
}

impl From<leviosa_domain_contracts::errors::Error> for Error {
    fn from(err: leviosa_domain_contracts::errors::Error) -> Self {
        // Convert contracts error directly to API error
        match err {
            leviosa_domain_contracts::errors::Error::NotFound(msg) => {
                Error::NotFound(msg)
            },
            leviosa_domain_contracts::errors::Error::Authorization(auth_err) => {
                match auth_err {
                    leviosa_domain_contracts::errors::AuthorizationError::NotAuthenticated => {
                        Error::Unauthorized
                    },
                    leviosa_domain_contracts::errors::AuthorizationError::Unauthorized => {
                        Error::Unauthorized
                    },
                    leviosa_domain_contracts::errors::AuthorizationError::MalformedToken(err) => {
                        Error::BadRequest(format!("Malformed token: {}", err))
                    },
                }
            },
            leviosa_domain_contracts::errors::Error::Validation(custom_err) => {
                Error::BadRequest(format!("{}: {}", custom_err.field, custom_err.message))
            },
            leviosa_domain_contracts::errors::Error::Internal(err) => {
                Error::InternalServerError(err)
            },
        }
    }
}
