import { format, parseISO } from "date-fns"
import { Suspense } from "react"
import { useTranslation } from "react-i18next"

import { MenuItem } from "components/Ariakit"
import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"
import { Text } from "ui"

import {
  namedOperations,
  useEditEventInstanceInSlotMutation,
  useGetDatesWithOpenSlotsQuery,
  useGetOpenSlotsSuspenseQuery,
} from "generated/graphql"

import styles from "./NextAvailableSlot.module.css"

type NextAvailableTimeSlot = {
  serviceTypeId: string
  providerId: string
  instanceId: string
}

function NextAvailableTimeSlot({
  serviceTypeId,
  providerId,
  instanceId,
}: NextAvailableTimeSlot) {
  const { data } = useGetDatesWithOpenSlotsQuery({
    variables: {
      filter: {
        providerId,
        serviceTypeId,
      },
    },
  })

  const { t } = useTranslation()

  const navigate = useNavigateCalendar()

  const nextAvailableDateSlot = data?.datesWithOpenTimeSlots[0]

  const [editEventInstance] = useEditEventInstanceInSlotMutation({
    refetchQueries: [namedOperations.Query.EventInstances],
  })

  const { data: dataTimeSlot } = useGetOpenSlotsSuspenseQuery({
    variables: {
      filter: {
        fromDate: nextAvailableDateSlot,
        toDate: nextAvailableDateSlot,
        providerId,
        serviceTypeId,
      },
    },
  })

  const nextAvailableTimeSlot = dataTimeSlot?.openTimeSlots[0]?.times[0]

  if (!nextAvailableDateSlot || !nextAvailableTimeSlot) return null

  const parsedDate = parseISO(nextAvailableTimeSlot.fromTime)

  const formattedDate = format(parsedDate, "EEE MMMM d - HH:mm")

  const redirectToEvent = (id: string) => {
    setTimeout(() => {
      navigate(RouteStrings.calendarViewEventInstance, {
        eventId: id,
        search: {
          provider: providerId,
          date: nextAvailableTimeSlot.fromTime.split("T")[0],
        },
      })
    }, 600)
  }

  const handleRescheduleEvent = async () => {
    const { data } = await editEventInstance({
      variables: {
        editEventInstanceId: instanceId,
        input: {
          fromDate: new Date(nextAvailableTimeSlot.fromTime),
          toDate: new Date(nextAvailableTimeSlot.toTime),
          serviceTypeId: {
            set: serviceTypeId,
          },
        },
      },
    })

    if (data?.editEventInstance.id) redirectToEvent(data.editEventInstance.id)
  }

  return (
    <MenuItem onClick={handleRescheduleEvent}>
      <Text size="small" className={styles.menuItemLabel}>
        {t("Next available")}
      </Text>
      <Text> {formattedDate}</Text>
    </MenuItem>
  )
}

type NextAvailableSlotProps = {
  isOpen: boolean
  serviceTypeId: string
  providerId: string
  instanceId: string
}

export const NextAvailableSlot = ({
  isOpen,
  serviceTypeId,
  providerId,
  instanceId,
}: NextAvailableSlotProps) => {
  const { t } = useTranslation()

  return (
    <Suspense fallback={<MenuItem>{t("Loading")}</MenuItem>}>
      {isOpen && (
        <NextAvailableTimeSlot
          serviceTypeId={serviceTypeId}
          providerId={providerId}
          instanceId={instanceId}
        />
      )}
    </Suspense>
  )
}
