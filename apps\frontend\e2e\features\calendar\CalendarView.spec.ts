import { test, expect } from "@playwright/test"
import { addDays, addMonths } from "date-fns"
import { startOfMonth } from "date-fns"

import { CheckUrl } from "../utils/testUtils"

test.describe("Switching views in the Calendar", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("http://localhost:3000/")
    await page.waitForLoadState()
    // Open Provider Day
    await page.click('[data-testid="provider-box-calendar"]')
    await page.waitForLoadState()
  })

  test("Should open Month view when Month button is selected", async ({
    page,
  }) => {
    // Arrange
    // Act
    await page.click('[data-testid="calendar-view-select"]')
    await page.click('[data-testid="select-option-month"]')

    // Assert
    await CheckUrl(
      page,
      /calendar\/month\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/
    )
  })

  test("Should open Week view when Week button is selected", async ({
    page,
  }) => {
    // Arrange
    // Act
    await page.click('[data-testid="calendar-view-select"]')
    await page.click('[data-testid="select-option-week"]')

    // Assert
    await CheckUrl(
      page,
      /calendar\/week\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/
    )
  })

  test("Should open Work week view when Work week button is selected", async ({
    page,
  }) => {
    // Arrange
    // Act
    await page.click('[data-testid="calendar-view-select"]')
    await page.click('[data-testid="select-option-work_week"]')

    // Assert
    await CheckUrl(
      page,
      /calendar\/work_week\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/
    )
  })

  test("Should open Day view when Day button is selected", async ({ page }) => {
    // Arrange
    // Act
    await page.click('[data-testid="calendar-view-select"]')
    await page.click('[data-testid="select-option-day"]')

    // Assert
    await CheckUrl(
      page,
      /calendar\/day\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/
    )
  })
})

test.describe("Navigating dates in the Calendar week view", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("http://localhost:3000/")
    await page.waitForLoadState()
    // Open Provider Day
    await page.click('[data-testid="provider-box-calendar"]')
    await page.waitForLoadState()
    // Open Week view
    await page.click('[data-testid="calendar-view-select"]')
    await page.click('[data-testid="select-option-week"]')
    await page.waitForLoadState()
  })

  test("Should open Week view at today by default", async ({ page }) => {
    // Arrange
    // Construct today's date with name of weekday
    const today = new Date()
    const dayOfMonth = today.getDate()
    const dayOfWeek = today.toLocaleString("default", { weekday: "short" })
    const todayDate = dayOfMonth + dayOfWeek
    const monday = getMondayThisWeek()

    // Act

    // Assert
    // Today is selected in mini calendar
    await expect(
      page.locator(
        '[class*="Days_tableRow"][aria-current="date"][data-selected="true"]',
        {
          hasText: dayOfMonth.toString(),
        }
      )
    ).toHaveCount(1)

    // The week view shows the current week
    await expect(page.locator('[class*="rbc-header"]').first()).toHaveText(
      monday
    )

    // The week view has one day selected and it is today
    await expect(page.locator('[class*="rbc-header rbc-today"]')).toHaveCount(1)
    await expect(page.locator('[class*="rbc-header rbc-today"]')).toHaveText(
      todayDate
    )
  })

  test("Should highlight selected date and today in minicalendar", async ({
    page,
  }) => {
    // Arrange
    const weekBeforeToday = new Date().getDate() - 7
    const weekFromToday = new Date().getDate() + 7
    let selectDate = weekFromToday.toString()
    // check if the date is at the end of the month. If it is, select date from last week to avoid selecting a date that is in the tail from last month.
    // eslint-disable-next-line playwright/no-conditional-in-test
    if (weekFromToday > 23) {
      selectDate = weekBeforeToday.toString()
    }
    const today = new Date().getDate().toString()

    // Act
    await page
      .locator('[class*="Days_tableRow"]', {
        hasText: selectDate,
      })
      .click()

    // Assert
    await expect(
      // Today is not selected
      page.locator(
        '[class*="Days_tableRow"][aria-current="date"][data-selected="true"]',
        {
          hasText: today,
        }
      )
    ).toHaveCount(0)
    // Only one date is selected
    await expect(
      page.locator('[class*="Days_tableRow"][data-selected="true"]')
    ).toHaveCount(1)
    // The selected date is the one that was clicked
    await expect(
      page.locator('[class*="Days_tableRow"][data-selected="true"]', {
        hasText: selectDate,
      })
    ).toHaveCount(1)
  })

  test("Should show next week in week view when date is clicked in minicalendar", async ({
    page,
  }) => {
    // Arrange
    const today = new Date()
    const dayOfMonth = today.getDate()
    let monday = getMondayOfTheWeek(today)
    let dateToSelect = dayOfMonth
    // eslint-disable-next-line playwright/no-conditional-in-test
    if (dayOfMonth > 23) {
      // if the date is in the last week of the month
      // select date from last week
      const lastWeek = addDays(today, -7)
      dateToSelect = lastWeek.getDate()
      monday = getMondayOfTheWeek(lastWeek)
    } else {
      // select date in one week
      const nextWeek = addDays(today, 7)
      dateToSelect = nextWeek.getDate()
      monday = getMondayOfTheWeek(nextWeek)
    }

    await page
      .locator('[class*="Days_tableRow"]')
      .getByText(dateToSelect.toString(), { exact: true })
      .click()

    // Assert
    // The week view shows next week
    await expect(page.locator('[class*="rbc-header"]').first()).toHaveText(
      monday
    )
  })

  test("Should show current month in mini calendar by default", async ({
    page,
  }) => {
    // Arrange
    const currentMonth = new Date().toLocaleString("default", {
      month: "short",
    })
    // Act
    // Assert
    await expect(
      // Current month is selected
      page.locator('[class*="Header_monthTitle"]', {
        hasText: currentMonth,
      })
    ).toHaveCount(1)
  })

  test("Should navigate next month in mini calendar", async ({ page }) => {
    // Arrange
    const dateNextMonth = addMonths(startOfMonth(new Date()), 1)
    const nextMonthName = dateNextMonth.toLocaleString("default", {
      month: "short",
    })

    // Act
    await page.click('[aria-label="Next month"]')

    // Assert
    await expect(
      // Current month is selected
      page.locator('[class*="Header_monthTitle"]', {
        hasText: nextMonthName,
      })
    ).toHaveCount(1)
  })

  test("Should navigate previous month in mini calendar", async ({ page }) => {
    // Arrange
    const date = addMonths(startOfMonth(new Date()), -1)
    const previousMonthName = date.toLocaleString("default", { month: "short" })

    // Act
    await page.click('[aria-label="Previous month"]')

    // Assert
    await expect(
      // Current month is selected
      page.locator('[class*="Header_monthTitle"]', {
        hasText: previousMonthName,
      })
    ).toHaveCount(1)
  })

  test("Should navigate to next week using the Next button", async ({
    page,
  }) => {
    // Arrange
    const monday = getMondayNextWeek()

    // Act
    await page.click('[data-testid="calendar-view-next"]')

    // Assert
    // The week view shows next week
    await expect(page.locator('[class*="rbc-header"]').first()).toHaveText(
      monday
    )
  })

  test("Should navigate to Previous week using the Previous button", async ({
    page,
  }) => {
    // Arrange
    const monday = getMondayPreviousWeek()

    // Act
    await page.click('[data-testid="calendar-view-prev"]')

    // Assert
    // The week view shows next week
    await expect(page.locator('[class*="rbc-header"]').first()).toHaveText(
      monday
    )
  })

  test("Should navigate to Today using the Today button", async ({ page }) => {
    // Arrange
    const monday = getMondayThisWeek()
    // Navigate to next week
    await page.click('[data-testid="calendar-view-next"]')

    // Act
    await page.click('[data-testid="calendar-view-today"]')

    // Assert
    // The week view shows current week
    await expect(page.locator('[class*="rbc-header"]').first()).toHaveText(
      monday
    )
  })

  function getMondayThisWeek() {
    return getMondayAfterShiftingToday(0)
  }

  function getMondayPreviousWeek() {
    return getMondayAfterShiftingToday(-7)
  }

  function getMondayNextWeek() {
    return getMondayAfterShiftingToday(7)
  }

  function getMondayAfterShiftingToday(days: number) {
    // Shift date
    const theDate = new Date()
    theDate.setDate(theDate.getDate() + days)

    // Find the date of last Monday
    return getMondayOfTheWeek(theDate)
  }

  function getMondayOfTheWeek(theDate: Date) {
    const nextMonday = theDate
    while (nextMonday.getDay() !== 1) {
      // 1 represents Monday
      nextMonday.setDate(nextMonday.getDate() - 1)
    }
    return nextMonday.getDate().toString() + "Mon"
  }
})

test.describe("Navigating dates in the Calendar month view", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("http://localhost:3000/")
    await page.waitForLoadState()
    // Open Provider Day
    await page.click('[data-testid="provider-box-calendar"]')
    await page.waitForLoadState()
    // Open Week view
    await page.click('[data-testid="calendar-view-select"]')
    await page.click('[data-testid="select-option-month"]')

    await page.waitForLoadState()
  })

  test("Should show current month in Month view and highlight today ", async ({
    page,
  }) => {
    // Arrange
    const today = new Date()
    const dayOfMonth = today.getDate()
    const paddedDayOfMonth = String(dayOfMonth).padStart(2, "0")

    // Act

    // Assert
    // Current month is shown
    const expectedMonthString = new Intl.DateTimeFormat("en-GB", {
      month: "short",
    }).format(today)
    await expect(page.locator('[data-testid="view-header"]')).toHaveText(
      expectedMonthString + " " + today.getFullYear()
    )
    // Today is highlighted in month view
    await expect(page.locator('[class*="rbc-date-cell rbc-now"]')).toHaveCount(
      1
    )
    await expect(page.locator('[class*="rbc-date-cell rbc-now"]')).toHaveText(
      paddedDayOfMonth
    )
  })

  test("Should navigate to next month when Next button is clicked", async ({
    page,
  }) => {
    // Arrange
    const nextMonth = addMonths(startOfMonth(new Date()), 1)

    // Act
    await page.click('[data-testid="calendar-view-next"]')

    // Assert
    // Next month is shown
    await expect(page.locator('[data-testid="view-header"]')).toHaveText(
      nextMonth.toLocaleString("default", { month: "short" }) +
        " " +
        nextMonth.getFullYear()
    )
  })

  test("Should navigate to previous month when previous button is clicked", async ({
    page,
  }) => {
    // Arrange
    const previousMonth = new Date()
    previousMonth.setMonth(previousMonth.getMonth() - 1)

    // Act
    await page.click('[data-testid="calendar-view-prev"]')

    // Assert
    // Previous month is shown
    const expectedMonthString =
      previousMonth.toLocaleString("en-GB", { month: "short" }) +
      " " +
      previousMonth.getFullYear()

    await expect(page.locator('[data-testid="view-header"]')).toHaveText(
      expectedMonthString
    )
  })

  test("Should navigate to current month when Today button is clicked", async ({
    page,
  }) => {
    // Arrange
    const today = new Date()
    await page.click('[data-testid="calendar-view-prev"]')

    // Act
    await page.click('[data-testid="calendar-view-today"]')

    // Assert
    const expectedMonthString = new Intl.DateTimeFormat("en-GB", {
      month: "short",
    }).format(today)
    await expect(page.locator('[data-testid="view-header"]')).toHaveText(
      expectedMonthString + " " + today.getFullYear()
    )
  })
})
