import { useApolloClient } from "@apollo/client"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"
import { z } from "zod"

import {
  useComboboxStore,
  useFilter,
  useSelectStore,
} from "components/Ariakit/hooks"
import DatePicker from "components/DatePicker/DatePicker"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Panel from "components/Panel/Panel"
import { logException } from "lib/sentry/sentry"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Modal } from "ui"
import { naiveDate } from "utils/naiveDate"

import {
  namedOperations,
  useCreateAvailabilityScheduleMutation,
  useGetProvidersQuery,
} from "generated/graphql"

import styles from "./AvailabilityScheduleModal.module.css"

export const AvailabilityScheduleFormSchema = z.object({
  providerId: z.string(),
  startDate: z.string().transform((d) => {
    if (!d) throw new Error("Start date is required")
    return new Date(d)
  }),
  endDate: z.string().transform((d) => {
    if (!d) throw new Error("End date is required")
    return new Date(d)
  }),
})

type AvailabilityScheduleModalProps = {
  showModal: boolean
  closeModal: () => void
  refetchSchedules?: boolean
  allowProviderSelect?: boolean
  providerId?: string
}

const AvailabilityScheduleModal = ({
  showModal,
  closeModal,
  refetchSchedules = false,
  allowProviderSelect = true,
  providerId,
}: AvailabilityScheduleModalProps) => {
  const { globalData } = useGlobalState()
  const actor = globalData.actor

  const { t } = useTranslation()
  const navigate = useNavigate()
  const [error, setError] = useState<string | null>(null)
  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const client = useApolloClient()

  const [createAvailabilitySchedule] = useCreateAvailabilityScheduleMutation()

  const { data: providersData } = useGetProvidersQuery()

  const comboboxProvidersStore = useComboboxStore({})
  const { value: providerValue } = comboboxProvidersStore.useState()

  const providers =
    providersData?.providers.map((provider) => ({
      label: provider.name,
      value: provider.id,
    })) ?? []

  const { filteredList: filteredProviders } = useFilter({
    defaultItems: providers || [],
    value: providerValue,
  })

  const selectStoreProviders = useSelectStore({
    combobox: comboboxProvidersStore,
    defaultValue: providerId || actor.id,
    focusLoop: "vertical",
  })

  const handleCloseModal = () => {
    closeModal()
    setError("")
    setValidationError(null)
  }

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    event.stopPropagation()

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())

    try {
      const validatedInput = AvailabilityScheduleFormSchema.safeParse({
        providerId: data.providerId,
        startDate: data.startDate,
        endDate: data.endDate,
      })

      if (!validatedInput.success) {
        setValidationError(validatedInput.error)
        console.error(validatedInput.error)

        return
      }

      const { providerId, startDate, endDate } = validatedInput.data

      if (!providerId) {
        setError(t("Please select a provider"))
        return
      }

      createAvailabilitySchedule({
        variables: {
          input: {
            providerId,
            fromDate: naiveDate(startDate),
            toDate: naiveDate(endDate),
          },
        },
        onError: (e) => {
          setError(t(e.message || "Something went wrong, please try again"))
        },
        onCompleted: async (data) => {
          handleCloseModal()
          navigate(
            generatePath(RouteStrings.calendarSchedule, {
              providerId,
              scheduleId: data.createAvailabilitySchedule.id,
            })
          )

          if (refetchSchedules) {
            await client.refetchQueries({
              include: [namedOperations.Query.GetAvailabilitySchedules],
            })
          }
        },
      })
    } catch (error) {
      logException(error)
      setValidationError(error)
    }
  }

  return (
    <Modal
      isOpen={showModal}
      title={t("Create Availability Schedule")}
      contentClassName={styles.container}
      onClose={handleCloseModal}
      allowOverflow
    >
      <form onSubmit={handleSubmit} className={styles.form}>
        <FiltrableSelect
          placeholder={t("Select Provider")}
          label={"Provider"}
          options={providers}
          selectStore={selectStoreProviders}
          filteredOptions={filteredProviders}
          comboboxStore={comboboxProvidersStore}
          name="providerId"
          hideMessage
          sameWidth
          portal={false}
          readOnly={!allowProviderSelect}
        />
        <DatePicker
          hideMessage
          label={t("Start Date")}
          name="startDate"
          defaultValue={new Date().toISOString().split("T")[0]}
          className={styles.datePicker}
        />
        <DatePicker
          hideMessage
          label={t("End Date")}
          name="endDate"
          className={styles.datePicker}
        />

        {validationError && (
          <Panel status="error">{validationError.message}</Panel>
        )}
        {error && <Panel status="error">{error}</Panel>}
        <div className={styles.confirmationButtons}>
          <Button variant="clear" onClick={handleCloseModal}>
            {t("cancel")}
          </Button>
          <Button variant="filled" type="submit">
            {t("doCreate")}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default AvailabilityScheduleModal
