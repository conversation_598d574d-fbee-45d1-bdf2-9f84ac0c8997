//! Customer service contract

use crate::errors::Result;
use async_trait::async_trait;
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FeedbackData {
    pub user: FeedbackUser,
    pub feedback: String,
    pub os: Option<String>,
    pub browser: Option<String>,
    pub browser_version: Option<String>,
    pub app_version: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FeedbackUser {
    pub user_id: ProviderId,
    pub user_name: String,
    pub user_email: String,
    pub organization_id: OrganisationId,
    pub organization_name: String,
}

/// Contract for customer service integration
#[async_trait]
#[mockall::automock]
pub trait CustomerService: Send + Sync {
    /// Create feedback from a user
    async fn create_feedback(&self, feedback: FeedbackData) -> Result<()>;
}
