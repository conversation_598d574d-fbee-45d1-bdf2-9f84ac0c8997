fragment DashboardFields on Encounter {
  id
  reason
  fromDate
  priority
  note
  responsibleProviders {
    ...ProviderInfoFragment
  }
  disposition
  subject {
    ...SubjectBasicInfoFragment
    # Location can only ever be Bed
    location {
      id
      label
      ... on Bed {
        room {
          id
          label
        }
      }
    }
    subjectHealthProfile {
      id
      dietaryAllowanceId
    }
  }
  journalEntries {
    id
    createdBy {
      id
      name
      nameInitials
    }
    updatedAt
    # COMEBACK SetSubjectJournalFocusedItem wants block ID
    sections {
      id
    }
  }
}

query Dashboard($teamId: UUID!, $status: EncounterStatus!) {
  team(id: $teamId) {
    id
    name
    members {
      ...ProviderInfoFragment
    }
    # COMEBACK when auto-refresh, this query has to be optimised as much as possible
    rows: encounters(status: $status) {
      ...DashboardFields
    }
  }

  locations(locationType: BED) {
    id
    label
  }
}
