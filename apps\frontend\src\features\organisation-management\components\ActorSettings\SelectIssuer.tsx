import { useEffect } from "react"
import { useTranslation } from "react-i18next"

import { inlineGuides } from "assets/inlineGuides"
import {
  useComboboxStore,
  useFilter,
  useSelectStore,
} from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"

import {
  useGetIssuersForInvoiceQuery,
  useSetDefaultIssuerForProviderMutation,
} from "generated/graphql"

type SelectIssuerProps = {
  defaultValue?: string
}

export const SelectIssuer = ({ defaultValue }: SelectIssuerProps) => {
  const comboboxStore = useComboboxStore()

  const { value: comboboxValue } = comboboxStore.useState()

  const { t: tProvider } = useTranslation("routes", {
    keyPrefix: "manageProvider",
  })

  const { t } = useTranslation()

  const { globalData } = useGlobalState()

  const actor = globalData.actor

  const [setDefaultIssuer, { loading, error, called }] =
    useSetDefaultIssuerForProviderMutation()

  const { data: issuersData } = useGetIssuersForInvoiceQuery({
    variables: {
      filter: null,
    },
  })

  const issuersOptions =
    issuersData?.invoiceIssuers.map(({ id, title }) => ({
      label: title,
      value: id,
    })) || []

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: defaultValue,
    focusLoop: "vertical",
  })

  const { filteredList } = useFilter({
    defaultItems: issuersOptions,
    value: comboboxValue,
  })

  useEffect(() => {
    if (defaultValue) {
      selectStore.setValue(defaultValue)
    }
  }, [defaultValue])

  const updatedSuccessfully = called && !loading && !error

  const status = loading
    ? "default"
    : updatedSuccessfully
      ? "success"
      : error
        ? "error"
        : "default"

  const message = loading
    ? t("Saving...")
    : updatedSuccessfully
      ? t("Default invoice issuer updated")
      : error
        ? t(
            "Could not update issuer due to an error. Please try again or reload the page"
          )
        : ""

  const inlineGuideContent = inlineGuides["DEFAULT_ISSUER"].content

  return (
    <FiltrableSelect
      isLoading={loading}
      status={status}
      message={message}
      inlineGuide={inlineGuideContent}
      label={tProvider("defaultInvoiceIssuer")}
      options={issuersOptions}
      filteredOptions={filteredList}
      sameWidth
      selectStore={selectStore}
      comboboxStore={comboboxStore}
      onSelectChange={(value) => {
        if (typeof value !== "string") return

        setDefaultIssuer({
          variables: {
            id: value,
            providerId: actor.id,
          },
        })
      }}
    />
  )
}
