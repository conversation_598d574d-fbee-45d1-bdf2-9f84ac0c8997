.wrap {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 44px;
  gap: 8px 16px;
}
.label {
  grid-column: span 2;
}

.selectWrap {
  position: relative;
  display: flex;
  gap: 7px;
}
.arrowIcon {
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
}

.select {
  padding-right: 38px;
  width: 100%;
}

.selectedItem[data-is-available="false"] {
  text-decoration: line-through;
}

.icons {
  position: absolute;
  top: 5px;
  right: 13px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.clearButton.clearButton {
  position: absolute;
  top: 5px;
  right: 26px;
  align-items: center;
  padding: 8px;
  border: none;
}

.locationCapacityIndicator {
  display: flex;
  align-items: center;
  gap: 3px;
}

.errorMessage {
  margin-top: 12px;
  display: flex;
  gap: 5px;
  grid-column: 1 / -1;
}

.errorIcon {
  color: var(--color-critical);
  width: 20px;
  height: 20px;
}

.errorText {
  color: var(--color-critical);
}
.selectItem {
  border-radius: 6px;
  cursor: pointer;
}

.selectItemTitle[data-is-available="false"] {
  text-decoration: line-through;
  line-height: 18px;
  font-size: 16px;
}

.selectItemMessage {
  display: flex;
  gap: 5px;
}

[data-active-item] .selectItemMessage {
  color: var(--color-text-primary);
}

.span7 {
  grid-column: span 7;
}

.span5 {
  grid-column: span 5;
}
