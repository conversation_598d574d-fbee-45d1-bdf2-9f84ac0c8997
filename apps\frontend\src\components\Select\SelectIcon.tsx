import { ReactNode } from "react"

import { SelectArrow } from "components/Ariakit"
import Icon from "components/Icon/Icon"

import styles from "./Select.module.css"

type Props = {
  isLoading?: boolean
  icon?: ReactNode
}

export const SelectIcon = ({ isLoading, icon }: Props) => {
  if (isLoading) return <Icon name="loader-4-line" spin />
  if (icon) return <>{icon}</>

  return <SelectArrow className={styles.arrowIcon} />
}
