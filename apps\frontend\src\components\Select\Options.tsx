import { Option, SelectItem } from "../Ariakit"
import Icon from "../Icon/Icon"
import styles from "../Select/Select.module.css"

export type OptionsProps<T extends string | string[]> = {
  isLoading: boolean
  options: Option<T extends string ? T : T[number]>[]
  value: T
  noOptionsPlaceholder: string
  onClick?: (value: T extends string ? T : T[number]) => void
  isClearable?: boolean
  hasComboboxValue?: boolean
}

export const Options = <T extends string | string[]>({
  options,
  value: optionValue,
  noOptionsPlaceholder,
  isLoading,
  onClick,
  isClearable,
}: OptionsProps<T>) => {
  if (isLoading)
    return (
      <div className={styles.optionsContent}>
        <Icon name="loader-4-line" spin />
      </div>
    )

  if (!options.length)
    return <div className={styles.optionsContent}>{noOptionsPlaceholder}</div>

  return (
    <>
      {isClearable && <SelectItem hidden disabled value="" />}
      {options.map(
        ({ value, label, subContent, subContentClassName, direction }) => {
          return (
            <SelectItem
              key={value}
              value={value}
              subContent={subContent}
              subContentClassName={subContentClassName}
              direction={direction}
              className={styles.option}
              data-is-multi={
                Array.isArray(optionValue) && optionValue.includes(value)
              }
              onClick={() => {
                onClick?.(value)
              }}
              data-testid={`select-option-${value}`}
            >
              {label}
            </SelectItem>
          )
        }
      )}
    </>
  )
}
