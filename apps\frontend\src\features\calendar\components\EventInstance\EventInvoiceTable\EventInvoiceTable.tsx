import { ReactNode, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath } from "react-router-dom"

import {
  Menu,
  MenuButton,
  MenuItem,
  MenuProvider,
  usePopoverStore,
} from "components/Ariakit"
import {
  AttachmentsPreview,
  useAttachmentContext,
} from "components/AttachmentsPreview/AttachmentsPreview"
import Icon from "components/Icon/Icon"
import { InvoicePaymentMethodTag } from "features/billing/components/InvoiceOverview/InvoiceOverviewTable/InvoicePaymentMethodTag/InvoicePaymentMethodTag"
import { InvoiceStatusTag } from "features/billing/components/InvoiceOverview/InvoiceOverviewTable/InvoiceStatusTag/InvoiceStatusTag"
import { invoicePdfUrl } from "features/billing/components/InvoiceOverview/invoicePdfUrl"
import { RouteStrings } from "routes/RouteStrings"
import { But<PERSON>, Table, Text } from "ui"
import TrLink from "ui/components/Table/TrLink"
import { formatNumberInThousand } from "utils/formatNumberInThousand"
import { printDocument } from "utils/printDocument"

import { EventInstanceQuery } from "generated/graphql"

import styles from "./EventInvoiceTable.module.css"
import { InvoiceEmailPopover } from "./InvoiceEmailPopover"

type Invoices = NonNullable<
  EventInstanceQuery["eventInstance"]["encounter"]
>["invoices"]

type Invoice = Invoices[0]

type EventInvoiceTableProps = {
  invoices: Invoices
  createInvoice: () => void
  loadingCreateInvoice: boolean
}

const ClickableCell = ({
  invoiceId,
  issued,
  invoiceNavLink,
  className,
  children,
}: {
  invoiceId: string
  issued: boolean
  invoiceNavLink: string
  className?: string
  children: ReactNode
}) => {
  const downloadLink = invoicePdfUrl(invoiceId)
  const { openAttachment } = useAttachmentContext()

  const handleIssuedClick: React.MouseEventHandler<HTMLAnchorElement> = (e) => {
    if (openAttachment) {
      e.preventDefault()
      openAttachment(downloadLink)
    }
  }

  return (
    <td className={className}>
      {!issued ? (
        <a href={invoiceNavLink}>{children}</a>
      ) : (
        <a href={downloadLink} download onClick={handleIssuedClick}>
          {children}
        </a>
      )}
    </td>
  )
}

export const EventInvoiceTable = ({
  invoices,
  createInvoice,
  loadingCreateInvoice,
}: EventInvoiceTableProps) => {
  const { t } = useTranslation()

  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)

  const popoverStore = usePopoverStore()
  const menuButtonRefs = useRef<Record<string, HTMLButtonElement | null>>({})

  const handleShowInvoiceEmailPopover = (
    e: React.MouseEvent,
    invoice: Invoice
  ) => {
    e.stopPropagation()
    setSelectedInvoice(invoice)
    popoverStore.setAnchorElement(menuButtonRefs.current[invoice.id])
    popoverStore.show()
  }

  const getInvoiceLink = (invoiceId: string) => {
    const basePath = generatePath(RouteStrings.patientInvoice, {
      invoiceId,
    })

    const searchParams = new URLSearchParams({
      returnTo: encodeURIComponent(location.pathname + location.search),
    })

    return `${basePath}?${searchParams}`
  }

  const attachments =
    invoices
      ?.filter(({ issued }) => issued)
      .map(({ id, invoiceNumber }) => ({
        name: `Invoice ${invoiceNumber}.pdf`,
        url: invoicePdfUrl(id),
      })) || []

  return (
    <AttachmentsPreview attachments={attachments}>
      <div className={styles.wrapper}>
        <Table spacing="narrow" className={styles.table}>
          <tbody>
            {invoices?.map((invoice: Invoice) => (
              <TrLink key={invoice.id} id={invoice.id}>
                <ClickableCell
                  invoiceId={invoice.id}
                  issued={invoice.issued}
                  invoiceNavLink={getInvoiceLink(invoice.id)}
                >
                  {invoice.invoiceNumber}
                </ClickableCell>
                <td className={styles.rightAlign}>
                  <Text size="small">
                    {formatNumberInThousand(invoice.totalPayableBySubject || 0)}{" "}
                    ISK
                  </Text>
                </td>
                <td
                  className={styles.rightAlign}
                  onClick={(e) => e.stopPropagation()}
                >
                  <InvoicePaymentMethodTag
                    id={invoice.id}
                    paymentMethod={invoice.paymentMethod}
                    size="small"
                  />
                </td>
                <td
                  className={styles.rightAlign}
                  onClick={(e) => (invoice.issued ? e.stopPropagation() : null)}
                >
                  <InvoiceStatusTag
                    id={invoice.id}
                    key={invoice.paymentStatus}
                    paymentStatus={invoice.paymentStatus}
                    size="small"
                    issued={invoice.issued}
                  />
                </td>
                <td className={styles.printCell}>
                  {invoice.issued && (
                    <MenuProvider placement="bottom-end">
                      <MenuButton
                        size="large"
                        aria-label="Invoice Menu"
                        icon={<Icon name="more-line" />}
                        className={styles.menuButton}
                        variant="clear"
                        onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                          e.stopPropagation()
                          popoverStore.hide()
                        }}
                        ref={(el) => (menuButtonRefs.current[invoice.id] = el)}
                      />
                      <Menu gutter={8} portal>
                        <MenuItem
                          onClick={(e) =>
                            handleShowInvoiceEmailPopover(e, invoice)
                          }
                        >
                          <Icon name="mail-line" className={styles.menuIcon} />
                          {t("Email invoice")}
                        </MenuItem>
                        <MenuItem
                          onClick={(e) => {
                            e.stopPropagation()
                            printDocument(invoicePdfUrl(invoice.id))
                          }}
                        >
                          <Icon
                            name="printer-line"
                            className={styles.menuIcon}
                          />
                          {t("Print invoice")}
                        </MenuItem>
                      </Menu>
                    </MenuProvider>
                  )}
                </td>
              </TrLink>
            ))}
          </tbody>
        </Table>
        <Button
          variant="clear"
          onClick={() => {
            !loadingCreateInvoice && createInvoice()
          }}
          icon={loadingCreateInvoice && <Icon name="loader-4-line" spin />}
          className={styles.createInvoiceButton}
        >
          {loadingCreateInvoice
            ? t("Creating invoice...")
            : t("Create invoice")}
        </Button>
      </div>

      {selectedInvoice && (
        <InvoiceEmailPopover
          key={selectedInvoice.id}
          invoiceId={selectedInvoice.id}
          payerEmail={selectedInvoice.payerEmail}
          subjectEmail={selectedInvoice.subject.email}
          popoverStore={popoverStore}
        />
      )}
    </AttachmentsPreview>
  )
}
