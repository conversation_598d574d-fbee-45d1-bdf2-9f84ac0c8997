/**
 * This is intended to be a basic starting point for linting in your app.
 * It relies on recommended configs out of the box for simplicity, but you can
 * and should modify this configuration to best suit your team's needs.
 */

/** @type {import('eslint').Linter.Config} */
module.exports = {
  root: true,
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  env: {
    browser: true,
    commonjs: true,
    es6: true,
  },
  ignorePatterns: ["!**/.server", "!**/.client"],

  // Base config
  extends: ["eslint:recommended", "../../.eslintrc.json"],

  overrides: [
      {
        files: ["*.ts", "*.tsx", "*.js", "*.jsx"],
        rules: {
          "playwright/no-skipped-test": "off",
          "playwright/expect-expect": "off",
          "no-extra-semi": "off",
          "typescript-eslint/no-extra-semi": "off"
        }
      },
      {
       files: ["*.ts", "*.tsx"],
       rules: {}
      },
      {
       files: ["*.js", "*.jsx"],
       rules: {}
      },
      {
       files: ["e2e/**/*.{ts,js,tsx,jsx}"],
       rules: {}
      },
      {
       files: [".eslintrc.cjs"],
        env: {
          node: true,
        },
      },
    ],
};
