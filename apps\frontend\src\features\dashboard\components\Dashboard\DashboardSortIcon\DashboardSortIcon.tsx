import Icon from "components/Icon/Icon"

import { SearchSettingsType } from "../Dashboard"
import styles from "./DashboardSortIcon.module.css"

type DashboardSortIconProps = {
  searchSettings: SearchSettingsType
  displayColumn: SearchSettingsType["column"]
}

export const DashboardSortIcon = ({
  searchSettings,
  displayColumn,
}: DashboardSortIconProps) => {
  const { column, order } = searchSettings

  if (order === "asc" && column === displayColumn)
    return <Icon name={"arrow-up-s-line"} />
  if (order === "desc" && column === displayColumn)
    return <Icon name={"arrow-down-s-line"} />

  return (
    <span data-icon={"default"} className={styles.sortIcons}>
      <Icon name={"arrow-up-s-line"} />
      <Icon name={"arrow-down-s-line"} />
    </span>
  )
}
