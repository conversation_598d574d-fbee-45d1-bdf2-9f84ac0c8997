import { useState } from "react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Button, Heading, notification } from "ui"
import ExtractByTypename from "utils/ExtractByTypename"

import { GetBuildingsQuery, useUpdateBuildingMutation } from "generated/graphql"

import { AddBedToRoom } from "../AddBedToRoom/AddBedToRoom"
import styles from "../BuildingOverview/BuildingOverview.module.css"
import { RoomTable } from "../RoomTable/RoomTable"
import { UpdateRoom } from "../UpdateRoom/UpdateRoom"
import { LocationBuildingForm } from "../forms/LocationBuildingForm/LocationBuildingForm"
import { BuildingCorridorTable } from "./BuildingCorridorTable"
import { CreateFormsForBuilding } from "./CreateFormsForBuilding"

export type Building = ExtractByTypename<
  GetBuildingsQuery["locations"][number],
  "Building"
>

type BuildingOverviewProps = {
  building?: Building
  activeCorridor?: Building["corridors"]["corridors"][0]
}

export const BuildingOverview = ({
  building,
  activeCorridor,
}: BuildingOverviewProps) => {
  const [showLocationBuildForm, setShowLocationBuildForm] = useState(false)
  const [roomId, setRoomId] = useState<string | null>(null)
  const [showAddBedToRoom, setShowAddBedToRoom] = useState<{
    roomId: string | null
    show: boolean
  }>({
    roomId: null,
    show: false,
  })

  const { t } = useTranslation()

  const { t: tLocation } = useTranslation("routes", {
    keyPrefix: "locations",
  })

  const [
    updateBuilding,
    { loading: updateBuildingLoading, error: updateBuildingError },
  ] = useUpdateBuildingMutation({
    onCompleted: (data) => {
      if (data) {
        notification.create({
          message: t("Building has been updated"),
          status: "success",
          maxWidth: "500px",
        })
      }

      setShowLocationBuildForm(false)
    },
  })

  if (!building) return null

  const { label, corridors, rooms, beds } = building

  const roomWithoutCorridor = rooms.rooms.filter(
    ({ corridor }) => corridor === null
  )

  const editRoom = roomWithoutCorridor.find((room) => room.id === roomId)

  const addBedToRoom = roomWithoutCorridor.find(
    (room) => room.id === showAddBedToRoom.roomId
  )

  const translatedBuildingDescription = tLocation("buildingDescription", {
    label: label,
    corridorsCount: corridors.count,
    roomsCount: rooms.count,
    bedsCount: beds.count,
  }).replace(/&#39;/g, "'")

  return (
    <>
      {!showLocationBuildForm ? (
        <>
          <div className={styles.buildingTitleWrap}>
            <Heading size="large">{label}</Heading>
            <Button
              icon={<Icon name={"pencil-line"} />}
              onClick={() => setShowLocationBuildForm(true)}
            >
              {t("Edit")}
            </Button>
          </div>
          <br />
          <p>{translatedBuildingDescription}</p>
          <br />
        </>
      ) : (
        <LocationBuildingForm
          onSubmit={(data) => {
            const { label, ...rest } = data

            data.addressLine2 || null

            updateBuilding({
              variables: {
                input: {
                  id: building.id,
                  label: label,
                  address: {
                    ...rest,
                    addressLine2: {
                      set: rest.addressLine2,
                    },
                    region: {
                      set: rest.region,
                    },
                  },
                },
              },
            })
          }}
          formData={building}
          onCancel={() => setShowLocationBuildForm(false)}
          error={updateBuildingError}
          loading={updateBuildingLoading}
        />
      )}
      <br />
      <BuildingCorridorTable
        activeCorridor={activeCorridor}
        building={building}
      />
      <br />
      <RoomTable
        onEditRoomClick={(id) => setRoomId(id)}
        onAddBedClick={(id) => {
          setShowAddBedToRoom({
            roomId: id,
            show: true,
          })
        }}
        rooms={roomWithoutCorridor}
      />
      <br />

      <CreateFormsForBuilding buildingId={building.id} />

      <UpdateRoom
        room={editRoom}
        roomId={roomId}
        onCancel={() => setRoomId(null)}
      />

      <AddBedToRoom
        room={addBedToRoom}
        roomId={showAddBedToRoom.roomId}
        onClose={() => {
          setShowAddBedToRoom({
            roomId: null,
            show: false,
          })
        }}
      />
    </>
  )
}
