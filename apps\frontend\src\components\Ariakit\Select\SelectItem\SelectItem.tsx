import * as Ariakit from "@ariakit/react"
import { forwardRef } from "react"

import MenuListItem, {
  MenuListItemOwnProps,
} from "components/MenuListItem/MenuListItem"

import styles from "./SelectItem.module.css"

export type SelectItemProps = Ariakit.SelectItemProps & MenuListItemOwnProps

export const SelectItem = forwardRef<HTMLDivElement, SelectItemProps>(
  (props, ref) => {
    const { className = "", subContent, subContentClassName, ...rest } = props

    return (
      <Ariakit.SelectItem
        ref={ref}
        render={
          props.render
            ? props.render
            : (p) => (
                <MenuListItem
                  {...p}
                  subContent={subContent}
                  subContentClassName={subContentClassName}
                />
              )
        }
        className={`${styles.selectItem} ${className}`}
        {...rest}
      />
    )
  }
)

export const SelectItemCheck = Ariakit.SelectItemCheck
