import { MouseEvent } from "react"
import { useTranslation } from "react-i18next"

import { formatPersonaId } from "@leviosa/utils"

import GenderIcon from "components/Icon/GenderIcon"
import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { Text } from "ui"
import parseAgeText from "utils/parseAgeText"

import { Subject } from "generated/graphql"

import styles from "./SubjectBox.module.css"

type PhoneNumberProps = {
  phoneNumber: string
  showTelLink: boolean
}
const PhoneNumber = ({ phoneNumber, showTelLink }: PhoneNumberProps) => {
  if (showTelLink) {
    return (
      <Text
        as="a"
        size="small"
        href={`tel:${phoneNumber}`}
        className={styles.infoItem}
        onClick={(e: MouseEvent<HTMLAnchorElement>) => {
          e.stopPropagation()
        }}
      >
        <Icon name="phone-line" />
        {phoneNumber}
      </Text>
    )
  }

  return (
    <Text size="small" className={styles.infoItem}>
      <Icon name="phone-line" />
      {phoneNumber}
    </Text>
  )
}

export type SubjectInfoProps = Pick<
  Subject,
  "id" | "age" | "gender" | "personaId" | "phoneNumber"
> & {
  showTelLink?: boolean
}
export const SubjectInfo = ({
  age,
  gender,
  personaId,
  phoneNumber,
  showTelLink = false,
}: SubjectInfoProps) => {
  const { t } = useTranslation()
  const ageString = parseAgeText(age)

  return (
    <PiiSensitive className={styles.subjectInfo}>
      <Text size="small">{formatPersonaId(personaId)}</Text>

      {gender && (
        <Text size="small" className={styles.infoItem}>
          <GenderIcon genderId={gender} />
          {ageString && t(...ageString)}
        </Text>
      )}

      {phoneNumber && (
        <PhoneNumber
          key={phoneNumber}
          phoneNumber={phoneNumber}
          showTelLink={showTelLink}
        />
      )}
    </PiiSensitive>
  )
}
