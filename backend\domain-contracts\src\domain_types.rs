//! Domain types re-exported for infrastructure layer
//!
//! This module re-exports all domain types that infrastructure needs
//! without creating a dependency on the domain implementation.

// Re-export all domain types
pub use leviosa_domain_types::*;

// Core domain interfaces that infrastructure needs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// Auth types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthData {
    pub user_id: String,
    pub permissions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticatedUser {
    pub id: String,
    pub email: String,
    pub permissions: Vec<String>,
}

// National Registry types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainNationalRegistryPerson {
    pub persona_id: PersonaIdIs,
    pub name: Name,
    pub gender: GenderId,
    pub date_of_birth_inferred: DateTime<Utc>,
    pub address: Option<DomainNationalRegistryAddress>,
    pub family_id: Option<String>,
    pub citizenship: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DomainNationalRegistryAddress {
    pub street: String,
    pub town: String,
    pub postal_code: String,
    pub country: String,
}

// External Organisation types
#[derive(Debug, Clone)]
pub struct ExternalOrganisation {
    pub id: String,
    pub name: String,
    pub external_id: String,
}

#[derive(Debug, Clone)]
pub struct FindExternalOrganisationsQuery {
    pub name_filter: Option<String>,
    pub limit: Option<usize>,
}

// Error types
pub use crate::errors::*;

// Basic domain types that need to be available
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Name {
    pub first: String,
    pub last: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailAddress(pub String);

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PhoneNumber(pub String);

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PersonaId(pub String);

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UserType {
    Provider,
    Subject,
    Admin,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Organisation {
    pub id: OrganisationId,
    pub name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Provider {
    pub id: ProviderId,
    pub name: String,
    pub email: EmailAddress,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Subject {
    pub id: SubjectId,
    pub name: String,
    pub email: Option<EmailAddress>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserName(pub String);

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrganisationQueryKey(pub String);

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub database_url: String,
    pub jwt_secret: String,
}

// Communication types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommunicationStatus {
    Pending,
    Sent,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutboundDoctorsLetter {
    pub id: String,
    pub content: String,
    pub recipient: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutboundReferral {
    pub id: String,
    pub content: String,
    pub recipient: String,
}

// Permission types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionKey(pub String);

// External message types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExternalMessage {
    pub id: String,
    pub content: String,
    pub sender: String,
}
