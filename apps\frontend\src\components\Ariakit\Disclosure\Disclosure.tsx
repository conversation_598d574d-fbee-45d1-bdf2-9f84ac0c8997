import {
  Disclosure as DisclosureAria<PERSON>it,
  DisclosureContent as Disclosure<PERSON>ontentAriaKit,
  DisclosureContentProps,
  DisclosureProps,
  DisclosureProvider as DisclosureProviderAriaKit,
  DisclosureProviderProps,
  DisclosureStoreProps,
  useDisclosureStore as useDisclosureStoreAriaKit,
} from "@ariakit/react"
import c from "classnames"

import Icon from "components/Icon/Icon"

import styles from "./Disclosure.module.css"

export const Disclosure = ({
  children,
  className,
  ...rest
}: DisclosureProps) => {
  return (
    <DisclosureAriaKit
      className={c(styles.disclosure, className)}
      toggleOnClick={true}
      {...rest}
    >
      <>
        {children}
        <Icon
          name="arrow-down-s-line"
          fontSize={20}
          className={styles.collapseExpandIcon}
        />
      </>
    </DisclosureAriaKit>
  )
}

export const DisclosureContent = ({
  children,
  wrapperClassName,
  ...rest
}: DisclosureContentProps & { wrapperClassName?: string }) => {
  return (
    <DisclosureContentAriaKit
      unmountOnHide
      {...rest}
      className={c(styles.disclosureContent, wrapperClassName)}
    >
      <div className={c(styles.disclosureContentInner, rest.className)}>
        <>{children}</>
      </div>
    </DisclosureContentAriaKit>
  )
}

export const DisclosureProvider = (props: DisclosureProviderProps) => (
  <DisclosureProviderAriaKit animated={true} {...props} />
)

export const useDisclosureStore = (props: DisclosureStoreProps) =>
  useDisclosureStoreAriaKit({ ...props })
