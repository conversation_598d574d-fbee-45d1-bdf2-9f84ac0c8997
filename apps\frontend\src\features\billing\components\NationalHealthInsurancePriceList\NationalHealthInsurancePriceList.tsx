import { useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSearchParams } from "react-router-dom"

import { useSelectStore } from "components/Ariakit/hooks"
import Select from "components/Select/Select"
import Restricted from "features/authentication/components/Restricted/Restricted"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { Heading } from "ui"

import {
  BillingCodeType,
  GetBillingCodeNhiQuery,
  PermissionKey,
  useGetBillingCodeNhiQuery,
  useGetNhiCategoriesQuery,
} from "generated/graphql"

import { CenteredLayout } from "../CenteredLayout/CenteredLayout"
import { SearchInput } from "../SearchInput/SearchInput"
import styles from "./NationalHealthInsurancePriceList.module.css"
import { NationalHealthInsurancePriceListTable } from "./NationalHealthInsurancePriceListTable/NationalHealthInsurancePriceListTable"

export type NhiPriceList = GetBillingCodeNhiQuery["billingCodes"]

const NationalHealthInsurancePriceListWithoutPermission = () => {
  const { t } = useTranslation()

  const { data: categoriesData } = useGetNhiCategoriesQuery()

  const categoryOptions = useMemo(() => {
    return (
      categoriesData?.nhiCategories.map((category) => ({
        label: category.name,
        value: category.name, // TODO: We need to use the category.id here when the backend has added support for filtering by id
      })) || []
    )
  }, [categoriesData])

  const [category, setCategory] = useState<string | null>("")

  const selectCategoryStore = useSelectStore({})

  const [searchParams] = useSearchParams()

  const searchParam = searchParams.get("search") || ""

  const { data, previousData, loading } = useGetBillingCodeNhiQuery({
    variables: {
      limit: null,
      filter: searchParam,
      billingCodeType: BillingCodeType.Nhi,
      category: category || null,
    },
  })

  return (
    <CenteredLayout>
      <Heading size="large" className={styles.header}>
        {t("National Health Insurance Price List")}
      </Heading>
      <div className={styles.searchWrap}>
        <SearchInput
          label={"Search National Health Insurance"}
          className={styles.input}
        />

        <Select
          label={t("Category")}
          selectStore={selectCategoryStore}
          options={categoryOptions}
          hideLabel
          hideMessage
          isClearable
          className={styles.selectCategory}
          onSelectChange={(value) => {
            setCategory(value)
          }}
        />
      </div>

      <NationalHealthInsurancePriceListTable
        billingCodes={data?.billingCodes || previousData?.billingCodes || []}
        isLoading={loading}
      />
    </CenteredLayout>
  )
}

const NationalHealthInsurancePriceList = () => {
  return (
    <Restricted
      to={PermissionKey.BillingBillingCodeView}
      fallback={<UnauthorizedPage />}
    >
      <NationalHealthInsurancePriceListWithoutPermission />
    </Restricted>
  )
}

export default NationalHealthInsurancePriceList
