.wrapper {
  --color-primaryaction-hover: var(--color-text);
  --color-primaryaction-main: var(--color-lev-blue-200);
  --color-surfaces-main: var(--color-white);
  --header-padding: 16px;
  box-shadow: 0 4px 4px rgba(35, 48, 129, 0.25);
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: var(--header-height);
  background-color: var(--color-surfaces-main);
  z-index: 3;
}

.logo {
  align-self: center;
  grid-column: span 2;
  height: 48px;

  &:hover {
    background-color: transparent;
  }
}
.logo svg {
  height: 100%;
}
.boxCommon {
  position: relative;
  grid-column: span 3;
  display: flex;
  flex-direction: row;
  justify-content: center;
  border-bottom: 4px solid var(--color-background-active);
  transition:
    background-color 50ms,
    border-color 50ms;
}

.boxCommon:hover {
  border-bottom-color: var(--color-background-active);
}
.boxCommon.active {
  border-bottom-color: var(--color-text-interactive);
}

.boxLink {
  flex-grow: 1;
  margin-right: 0;
  padding: 0 var(--header-padding);
}
.boxLink:hover {
  color: var(--color-text-interactive);
  background-color: var(--color-background-hover);
  flex-grow: 1;
}
.boxLink:focus-visible .boxTitle {
  outline: 2px solid var(--color-text-interactive);
  border-radius: 8px;
  outline-offset: 8px;
}

.boxTitle {
  display: inline-flex;
  align-items: center;
  position: relative;
  text-wrap: balance;
}
.secondaryAction.secondaryAction {
  align-self: center;
  border: none;
  padding: 7px;
  font-size: 24px;
  line-height: 1;
}

.dropdownButton {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 50ms;

  align-self: stretch;
  padding: 4px 11px;
  height: auto;
  width: 46px;
}

.dropdownButton:hover {
  background-color: var(--color-background-hover);
  color: var(--color-text);
}
.dropdownButton:focus {
  outline: 1px solid var(--color-text-interactive);
}

@media print {
  .wrapper {
    display: none;
  }
}

@media (max-width: 1600px) {
  .logo {
    grid-column: span 1;
  }

  .boxTitle {
    font-size: 16px;
  }
}
