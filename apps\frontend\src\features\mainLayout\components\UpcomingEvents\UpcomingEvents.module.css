.list {
  display: flex;
  flex-direction: column;
  align-self: center;
  max-width: 360px;
  gap: 4px;
}

.eventWrapper {
  display: grid;
  grid-template-columns: 1fr max-content;
  align-items: stretch;
  border-radius: var(--radius-button-half);
  height: 28px;
  padding-right: 8px;
  gap: 4px;
  overflow: hidden;
}

.eventWrapper:has(:nth-child(3)) {
  grid-template-columns: 1fr max-content max-content;
}

.eventContent {
  display: flex;
  gap: 8px;
  padding-left: 12px;
  text-overflow: ellipsis;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  border-radius: var(--radius-button-half);
}

.eventLabel {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.eventMeta {
  display: grid;
  grid-template-columns: max-content 1fr;
  align-items: center;
}
.duration {
  align-self: center;
}
