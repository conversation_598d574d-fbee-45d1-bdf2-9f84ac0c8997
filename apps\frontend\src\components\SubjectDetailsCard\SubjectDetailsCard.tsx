import c from "classnames"
import { MouseEvent } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

import { formatPersonaId } from "@leviosa/utils"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import GenderIcon from "components/Icon/GenderIcon"
import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { ButtonText, notification, Text } from "ui"
import parseAgeText from "utils/parseAgeText"

import {
  GenderId,
  Subject,
  useCreateSubjectInteractionMutation,
} from "generated/graphql"

import styles from "./SubjectDetailsCard.module.css"

type PhoneNumberProps = {
  phoneNumber: string
  showTelLink: boolean
  className?: string
}
const PhoneNumber = ({
  phoneNumber,
  showTelLink,
  className,
}: PhoneNumberProps) => {
  if (showTelLink) {
    return (
      <Text
        as="a"
        size="small"
        href={`tel:${phoneNumber}`}
        tabIndex={-1}
        className={c(styles.subjectInfoItem, styles.subjectTelLink, className)}
        onClick={(e: MouseEvent<HTMLAnchorElement>) => {
          e.stopPropagation()
        }}
      >
        <Icon name="phone-line" />
        {phoneNumber}
      </Text>
    )
  }

  return (
    <Text size="small" className={styles.subjectInfoItem}>
      <Icon name="phone-line" />
      {phoneNumber}
    </Text>
  )
}

const fallbackPersonaId = "XXXXXXXXXX"
const fallbackPhoneNumber = "XXXXXXX"
export const SubjectInfoSectionLoading = () => {
  return (
    <div className={c(styles.subjectInfoSection, styles.loading)}>
      <Text size="small" aria-hidden>
        {fallbackPersonaId}
      </Text>

      <Text size="small" className={styles.subjectInfoItem} aria-hidden>
        <GenderIcon genderId={GenderId.Unknown} />
      </Text>

      <Text size="small" className={styles.subjectInfoItem} aria-hidden>
        <Icon name="phone-line" />
        {fallbackPhoneNumber}
      </Text>
    </div>
  )
}

export type SubjectInfoSectionProps = Pick<
  Subject,
  "age" | "gender" | "personaId" | "phoneNumber"
> & {
  showTelLink?: boolean
  sectionCn?: string
  telLinkCn?: string
}
export const SubjectInfoSection = ({
  age,
  gender,
  personaId,
  phoneNumber,
  showTelLink = false,
  sectionCn,
  telLinkCn,
}: SubjectInfoSectionProps) => {
  const { t } = useTranslation()

  return (
    <PiiSensitive className={c(styles.subjectInfoSection, sectionCn)}>
      <Text size="small">{formatPersonaId(personaId)}</Text>

      <Text size="small" className={styles.subjectInfoItem}>
        <GenderIcon genderId={gender} />

        {t(...parseAgeText(age))}
      </Text>

      {phoneNumber && (
        <PhoneNumber
          key={phoneNumber}
          phoneNumber={phoneNumber}
          showTelLink={showTelLink}
          className={telLinkCn}
        />
      )}
    </PiiSensitive>
  )
}

type SubjectDetailsCardProps = SubjectInfoSectionProps & {
  id: string
  name?: string
  subjectInfoIcon?: React.ReactNode
  to?: string
  tooltipContent?: string
}
export const SubjectDetailsCard = ({
  id,
  name,
  subjectInfoIcon,
  to,
  tooltipContent,
  ...props
}: SubjectDetailsCardProps) => {
  const navigate = useNavigate()
  const { t } = useTranslation()
  const [setSelectedSubject] = useCreateSubjectInteractionMutation()

  const handleSelectSubject = () => {
    setSelectedSubject({
      variables: { subjectId: id },
      onError: () => {
        notification.create({
          message: t(
            "Something went wrong when selecting the subject, please try again"
          ),
          status: "error",
        })
      },
    })
  }

  const handleClick = () => {
    if (to) {
      navigate(to)
    } else {
      handleSelectSubject()
    }
  }
  return (
    <div
      className={styles.subjectDetailsCard}
      onClick={handleClick}
      role={to ? "link" : "button"}
      tabIndex={0}
      onKeyDown={(e: React.KeyboardEvent<HTMLDivElement>) => {
        if (e.key === "Enter" || e.key === " ") {
          handleClick()
        }
      }}
    >
      <Text className={styles.subjectNameWrap}>
        {tooltipContent ? (
          <Tooltip
            tooltipContent={tooltipContent}
            unmountOnHide
            focusable={false}
          >
            <PiiSensitive as={ButtonText}>{name}</PiiSensitive>
          </Tooltip>
        ) : (
          <PiiSensitive as={ButtonText}>{name}</PiiSensitive>
        )}

        {subjectInfoIcon}
      </Text>
      <SubjectInfoSection {...props} showTelLink={true} />
    </div>
  )
}
