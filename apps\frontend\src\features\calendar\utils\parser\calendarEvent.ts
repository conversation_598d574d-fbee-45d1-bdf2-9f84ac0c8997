import { ServiceTypeAvailabilities } from "features/calendar/components/Calendar/getAvailabilityBlocks"

import { EventInstancesQuery } from "generated/graphql"

type EventInstance = EventInstancesQuery["eventInstances"][0] & {
  type: "eventInstance"
}

type Availability = ServiceTypeAvailabilities & {
  type: "availability"
  resourceId?: string
}

type Holiday = { id: string } & {
  type: "holiday"
}

type CalendarResourceType<T> = T extends "eventInstance"
  ? EventInstance
  : T extends "availability"
    ? Availability
    : T extends "holiday"
      ? Holiday
      : never

export type CalendarEvent<
  T extends string = "eventInstance" | "availability" | "holiday",
> = {
  start: Date
  end: Date
  title: string
  resource: CalendarResourceType<T>
  allDay: boolean
  subjects?: string[]
  resourceId?: string
}

export const getEventResource = (
  event: CalendarEvent<"eventInstance" | "availability" | "holiday">
) => {
  if (
    ["eventInstance", "availability", "holiday"].includes(event.resource.type)
  ) {
    return event.resource
  }

  throw new Error("Invalid event resource type")
}
