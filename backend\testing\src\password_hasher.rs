use leviosa_domain_contracts::auth::{PasswordError, PasswordHasher};

pub struct TestPasswordHasher;

impl TestPasswordHasher {
    pub fn new() -> Self {
        Self
    }
}

impl PasswordHasher for TestPasswordHasher {
    fn verify(&self, input: &str, hashed: &str) -> Result<(), PasswordError> {
        if input == hashed {
            Ok(())
        } else {
            Err(PasswordError::InvalidPassword)
        }
    }

    fn hash(&self, password: &str) -> Result<String, PasswordError> {
        Ok(password.to_string())
    }
}
