import { useTranslation } from "react-i18next"

import { Popover, PopoverDisclosure } from "components/Ariakit"
import { usePopoverStore } from "components/Ariakit"
import { Text } from "ui"
import CountCircled from "ui/components/CountCircled/CountCircled"

import { useTeamStatisticsQuery, EncounterDisposition } from "generated/graphql"

import styles from "./TeamStatistics.module.css"
import { parseCounts } from "./parseCounts"

export type TeamStatisticsProps = {
  id: string
  title?: string
  className?: string
}

const TeamStatistics = ({ id, title, className = "" }: TeamStatisticsProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "teamStatistics" })
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "EncounterDisposition",
  })

  const popoverStore = usePopoverStore({ placement: "left-start" })

  const { data } = useTeamStatisticsQuery({
    variables: { id },
    skip: !popoverStore.useState().open,
  })
  const { statistics } = data?.team || {}

  return (
    <>
      <PopoverDisclosure store={popoverStore} className={className}>
        {title || t("statsButton")}
      </PopoverDisclosure>
      <Popover className={styles.wrapper} store={popoverStore}>
        <table className={styles.content}>
          <thead>
            <tr>
              <th></th>
              <th>{tEnum(EncounterDisposition.Discharge)}</th>
              <th>{tEnum(EncounterDisposition.Transfer)}</th>
              <th>{tEnum(EncounterDisposition.TransferExternal)}</th>
              <th>{tEnum(EncounterDisposition.Observation)}</th>
              <th>{t("nullStatus")}</th>
              <th>{t("avgAge")}</th>
            </tr>
          </thead>
          <tbody>
            {(statistics ?? [])
              // prefix data with totals
              .map((row) => ({
                ...row,
                totals:
                  (row.nullDisposition || 0) +
                  (row.transferCount || 0) +
                  (row.transferExternalCount || 0) +
                  (row.dischargeCount || 0) +
                  (row.observationCount || 0),
              }))
              .map((stat, i) => (
                <tr key={i}>
                  <td>
                    <CountCircled>{stat.totals}</CountCircled>
                    <Text size="small" className={styles.secondaryContent}>
                      {t(stat.period || "0")}
                    </Text>
                  </td>
                  {parseCounts(stat.dischargeCount, stat.dischargeAvgDuration)}
                  {parseCounts(stat.transferCount, stat.transferAvgDuration)}
                  {parseCounts(
                    stat.transferExternalCount,
                    stat.transferExternalAvgDuration
                  )}
                  {parseCounts(
                    stat.observationCount,
                    stat.observationAvgDuration
                  )}
                  <td className={styles.missing}>{stat.nullDisposition}</td>
                  <td>
                    <div>{stat.avgAge}</div>
                    <Text size="small" className={styles.secondaryContent}>
                      {stat.malePercentage}%
                    </Text>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </Popover>
    </>
  )
}

export default TeamStatistics
