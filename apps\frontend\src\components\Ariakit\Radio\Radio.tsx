import {
  Radio as RadioAriakit,
  RadioProps as RadioPropsAriakit,
} from "@ariakit/react"

import { Text } from "ui"

import styles from "./Radio.module.css"

export type RadioProps = RadioPropsAriakit & {
  label: string
  labelClassName?: string
}

export const Radio = ({
  className = "",
  labelClassName = "",
  label,
  ...rest
}: RadioProps) => {
  return (
    <Text className={`${labelClassName} ${styles.label} `} as="label">
      <RadioAriakit {...rest} className={`${styles.radio} ${className}`} />
      {label}
    </Text>
  )
}
