import { useTranslation } from "react-i18next"

import { Heading } from "ui"

import styles from "./WelcomeBackHeading.module.css"

type WelcomeBackHeadingProps = {
  name: string
}
export const WelcomeBackHeading = ({ name }: WelcomeBackHeadingProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "auth" })

  return (
    <div className={styles.welcomeBackWrap}>
      <Heading>{tRoutes("welcomeBack")}</Heading>
      <Heading size="large" className={styles.welcomeBackName}>
        {name}
      </Heading>
    </div>
  )
}
