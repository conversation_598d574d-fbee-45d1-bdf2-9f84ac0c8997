name: <PERSON><PERSON> Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        type: environment

permissions:
  id-token: write
  contents: read

jobs:
  run-migrations:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ECS_DEPLOY_ROLE }}
          role-session-name: GitHubActions-${{ github.run_id }}
          aws-region: eu-west-1
      
      - name: Get current migrations task definition
        id: task-def
        run: |
          TASK_DEFINITION="${{ github.event.inputs.environment }}-migrations-task"
          
          echo "Getting migrations task definition: $TASK_DEFINITION"
          
          # Get task definition
          aws ecs describe-task-definition \
            --task-definition $TASK_DEFINITION \
            --query taskDefinition > migrations-task-definition.json
      
      - name: Render migrations task definition with new image
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        id: render-migrations-task
        with:
          task-definition: migrations-task-definition.json
          container-name: db-migrations
          image: "${{ secrets.AWS_ECR_REPOSITORY_URL }}/db-migrations:${{ github.sha }}"
      
      - name: Run migrations task
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.render-migrations-task.outputs.task-definition }}
          cluster: "${{ github.event.inputs.environment }}-cluster"
          run-task: true
          wait-for-task-stopped: true
          run-task-subnets: ${{ secrets.AWS_MIGRATOR_SUBNETS }}
          run-task-security-groups: ${{ secrets.AWS_MIGRATOR_SECURITY_GROUPS }}
          run-task-assign-public-IP: DISABLED

  deploy:
    needs: [run-migrations]
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    strategy:
      matrix:
        include:
          - service: 'gql-api'
            repo: 'gql-api'
          - service: 'public-api'
            repo: 'public-gql-api'
          - service: 'check-in-kiosk'
            repo: 'check-in-kiosk'
          - service: 'online-booking'
            repo: 'online-booking'
          - service: 'pdf-generator'
            repo: 'pdf-generator'
          - service: 'nhi'
            repo: 'nhi-service'
          - service: 'clinic-portal'
            repo: 'clinic-portal'
    
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ECS_DEPLOY_ROLE }}
          role-session-name: GitHubActions-${{ github.run_id }}
          aws-region: eu-west-1
      
      - name: Get current task definition
        id: task-def
        run: |
          # Build cluster name directly
          CLUSTER_NAME="${{ github.event.inputs.environment }}-cluster"
          
          SERVICE_ARN=$(aws ecs describe-services \
            --cluster $CLUSTER_NAME \
            --services "${{ github.event.inputs.environment }}-${{ matrix.service }}-service" \
            --query "services[0].taskDefinition" \
            --output text)
          
          echo "Current task definition ARN: $SERVICE_ARN"
          
          # Get task definition
          aws ecs describe-task-definition \
            --task-definition $SERVICE_ARN \
            --query "taskDefinition" \
            --output json > task-definition.json
          
          # Get container name and repo
          CONTAINER_NAME=$(cat task-definition.json | jq -r '.containerDefinitions[0].name')
          
          echo "container_name=$CONTAINER_NAME" >> $GITHUB_OUTPUT
          echo "cluster_name=$CLUSTER_NAME" >> $GITHUB_OUTPUT
      
      - name: Render task definition with new image
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        id: render-task-def
        with:
          task-definition: task-definition.json
          container-name: ${{ steps.task-def.outputs.container_name }}
          image: "${{ secrets.AWS_ECR_REPOSITORY_URL }}/${{ matrix.repo }}:${{ github.sha }}"
      
      - name: Deploy to Amazon ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.render-task-def.outputs.task-definition }}
          service: ${{ github.event.inputs.environment }}-${{ matrix.service }}-service
          cluster: ${{ steps.task-def.outputs.cluster_name }}
          wait-for-service-stability: true
          wait-for-minutes: 7
      
      - name: Deployment summary
        run: |
          echo "Deployed service ${{ github.event.inputs.environment }}-${{ matrix.service }}-service with image ${{ matrix.repo }}:${{ github.sha }}"
