fragment UpcomingEventFragment on EventInstance {
  id
  fromDate
  toDate
  title
  serviceType {
    id
    name
    color
  }
  encounter {
    id
  }
  team {
    id
  }
  canceledAt
  cancellationReason
  participants {
    ... on ParticipantProvider {
      provider {
        id
        name
        specialty
      }
    }
    ... on ParticipantSubject {
      subject {
        id
      }
    }
  }
}
