//! Filter types for repository queries

use crate::ids::*;

// Journal Entry Filters
#[derive(Debug, Clone)]
pub enum GetJournalEntryFilter {
    Id(JournalEntryId),
}

#[derive(Debug, Clone)]
pub struct FindJournalEntryFilter {
    pub organisation_id: Option<OrganisationId>,
    pub subject_id: Option<SubjectId>,
    pub encounter_id: Option<EncounterId>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

// Encounter Filters
#[derive(Debug, Clone)]
pub struct FindEncountersFilter {
    pub organisation_id: Option<OrganisationId>,
    pub subject_id: Option<SubjectId>,
    pub provider_id: Option<ProviderId>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

// Note Filters
#[derive(Debug, Clone)]
pub struct FindNotesFilter {
    pub organisation_id: Option<OrganisationId>,
    pub journal_entry_id: Option<JournalEntryId>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

// Intervention Period Filters
#[derive(Debug, Clone)]
pub struct InterventionPeriodFilter {
    pub subject_id: SubjectId,
}

// Journal Entry Attachment Filters
#[derive(Debug, Clone)]
pub enum GetJournalEntryAttachmentFilter {
    Id { id: JournalEntryAttachmentId },
}

#[derive(Debug, Clone)]
pub struct FindJournalEntryAttachmentFilter {
    pub journal_entry_id: Option<JournalEntryId>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

// Inbound Data Filters
#[derive(Debug, Clone)]
pub struct GetInboundDataFilter {
    pub id: InboundDataId,
}

#[derive(Debug, Clone)]
pub struct FindInboundDataFilter {
    pub organisation_id: Option<OrganisationId>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

// Inbound Entry Filters
#[derive(Debug, Clone)]
pub struct FindInboundEntryFilter {
    pub organisation_id: Option<OrganisationId>,
    pub inbound_data_id: Option<InboundDataId>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

// Template Filters
#[derive(Debug, Clone)]
pub enum GetDrugPrescriptionTemplateFilter {
    Id(DrugPrescriptionTemplateId),
}

#[derive(Debug, Clone)]
pub struct FindDrugPrescriptionTemplateFilter {
    pub organisation_id: Option<OrganisationId>,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}
