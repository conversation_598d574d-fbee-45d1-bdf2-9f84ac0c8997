import { useState } from "react"
import { useTranslation } from "react-i18next"
import z from "zod"

import Panel from "components/Panel/Panel"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { Button, FormGrid, Input, Modal } from "ui"
import FormFooter from "ui/components/FormFooter/FormFooter"

import { namedOperations, useCreateDepartmentMutation } from "generated/graphql"

import styles from "./DepartmentCreate.module.css"

const formSchema = z.object({
  name: z.string().min(3),
  externalEhrId: z.string().min(8).optional(),
})

type DepartmentCreateProps = {
  organisationId: string
  onClose: () => void
  leviosaKindId: GlobalDataWithNonNullableActor["config"]["leviosaKindId"]
}

export const DepartmentCreate = ({
  organisationId,
  onClose,
  leviosaKindId,
}: DepartmentCreateProps) => {
  const { t } = useTranslation()
  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const [createDepartment, { loading, error }] = useCreateDepartmentMutation({
    onCompleted: onClose,
    refetchQueries: [namedOperations.Query.OrganisationManage],
  })

  function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()
    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const filteredData = Object.fromEntries(
      Object.entries(data).filter(([, value]) => !!value)
    )
    const validatedInput = formSchema.safeParse(filteredData)
    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    createDepartment({
      variables: {
        input: {
          ...validatedInput.data,
          externalEhrId: validatedInput.data.externalEhrId?.toString(),
        },
        organisationId,
      },
    })
  }

  return (
    <Modal
      isOpen
      onClose={onClose}
      title={t("routes:manageOrg.createDepartment")}
      footer={
        <FormFooter>
          {(error || validationError) && (
            <Panel status="error">
              {validationError?.message}
              {error?.message}
            </Panel>
          )}

          <Button onClick={onClose}>{t("cancel")}</Button>
          <Button
            type="submit"
            form={"depCreateForm"}
            disabled={loading}
            variant="filled"
          >
            {t("Submit")}
          </Button>
        </FormFooter>
      }
    >
      <FormGrid
        as="form"
        id="depCreateForm"
        onSubmit={handleSubmit}
        className={styles.form}
      >
        <Input label={t("Name")} name="name" required autoComplete="off" />

        {leviosaKindId === "LITE" && (
          <Input
            label={t("ExternalEhrId")}
            name="externalEhrId"
            autoComplete="off"
          />
        )}
      </FormGrid>
    </Modal>
  )
}
