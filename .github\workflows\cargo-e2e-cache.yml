name: Update Cargo E2E Cache
on:
  push:
    branches: ["development"]
jobs:
  set-up-cargo-cache:
    name: Set up cargo cache
    runs-on: ubuntu-latest
    env:
      CARGO_INCREMENTAL: 0
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: |
            backend
            packages
            .github

      - name: Check for cache hit
        id: cargo-cache
        uses: actions/cache/restore@v3
        continue-on-error: false
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-e2e-${{ hashFiles('**/Cargo.lock') }}
          lookup-only: true

      - name: Install Rust
        if: ${{ steps.cargo-cache.outputs.cache-hit != 'true' }}
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: 1.87.0
          override: true

      - name: Build cache
        if: ${{ steps.cargo-cache.outputs.cache-hit != 'true' }}
        run: |
          cargo build --locked -p migration
          cargo build --locked -p leviosa-api

      - name: Save cache
        if: ${{ steps.cargo-cache.outputs.cache-hit != 'true' }}
        uses: actions/cache/save@v3
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-e2e-${{ hashFiles('**/Cargo.lock') }}
