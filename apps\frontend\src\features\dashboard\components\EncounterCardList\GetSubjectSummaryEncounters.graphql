query GetSubjectSummaryEncounters($subjectId: UUID!) {
  subject(id: $subjectId) {
    id
    encountersInProgress: encounters(statuses: [IN_PROGRESS]) {
      ...EncounterCardFragment
    }
    encountersCheckedOut: encounters(statuses: [CHECKED_OUT]) {
      ...EncounterCardFragment
    }
    encountersPlanned: encounters(statuses: [PLANNED]) {
      ...EncounterCardFragment
    }

    organisation {
      id
      members {
        ...ProviderInfoFragment
      }
    }
  }
}
