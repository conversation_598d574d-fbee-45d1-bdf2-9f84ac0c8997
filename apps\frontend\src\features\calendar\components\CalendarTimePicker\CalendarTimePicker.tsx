import { addDays, format, parse, parseISO } from "date-fns"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"

import { useSelectStore } from "components/Ariakit/hooks"
import DatePicker from "components/DatePicker/DatePicker"
import { Select } from "components/Select/Select"
import { TimePicker } from "components/TimePicker/TimePicker"
import eventRepeatIntervalOptions, {
  DO_NOT_REPEAT,
} from "features/calendar/utils/eventRepeatIntervalOptions"
import { Input } from "ui"

import { useEventInstance } from "../forms/EventInstanceForm/EventInstance.context"
import { RepeatIntervalText } from "../forms/EventInstanceForm/RepeatIntervalText"
import { dateFormat } from "../forms/EventInstanceForm/useEventInstanceState"
import styles from "./CalendarTimePicker.module.css"
import { addMinutesToTime } from "./addMinutesToTime"
import { getDiffInMinutes } from "./getDiffInMinutes"
import { isTime1BeforeTime2 } from "./isTime1BeforeTime2"
import { isValid24HourFormat } from "./isValid24HourFormat"
import { useScrollComboboxItem } from "./useScrollComboboxItem"

type Props = {
  readOnly?: boolean
  formType: "Create" | "Edit"
  repeatInterval?: string
  untilDate?: string
}

const addOneDay = (fromDate: string) => {
  const startingDate = new Date(fromDate)

  const newDate = addDays(startingDate, 1)

  const formattedDate = newDate.toISOString().slice(0, 10)

  return formattedDate
}

export const CalendarTimePicker = ({
  readOnly = false,
  formType,
  repeatInterval,
  untilDate: selectedUntilDate,
}: Props) => {
  const {
    setFromDateState,
    setToDateState,
    comboboxFromTime,
    comboboxToTime,
    fromDateState,
    toDateState,
    fromDateTimeLocal,
    toDateTimeLocal,
  } = useEventInstance()

  const [untilDate, setUntilDate] = useState<string>(addOneDay(fromDateState))
  const [diffMinutes, setDiffMinutes] = useState(0)

  const { t } = useTranslation()

  const selectRepeatInterval = useSelectStore({
    defaultValue: DO_NOT_REPEAT,
  })

  const repeatIntervalValue = selectRepeatInterval.useState("value")

  const isRepeatIntervalSelected = repeatIntervalValue !== DO_NOT_REPEAT

  const comboboxFromTimeValue = comboboxFromTime.useState("value")
  const comboboxToTimeValue = comboboxToTime.useState("value")

  const selectEventRepeatIntervalOptions = eventRepeatIntervalOptions(t)

  useEffect(() => {
    if (!diffMinutes) return

    if (!isValid24HourFormat(comboboxFromTimeValue)) return

    comboboxToTime.setValue(
      addMinutesToTime(comboboxFromTimeValue, diffMinutes)
    )
  }, [comboboxFromTimeValue])

  useScrollComboboxItem(comboboxFromTime, "fromTime")
  useScrollComboboxItem(comboboxToTime, "toTime")

  useEffect(() => {
    const diffInMinutes = getDiffInMinutes(
      comboboxFromTimeValue,
      comboboxToTimeValue,
      diffMinutes
    )

    setDiffMinutes(diffInMinutes)
  }, [comboboxToTimeValue])

  const isFromTimeBefore = isTime1BeforeTime2(
    comboboxFromTimeValue,
    comboboxToTimeValue
  )

  useEffect(() => {
    if (!isFromTimeBefore) {
      const parsedDate = parse(fromDateState, dateFormat, new Date())
      const newDate = addDays(parsedDate, 1)
      const newDateString = format(newDate, dateFormat)
      setToDateState(newDateString)

      return
    }

    setToDateState(fromDateState)
  }, [isFromTimeBefore, fromDateState])

  const minToDate = format(addDays(parseISO(fromDateState), 1), "yyyy-MM-dd")

  const showToDate = !isFromTimeBefore && fromDateState < toDateState

  const isEditRecurrence =
    (repeatInterval &&
      repeatInterval !== DO_NOT_REPEAT &&
      formType === "Edit") ||
    false

  return (
    <div>
      <div className={styles.wrapper}>
        <DatePicker
          onChange={(e) => setFromDateState(e.target.value)}
          value={fromDateState}
          label={showToDate ? t("From Date") : t("Date")}
          name="fromDate"
          readOnly={readOnly}
          required
          inputProps={{
            className: styles.dateInputs,
          }}
          disabled={isEditRecurrence}
        />

        <div className={styles.timePickers} data-is-edit={isEditRecurrence}>
          <TimePicker
            store={comboboxFromTime}
            minuteStep={15}
            readOnly={readOnly}
            name="fromDate"
            required
            portal={false}
            hideLabel={false}
            label="From"
            startTime="08:00"
            inputClassName={styles.minutesInput}
          />

          <TimePicker
            name="toDate"
            required
            minuteStep={15}
            readOnly={readOnly}
            store={comboboxToTime}
            portal={false}
            hideLabel={false}
            label="To"
            startTime="08:00"
            inputClassName={styles.minutesInput}
          />
        </div>

        {showToDate && (
          <DatePicker
            label={t("To Date")}
            name="toDate"
            required
            readOnly={readOnly}
            value={toDateState}
            onChange={(e) => {
              const value = e.target.value
              setToDateState(value)
            }}
            min={minToDate}
            inputProps={{
              className: styles.dateInputs,
            }}
            disabled={isEditRecurrence}
          />
        )}

        {/*Data forms needed for getting values in submit logic */}
        <Input
          label="fromDate"
          hideLabel={true}
          type="hidden"
          name="fromDate"
          value={fromDateTimeLocal}
          className={styles.hiddenInput}
        />

        <Input
          label="toDate"
          hideLabel={true}
          type="hidden"
          name="toDate"
          value={toDateTimeLocal}
          className={styles.hiddenInput}
        />
      </div>
      <div
        className={styles.intervalWrap}
        data-has-interval={isRepeatIntervalSelected}
        data-is-edit={formType === "Edit"}
      >
        {formType === "Create" && (
          <Select
            label={t("Repeat interval")}
            name="repeatInterval"
            selectStore={selectRepeatInterval}
            options={selectEventRepeatIntervalOptions}
            readOnly={readOnly}
            className={styles.repeatInterval}
          />
        )}
        {isRepeatIntervalSelected && formType === "Create" && (
          <DatePicker
            required
            label={t("Until Date")}
            name="untilDate"
            readOnly={readOnly}
            value={untilDate}
            onChange={(e) => setUntilDate(e.target.value)}
          />
        )}
      </div>

      <RepeatIntervalText
        repeatIntervalValue={
          formType === "Create"
            ? repeatIntervalValue.toString()
            : repeatInterval
        }
        untilDate={formType === "Create" ? untilDate : selectedUntilDate}
        isRepeatIntervalSelected={
          formType === "Create" ? isRepeatIntervalSelected : true
        }
        fromTime={comboboxFromTimeValue}
        toTime={comboboxToTimeValue}
      />
    </div>
  )
}
