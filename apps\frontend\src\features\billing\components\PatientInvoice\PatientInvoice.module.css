.wrap {
  gap: var(--grid-gap);
  padding-bottom: 40px;
  grid-template-rows: auto 1fr;
}

.subjectWrap,
.invoiceWrap,
.invoiceInformationWrap {
  border-radius: 12px;
  /* background: var(--color-lev-blue-00); */
  grid-column: 2 / 10;
  padding: 24px 40px;
  display: grid;
  grid-row-gap: var(--grid-gap);
  align-self: start;
}

.invoiceWrap {
  grid-column: 2 / 10;
  padding: 24px 40px;
  display: grid;
  grid-row-gap: var(--grid-gap);
  align-content: start;
}

.invoiceInformationWrap {
  grid-column: -4 / -1;
  grid-row: 1 / 3;
  padding: 32px;

  display: grid;
  grid-row-gap: var(--grid-gap);
  align-content: start;
}

.subject {
  display: flex;
  justify-content: space-between;
}

.subjectInputs {
  display: flex;
  gap: var(--grid-gap);
}

.subjectInputs > div {
  width: 100%;
}

.insuranceStatus {
  color: var(--LEV-Blue-Gray-violet-dark, #8386b8);
}

.price {
  color: var(--color-text);
}

@media (max-width: 1919px) {
  .subjectWrap {
    grid-column: 1/ 10;
  }

  .invoiceWrap {
    grid-column: 1 / 10;
  }

  .invoiceInformationWrap {
    grid-column: -4/ -1;
  }
}

@media (max-width: 1599px) {
  .subjectWrap {
    grid-column: 1/ -1;
  }
  .invoiceWrap {
    grid-column: 1 / -1;
  }
  .invoiceInformationWrap {
    grid-column: 1 / -1;
    grid-row: 3;

    grid-template-columns: repeat(2, 1fr);
    grid-gap: var(--grid-gap);
  }
}
