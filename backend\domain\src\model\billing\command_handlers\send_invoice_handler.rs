use crate::auth::AuthenticatedUser;
use crate::billing::{
    GetInvoiceFilter, Invoice, InvoiceId, LocaleKey, UpdateInvoiceInput, translate,
};
use crate::command::CommandHandler;
use crate::email::{EmailAttachment, EmailSendInput, EmailSender};
use crate::errors::Result;
use crate::file_repo::IFileRepo;
use crate::i18n::LanguageId;
use crate::pdf_generator::PdfGenerator;
use crate::permissions::PermissionKey;
use crate::repo_connection::{IRepoConnection, IRepoTransaction, ITransactionManager};
use crate::set::Set;
use tracing;

use super::download_invoice_pdf_handler::DownloadInvoicePdfHandler;

#[derive(Default)]
pub struct SendInvoiceCommand {
    pub invoice_id: InvoiceId,
    pub email: Option<String>,
    pub update_subject_email: bool,
}

impl SendInvoiceCommand {
    pub fn new(invoice_id: InvoiceId) -> Self {
        Self {
            invoice_id,
            email: None,
            update_subject_email: false,
        }
    }
}

pub struct SendInvoiceHandler<'a, T: ITransactionManager> {
    tx_manager: T,
    user: AuthenticatedUser,
    token: String,
    language_id: LanguageId,
    file_repo: &'a dyn IFileRepo,
    pdf_generator: &'a dyn PdfGenerator,
    email_sender: &'a dyn EmailSender,
}

impl<'a, T: ITransactionManager> SendInvoiceHandler<'a, T> {
    pub fn tx_manager(&self) -> &T {
        &self.tx_manager
    }

    pub fn user(&self) -> &AuthenticatedUser {
        &self.user
    }
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        tx_manager: T,
        user: AuthenticatedUser,
        token: String,
        language_id: LanguageId,
        file_repo: &'a dyn IFileRepo,
        pdf_generator: &'a dyn PdfGenerator,
        email_sender: &'a dyn EmailSender,
    ) -> Self {
        Self {
            tx_manager,
            user,
            token,
            language_id,
            file_repo,
            pdf_generator,
            email_sender,
        }
    }

    async fn send_invoice_via_email(
        &self,
        invoice: &Invoice,
        tx: &<T as ITransactionManager>::Transaction,
        override_email: Option<String>,
        update_subject_email: bool,
    ) -> Result<()> {
        let language_id = self.language_id;
        let email_to_use = match &override_email {
            Some(email) => email.clone(),
            None => {
                if invoice.payer_email.is_none() {
                    return Err(anyhow::anyhow!(
                        "Cannot send invoice {}: no email address available",
                        invoice.id()
                    )
                    .into());
                }
                invoice.payer_email.clone().unwrap()
            }
        };

        let org_repo = tx.repos().organisation_repo();
        let org = org_repo.get(self.user.organisation_id()).await?;

        let subject_repo = tx.repos().subject_repo();
        let subject = subject_repo
            .get(crate::accounts::GetSubjectFilter::Id {
                id: invoice.subject_id(),
            })
            .await?;

        let subject_name = subject.name.value().to_string();

        if update_subject_email && override_email.is_some() {
            tracing::info!(
                "Updating subject {} email from {} to {}",
                subject.id,
                if subject.email.is_some() {
                    "Some(...)"
                } else {
                    "None"
                },
                email_to_use
            );

            let updated_subject = subject.update(
                crate::accounts::SubjectUpdateArgs {
                    name: None,
                    address_id: None,
                    email: Some(Set {
                        set: Some(email_to_use.clone()),
                    }),
                    phone_number: None,
                    other: None,
                },
                &self.user,
            )?;
            subject_repo.save(updated_subject).await?;
        }

        let pdf = DownloadInvoicePdfHandler::<T::Transaction>::get_invoice_pdf(
            invoice,
            &self.user,
            &self.token,
            self.pdf_generator,
            self.file_repo,
        )
        .await?;

        let subject_template = translate(LocaleKey::InvoiceEmailSubject, language_id).to_string();
        let body_template = translate(LocaleKey::InvoiceEmailBody, language_id).to_string();

        tracing::info!(
            "Sending invoice {} via email to {} ({})",
            invoice.id(),
            subject_name,
            email_to_use
        );

        self.email_sender
            .send(EmailSendInput {
                to_email: email_to_use,
                to_name: subject_name,
                subject_template,
                body_template,
                params: [("org_name".to_string(), org.name().clone())].into(),
                attachments: vec![EmailAttachment {
                    content_type: "application/pdf".to_string(),
                    filename: "invoice.pdf".to_string(),
                    content: pdf.content,
                }],
                from_email: Some("<EMAIL>".to_string()),
                from_name: Some(org.name().to_string()),
            })
            .await?;

        Ok(())
    }
}

#[async_trait::async_trait]
impl<T: ITransactionManager> CommandHandler<SendInvoiceCommand> for SendInvoiceHandler<'_, T> {
    type Model = Invoice;

    fn required_permission() -> PermissionKey {
        PermissionKey::Billing_Invoice_Create
    }

    async fn execute(&self, command: SendInvoiceCommand) -> Result<Invoice> {
        let tx = self.tx_manager.transaction().await?;

        let result = async {
            let invoice = tx
                .repos()
                .invoice_repo()
                .get(GetInvoiceFilter::Id(command.invoice_id))
                .await?;

            if !invoice.issued() {
                return Err(
                    anyhow::anyhow!("Cannot send an invoice that has not been issued.").into(),
                );
            }

            self.send_invoice_via_email(
                &invoice,
                &tx,
                command.email.clone(),
                command.update_subject_email,
            )
            .await?;

            let total_payable_by_subject = invoice.total_payable_by_subject();
            let total_payable_by_insurance = invoice.total_payable_by_insurance();

            let payer_email = if command.email.is_some() {
                command.email.clone()
            } else {
                invoice.payer_email.clone()
            };

            let update_invoice_input = UpdateInvoiceInput {
                provider_id: invoice.provider_id(),
                issuer_id: invoice.issuer_id(),
                payment_date: invoice.payment_date(),
                treatment_date: invoice.treatment_date(),
                due_date: invoice.due_date(),
                total_discount: invoice.total_discount(),
                total_vat: invoice.total_vat(),
                total: invoice.total(),
                subject_discount: invoice.subject_discount(),
                nhi_pays_all: Some(invoice.nhi_pays_all()),
                total_payable_by_subject,
                total_payable_by_insurance,
                reference: invoice.reference().as_ref().map(|s| s.to_string()),
                payer_id: invoice.payer_id().as_ref().map(|s| s.to_string()),
                payer_name: invoice.payer_name().as_ref().map(|s| s.to_string()),
                payer_email,
                comment: invoice.comment().as_ref().map(|s| s.to_string()),
                send_invoice_mail: Some(true),
                print_invoice: invoice.print_invoice(),
                payment_method: *invoice.payment_method(),
            };

            let updated_invoice = invoice.update(update_invoice_input, &self.user);
            let result = tx.repos().invoice_repo().save(updated_invoice).await?;
            Ok(result)
        }
        .await;

        match result {
            Ok(invoice) => {
                tx.commit().await?;
                Ok(invoice)
            }
            Err(e) => {
                let _ = tx.rollback().await;
                Err(e)
            }
        }
    }
}
