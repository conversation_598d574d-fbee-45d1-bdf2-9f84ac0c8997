import { Form } from "@remix-run/react"
import { useTranslation } from "react-i18next"

import Numpad from "../../components/Numpad/Numpad"
import styles from "./CheckIn.module.css"

type CheckInProps = {
  validationError?: string
  onFormUpdate?: () => void
}

const CheckIn = ({ validationError, onFormUpdate }: CheckInProps) => {
  const { t } = useTranslation()

  return (
    <div className={styles.container}>
      <h2
        className={styles.heading}
        dangerouslySetInnerHTML={{
          __html: t("insertToCheckIn"),
        }}
      />
      <Form method="post">
        <Numpad
          validationError={validationError}
          placeholder="000000-0000"
          submitLabel="checkin"
          onFormUpdate={onFormUpdate}
        />
      </Form>
    </div>
  )
}

export default CheckIn
