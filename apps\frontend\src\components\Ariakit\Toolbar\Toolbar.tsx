import * as Ariakit from "@ariakit/react"
import c from "classnames"
import { forwardRef } from "react"

import { SelectArrow, SelectPopover, SelectProvider } from "components/Ariakit"

import styles from "./Toolbar.module.css"

export const Toolbar = forwardRef<
  HTMLDivElement,
  Omit<Ariakit.ToolbarProps, "store">
>(function Toolbar({ className, ...props }, ref) {
  const toolbar = Ariakit.useToolbarStore()
  return (
    <Ariakit.Toolbar
      ref={ref}
      store={toolbar}
      className={c(styles.toolbar, className)}
      {...props}
    />
  )
})

export const ToolbarItem = Ariakit.ToolbarItem

type ToolbarSelectProps = {
  label: React.ReactNode
  value: string | string[]
  children: React.ReactNode
  selectProps: Omit<Ariakit.SelectProps, "store">
}

export const ToolbarSelect = ({
  label,
  value,
  children,
  selectProps,
}: ToolbarSelectProps) => {
  return (
    <SelectProvider value={value}>
      {/* we're using Ariakit's Select to avoid custom styling */}
      <Ariakit.Select render={<ToolbarItem />} {...selectProps}>
        {label}
        <SelectArrow />
      </Ariakit.Select>

      <SelectPopover portal sameWidth={false}>
        {children}
      </SelectPopover>
    </SelectProvider>
  )
}

export const ToolbarSeparator = forwardRef<
  HTMLHRElement,
  Ariakit.ToolbarSeparatorProps
>(function ToolbarSeparator({ className, ...props }, ref) {
  return (
    <Ariakit.ToolbarSeparator
      ref={ref}
      className={c(styles.toolbarSeparator, className)}
      {...props}
    />
  )
})
