import { useEffect, useRef, useState } from "react"

import { ErrorBoundary } from "components/ErrorBoundary/ErrorBoundary"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { PanelWithIcon } from "components/Panel/Panel"
import Restricted from "features/authentication/components/Restricted/Restricted"

import {
  LeviosaKindId,
  PermissionKey,
  useGetSubjectSummaryEncountersQuery,
} from "generated/graphql"

import { CreateEncounter } from "../../../subject-journal/components/CreateEncounter/CreateEncounter"
import { SubjectPastEvents } from "../SubjectPastEvents/SubjectPastEvents"
import { SubjectUpcomingEvents } from "../SubjectUpcomingEvents/SubjectUpcomingEvents"
import { EncounterCardItem } from "./EncounterCardItem"
import styles from "./EncounterCardList.module.css"

export type EncounterCardListProps = {
  subjectId: string
}

export default function EncounterCardList({
  subjectId,
}: EncounterCardListProps) {
  const [showAddEncounterForm, setShowAddEncounterForm] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (showAddEncounterForm && ref.current) {
      ref.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [showAddEncounterForm])

  const {
    globalData: {
      config: { leviosaKindId },
    },
  } = useGlobalState()

  const { data } = useGetSubjectSummaryEncountersQuery({
    variables: { subjectId },
  })

  const mergedEncounters = [
    ...(data?.subject.encountersInProgress || []),
    ...(data?.subject.encountersCheckedOut || []),
    ...(data?.subject.encountersPlanned || []),
  ]

  const members = data?.subject.organisation.members

  return (
    <div className={styles.wrap} ref={ref}>
      {!showAddEncounterForm && (
        <>
          <Restricted to={PermissionKey.CalendarView}>
            <ErrorBoundary>
              <SubjectUpcomingEvents subjectId={subjectId} />
            </ErrorBoundary>
          </Restricted>
          <Restricted to={PermissionKey.SubjectJournalEncounterView}>
            <ul className={styles.listWrap}>
              {mergedEncounters.map((encounter) => (
                <ErrorBoundary
                  fallback={
                    <PanelWithIcon status="warning">
                      We've encountered and error while displaying this data.
                      Please refresh the page.
                    </PanelWithIcon>
                  }
                  key={encounter.id}
                >
                  <EncounterCardItem
                    subjectId={subjectId}
                    encounter={encounter}
                  />
                </ErrorBoundary>
              ))}
            </ul>
          </Restricted>
          <Restricted to={PermissionKey.CalendarView}>
            <ErrorBoundary>
              <SubjectPastEvents subjectId={subjectId} />
            </ErrorBoundary>
          </Restricted>
        </>
      )}

      {leviosaKindId === LeviosaKindId.Mss && (
        <Restricted to={PermissionKey.SubjectJournalEncounterEdit}>
          <CreateEncounter
            subjectId={subjectId}
            showAddEncounterForm={showAddEncounterForm}
            onAddEncounter={() =>
              setShowAddEncounterForm(!showAddEncounterForm)
            }
            onCancel={() => setShowAddEncounterForm(!showAddEncounterForm)}
            members={Array.from(members?.slice() ?? [])}
            formClassName={styles.addEncounterWrap}
            refetchSubjectSummaryEncounters
          />
        </Restricted>
      )}
    </div>
  )
}
