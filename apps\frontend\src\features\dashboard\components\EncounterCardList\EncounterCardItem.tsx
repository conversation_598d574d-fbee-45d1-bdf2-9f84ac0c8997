import { useEncounterActions } from "hooks/useEncounterActions"
import { CardActionMenu } from "ui/components/CardActionMenu/CardActionMenu"
import { EncounterCard } from "ui/components/Encounter/EncounterCard"

import {
  GetSubjectSummaryEncountersQuery,
  useAddResponsibleProvidersMutation,
  useDeleteResponsibleProvidersMutation,
  useUpdateEncounterDispositionMutation,
} from "generated/graphql"

type EncounterCardItemProps = {
  encounter:
    | GetSubjectSummaryEncountersQuery["subject"]["encountersCheckedOut"][0]
    | GetSubjectSummaryEncountersQuery["subject"]["encountersPlanned"][0]
    | GetSubjectSummaryEncountersQuery["subject"]["encountersInProgress"][0]

  subjectId: string
}

export const EncounterCardItem = ({
  encounter,
  subjectId,
}: EncounterCardItemProps) => {
  const {
    id,
    responsibleProviders,
    reason,
    journalEntries,
    status,
    disposition,
    toDate,
    fromDate,
    primaryTeam,
    note,
    priority,
  } = encounter

  const { encounterTransitions } = useEncounterActions({
    encounterId: encounter.id,
  })
  const [updateEncounterDisposition] = useUpdateEncounterDispositionMutation()

  const [addResponsibleProviders] = useAddResponsibleProvidersMutation()
  const [deleteResponsibleProviders] = useDeleteResponsibleProvidersMutation()

  return (
    <EncounterCard
      entriesLength={journalEntries.length}
      id={id}
      note={note || ""}
      priority={priority ?? undefined}
      reason={reason}
      responsibleProviders={[...responsibleProviders]}
      subjectId={subjectId}
      teamMembers={[...primaryTeam.members]}
      teamName={primaryTeam.name}
      actionMenu={
        <CardActionMenu
          fromDate={fromDate}
          toDate={toDate}
          allowedTransitions={status.allowedTransitions}
          disposition={disposition}
          status={status.status}
          updateEncounterStatus={(status) => encounterTransitions[status]()}
          updateEncounterDisposition={(disposition) =>
            updateEncounterDisposition({
              variables: {
                updateEncounterDispositionId: id,
                disposition,
              },
            })
          }
        />
      }
      onAddResponsibleProvider={(selected) => {
        addResponsibleProviders({
          variables: {
            encounterId: id,
            providerIds: [selected],
          },
        })
      }}
      onDeleteResponsibleProvider={(selected) => {
        deleteResponsibleProviders({
          variables: {
            encounterId: id,
            providerIds: [selected],
          },
        })
      }}
    />
  )
}
