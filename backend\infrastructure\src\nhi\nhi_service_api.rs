use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::{PersonaIdType, SubjectId};
use leviosa_domain_contracts::{
    errors::{CustomError, Error},
    services::{MedicalBill, NhiService, NhiSubmissionResult, ServiceCommunicator, ServiceCommunicatorError, SubjectInsuranceStatus},
};
use serde::Deserialize;
use serde_json::Value;

#[derive(Debug, Deserialize)]
struct SubjectPaymentStatusResponse {
    // Categories such as "ALM", "ELLI", "BARN" that describe the subject's insurance status.
    pub subject_status: String,
    /// Max amount the subject should pay on an invoice.
    pub payment_status: f64,
    #[allow(dead_code)]
    pub updated_at: DateTime<Utc>,
    #[allow(dead_code)]
    pub has_discount_card: bool,
    pub is_insured: bool,
    /// The percentage that the insurance covers. This is different between `subject_status`.
    /// It needs to be divided by 100 in calculations
    pub insurance_percentage: i32,
    /// `radnumer_si` uniquely identifies the subject's payment status and must be sent to NHI with the invoice.
    pub radnumer_si: i32,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct NhiServiceApi<T: ServiceCommunicator> {
    communicator: T,
}

impl<T: ServiceCommunicator> NhiServiceApi<T> {
    pub fn new(communicator: T) -> Self {
        Self { communicator }
    }
}

#[async_trait]
impl<T: ServiceCommunicator> NhiService for NhiServiceApi<T> {
    async fn get_subject_payment_status(
        &self,
        subject_id: SubjectId,
        token: &str,
    ) -> leviosa_domain::errors::Result<SubjectInsuranceStatus> {
        // For now, assume all subjects are Icelandic ID numbers
        // In a real implementation, you'd need to check the persona ID type
        let path = format!("/payment-status?person_id={}", subject_id);
        let result = self.communicator.get_json(path.as_str(), token).await;

        return match result {
            Ok(json) => {
                let payment_status = serde_json::from_value::<SubjectPaymentStatusResponse>(json);
                match payment_status {
                    Ok(payment_status) => {
                        if payment_status.is_insured {
                            Ok(SubjectInsuranceStatus::Insured {
                                insurance_category: payment_status.subject_status,
                                maximum_payable_by_subject: payment_status.payment_status,
                                insurance_percentage: payment_status.insurance_percentage,
                                payment_status_serial_number: payment_status.radnumer_si as i64,
                            })
                        } else {
                            Ok(SubjectInsuranceStatus::Uninsured)
                        }
                    }
                    Err(e) => Err(CustomError::new(
                        None,
                        format!("Failed to parse payment status: {e:?}"),
                    )
                    .into()),
                }
            }
            Err(ServiceCommunicatorError::NotFound(_)) => {
                tracing::warn!("Subject {} not found in NHI", subject_id);
                return Ok(SubjectInsuranceStatus::Uninsured);
            }
            Err(e) => {
                Err(CustomError::new(None, format!("Failed to get payment status: {e:?}")).into())
            }
        };
    }

    async fn submit_invoice(
        &self,
        medical_bill: MedicalBill,
        on_behalf_of: &str,
        token: &str,
    ) -> leviosa_domain::errors::Result<NhiSubmissionResult> {
        let invoice_number = medical_bill.receipt_number.clone();
        let path = format!("/send-medical-bill?on_behalf_of={on_behalf_of}");
        let medical_bill_json = match serde_json::to_value(medical_bill) {
            Ok(json) => json,
            Err(e) => {
                return Err(CustomError::new(
                    None,
                    format!("Failed to serialize medical bill {invoice_number}: {e:?}"),
                )
                .into());
            }
        };

        let result: Result<Value, ServiceCommunicatorError> = self
            .communicator
            .post_json(path.as_str(), token, medical_bill_json)
            .await;

        match result {
            Ok(json_response) => {
                // Attempt to deserialize the entire JSON response into NhiSubmissionResult
                match serde_json::from_value::<NhiSubmissionResult>(json_response.clone()) {
                    Ok(submission_result) => Ok(submission_result),
                    Err(e) => {
                        // Log the raw response for debugging if parsing fails
                        tracing::error!(raw_response = ?json_response, error = %e, "Failed to parse NhiSubmissionResult from SÍ response for invoice {}", invoice_number);
                        Err(CustomError::new(
                            None,
                            format!(
                                "Failed to parse SÍ response for invoice {invoice_number}: {e:?}"
                            ),
                        )
                        .into())
                    }
                }
            }
            Err(e) => {
                tracing::error!(error = %e, "Failed to submit invoice {} to SÍ", invoice_number);
                Err(CustomError::new(
                    None,
                    format!("Failed to submit invoice {invoice_number}: {e:?}"),
                )
                .into())
            }
        }
    }
}
