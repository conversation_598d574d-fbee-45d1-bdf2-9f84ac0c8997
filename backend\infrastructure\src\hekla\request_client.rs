use crate::auth::AesGcmSivEncryptor;

use super::{
    CreateHeklaDoctorPasswordCache, CreateHeklaRequest, <PERSON>kla<PERSON><PERSON>, <PERSON>klaDoctorPasswordCache,
    HeklaIntegrationError, HeklaRequest, HeklaServiceId, IHeklaDoctorPasswordCacheRepo,
    IHeklaRequestRepository,
};
use leviosa_domain_types::DataEncryptor;
use serde::de::DeserializeOwned;
use uuid::Uuid;

pub struct HeklaRequestClientConfig {
    pub retry_count: u32,
    pub retry_interval: std::time::Duration,
}

impl Default for HeklaRequestClientConfig {
    fn default() -> Self {
        Self {
            retry_count: 3,
            retry_interval: std::time::Duration::from_millis(1000),
        }
    }
}

/// Used for sending `DrugPrescription` requests to Hekla
pub struct HeklaRequestClient {
    api: Box<dyn He<PERSON><PERSON><PERSON>>,
    request_repo: Box<dyn IHeklaRequestRepository>,
    doctor_password_cache_repo: Box<dyn IHeklaDoctorPasswordCacheRepo>,
    encryptor: AesGcmSivEncryptor,
    config: He<PERSON>RequestClientConfig,
}

impl HeklaRequestClient {
    pub fn new(
        api: Box<dyn HeklaApi>,
        request_repo: Box<dyn IHeklaRequestRepository>,
        doctor_password_cache_repo: Box<dyn IHeklaDoctorPasswordCacheRepo>,
        encryptor: AesGcmSivEncryptor,
    ) -> Self {
        Self {
            api,
            request_repo,
            doctor_password_cache_repo,
            encryptor,
            config: HeklaRequestClientConfig::default(),
        }
    }

    #[must_use]
    pub fn with_config(self, config: HeklaRequestClientConfig) -> Self {
        Self { config, ..self }
    }

    pub async fn current_request(
        &self,
        entity_id: Uuid,
    ) -> Result<Option<HeklaRequest>, HeklaIntegrationError> {
        self.request_repo.newest_by_entity_id(entity_id).await
    }

    pub async fn send_sync_request(
        &self,
        service_id: HeklaServiceId,
        xml_body: String,
        timeout_ms: Option<u64>,
    ) -> Result<String, HeklaIntegrationError> {
        self.api.sync(service_id, xml_body, timeout_ms).await
    }

    pub async fn send_request(
        &self,
        message_id: Uuid,
        entity_id: Uuid,
        service_id: HeklaServiceId,
        xml_body: String,
        receiver: Option<String>,
    ) -> Result<Uuid, HeklaIntegrationError> {
        self.request_repo
            .create(CreateHeklaRequest {
                id: message_id,
                entity_id,
                service_id,
            })
            .await?;

        let result = self
            .api
            .post_xml(service_id, message_id, xml_body, None, receiver)
            .await?;

        self.request_repo.pending(message_id).await?;

        Ok(result)
    }

    pub async fn get_response_with_retries<T: HeklaResponseBody>(
        &self,
        message_id: Uuid,
        service_id: HeklaServiceId,
    ) -> Result<T, HeklaIntegrationError> {
        let total_attempts = self.config.retry_count + 1;
        let mut attempt = 0;

        while attempt < total_attempts {
            attempt += 1;

            let result = self.get_response(message_id, service_id).await;

            if attempt < total_attempts {
                if let Err(HeklaIntegrationError::MessageResponseNotFound(_)) = result {
                    std::thread::sleep(self.config.retry_interval);
                    continue;
                }
            }

            return result;
        }

        Err(HeklaIntegrationError::MessageResponseNotFound(message_id))
    }

    async fn get_response<T: HeklaResponseBody>(
        &self,
        message_id: Uuid,
        service_id: HeklaServiceId,
    ) -> Result<T, HeklaIntegrationError> {
        let response = self.api.get_xml(service_id, None, None).await?;

        let request_messages: Vec<_> = response
            .into_iter()
            .filter(|m| m.message_id == message_id.to_string())
            .collect();

        // No response
        if request_messages.is_empty() {
            return Err(HeklaIntegrationError::MessageResponseNotFound(message_id));
        }
        // Response received
        else if let Some(xml_body) = request_messages.iter().find_map(|m| m.xml_body.clone()) {
            let body = serde_xml_rs::from_str::<T>(&xml_body).map_err(|_| {
                HeklaIntegrationError::Parsing(format!(
                    "Unable to parse hekla response XML for request {message_id}, received:\n {xml_body}"
                ))
            })?;

            if body.success() {
                self.request_repo.success(message_id, xml_body).await?;
            } else {
                self.request_repo.failed(message_id, xml_body).await?;
            }

            self.delete_request(message_id, service_id).await?;

            return Ok(body);
        }
        // ACK
        else if request_messages.iter().any(|m| m.result_type == 3) {
            self.request_repo.ack(message_id).await?;
            return Err(HeklaIntegrationError::MessageResponseNotFound(message_id));
        }
        // NACK
        else if let Some(res) = request_messages.into_iter().find(|m| m.result_type == 4) {
            self.request_repo
                .nack(message_id, res.error_message.clone())
                .await?;
            self.delete_request(message_id, service_id).await?;

            return Err(HeklaIntegrationError::NotAcknowledged(
                res.error_message.unwrap_or(
                    "The remote service did not acknowledge the request. Please try again later."
                        .to_string(),
                ),
            ));
        }

        Err(HeklaIntegrationError::Other(format!(
            "Received a response for request '{message_id}', but it wasn't recognized as an Ack, Nack, Success or Failed."
        )))
    }

    async fn delete_request(
        &self,
        message_id: Uuid,
        service_id: HeklaServiceId,
    ) -> Result<(), HeklaIntegrationError> {
        let all_requests = self.api.get_xml(service_id, None, None).await?;

        let messages_to_delete: Vec<_> = all_requests
            .into_iter()
            .filter(|m| m.message_id == message_id.to_string())
            .collect();

        for message in messages_to_delete {
            self.api
                .delete_xml(service_id, &message.handle, None)
                .await?;
        }

        self.request_repo.delete(message_id).await?;

        Ok(())
    }

    pub async fn get_doctor_password(
        &self,
        doctor_id: String,
    ) -> Result<Option<String>, HeklaIntegrationError> {
        let cache = self.doctor_password_cache_repo.get(doctor_id).await?;

        if let Some(cache) = cache {
            let decrypted_password = self
                .encryptor
                .decrypt_from_string(&cache.doctor_password)
                .map_err(|e| {
                    HeklaIntegrationError::Other(format!("Failed to decrypt doctor password: {e}",))
                })?;

            let password_str = String::from_utf8(decrypted_password).map_err(|e| {
                HeklaIntegrationError::Other(format!(
                    "Failed to convert decrypted password to string: {e}"
                ))
            })?;

            Ok(Some(password_str))
        } else {
            Ok(None)
        }
    }

    pub async fn create_doctor_password(
        &self,
        doctor_id: String,
        doctor_password: String,
    ) -> Result<HeklaDoctorPasswordCache, HeklaIntegrationError> {
        let encrypted_password = self
            .encryptor
            .encrypt_to_string(doctor_password.as_bytes())
            .map_err(|e| {
                HeklaIntegrationError::Other(format!("Failed to encrypt doctor password: {e}"))
            })?;

        self.doctor_password_cache_repo
            .create(CreateHeklaDoctorPasswordCache {
                doctor_id,
                doctor_password: encrypted_password,
            })
            .await
    }
}

pub trait HeklaResponseBody: DeserializeOwned {
    fn success(&self) -> bool;
}
