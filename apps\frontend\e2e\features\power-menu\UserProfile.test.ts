// import { faker } from "@faker-js/faker"
import { test, expect } from "@playwright/test"

import { CheckUrl } from "../utils/testUtils"

/* eslint-disable playwright/no-wait-for-timeout */

test.describe("User Profile", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/")
    await page.waitForTimeout(800)
  })

  test("should open user profile from the power-menu", async ({ page }) => {
    // Arrange
    // Navigate back to front page
    await page.goto("/")

    // Act
    // Open power menu
    await page.click('[data-testid="power-menu-button"]')
    // Wait for power menu to appear
    await page.waitForSelector("[data-testid=power-menu]")
    // Check if power menu is open
    await expect(page.locator("[data-testid=power-menu]")).toBeVisible()
    // Search for "settings"
    await page.fill('[data-testid="search-input"]', "settings")
    // Check if search results are visible
    await expect(
      page.locator('[data-testid="power-menu-item"]', { hasText: "Settings" })
    ).toBeVisible()
    // Click on the search result
    await page.click('[data-testid="power-menu-item"] >> text="Settings"')
    // Wait for the page to finish loading
    await page.waitForLoadState()

    // Assert
    // Check if the page is redirected to the settings
    await CheckUrl(page, /settings/)
  })

  // test should update name
  // SKIP this test because
  // - it is flaky
  // - manually testing change name is fast and easy
  // - we will be refactoring this at some point
  // test.skip("should update name", async ({ page }) => {
  //   // Arrange
  //   const newName = faker.name.firstName()

  //   // Act
  //   // Fill name field
  //   page.locator('[type="text"]').first().fill(newName)
  //   await page.click('button:has-text("Submit")') //page has two Submit buttons so we need to use the button text that happens to be "Submit"

  //   // Assert
  //   await page.goto("/")
  //   // Wait for the page to finish loading
  //   await page.waitForLoadState()
  //   // expect the name in the header to be the new name
  //   await expect(page.locator('[data-testid="provider-name"]')).toHaveText(
  //     newName
  //   )
  // })

  // test should update the password
  // SKIP this test because
  // - it is flaky
  // - manually testing change password is fast and easy
  // - we will be refactoring this at some point
  test.skip("should update password", async ({ page }) => {
    // Arrange
    const originalPassword = "somepassword"
    const newPassword = "Randompassword12345!@#"

    // Fill current password field
    await page
      .locator('[class*="Input_wrap__"] :text("Current password") + input')
      .first()
      .fill(originalPassword)
    // Fill New password field
    await page
      .locator('[class*="Input_wrap__"] :text("New password") + input')
      .first()
      .fill(newPassword)
    // Fill Repeat new password field
    await page
      .locator('[class*="Input_wrap__"] :text("Repeat new password") + input')
      .first()
      .fill(newPassword)

    // Act
    await page.click('button:has-text("Change Password")') //page has two Submit buttons so we need to use the button text
    // wait for the response to come back from the server
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(1000)

    // Assert
    // if the notification is still visible check the data-status attribute of the notification element
    // eslint-disable-next-line playwright/no-conditional-in-test
    if (
      // eslint-disable-next-line playwright/no-conditional-in-test
      (await page.locator('[class*="Notification_wrapper"]').isVisible()) &&
      (await page.locator('[class*="Notification_content"]').isVisible())
    ) {
      await expect(
        page.locator('[class*="Notification_wrapper"]')
      ).toHaveAttribute("data-status", "success")
      await expect(page.locator('[class*="Notification_content"]')).toHaveText(
        "password has been changed successfully"
      )
    }

    // wait for 5 seconds so the notification panel disappears before trying to validate the new password works and resetting the password to the original value
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(5000)

    // Clean up
    // Fill current password field with new password
    await page
      .locator('[class*="Input_wrap__"] :text("Current password") + input')
      .first()
      .fill(newPassword)
    // Fill New password field with old password
    await page
      .locator('[class*="Input_wrap__"] :text("New password") + input')
      .first()
      .fill(originalPassword)
    // Fill Repeat new password field with old password
    await page
      .locator('[class*="Input_wrap__"] :text("Repeat new password") + input')
      .first()
      .fill(originalPassword)

    await page.click('button:has-text("Change Password")') //page has two Submit buttons so we need to use the button text
    // wait for the response to come back from the server
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(1000)

    // if the notification is still visible check the data-status attribute of the notification element
    // eslint-disable-next-line playwright/no-conditional-in-test
    if (
      // eslint-disable-next-line playwright/no-conditional-in-test
      (await page.locator('[class*="Notification_wrapper"]').isVisible()) &&
      (await page.locator('[class*="Notification_content"]').isVisible())
    ) {
      await expect(
        page.locator('[class*="Notification_wrapper"]')
      ).toHaveAttribute("data-status", "success")
      await expect(page.locator('[class*="Notification_content"]')).toHaveText(
        "password has been changed successfully"
      )
    }
  })
})
