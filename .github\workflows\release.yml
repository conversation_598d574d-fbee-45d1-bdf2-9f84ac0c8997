name: Release
on: workflow_dispatch

jobs:
  backend-rust:
    name: Backend Rust
    needs: [ migrator ]
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/backend-rust/backend-rust.dockerfile
      tag_prefix: leviosa_backend_rust
      env_file_path: ./backend/api/.env.example
      needs_postgres: true
      run_options: --mount type=bind,source="$(pwd)"/backend/api/dev.private.key,target=/leviosa/dev.private.key --mount type=bind,source="$(pwd)"/backend/api/dev.public.pem,target=/leviosa/dev.public.pem
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}


  backend-go:
    name: Backend Go
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/backend-go/backend-go.dockerfile
      tag_prefix: leviosa_backend_go
      env_file_path: ./micro-services/nhi-service/.env.example
      run_options: --mount type=bind,source="$(pwd)"/micro-services/nhi-service/dev.public.pem,target=/leviosa/dev.public.pem
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}


  public-api:
    name: Public API
    needs: [ migrator ]
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/public-api/public-api.dockerfile
      tag_prefix: leviosa_public_api
      env_file_path: ./backend/public-api/.env.example
      needs_postgres: true
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}


  backup-postgresql:
    name: Backup postgresql
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/backups/backup.dockerfile
      build_context: docker/backups/
      tag_prefix: backup_postgresql
      env_file_path: ./docker/backups/.env.example
      skip_testing: true
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}


  migrator:
    name: Migrator
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/migrator/migrator.dockerfile
      tag_prefix: leviosa_migrator
      env_file_path: ./backend/migration/.env.example
      skip_testing: true
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}


  frontend:
    name: Frontend
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/frontend/frontend.dockerfile
      tag_prefix: leviosa_frontend
      env_file_path: ./apps/frontend/.env.example
    secrets:
      sentry_auth_token: ${{ secrets.SENTRY_CLINIC_PORTAL_AUTH_TOKEN }}
      sentry_clinic_portal_dsn: ${{ secrets.SENTRY_CLINIC_PORTAL_DSN }}
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}


  check-in-kiosk:
    name: Check in kiosk
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/check-in-kiosk/check-in-kiosk.dockerfile
      tag_prefix: leviosa_check_in_kiosk
      env_file_path: ./apps/check-in-kiosk/.env.example
    secrets: 
      sentry_auth_token: ${{ secrets.SENTRY_KIOSK_AUTH_TOKEN }}
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}

  online-booking:
    name: Online booking
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/online-booking/online-booking.dockerfile
      tag_prefix: leviosa_online_booking
      env_file_path: ./apps/online-booking/.env.example
    secrets:
      sentry_auth_token: ${{ secrets.SENTRY_ONLINE_BOOKING_AUTH_TOKEN }}
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}

  external-ehr-client:
    name: External EHR Client
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/external-ehr-client/external-ehr-client.dockerfile
      tag_prefix: leviosa_external_ehr_client
      env_file_path: ./backend/external-ehr-client/.env.example
      skip_testing: true
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}

  cron-jobs:
    name: Cron jobs
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/cron-jobs/cron-jobs.dockerfile
      tag_prefix: leviosa_cron_jobs
      env_file_path: ./backend/cron-jobs/.env.example
      skip_testing: true
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}


  pdf-generator:
    name: Pdf Generator
    uses: ./.github/workflows/build-container.yml
    with:
      dockerfile_path: ./docker/pdf-generator/pdf-generator.dockerfile
      tag_prefix: leviosa_pdf_generator
      env_file_path: ./apps/pdf-generator/.env.example
    secrets:
      gcp_registry_token: ${{ secrets.GCP_REGISTRY_TOKEN }}

