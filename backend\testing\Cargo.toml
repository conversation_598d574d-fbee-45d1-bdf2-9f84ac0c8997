[package]
name = "leviosa-testing"
version = "0.1.0"
edition.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
migration = { path = "../migration" }
leviosa-domain = { path = "../domain" }
leviosa-domain-contracts = { path = "../domain-contracts" }
leviosa-infrastructure = { path = "../infrastructure" }
sea-orm.workspace = true
uuid.workspace = true
rand.workspace = true
chrono.workspace = true
dotenv.workspace = true
serde.workspace = true
envy.workspace = true
tokio.workspace = true
mockall.workspace = true
serde_json.workspace = true