import { MenuButton } from "@ariakit/react"
import {
  addMonths,
  addWeeks,
  endOfMonth,
  endOfWeek,
  startOfMonth,
  startOfWeek,
} from "date-fns"
import { useCallback, useState } from "react"
import { useTranslation } from "react-i18next"

import { MenuGroup<PERSON>abel, MenuProvider } from "components/Ariakit"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useMenuStore } from "components/Ariakit/hooks"
import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"

import {
  namedOperations,
  useEditEventInstanceInSlotMutation,
} from "generated/graphql"

import OptionIntervalMenu from "../OptionIntervalSubMenu/OptionIntervalMenu"
import { NextAvailableSlot } from "./NextAvailableSlot/NextAvailableSlot"
import styles from "./RescheduleEventMenu.module.css"

type RescheduleEventMenuProps = {
  eventDate: string
  serviceTypeId: string
  providerId: string
  subjectId: string
  formattedTime: string
  eventInstanceId: string
}

export const RescheduleEventMenu = ({
  eventDate,
  serviceTypeId,
  providerId,
  subjectId,
  formattedTime,
  eventInstanceId,
}: RescheduleEventMenuProps) => {
  const { t } = useTranslation()
  const menuStore = useMenuStore()
  const isOpen = menuStore.useState().open

  const [selectedFromTime, setSelectedFromTime] = useState("")

  const navigateCalendar = useNavigateCalendar()

  const optionIntervals = [
    {
      label: t("This week"),
      // Available slots from the start of the current week to the end of the current week
      fromDate: startOfWeek(new Date(eventDate)).toISOString(),
      toDate: endOfWeek(new Date(eventDate)).toISOString(),
    },
    {
      label: t("Next week"),
      // Available slots from the start of the next week to the end of the next week
      fromDate: startOfWeek(addWeeks(new Date(eventDate), 1)).toISOString(),
      toDate: endOfWeek(addWeeks(new Date(eventDate), 1)).toISOString(),
    },
    {
      label: t("Next month"),
      // Available slots from the start of the next month to the end of the next month
      fromDate: startOfMonth(addMonths(new Date(eventDate), 1)).toISOString(),
      toDate: endOfMonth(addMonths(new Date(eventDate), 1)).toISOString(),
    },
  ]

  const [editEventInstance, mutationResult] =
    useEditEventInstanceInSlotMutation({
      refetchQueries: [namedOperations.Query.EventInstances],
    })

  const handleRescheduleEvent = async (fromTime: string, toTime: string) => {
    setSelectedFromTime(fromTime)
    const { data } = await editEventInstance({
      variables: {
        editEventInstanceId: eventInstanceId,
        input: {
          fromDate: new Date(fromTime),
          toDate: new Date(toTime),
          serviceTypeId: {
            set: serviceTypeId,
          },
        },
      },
    })

    if (data?.editEventInstance.id) {
      setTimeout(() => {
        navigateCalendar(RouteStrings.calendarViewEventInstance, {
          eventId: data.editEventInstance.id,
          search: { provider: providerId, date: fromTime.split("T")[0] },
        })
      }, 600)
    }
  }

  const getMutationResult = useCallback(
    (fromTime: string) => {
      if (selectedFromTime === fromTime)
        return {
          loading: mutationResult.loading,
          called: mutationResult.called,
          error: mutationResult.error,
        }

      return {
        loading: false,
        called: false,
        error: undefined,
      }
    },
    [selectedFromTime, mutationResult]
  )

  return (
    <MenuProvider store={menuStore}>
      <Tooltip tooltipContent={`Reschedule event`}>
        <MenuButton
          className={styles.rescheduleButton}
          aria-label={t("Reschedule event")}
        >
          {formattedTime}
        </MenuButton>
      </Tooltip>

      <OptionIntervalMenu
        serviceTypeId={serviceTypeId}
        providerId={providerId}
        subjectId={subjectId}
        isOpen={isOpen}
        getMutationResult={getMutationResult}
        handleSelectSlot={handleRescheduleEvent}
        optionIntervals={optionIntervals}
      >
        <MenuGroupLabel>{t("Reschedule")}</MenuGroupLabel>

        <NextAvailableSlot
          serviceTypeId={serviceTypeId}
          providerId={providerId}
          isOpen={isOpen}
          instanceId={eventInstanceId}
        />
      </OptionIntervalMenu>
    </MenuProvider>
  )
}
