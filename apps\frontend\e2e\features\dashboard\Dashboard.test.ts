import { test, expect } from "@playwright/test"

import { openDashboardPageViaPowerMenu } from "../utils/dashboardTestUtils"
import { CheckUrl } from "../utils/testUtils"

test.describe("Dashboard tests", () => {
  test.describe("Dashboard page data", () => {
    test.beforeEach(async ({ page }) => {
      await page.goto("/")
      await openDashboardPageViaPowerMenu(page)
    })

    test("Dashboard page loads data", async ({ page }) => {
      // Arrange
      // Act
      // Assert
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(16)
    })

    test("Dashboard row has correct data", async ({ page }) => {
      // Arrange
      // Check if the first, 8th and last row has correct data
      const firstRow = page.locator('[class*="DashboardRow_row"]').nth(0)
      const eigthRow = page.locator('[class*="DashboardRow_row"]').nth(7)
      const lastRow = page.locator('[class*="DashboardRow_row"]').nth(15)

      // Act
      // Assert

      await expect(
        firstRow.locator('[data-testid="dashboard-age"]')
      ).toHaveText("3 M, 3 D")
      await expect(
        firstRow.locator('[data-testid="dashboard-name"]')
      ).toHaveText("Baby One")
      await expect(
        firstRow.locator('[data-testid="dashboard-reason"]')
      ).toHaveText("Broken bones")

      await expect(
        eigthRow.locator('[data-testid="dashboard-age"]')
      ).toHaveText("23 Y")
      await expect(
        eigthRow.locator('[data-testid="dashboard-name"]')
      ).toHaveText("Harriette")
      await expect(
        eigthRow.locator('[data-testid="dashboard-reason"]')
      ).toHaveText("Breathing difficulty")

      await expect(lastRow.locator('[data-testid="dashboard-age"]')).toHaveText(
        "23 Y"
      )
      await expect(
        lastRow.locator('[data-testid="dashboard-name"]')
      ).toHaveText("Some Other Name")
      await expect(
        lastRow.locator('[data-testid="dashboard-reason"]')
      ).toHaveText("Chest pain")
      // await expect(
      //   lastRow.locator('[data-testid="dashboard-note"]')
      // ).toHaveText("need a mobile charger")
      // await expect(
      //   lastRow.locator('[data-testid="dashboard-providers"]')
      // ).toHaveText("AD")
    })

    test("Dashboard name is bold", async ({ page }) => {
      // Arrange
      // Act
      const htmlvalue = await page
        .locator('[class*="DashboardRow_row"]')
        .nth(5)
        .locator('[data-testid="dashboard-name"]')
        .innerHTML()
      // Assert
      expect(htmlvalue).toContain("Text_bold")
    })
  })

  test.describe("Dashboard navigation", () => {
    test.beforeEach(async ({ page }) => {
      await page.goto("/")
      await openDashboardPageViaPowerMenu(page)
    })

    test("Dashboard row click opens Subject summary", async ({ page }) => {
      // Arrange
      // Act
      await page.locator('[class*="DashboardRow_row"]').nth(2).click()
      const subjectSummary = page.locator('[class*="PersonSummary_wrap"]')
      // Assert
      // Check if subject summary popover is visible
      await expect(subjectSummary).toBeVisible()
      await expect(
        subjectSummary.locator('[class*="PersonSummary_headingWrapper"]')
      ).toContainText("Cho")
    })

    test("Clicking entries opens subject journal", async ({ page }) => {
      // Arrange
      // Act
      await page
        .locator('[class*="DashboardRow_row"]')
        .nth(5)
        .locator('[class*="DashboardRow_journalEntries"]')
        .click()
      // Assert
      // Check if subject journal opens
      await CheckUrl(page, /subject\/[a-f0-9-]+\/journal/)
    })
  })

  test.describe("Dashboard filtering", () => {
    test.beforeEach(async ({ page }) => {
      await page.goto("/")
      await openDashboardPageViaPowerMenu(page)
    })

    test("Dashboard filter by encounter status checked out", async ({
      page,
    }) => {
      // Arrange
      // Act
      await page.locator('[data-testid="CHECKED_OUT"]').click()

      // Assert
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(3)
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(0)
      ).toHaveText("Alex")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(1)
      ).toHaveText("Mia")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(2)
      ).toHaveText("Sam")
    })

    test("Dashboard filter by encounter status In progress", async ({
      page,
    }) => {
      // Arrange
      // Act
      await page.locator('[data-testid="IN_PROGRESS"]').click()

      // Assert
      // only one row should be visible
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(16)
    })

    test("Dashboard filter by free text returns many rows", async ({
      page,
    }) => {
      // Arrange
      // Act
      await page.getByPlaceholder("Filter dashboard").click()
      await page.getByPlaceholder("Filter dashboard").fill("un")

      // Assert
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(2)
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(0)
      ).toHaveText("Elder Two")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(1)
      ).toHaveText("Luna")
    })

    test("Dashboard filter by free text returns one row", async ({ page }) => {
      // Arrange
      // Act
      await page.getByPlaceholder("Filter dashboard").click()
      await page.getByPlaceholder("Filter dashboard").fill("nev")

      // Assert
      // only one row should be visible
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(1)
    })

    test("Dashboard filter by free text returns no row", async ({ page }) => {
      // Arrange
      // Act
      await page.getByPlaceholder("Filter dashboard").click()
      await page.getByPlaceholder("Filter dashboard").fill("nothing")

      // Assert
      // no row should be visible
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(0)
    })

    test.skip("Dashboard filter by provider name in free text", async ({
      page,
    }) => {
      // Arrange
      // Act
      await page.getByPlaceholder("Filter dashboard").click()
      await page.getByPlaceholder("Filter dashboard").fill("Ginny")

      // Assert
      // two rows should be visible
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(2)
      // provider name should be Ginny Weasley, possibly followed another provider name

      const dashboardRows = page.locator('[data-testid="dashboard-providers"]')
      await expect(dashboardRows).toHaveCount(2)

      await expect(dashboardRows.nth(0)).toHaveText("GW")
      await expect(dashboardRows.nth(1)).toContainText("GW")
    })
  })

  test.describe("Dashboard sorting", () => {
    test.beforeEach(async ({ page }) => {
      await page.goto("/")
      await openDashboardPageViaPowerMenu(page)
    })

    test("Dashboard sort by subject age", async ({ page }) => {
      // Arrange
      // Act
      await page.locator('[data-testid="sort-by-age"]').click()
      // Assert
      // all rows should be visible
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(16)
      await expect(
        page.locator('[data-testid="dashboard-name"]').first()
      ).toHaveText("Baby Two")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(1)
      ).toHaveText("Baby One")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(2)
      ).toHaveText("Draco")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(3)
      ).toHaveText("Harriette")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(4)
      ).toHaveText("Some Other Name")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(5)
      ).toHaveText("Neville")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(6)
      ).toHaveText("Ginger")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(7)
      ).toHaveText("Harry")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(8)
      ).toHaveText("Some Name")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(9)
      ).toHaveText("Ronald")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(10)
      ).toHaveText("Luna")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(11)
      ).toHaveText("Same Age Two")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(12)
      ).toHaveText("Same Age One")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(13)
      ).toHaveText("Cho")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(14)
      ).toHaveText("Elder One")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(15)
      ).toHaveText("Elder Two")
    })

    test("Dashboard sort by subject name", async ({ page }) => {
      // Arrange
      // Act
      await page.locator('[data-testid="sort-by-name"]').click()
      // Assert
      // all rows should be visible
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(16)
      await expect(
        page.locator('[data-testid="dashboard-name"]').first()
      ).toHaveText("Baby One")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(1)
      ).toHaveText("Baby Two")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(2)
      ).toHaveText("Cho")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(3)
      ).toHaveText("Draco")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(4)
      ).toHaveText("Elder One")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(5)
      ).toHaveText("Elder Two")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(6)
      ).toHaveText("Ginger")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(7)
      ).toHaveText("Harriette")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(8)
      ).toHaveText("Harry")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(9)
      ).toHaveText("Luna")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(10)
      ).toHaveText("Neville")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(11)
      ).toHaveText("Ronald")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(12)
      ).toHaveText("Same Age One")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(13)
      ).toHaveText("Same Age Two")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(14)
      ).toHaveText("Some Name")
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(15)
      ).toHaveText("Some Other Name")
    })

    test.skip("Dashboard sort desc by from date", async ({ page }) => {
      // Arrange
      // Act
      await page.locator('[data-testid="sort-by-fromDate"]').click()
      await page.locator('[data-testid="sort-by-fromDate"]').click()
      // Assert
      // all rows should be visible
      await expect(page.locator('[class*="DashboardRow_row"]')).toHaveCount(16)
      // first subject name should be "Rudy Gainsburg"
      await expect(
        page.locator('[data-testid="dashboard-name"]').first()
      ).toHaveText("Rudy Gainsburg")
      // second subject name should be "Alice Anderson"
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(1)
      ).toHaveText("Alice Anderson")
      // third subject name should be "Fiona Franklin"
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(2)
      ).toHaveText("Fiona Franklin")
      // second last subject name should be "Yong Freddie"
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(14)
      ).toHaveText("Yong Freddie")
      // last subject name should be "Jonatan Livingstone"
      await expect(
        page.locator('[data-testid="dashboard-name"]').nth(15)
      ).toHaveText("Jonatan Livingstone")
    })
  })
})
