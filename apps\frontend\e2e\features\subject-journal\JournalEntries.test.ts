/* eslint-disable playwright/no-useless-await */
/* eslint-disable playwright/no-wait-for-timeout */
import { test, expect } from "@playwright/test"

import { login } from "../utils/authenticationUtils"
import {
  createCompletedJournalEntry,
  createEncounter,
  openSubjectJournalForSubject,
} from "../utils/subjectJournalTestUtils"

test.describe.configure({ mode: "serial" })

test.describe("Create journal entry", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("/")
    await page.waitForTimeout(2000)
    login(page)
    await page.waitForTimeout(1000)
  })

  test("Should create one new journal entry", async ({ page }) => {
    // Arrange
    // Open subject journal
    // this test needs extra time to load the journal for some reason
    await page.waitForTimeout(2000)
    await openSubjectJournalForSubject(page, "<PERSON>")
    await createEncounter(page, "Create one entry test", false)

    // Act
    // Click on template button
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForTimeout(800)

    // Assert
    // There is exactly one editor on the page
    const jeEditor = page.locator(".editor")
    await expect(jeEditor).toHaveCount(1)
    await expect(jeEditor).toBeVisible()

    // Only one editor is editable
    const editableCount = await page
      .locator('[class*="Editor_container"]')
      .count()
    expect(editableCount).toBe(1)

    // There are no other empty journal entries on the page
    const otherJECount = await page
      .locator(".JournalEntryBlockNote_content")
      .count()
    expect(otherJECount).toBe(0)
  })

  test("Can add more than one journal entries", async ({ page }) => {
    // Arrange
    // Open subject journal
    await openSubjectJournalForSubject(page, "Emma Brown")
    await createEncounter(page, "Two entries test")

    // Act
    // Click on a template button
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForTimeout(400)
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForTimeout(2000)

    // Assert
    // There are two editors on the page
    const jeEditor = page.locator(".editor")
    await expect(jeEditor).toHaveCount(2)

    // Only one editor is editable
    const editableEditors = page.locator('[class*="Editor_container"]')
    await expect(editableEditors).toHaveCount(1)

    // There is one other empty journal entry on the page
    const otherJE = page.locator('text="Click to add description"')
    await expect(otherJE).toHaveCount(1)
    await expect(otherJE).toHaveAttribute("data-is-empty", "true")
    await expect(otherJE).toHaveAttribute("data-is-not-completed-entry", "true")
  })

  test("Owner of new journal entry is correct", async ({ page }) => {
    // Arrange
    // Open subject journal
    await openSubjectJournalForSubject(page, "Ava Davis")
    await createEncounter(page, "Owner of entry test")

    // Act
    // Click on "Blank entry" button
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForTimeout(400)

    const jeOwner = page.getByRole("link", {
      name: "Albus Dumbledore • Attending",
    })
    const count = await jeOwner.count()

    // Assert
    await expect(count).toBe(1)
    await expect(jeOwner).toHaveText("Albus Dumbledore•Attending physician")
  })
})

test.describe("Complete journal entry tests", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("/")
    await page.waitForTimeout(2000)
    login(page)
  })

  test("Complete button state is filled for journal entry with text", async ({
    page,
  }) => {
    // Arrange
    // Open subject journal
    await openSubjectJournalForSubject(page, "Jack Wilson")
    await createEncounter(page, "Complete button state test")

    // Act
    // Click on template button
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForTimeout(800)
    await page
      .locator("#drag-file-element")
      .getByRole("textbox")
      .fill("This is a test journal entry")

    // Put focus on the first button that has the text "Complete Entry"
    const completeButton = page
      .locator('button:has-text("Complete Entry")')
      .first()
    await completeButton.focus()

    // Assert
    await expect(completeButton).toHaveAttribute("data-variant", "filled")
  })

  test("Complete button state is outlined for journal entry with no text", async ({
    page,
  }) => {
    // Arrange
    // Open subject journal
    await openSubjectJournalForSubject(page, "Lily Moore")
    await createEncounter(page, "Complete button state empty test")

    // Act
    // Click on template button
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForTimeout(800)

    // Put focus on the "Complete Entry" button
    const completeButton = page
      .locator('button:has-text("Complete Entry")')
      .first()
    await completeButton.focus()

    // Assert
    await expect(completeButton).toHaveAttribute("data-variant", "outline")
  })

  test("Behaves correctly when completing journal entry", async ({ page }) => {
    // Arrange
    // Open subject journal
    await openSubjectJournalForSubject(page, "Ryan White")
    await createEncounter(page, "Completing entry test")

    // Act
    // Click on template button
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForLoadState()
    await page
      .locator("#drag-file-element")
      .getByRole("textbox")
      .fill("This is a test for completing journal entry")

    // Put focus on the first button that has the text "Complete Entry"
    const completeButton = page
      .locator('button:has-text("Complete Entry")')
      .first()
    await completeButton.focus()
    await page.waitForTimeout(2000)

    // Click the "Complete Entry" button
    await completeButton.click()

    // Assert
    // The toast message is visible
    const toastMessage = page.locator('text="Journal entry has been completed"')
    await expect(toastMessage.first()).toBeVisible()
    // The "Complete Entry" button is hidden
    await expect(completeButton).toBeHidden()
  })
})

test.describe("Delete journal entry tests", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("/")
    await page.waitForTimeout(2000)
    login(page)
  })

  test("Can delete journal entry", async ({ page }) => {
    // Arrange
    // Open subject journal
    await openSubjectJournalForSubject(page, "Ella Clark")
    await createEncounter(page, "Delete entry test")

    // Act
    // Click on template button
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForLoadState()
    await page
      .locator("#drag-file-element")
      .getByRole("textbox")
      .fill("This is a test for deleting journal entry")

    // click the elipsis button and select delete entry
    await page.getByLabel("Journal Entry Options").first().click()
    await page.getByRole("menuitem", { name: "Delete Entry" }).click()
    await page.locator('button:has-text("Delete")').first().click()

    // Assert
    // The toast message is visible
    const toastMessage = page.locator('text="Journal entry has been deleted"')
    await expect(toastMessage.first()).toBeVisible()
    // The journal entry is not visible
    const jeEditor = page.locator(".editor")
    await expect(jeEditor).toBeHidden()
  })

  test("Cannot delete journal entry that is completed", async ({ page }) => {
    // Arrange
    // Open subject journal
    await openSubjectJournalForSubject(page, "Luke Hall")
    await createEncounter(page, "Cannot delete entry test")

    // Act
    // Click on "Blank entry" button
    await createCompletedJournalEntry(page)

    // Assert
    const completeButton = page.getByRole("button", { name: "Complete Entry" })
    // Complete entry button is not visible
    await expect(completeButton).toBeHidden()
    // Journal entry options are not visible
    await expect(page.locator("Journal Entry Options")).toBeHidden()
  })
})
