use chrono::{Duration, Utc};
use leviosa_domain::{
    errors::Error,
    online_payment_service::{
        AuthorizationType, Billing, Card, CheckoutStatus, CreateCustomerResponse, CurrencyCode,
        Customer, InteractionType, OnlinePaymentService, PaymentCheckout, PaymentCheckoutResponse,
        PaymentMode, PaymentRequest, PaymentResponse, PaymentStatus, ShopperInteraction, ThreeDSecureConfig,
    },
};
use uuid::Uuid;

#[derive(Clone)]
pub struct MockPaymentService {}

impl MockPaymentService {
    pub fn new() -> Self {
        MockPaymentService {}
    }
}

#[async_trait::async_trait]
impl OnlinePaymentService for MockPaymentService {
    async fn api_auth_header(&self) -> Result<String, Error> {
        Ok("mocked_auth".to_string())
    }

    async fn get_checkout(&self, checkout_id: Uuid) -> Result<CheckoutStatus, Error> {
        let date = Utc::now();
        let created_by = Uuid::new_v4();

        Ok(CheckoutStatus {
            id: checkout_id.to_string(),
            events: vec![],
            amount: 1.0,
            configurations: {
                leviosa_domain::online_payment_service::CheckoutConfiguration {
                    config_type: Card {
                        mode: PaymentMode::Payment3ds,
                        payment_contract_id: created_by,

                        threed_secure: Some(ThreeDSecureConfig {
                            threeds_contract_id: Uuid::new_v4(),
                            enabled: true,
                        }),
                        account_validation: Some(true),
                        capture_now: Some(false),
                        cvv_required: Some(true),
                        authorization_type: Some(AuthorizationType::FinalAuth),
                        shopper_interaction: Some(ShopperInteraction::Ecommerce),
                    },
                }
            },
            currency_code: CurrencyCode::Isk,
            expiry_time: date + Duration::minutes(15),
            merchant_reference: "mocked reference".to_string(),
            entity_id: created_by,
            return_url: None,
            i18n: serde_json::json!({}),
            status: "EXPIRED".to_string(),
            display_line_items: false,
            fraud_protection_data: serde_json::json!({}),
            interaction_type: InteractionType::Iframe,
            url: "mocked_url".to_string(),
            created_by,
            created_at: date,
            updated_at: date,
        })
    }

    async fn create_checkout(
        &self,
        _payment_checkout: PaymentCheckout,
    ) -> Result<PaymentCheckoutResponse, Error> {
        let id = Uuid::new_v4();
        Ok(PaymentCheckoutResponse {
            id,
            url: format!("http://mocked_url/loader.js?checkoutId={id}"),
        })
    }

    async fn create_customer(&self, _customer: Customer) -> Result<CreateCustomerResponse, Error> {
        let id = Uuid::new_v4();
        let date = Utc::now();
        let created_by = Uuid::new_v4();

        Ok(CreateCustomerResponse {
            id,
            billing: Billing {
                address_1: "st. Unknown No.1".to_string(),
                city: "Keflavik".to_string(),
                country_code: "IS".to_string(),
                first_name: "Test".to_string(),
                last_name: "Testson".to_string(),
                postal_code: "12345".to_string(),
            },
            entity_id: created_by,
            created_at: date,
            updated_at: date,
            email_address: "<EMAIL>".to_string(),
        })
    }

    async fn create_payment(&self, _request: PaymentRequest) -> Result<PaymentResponse, Error> {
        let id = Uuid::new_v4();
        Ok(PaymentResponse {
            id,
            status: "AUTHORIZED".to_string(),
            amount: 100.0,
        })
    }

    async fn get_payment_status(&self, payment_id: &str) -> Result<PaymentStatus, Error> {
        let id = Uuid::parse_str(payment_id).unwrap_or_else(|_| Uuid::new_v4());
        Ok(PaymentStatus {
            id,
            status: "COMPLETED".to_string(),
            amount: 100.0,
            created_at: Utc::now(),
        })
    }

    async fn process_webhook(&self, _payload: &str, _signature: &str) -> Result<PaymentStatus, Error> {
        let id = Uuid::new_v4();
        Ok(PaymentStatus {
            id,
            status: "WEBHOOK_PROCESSED".to_string(),
            amount: 100.0,
            created_at: Utc::now(),
        })
    }
}
