import { useNavigate, useParams } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { RouteStrings } from "routes/RouteStrings"

import DepartmentCreate from "../../DepartmentCreate"

export const OrganisationManageCreateDepartment = () => {
  const { organisationId } = useParams<{ organisationId: string }>()
  const navigate = useNavigate()
  const {
    globalData: { config },
  } = useGlobalState()

  if (!organisationId) return null

  return (
    <DepartmentCreate
      organisationId={organisationId}
      onClose={() => navigate(RouteStrings.organisationManage)}
      leviosaKindId={config.leviosaKindId}
    />
  )
}
