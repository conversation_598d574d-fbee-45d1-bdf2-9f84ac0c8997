import { useTranslation } from "react-i18next"

import { notification } from "ui"
import { Modal } from "ui/components/Modal/Modal"

import { useAddBedToRoomMutation } from "generated/graphql"

import { Building } from "../BuildingOverview/BuildingOverview"
import { BedForm } from "../forms/BedForm/BedForm"
import styles from "./AddBedToRoom.module.css"

type AddBedToRoomProps = {
  room?: Building["rooms"]["rooms"][0]
  roomId: string | null
  onClose: () => void
  corridorId?: string
}

export const AddBedToRoom = ({ onClose, roomId }: AddBedToRoomProps) => {
  const { t } = useTranslation()

  const [addBedToRoom, { loading, error: createBuildingError }] =
    useAddBedToRoomMutation({
      onCompleted: (data) => {
        if (data) {
          notification.create({
            message: t("Bed has been added to location"),
            status: "success",
            maxWidth: "500px",
          })
        }

        onClose()
      },
    })

  if (roomId === null) return null

  return (
    <Modal
      title={t("Beds")}
      onClose={onClose}
      isOpen={true}
      contentClassName={styles.modalContent}
    >
      <div className={styles.addBedToRoomWrap}>
        <BedForm
          onSubmit={(data) => {
            const { label, bedType } = data

            addBedToRoom({
              variables: {
                input: {
                  label: label,
                  bedType: bedType,
                  roomId,
                },
              },
            })
          }}
          onCancel={onClose}
          error={createBuildingError}
          loading={loading}
        />
      </div>
    </Modal>
  )
}
