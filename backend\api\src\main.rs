#![warn(clippy::all)]
#![warn(clippy::pedantic)]
#![deny(unused_must_use)]
#![warn(clippy::todo)]
#![allow(clippy::module_name_repetitions)]
#![allow(clippy::must_use_candidate)]
#![allow(clippy::missing_errors_doc)]
#![allow(clippy::missing_panics_doc)]
#![allow(clippy::new_without_default)]
#![allow(unstable_name_collisions)]
#![allow(clippy::items_after_statements)]

use anyhow::Result;
use async_graphql::EmptySubscription;
use axum::serve;
use cookie::Key;
use leviosa_api::{
    app::{App, ContextBase},
    infrastructure::Env,
    resolvers::{Mutation, Query},
    routes::get_routes,
};
use leviosa_domain::{
    accounts::AuthRepo,
    audit_logger::AuditLogger,
    calendar_notification_service::CalendarNotificationService,
    customer_service::mock::CustomerServiceMock,
    doctor_letter_and_referral_api::DoctorLetterAndReferralApi,
    external_organisation_integration::ExternalOrganisationIntegration,
    national_registry_integration::DomainNationalRegistry,
    notification_service_integration::DomainNotificationService,
    oracle_api::OracleApi,
    prescription_api::PrescriptionApi,
    text_message_integration::TextMessageIntegration,
};
use leviosa_domain_contracts::{
    auth::{ElectronicId},
    services::{
        CustomerService, EmailSender, NhiService, OnlinePaymentService, PdfGenerator,
    },
};
use leviosa_infrastructure::{
    EmptyNhiService, EnvParser, NhiServiceApi, PdfGeneratorApi,
    audit_logger::DbAuditLogger,
    auth::{AesGcmSivEncryptor, AsymmetricJwtEncoder, BcryptPasswordHasher, IcelandicElectronicId},
    calendar_notification_integration::{
        CalendarNotificationServiceIntegration, EmptyCalendarNotificationServiceIntegration,
        MockCalendarNotificationServiceIntegration,
    },
    db::PgConfig,
    email::{SmtpEmailSender},
    files::{EmptyFileStorage, FileRepo, FileStorage, InMemoryFileStorage, S3FileStorage},
    hekla::{
        EmptyDoctorLetterAndReferralIntegration, HealthcareInstitutionIntegration,
        HeklaDoctorLetterAndReferralIntegration, HeklaDoctorPasswordCacheRepo,
        HeklaMessageProcessingRepository, HeklaMessageRepository, HeklaPrescriptionIntegration,
        HeklaRequestClient, HeklaRequestRepository, HeklaRestApi,
        MockDoctorLetterAndReferralIntegration, MockPrescriptionIntegration,
        electronic_sheets_service::ElectronicSheetsService,
        healthcare_institution_service::{HealthcareInstitutionRepo, HealthcareInstitutionService},
    },
    jira::Jira,
    messaging::{EmptyTextMessageIntegration, MockTextMessageIntegration},
    national_registry_integration::{
        EmptyNationalRegistryIntegration, NationalRegistry, NationalRegistryIntegration,
        get_mock_national_registry,
    },
    notifications::{MessageRepo, NotificationService},
    observability,
    online_payment::{EmptyPaymentService, MockPaymentService, VerifonePaymentService},
    oracle_api::{HsuOracleApi, MockOracleApi, NoneOracleApi},
    pdf_generator::EmptyPdfGenerator,
};
use leviosa_infrastructure::{
    messaging::TextMessageIntegrationApi, service_communicator::HttpServiceCommunicator,
};
use leviosa_infrastructure::{
    nhi::get_mock_nhi_service,
    notification_integration::{
        EmptyNotificationServiceIntegration, MockNotificationServiceIntegration,
        NotificationServiceIntegration,
    },
};
use sentry::IntoDsn;
use std::{fs, net::SocketAddr, sync::Arc};
use tokio::net::TcpListener;

// Simple mock implementation for contracts ElectronicId
struct MockElectronicId;

impl MockElectronicId {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl ElectronicId for MockElectronicId {
    async fn login(&self, _phone_number: &str) -> anyhow::Result<leviosa_domain_contracts::auth::ElectronicIdUserInfo> {
        Ok(leviosa_domain_contracts::auth::ElectronicIdUserInfo {
            name: "Mock User".to_string(),
            national_register_id: "1234567890".to_string(),
        })
    }
}

#[tokio::main]
#[allow(clippy::too_many_lines)] // App entrypoint should be refactored later.
async fn main() -> Result<()> {
    if let Ok(val) = std::env::var("DRY_RUN") {
        if val == "true" {
            fs::write(
                "schema.graphql",
                App::<Query, Mutation, EmptySubscription>::sdl(),
            )?;
            return Ok(());
        }
    }

    tracing::info!("Starting server...");

    tracing::info!("Initializing environment...");
    let env = EnvParser::parse::<Env>()?;

    let app_environment = if env.app_environment().as_deref() == Some("development") {
        "development".to_string()
    } else {
        "production".to_string()
    };

    let guard = if env.sentry_dsn() == "mock" {
        None
    } else {
        let dsn = env.sentry_dsn().clone().into_dsn()?;
        let Some(dsn) = dsn else {
            panic!("Missing value for field sentry_dsn")
        };

        tracing::info!("Configuring sentry error reporting...");
        let guard = observability::init(
            env.log_level(),
            &env.logs_structured(),
            observability::SentryConfig {
                dsn: &dsn,
                release: env.sentry_release().as_deref(),
                environment: env.sentry_environment(),
                sample_rate: env.sentry_sample_rate(),
                traces_sample_rate: env.sentry_traces_sample_rate(),
                profiles_sample_rate: env.sentry_profiles_sample_rate(),
                server_name: env.hostname().as_deref(),
            },
        );
        Some(guard)
    };

    tracing::info!("Initializing database connection...");
    let sea_database = PgConfig {
        pg_host: env.pg_host().clone(),
        pg_port: env.pg_port().clone(),
        pg_db: env.pg_db().clone(),
        pg_user: env.pg_user().clone(),
        pg_password: env.pg_password().clone(),
    }
    .connection(4, 20)
    .await?;

    tracing::info!("Initializing S3 file store...");
    let file_storage: Arc<dyn FileStorage> =
        if let Some(file_storage_bucket) = env.file_storage_bucket().clone() {
            let file_store_result = initialize_file_storage(file_storage_bucket, &env).await;

            match file_store_result {
                Ok(s3_file_store) => s3_file_store,
                Err(error) => {
                    tracing::error!("Failed to initialize file store: {:?}", error);
                    Arc::new(EmptyFileStorage)
                }
            }
        } else {
            tracing::warn!("No file storage bucket configured. Using EmptyFileStore.");
            Arc::new(EmptyFileStorage)
        };

    tracing::info!("Initializing email sender...");
    let email_sender: Arc<dyn EmailSender> = {
        match env.email_sender().to_lowercase().as_ref() {
            "mock" => {
                Arc::new(leviosa_domain_contracts::MockEmailSender::new())
            }
            "smtp" => Arc::new(SmtpEmailSender::new(
                env.email_sender_from_email()
                    .clone()
                    .unwrap_or(String::from("<EMAIL>")),
            )),
            _ => panic!("EMAIL_SENDER configuration is not valid. Should be 'mock' or 'smtp'"),
        }
    };

    tracing::info!("Initializing OracleAPI...");
    let oracle_api: Arc<dyn OracleApi> = {
        let oracle_api: Option<String> = env.oracle_api().clone().map(|s| s.to_lowercase());
        match oracle_api.as_deref() {
            Some("mock") => Arc::new(MockOracleApi::new()),
            Some("hsu") => {
                let endpoint = env
                    .oracle_api_endpoint()
                    .as_ref()
                    .expect("HSU Oracle API endpoint is not configured")
                    .clone();
                Arc::new(HsuOracleApi::new(endpoint))
            }
            _ => Arc::new(NoneOracleApi::new()),
        }
    };

    tracing::info!("Initializing PrescriptionAPI...");
    let prescription_api: Arc<dyn PrescriptionApi> = {
        let prescription_api: Option<String> =
            env.prescription_api().clone().map(|s| s.to_lowercase());
        match prescription_api.as_deref() {
            Some("mock") => Arc::new(MockPrescriptionIntegration::new()),
            Some("hekla") => {
                let endpoint = env
                    .hekla_url()
                    .clone()
                    .expect("Hekla Prescription API endpoint is not configured");
                let username = env
                    .hekla_username()
                    .clone()
                    .expect("Hekla Prescription API username is not configured");
                let password = env
                    .hekla_password()
                    .clone()
                    .expect("Hekla Prescription API password is not configured");
                let aes_gcm_siv_key = env
                    .aes_gcm_siv_key()
                    .clone()
                    .expect("AES_GCM_SIV key is not configured");

                Arc::new(HeklaPrescriptionIntegration::new(HeklaRequestClient::new(
                    Box::new(HeklaRestApi::new(endpoint, username, password, None)),
                    Box::new(HeklaRequestRepository::new(sea_database.clone())),
                    Box::new(HeklaDoctorPasswordCacheRepo::new(sea_database.clone())),
                    AesGcmSivEncryptor::new(&aes_gcm_siv_key)
                        .expect("Failed to create AesGcmSivEncryptor"),
                )))
            }
            _ => panic!("PrescriptionApi configuration is not valid. Should be 'mock' or 'hekla'"),
        }
    };

    tracing::info!("Initializing DoctorLetterAndReferralAPI...");
    let doctor_letter_and_referral_api: Arc<dyn DoctorLetterAndReferralApi> = {
        let doctor_letter_and_referral_api: Option<String> = env
            .doctor_letter_and_referral_api()
            .clone()
            .map(|s| s.to_lowercase());
        match doctor_letter_and_referral_api.as_deref() {
            Some("mock") => {
                let organisation_id = env
                    .doctor_letter_and_referral_mock_organisation_id()
                    .expect("Mock organisation id is not configured for mock doctor letter and referral integration");

                let mock_integration = MockDoctorLetterAndReferralIntegration::new(
                    sea_database.clone(),
                    organisation_id.into(),
                    &AuthRepo::new(&sea_database.clone()),
                    &BcryptPasswordHasher::new(env.salt_cost().unwrap_or(12)),
                    &AsymmetricJwtEncoder::new(&env.jwt_private_key()?, &env.jwt_public_key()?),
                    env.jwt_expiration_time(),
                )
                .await
                .unwrap();

                Arc::new(mock_integration)
            }
            Some("hekla") => {
                let endpoint = env
                    .hekla_url()
                    .clone()
                    .expect("Hekla DoctorLetterAndReferral API endpoint is not configured");
                let username = env
                    .hekla_username()
                    .clone()
                    .expect("Hekla DoctorLetterAndReferral API username is not configured");
                let password = env
                    .hekla_password()
                    .clone()
                    .expect("Hekla DoctorLetterAndReferral API password is not configured");

                let electronic_sheet_service = ElectronicSheetsService::new(
                    Box::new(HeklaRestApi::new(
                        endpoint.clone(),
                        username.clone(),
                        password.clone(),
                        None,
                    )),
                    Box::new(HeklaMessageRepository::new(sea_database.clone())),
                    Box::new(HeklaMessageProcessingRepository::new(sea_database.clone())),
                );

                let healthcare_institution_service = HealthcareInstitutionService::new(
                    Arc::new(HeklaRestApi::new(
                        endpoint.clone(),
                        username.clone(),
                        password.clone(),
                        None,
                    )),
                    HealthcareInstitutionRepo::new(sea_database.clone()),
                );

                Arc::new(HeklaDoctorLetterAndReferralIntegration::new(
                    electronic_sheet_service,
                    healthcare_institution_service,
                ))
            }
            None => Arc::new(EmptyDoctorLetterAndReferralIntegration::new()),
            _ => panic!(
                "DoctorLetterAndReferralAPI configuration is not valid. Should be 'mock' or 'hekla'"
            ),
        }
    };

    tracing::info!("Initializing External Organisation Integration...");
    let external_organisation_integration: Arc<dyn ExternalOrganisationIntegration> = {
        let external_organisation_integration: String = env
            .external_organisation_integration()
            .clone()
            .to_lowercase();
        match external_organisation_integration.as_str() {
            "mock" => {
                let repo = HealthcareInstitutionRepo::new(sea_database.clone());
                Arc::new(
                    HealthcareInstitutionIntegration::create_mock(repo)
                        .await
                        .unwrap(),
                )
            }
            "hekla" => {
                let endpoint = env
                    .hekla_url()
                    .clone()
                    .expect("Hekla ExternalOrganisationIntegration API endpoint is not configured");
                let username = env
                    .hekla_username()
                    .clone()
                    .expect("Hekla ExternalOrganisationIntegration API username is not configured");
                let password = env
                    .hekla_password()
                    .clone()
                    .expect("Hekla ExternalOrganisationIntegration API password is not configured");
                Arc::new(HealthcareInstitutionIntegration::new(
                    HealthcareInstitutionService::new(
                        Arc::new(HeklaRestApi::new(endpoint, username, password, None)),
                        HealthcareInstitutionRepo::new(sea_database.clone()),
                    ),
                ))
            }
            _ => panic!(
                "ExternalOrganisationIntegration configuration is not valid. Should be 'mock' or 'hekla'"
            ),
        }
    };

    tracing::info!("Initializing audit logger...");
    let audit_logger: Arc<dyn AuditLogger> = Arc::new(DbAuditLogger::new(false));

    let pdf_service_url = env.pdf_service_url().clone();
    let pdf_generator: Arc<dyn PdfGenerator> = match pdf_service_url {
        Some(pdf_service_url) => Arc::new(PdfGeneratorApi::new(HttpServiceCommunicator::new(
            pdf_service_url.clone(),
        ))),
        None => Arc::new(EmptyPdfGenerator),
    };
    let nhi_service_url = env.nhi_service_url().clone().map(|s| s.to_lowercase());
    let nhi_service: Arc<dyn NhiService> = match nhi_service_url.as_deref() {
        Some("mock") => Arc::new(get_mock_nhi_service()),
        Some(nhi_service_url) => Arc::new(NhiServiceApi::new(HttpServiceCommunicator::new(
            nhi_service_url.to_string(),
        ))),
        None => Arc::new(EmptyNhiService),
    };

    tracing::info!("Initializing Online Payment API...");
    let online_payment_api: Arc<dyn OnlinePaymentService> = {
        let online_payment: Option<String> = env.online_payment().clone().map(|s| s.to_lowercase());
        match online_payment.as_deref() {
            Some("mock") => Arc::new(leviosa_domain_contracts::MockOnlinePaymentService::new()),
            Some("verifone") => {
                let op_checkout_url = env
                    .online_payment_checkout_url()
                    .clone()
                    .expect("Online Payment Checkout API endpoint is not configured");
                let op_customer_url = env
                    .online_payment_customer_url()
                    .clone()
                    .expect("Online Payment Customer API endpoint is not configured");
                let op_username = env
                    .online_payment_username()
                    .clone()
                    .expect("Online Payment API username is not configured");
                let op_password = env
                    .online_payment_password()
                    .clone()
                    .expect("Online Payment API password is not configured");
                Arc::new(VerifonePaymentService::new(
                    HttpServiceCommunicator::new(op_checkout_url.to_string()),
                    HttpServiceCommunicator::new(op_customer_url.to_string()),
                    op_username,
                    op_password,
                ))
            }
            _ => Arc::new(leviosa_domain_contracts::MockOnlinePaymentService::new()),
        }
    };

    let text_message_integration: Arc<dyn TextMessageIntegration> = {
        let integration: Option<String> = env
            .notification_service_integration()
            .clone()
            .map(|s| s.to_lowercase());
        match integration.as_deref() {
            Some("mock") => Arc::new(MockTextMessageIntegration::new()),
            Some("notification") => {
                let account_sid = env
                    .twilio_account_sid()
                    .clone()
                    .expect("Twilio account SID is not configured");
                let auth_token = env
                    .twilio_auth_token()
                    .clone()
                    .expect("Twilio auth token is not configured");
                Arc::new(TextMessageIntegrationApi {
                    account_sid,
                    auth_token,
                })
            }
            _ => Arc::new(EmptyTextMessageIntegration),
        }
    };

    let notification_service_integration: Arc<dyn DomainNotificationService> = {
        let integration: Option<String> = env
            .notification_service_integration()
            .clone()
            .map(|s| s.to_lowercase());
        match integration.as_deref() {
            Some("mock") => Arc::new(MockNotificationServiceIntegration::new()),
            Some("notification") => {
                let message_repo = MessageRepo::new(sea_database.clone());
                let notification_service = NotificationService::new(
                    Box::new(message_repo),
                    text_message_integration.clone(),
                );
                Arc::new(NotificationServiceIntegration {
                    notification_service,
                })
            }
            _ => Arc::new(EmptyNotificationServiceIntegration),
        }
    };

    let calendar_notification_service_integration: Arc<dyn CalendarNotificationService> = {
        let integration: Option<String> = env
            .notification_service_integration()
            .clone()
            .map(|s| s.to_lowercase());
        match integration.as_deref() {
            Some("mock") => Arc::new(MockCalendarNotificationServiceIntegration::new()),
            Some("notification") => {
                let message_repo = MessageRepo::new(sea_database.clone());
                let notification_service =
                    NotificationService::new(Box::new(message_repo), text_message_integration);
                Arc::new(CalendarNotificationServiceIntegration {
                    notification_service,
                })
            }
            _ => Arc::new(EmptyCalendarNotificationServiceIntegration),
        }
    };

    let electronic_id: Arc<dyn ElectronicId> = if env.electronic_id_base_url() == "mock" {
        Arc::new(MockElectronicId::new())
    } else {
        Arc::new(IcelandicElectronicId::new(
            env.electronic_id_base_url().clone(),
            env.electronic_id_client_id()
                .clone()
                .expect("Missing value for field electronic_id_client_id"),
            env.electronic_id_client_secret()
                .clone()
                .expect("Missing value for field electronic_id_client_secret"),
        ))
    };

    let customer_service: Arc<dyn CustomerService> = if env.jira_host() == "mock" {
        Arc::new(CustomerServiceMock::new())
    } else {
        Arc::new(Jira::new(
            env.jira_host().clone(),
            env.jira_api_key()
                .clone()
                .expect("Missing value for field jira_api_key"),
            env.jira_service_desk_id()
                .clone()
                .expect("Missing value for field jira_service_desk_id"),
            env.jira_request_type_id()
                .clone()
                .expect("Missing value for field jira_request_type_id"),
            env.sentry_environment().clone(),
        ))
    };

    let national_registry: Arc<dyn DomainNationalRegistry> = {
        let integration: Option<String> = env
            .national_registry_api()
            .clone()
            .map(|s| s.to_lowercase());
        match integration.as_deref() {
            Some("mock") => Arc::new(get_mock_national_registry()),
            Some("national_registry") => {
                let api_key = env
                    .national_registry_api_key()
                    .clone()
                    .expect("National registry API key is not configured");
                let national_registry = Box::new(NationalRegistry::new(api_key));
                Arc::new(NationalRegistryIntegration { national_registry })
            }
            _ => Arc::new(EmptyNationalRegistryIntegration),
        }
    };

    let cookie_key = env.cookie_key().unwrap_or_else(|_| {
        tracing::warn!("Failed to get cookie key from environment, generating a random one");
        Key::generate()
    });

    let cookie_session_minutes = env.cookie_session_minutes().unwrap_or(600);

    let context_base = ContextBase {
        sea_database: sea_database.clone(),
        email_sender,
        audit_logger,
        admin_key: env.admin_key().clone(),
        refresh_token_expiration_time: env.refresh_token_expiration_time().unwrap_or(1800),
        token_lifetime_seconds: env.jwt_expiration_time(),
        jwt: Arc::new(AsymmetricJwtEncoder::new(
            &env.jwt_private_key()?,
            &env.jwt_public_key()?,
        )),
        password_hasher: Arc::new(BcryptPasswordHasher::new(env.salt_cost().unwrap_or(12))),
        electronic_id,
        customer_service,
        oracle_api,
        prescription_api,
        pdf_generator,
        nhi_service,
        notification_service_integration,
        calendar_notification_service_integration,
        file_repo: Arc::new(FileRepo::new(sea_database, file_storage)),
        national_registry,
        online_payment_service: online_payment_api,
        doctor_letter_and_referral_api,
        external_organisation_integration,
        cookie_key,
        cookie_session_minutes,
        app_environment,
    };

    if env.emit_schema_file() {
        fs::write(
            "schema.graphql",
            App::<Query, Mutation, EmptySubscription>::sdl(),
        )?;
    }

    tracing::info!("Creating HTTP handlers...");

    let routes = get_routes(context_base);

    tracing::info!("Server started on port {}", env.port());

    let addr = SocketAddr::from(([0, 0, 0, 0], env.port()));
    let listener = TcpListener::bind(addr).await?;
    serve(
        listener,
        routes.into_make_service_with_connect_info::<SocketAddr>(),
    )
    .await?;

    drop(guard);

    Ok(())
}

async fn initialize_file_storage(
    file_storage_bucket: String,
    env: &Env,
) -> Result<Arc<dyn FileStorage>, anyhow::Error> {
    if file_storage_bucket.to_lowercase() == "mock" {
        Ok(Arc::new(InMemoryFileStorage::new()))
    } else {
        let mut missing_config = Vec::<&str>::new();

        if env.s3_region().is_none() {
            missing_config.push("S3_REGION");
        }

        if env.s3_endpoint().is_none() {
            missing_config.push("S3_ENDPOINT");
        }

        if !missing_config.is_empty() {
            return Err(anyhow::anyhow!(
                "missing config: {}",
                missing_config.join(", ")
            ));
        }

        let s3 = S3FileStorage::new(
            env.s3_region().clone().unwrap().to_string(),
            &env.s3_endpoint().clone().unwrap(),
            file_storage_bucket,
        )
        .await;

        Ok(Arc::new(s3))
    }
}
