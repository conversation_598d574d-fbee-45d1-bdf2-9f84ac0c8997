use super::LeviosaContext;
use crate::lib::{context::ContextData, errors::Result};
use leviosa_domain_contracts::{
    OrganisationId, Provider, ProviderId,
    AuthData, AuthScope, AuthenticatedUser,
    AuthorizationError,
    PermissionKey, Role,
};

impl ContextData {
    pub fn auth_data(&self) -> AuthData {
        self.auth_data.read().unwrap().clone()
    }

    pub fn check_permission(&self, permission: Option<PermissionKey>) -> Result<AuthenticatedUser> {
        self.auth_data()
            .check_permission(permission)
            .map_err(Into::into)
    }

    pub fn log_in(&self, access_token: &str) -> Result<()> {
        let auth_data = AuthData::from_access_token(access_token, self.jwt())?;
        let mut auth = self.auth_data.write().unwrap();
        *auth = auth_data;
        Ok(())
    }

    pub fn is_admin_key(&self, admin_key: &str) -> Result<()> {
        if admin_key == self.admin_key() {
            Ok(())
        } else {
            Err(AuthorizationError::MustBeAdmin.into())
        }
    }

    pub async fn make_access_token_by_admin_key(
        &self,
        admin_key: String,
        user_id: ProviderId,
        organisation_id: OrganisationId,
    ) -> Result<String> {
        self.is_admin_key(&admin_key)?;

        let auth_data = AuthData::User(
            AuthenticatedUser::new(user_id, organisation_id, vec![], vec![]),
            String::new(),
        );
        let admin = Role::get(&self.sea_db(), &auth_data, String::from("Admin")).await?;

        let access_token = self.jwt().encode_access(
            &AuthenticatedUser::new(
                user_id,
                organisation_id,
                vec![],
                admin.permissions().clone(),
            ),
            self.token_lifetime_seconds(),
        )?;

        Ok(access_token)
    }

    pub async fn actor(&self) -> Result<Provider> {
        let actor_id = self.check_permission(None)?.user_id();
        let db = self.sea_db();
        let provider = Provider::get(&db, &AuthScope::Normal(&self.auth_data()), actor_id).await?;
        Ok(provider)
    }
}

pub trait LeviosaAuthContext<'a> {
    fn auth_data(&self) -> Result<AuthData>;

    fn user(&self) -> Result<AuthenticatedUser>;

    fn token(&self) -> Result<String>;

    fn check_permission(&self, permission: Option<PermissionKey>) -> Result<AuthenticatedUser>;
}

impl<'a, T: LeviosaContext<'a>> LeviosaAuthContext<'a> for T {
    fn auth_data(&self) -> Result<AuthData> {
        let ctx = self.get()?;
        Ok(ctx.auth_data())
    }

    fn user(&self) -> Result<AuthenticatedUser> {
        let ctx = self.get()?;
        Ok(ctx.auth_data().user()?)
    }

    fn token(&self) -> Result<String> {
        let ctx = self.get()?;
        Ok(ctx.auth_data().token()?.to_string())
    }

    fn check_permission(&self, permission: Option<PermissionKey>) -> Result<AuthenticatedUser> {
        self.auth_data()?
            .check_permission(permission)
            .map_err(Into::into)
    }
}
