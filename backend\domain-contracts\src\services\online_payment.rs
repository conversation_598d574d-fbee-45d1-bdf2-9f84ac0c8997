//! Online payment service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentRequest {
    pub invoice_id: InvoiceId,
    pub amount: i64, // Amount in cents
    pub currency: String,
    pub description: String,
    pub customer_info: CustomerInfo,
    pub return_url: String,
    pub webhook_url: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CustomerInfo {
    pub name: String,
    pub email: String,
    pub phone: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentResponse {
    pub payment_id: String,
    pub payment_url: String,
    pub status: PaymentStatus,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum PaymentStatus {
    Pending,
    Completed,
    Failed,
    Cancelled,
    Refunded,
}

/// Contract for online payment service
#[async_trait]
#[mockall::automock]
pub trait OnlinePaymentService: Send + Sync {
    /// Create a payment request
    async fn create_payment(
        &self,
        request: PaymentRequest,
    ) -> Result<PaymentResponse>;

    /// Get payment status
    async fn get_payment_status(
        &self,
        payment_id: &str,
    ) -> Result<PaymentStatus>;

    /// Process payment webhook
    async fn process_webhook(
        &self,
        payload: &str,
        signature: &str,
    ) -> Result<PaymentStatus>;
}
