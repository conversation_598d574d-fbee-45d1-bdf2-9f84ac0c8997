//! Online payment service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentRequest {
    pub invoice_id: InvoiceId,
    pub amount: i64, // Amount in cents
    pub currency: String,
    pub description: String,
    pub customer_info: CustomerInfo,
    pub return_url: String,
    pub webhook_url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomerInfo {
    pub name: String,
    pub email: String,
    pub phone: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentResponse {
    pub payment_id: String,
    pub payment_url: String,
    pub status: PaymentStatus,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum PaymentStatus {
    Pending,
    Completed,
    Failed,
    Cancelled,
    Refunded,
}

// Additional types for Verifone integration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PaymentCheckout {
    pub amount: i64,
    pub currency: String,
    pub description: String,
    pub return_url: String,
    pub cancel_url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaymentCheckoutResponse {
    pub checkout_id: uuid::Uuid,
    pub checkout_url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckoutStatus {
    pub id: uuid::Uuid,
    pub status: String,
    pub amount: i64,
    pub currency: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Customer {
    pub email: String,
    pub name: String,
    pub phone: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCustomerResponse {
    pub customer_id: String,
    pub email: String,
    pub name: String,
}

/// Contract for online payment service
#[async_trait]
#[mockall::automock]
pub trait OnlinePaymentService: Send + Sync {
    /// Create a payment request
    async fn create_payment(
        &self,
        request: PaymentRequest,
    ) -> Result<PaymentResponse>;

    /// Get payment status
    async fn get_payment_status(
        &self,
        payment_id: &str,
    ) -> Result<PaymentStatus>;

    /// Process payment webhook
    async fn process_webhook(
        &self,
        payload: &str,
        signature: &str,
    ) -> Result<PaymentStatus>;

    // Additional methods for Verifone integration
    /// Get API authentication header
    async fn api_auth_header(&self) -> Result<String>;

    /// Get checkout status
    async fn get_checkout(&self, checkout_id: uuid::Uuid) -> Result<CheckoutStatus>;

    /// Create a payment checkout
    async fn create_checkout(&self, payment_checkout: PaymentCheckout) -> Result<PaymentCheckoutResponse>;

    /// Create a customer
    async fn create_customer(&self, customer: Customer) -> Result<CreateCustomerResponse>;
}
