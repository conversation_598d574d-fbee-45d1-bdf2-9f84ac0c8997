import { Meta, StoryFn } from "@storybook/react-vite"

import { useSelectStore } from "../../hooks/useSelectStore/useSelectStore"
import { SelectPopover, SelectPopoverProps } from "./SelectPopover"

export default {
  title: "Ariakit/SelectPopover",
  component: SelectPopover,
} as Meta

export const SelectPopoverExample: StoryFn<SelectPopoverProps> = (args) => {
  const select = useSelectStore({
    defaultValue: "Apple",
  })

  return <SelectPopover {...args} store={select} />
}
