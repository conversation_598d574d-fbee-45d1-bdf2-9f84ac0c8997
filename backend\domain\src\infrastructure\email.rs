// Re-export from contracts
// Email service interfaces - moved to domain for isolation
use async_trait::async_trait;
use crate::errors::Result;

#[derive(Debug, <PERSON><PERSON>)]
pub struct EmailAttachment {
    pub filename: String,
    pub content: Vec<u8>,
    pub content_type: String,
}

#[derive(Debug, <PERSON>lone)]
pub struct EmailSendInput {
    pub to: Vec<String>,
    pub to_email: String,
    pub to_name: String,
    pub subject: String,
    pub subject_template: String,
    pub body: String,
    pub body_template: String,
    pub params: std::collections::HashMap<String, String>,
    pub from_email: Option<String>,
    pub from_name: Option<String>,
    pub attachments: Vec<EmailAttachment>,
}

#[derive(Debug, <PERSON>lone)]
pub struct EmailSubmitInput {
    pub to: Vec<String>,
    pub to_email: String,
    pub to_name: String,
    pub subject: String,
    pub body: String,
    pub from_email: Option<String>,
    pub from_name: Option<String>,
    pub attachments: Vec<EmailAttachment>,
}

#[async_trait]
pub trait EmailSender: Send + Sync {
    async fn send(&self, input: EmailSendInput) -> Result<()>;
    async fn submit(&self, input: EmailSubmitInput) -> Result<()>;
}

use crate::i18n::Translations;

/// Helper for email template processing
pub struct EmailTemplateProcessor;

impl EmailTemplateProcessor {
    pub async fn send_with_template<T: EmailSender>(
        sender: &T,
        input: EmailSendInput,
    ) -> Result<()> {
        let subject = Translations::interpolate(input.subject_template, &input.params)?;
        let body = Translations::interpolate(input.body_template, &input.params)?;

        let submit_input = EmailSubmitInput {
            to_email: input.to_email,
            to_name: input.to_name,
            subject,
            body,
            attachments: input.attachments,
            from_email: input.from_email,
            from_name: input.from_name,
        };

        sender.submit(submit_input).await
    }
}
