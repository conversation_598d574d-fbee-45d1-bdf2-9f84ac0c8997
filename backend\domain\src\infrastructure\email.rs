// Re-export from contracts
pub use leviosa_domain_contracts::services::{<PERSON>ailSender, EmailAttachment, EmailSubmitInput, EmailSendInput};

use crate::i18n::Translations;

/// Helper for email template processing
pub struct EmailTemplateProcessor;

impl EmailTemplateProcessor {
    pub async fn send_with_template<T: EmailSender>(
        sender: &T,
        input: EmailSendInput,
    ) -> Result<(), anyhow::Error> {
        let subject = Translations::interpolate(input.subject_template, &input.params)?;
        let body = Translations::interpolate(input.body_template, &input.params)?;

        let submit_input = EmailSubmitInput {
            to_email: input.to_email,
            to_name: input.to_name,
            subject,
            body,
            attachments: input.attachments,
            from_email: input.from_email,
            from_name: input.from_name,
        };

        sender.submit(submit_input).await.map_err(|e| anyhow::anyhow!(e))
    }
}
