use crate::errors::Error;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Deserialize, Serialize)]
pub enum CurrencyCode {
    #[serde(rename = "ISK")]
    Isk,
    #[serde(rename = "EUR")]
    Eur,
}
#[derive(Debug, Deserialize, Serialize)]
pub enum InteractionType {
    #[serde(rename = "IFRAME")]
    Iframe,
    #[serde(rename = "PAYMENT_LINK")]
    PaymentLink,
}
#[derive(Debug, Deserialize, Serialize)]
pub enum PaymentMode {
    #[serde(rename = "PAYMENT")]
    Payment,
    #[serde(rename = "3DS_PAYMENT")]
    Payment3ds,
}
#[derive(Debug, Deserialize, Serialize)]
pub enum AuthorizationType {
    #[serde(rename = "FINAL_AUTH")]
    FinalAuth,
    #[serde(rename = "PRE_AUTH")]
    PreAuth,
}
#[derive(Debug, Deserialize, Serialize)]
pub enum ShopperInteraction {
    #[serde(rename = "ECOMMERCE")]
    Ecommerce,
}
#[derive(Debug, Deserialize, Serialize)]
pub struct ThreeDSecureConfig {
    pub threeds_contract_id: Uuid,
    pub enabled: bool,
}
#[derive(Debug, Deserialize, Serialize)]
pub struct Card {
    pub mode: PaymentMode,
    ///Payment Provider Contract ID
    pub payment_contract_id: Uuid,
    ///Required only for 3DS mode
    pub threed_secure: Option<ThreeDSecureConfig>,
    pub account_validation: Option<bool>,
    pub capture_now: Option<bool>,
    pub cvv_required: Option<bool>,
    pub authorization_type: Option<AuthorizationType>,
    pub shopper_interaction: Option<ShopperInteraction>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CheckoutConfiguration {
    pub config_type: Card,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct PaymentCheckout {
    /// The organisation_id of the merchant site
    pub entity_id: Uuid,
    pub currency_code: CurrencyCode,
    pub amount: f64,
    /// ID of a Customer created via the Customer API needed for 3DS
    pub customer: Option<Uuid>,
    pub configuration: CheckoutConfiguration,
    pub merchant_reference: String, // Merchant reference ID
    /// Redirect link in case of successful Checkout
    pub return_url: Option<String>,
    /// Redirect link in case of cancelled Checkout
    pub shop_url: Option<String>,
    pub interaction_type: InteractionType,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct PaymentCheckoutResponse {
    pub id: Uuid,
    pub url: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CheckoutStatus {
    pub id: String,
    pub events: Vec<OnlinePaymentEvent>,
    pub amount: f64,
    pub configurations: CheckoutConfiguration,
    pub currency_code: CurrencyCode,
    pub expiry_time: DateTime<Utc>,
    pub merchant_reference: String,
    pub entity_id: Uuid,
    pub return_url: Option<String>,
    pub i18n: serde_json::Value,
    pub status: String,
    pub display_line_items: bool,
    pub fraud_protection_data: serde_json::Value,
    pub interaction_type: InteractionType,
    pub url: String,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct OnlinePaymentEvent {
    pub r#type: String, // "type": "TRANSACTION_SUCCESS",  or "TRANSACTION_FAILED" or "TRANSACTION_DECLINED"
    pub id: Uuid,
    pub timestamp: DateTime<Utc>,
    pub details: PaymentDetails,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct PaymentDetails {
    pub id: Uuid,
    pub payment_provider_contract: Uuid,
    pub amount: f64,
    pub blocked: bool,
    pub customer: Option<Uuid>,
    pub merchant_reference: String,
    pub payment_product: String,
    pub status: String, // "status": "AUTHORIZED" or "DECLINED" or "FAILED"
    pub authorization_code: Option<String>,
    pub created_by: Uuid,
    pub cvv_result: String,
    pub reason_code: Option<String>,
    pub rrn: Option<String>,
    pub shopper_interaction: String,
    pub stan: String,
    pub reversal_status: String,
    pub geo_location: (f64, f64),
    pub city: String,
    pub country_code: String,
    pub details: AdditionalDetails,
    pub additional_data: AdditionalData,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct AdditionalDetails {
    pub auto_capture: bool,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct AdditionalData {
    pub acquirer_response_code: String,
    pub initiator_trace_id: String,
}

#[async_trait::async_trait]
#[mockall::automock]
pub trait OnlinePaymentService: Send + Sync {
    async fn api_auth_header(&self) -> Result<String, Error>;

    async fn create_checkout(
        &self,
        payment_checkout: PaymentCheckout,
    ) -> Result<PaymentCheckoutResponse, Error>;

    async fn get_checkout(&self, checkout_id: Uuid) -> Result<CheckoutStatus, Error>;

    async fn create_customer(&self, customer: Customer) -> Result<CreateCustomerResponse, Error>;

    // Additional methods for infrastructure compatibility
    async fn create_payment(&self, request: PaymentRequest) -> Result<PaymentResponse, Error>;
    async fn get_payment_status(&self, payment_id: &str) -> Result<PaymentStatus, Error>;
    async fn process_webhook(&self, payload: &str, signature: &str) -> Result<PaymentStatus, Error>;

    // may be needed in the future
    // async fn list_customers(&self, search_string: String) -> Result<Vec<Customer>, Error>;
}

#[derive(Serialize, Deserialize, Debug)]
pub struct Customer {
    pub entity_id: Uuid,
    pub billing: Billing,
    pub email_address: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct Billing {
    pub address_1: String,
    pub city: String,
    pub country_code: String,
    pub first_name: String,
    pub last_name: String,
    pub postal_code: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct CreateCustomerResponse {
    pub id: Uuid,
    pub billing: Billing,
    pub entity_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub email_address: String,
}

// Additional payment types for infrastructure compatibility
#[derive(Debug, Serialize, Deserialize)]
pub struct PaymentRequest {
    pub amount: f64,
    pub currency: CurrencyCode,
    pub merchant_reference: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaymentResponse {
    pub id: Uuid,
    pub status: String,
    pub amount: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaymentStatus {
    pub id: Uuid,
    pub status: String,
    pub amount: f64,
    pub created_at: DateTime<Utc>,
}
