import c from "classnames"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

import { formatPersonaId } from "@leviosa/utils"

import PersonaIdInput from "components/PersonaIdInput/PersonaIdInput"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import Restricted from "features/authentication/components/Restricted/Restricted"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { color } from "styles/colors"
import { Grid, Heading, Input, Text } from "ui"
import { formatNumberInThousand } from "utils/formatNumberInThousand"

import {
  PermissionKey,
  useGetInvoiceQuery,
  useGetSubjectInsuranceStatusQuery,
} from "generated/graphql"

import styles from "./PatientInvoice.module.css"
import { PatientInvoiceInformationPayment } from "./PatientInvoiceInformationPayment/PatientInvoiceInformationPayment"
import { PatientInvoiceSection } from "./PatientInvoiceSection/PatientInvoiceSection"

const PatientInvoiceWithoutPermission = () => {
  const { t } = useTranslation()

  const { invoiceId } = useParams<{
    invoiceId: string
  }>()

  const { data } = useGetInvoiceQuery({
    variables: { invoiceId: invoiceId || "" },
    skip: invoiceId === undefined,
  })

  const { data: insuranceStatus, error } = useGetSubjectInsuranceStatusQuery({
    variables: { subjectId: data?.invoice.subject.id || "" },
    skip: data?.invoice.subject.id === undefined,
  })

  if (!data) return null

  const subject = data.invoice.subject

  const status = insuranceStatus?.subjectInsuranceStatus.subjectStatus
  const paymentStatus = insuranceStatus?.subjectInsuranceStatus.paymentStatus
  const isInsured = insuranceStatus?.subjectInsuranceStatus.isInsured

  const notFoundErrorText = error
    ? error?.message.toLocaleLowerCase().includes("not found")
      ? t("Person id not found")
      : t("Error fetching insurance status")
    : false

  const insuranceStatusText = notFoundErrorText
    ? notFoundErrorText
    : !isInsured
      ? t("Uninsured")
      : `${formatNumberInThousand(paymentStatus || 0)} ISK - ${status}`

  return (
    <Grid className={styles.wrap}>
      <div className={c(styles.subjectWrap, color.light)}>
        <div className={styles.subject}>
          <Heading size="large">{t("Subject")}</Heading>
          {insuranceStatusText && (
            <Text className={styles.insuranceStatus}>
              {t("Insurance Status")}:{" "}
              <span className={styles.price}>{insuranceStatusText}</span>
            </Text>
          )}
        </div>
        <PiiSensitive as="div" className={styles.subjectInputs}>
          <Input disabled defaultValue={subject.name} label={t("Name")} />
          <PersonaIdInput
            label={t("Persona Id")}
            defaultValue={formatPersonaId(subject.personaId)}
            disabled
          />
          <Input
            label={t("Address")}
            defaultValue={subject.address?.addressLine1}
            disabled
            onBlur={(e) => {
              //Update on blur. Backend api needed
              // eslint-disable-next-line no-console
              console.log(e.target.value)
            }}
          />
        </PiiSensitive>
      </div>
      <div className={c(styles.invoiceWrap, color.light)}>
        <PatientInvoiceSection invoice={data.invoice} />
      </div>
      <div className={c(styles.invoiceInformationWrap, color.light)}>
        <PatientInvoiceInformationPayment invoice={data.invoice} />
      </div>
    </Grid>
  )
}

export const PatientInvoice = () => {
  return (
    <Restricted
      to={PermissionKey.BillingBillingCodeView}
      fallback={<UnauthorizedPage />}
    >
      <PatientInvoiceWithoutPermission />
    </Restricted>
  )
}

export default PatientInvoice
