import { useTranslation } from "react-i18next"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { Button } from "ui"

import {
  ParticipantRsvpStatus,
  useSetParticipantRsvpMutation,
} from "generated/graphql"

import { UseParticipantsState } from "../Participants/useParticipantsState"
import styles from "./SetRsvpStatus.module.css"

type SetRsvpStatusProps = {
  className?: string
  formType?: "Create" | "Edit"
  isEventInstanceForm?: boolean
} & Pick<UseParticipantsState, "selectedParticipants" | "onUpdate">

export const SetRsvpStatus = ({
  className = "",
  selectedParticipants,
  onUpdate,
  formType,
  isEventInstanceForm,
}: SetRsvpStatusProps) => {
  const { t } = useTranslation()

  const { globalData } = useGlobalState()

  const [setParticipantRsvp] = useSetParticipantRsvpMutation({
    onCompleted: (data) => {
      const rsvpStatus = data.setParticipantRsvp?.rsvpStatus
      const userId = data.setParticipantRsvp?.id

      const participant = selectedParticipants?.find(
        (p) => p.participantId === userId
      )

      if (!participant || rsvpStatus === null) return

      onUpdate(participant.userId, "rsvpStatus", rsvpStatus)
    },
  })

  const currentParticipant = selectedParticipants?.find(
    (p) => p.userId === globalData.actor.id
  )

  if (!currentParticipant || formType === "Create" || !isEventInstanceForm)
    return null

  const isDeclined = currentParticipant?.rsvpStatus === "DECLINED"
  const isAccepted = currentParticipant?.rsvpStatus === "ACCEPTED"

  const handleRsvp = (rsvpStatus: ParticipantRsvpStatus) => {
    setParticipantRsvp({
      variables: {
        setParticipantRsvpId: currentParticipant.participantId || "",
        input: { rsvpStatus },
      },
    })
  }

  return (
    <div className={`${styles.rsvpStatusWrapper} ${className}`}>
      <Button
        status="error"
        variant={isDeclined ? "filled" : "outline"}
        onClick={() => handleRsvp(ParticipantRsvpStatus.Declined)}
      >
        {isDeclined ? t("Rejected") : t("Reject")}
      </Button>

      <Button
        status={isAccepted ? "success" : "default"}
        variant={!isDeclined ? "filled" : "outline"}
        onClick={() => handleRsvp(ParticipantRsvpStatus.Accepted)}
      >
        {isAccepted ? t("Accepted") : t("Accept")}
      </Button>
    </div>
  )
}
