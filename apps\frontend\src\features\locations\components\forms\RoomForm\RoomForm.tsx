import { ApolloError } from "@apollo/client"
import _ from "lodash"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import { Button, FormGrid, Heading, Input } from "ui"
import { Center } from "ui/components/Layout/Center"
import { camelCaseToNormalWord } from "utils/camelCaseToNormal"

import { RoomType } from "generated/graphql"

import { Building } from "../../BuildingOverview/BuildingOverview"
import styles from "../LocationForm.module.css"

const baseSchema = z.object({
  capacity: z
    .string()
    .transform((d) => parseInt(d, 10))
    .optional(),
  label: z.string(),
  roomType: z.nativeEnum(RoomType),
})

type RoomFormProps = {
  onSubmit: (data: z.infer<typeof baseSchema>) => void
  onCancel: () => void
  error?: ApolloError
  loading: boolean
  formData?: Building["rooms"]["rooms"][0]
}

export const RoomForm = ({
  onSubmit,
  onCancel,
  error,
  loading,
  formData,
}: RoomFormProps) => {
  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const { t } = useTranslation()
  const roomTypeOptions = Object.keys(RoomType).map((key) => ({
    label: camelCaseToNormalWord(key),
    value: (RoomType as Record<string, string>)[key],
  }))

  const selectRoomTypeStore = useSelectStore({
    defaultValue: formData?.roomType || "",
  })

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setValidationError(null)

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = baseSchema.safeParse(data)

    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    onSubmit?.(validatedInput.data)
  }

  return (
    <Panel as="form" onSubmit={handleSubmit} className={styles.panelForm}>
      <Center as={Heading} size="display">
        {_.isEmpty(formData) ? t("Add Room") : t("Edit Room")}
      </Center>

      <br />
      <FormGrid as="div" colSpan={4}>
        <Input
          defaultValue={formData?.label}
          label={t("label")}
          name="label"
          type="text"
          required
          className={styles.span2}
        />
        <Select
          label={t("roomType")}
          name="roomType"
          selectStore={selectRoomTypeStore}
          options={roomTypeOptions}
          className={styles.span2}
          disabled={!_.isEmpty(formData)}
        />
        <Input
          defaultValue={formData?.capacity || ""}
          label={t("capacity")}
          name="capacity"
          type="number"
          className={styles.span2}
        />
      </FormGrid>
      {(error || validationError) && (
        <Panel status="error">
          {validationError?.message}
          {error?.message}
        </Panel>
      )}

      <div className={styles.footer}>
        <Button disabled={loading} onClick={onCancel}>
          {t("Cancel")}
        </Button>
        <Button type="submit" variant="filled" disabled={loading}>
          {_.isEmpty(formData) ? t("Save") : t("Update")}
        </Button>
      </div>
    </Panel>
  )
}
