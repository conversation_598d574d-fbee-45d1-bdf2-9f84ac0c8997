import c from "classnames"
import { useTranslation } from "react-i18next"
import { generatePath, Link, NavLink, useLocation } from "react-router-dom"

import Icon from "components/Icon/Icon"
import { Button } from "ui"

import styles from "./Pagination.module.css"

const range = (start: number, end: number) =>
  Array.from({ length: end - start + 1 }, (_, i) => start + i)

export function getPaginationRange(currentPage: number, totalPages: number) {
  const siblingCount = 1
  const totalPageNumbers = siblingCount + 5

  if (totalPages > totalPageNumbers) {
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1)
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages)

    const shouldShowLeftDots = leftSiblingIndex > 3
    const shouldShowRightDots = rightSiblingIndex < totalPages - 2

    const firstPageIndex = 1
    const lastPageIndex = totalPages

    if (!shouldShowLeftDots && shouldShowRightDots) {
      const leftItemCount = 3 + 2 * siblingCount
      return [...range(1, leftItemCount), "...", lastPageIndex]
    }

    if (shouldShowLeftDots && !shouldShowRightDots) {
      const rightItemCount = 3 + 2 * siblingCount
      return [
        firstPageIndex,
        "...",
        ...range(totalPages - rightItemCount + 1, totalPages),
      ]
    }

    if (shouldShowLeftDots && shouldShowRightDots) {
      return [
        firstPageIndex,
        "...",
        ...range(leftSiblingIndex, rightSiblingIndex),
        "...",
        lastPageIndex,
      ]
    }
  }

  return range(1, totalPages)
}

type PaginationProps = {
  currentPage: number
  totalPages: number
} & (
  | {
      pageAsQueryParam: true
      routeString?: string
    }
  | {
      pageAsQueryParam?: false
      routeString: string
    }
)

export const Pagination = ({
  totalPages,
  currentPage,
  routeString,
  pageAsQueryParam = false,
}: PaginationProps) => {
  const { pathname, search } = useLocation()
  const paginationRange = getPaginationRange(currentPage, totalPages)
  const { t } = useTranslation("components", { keyPrefix: "pagination" })

  const firstPage = 1
  const lastPage = totalPages

  const disablePrevNavigation = currentPage === firstPage
  const disableNextNavigation = currentPage === lastPage

  const getToProp = (targetPage: number | string) => {
    if (pageAsQueryParam) {
      const searchParams = new URLSearchParams(search)
      searchParams.set("page", String(targetPage))
      return {
        pathname,
        search: searchParams.toString(),
      }
    } else {
      return {
        pathname: generatePath(routeString as string, {
          pageNumber: targetPage,
        }),
        search: location.search,
      }
    }
  }

  return (
    <nav role="navigation" aria-label={t("navigationLabel")}>
      <ul className={styles.wrap}>
        <li>
          <Button
            variant="clear"
            icon={<Icon name="arrow-left-s-line" />}
            as={Link}
            className={c(styles.navigationButton, styles.navigationIconButtons)}
            to={getToProp(currentPage - 1)}
            readOnly={disablePrevNavigation}
            relative="path"
            aria-label={t("previousPageLabel")}
          />
        </li>

        {paginationRange.map((pageNumberOrDots, index) => {
          if (pageNumberOrDots === "...") {
            return (
              <span key={`dots-${index}`} className={styles.dots}>
                ...
              </span>
            )
          }
          const pageNumber = pageNumberOrDots as number

          return (
            <li key={`page-${pageNumber}`}>
              <Button
                to={getToProp(pageNumber)}
                relative="path"
                variant="clear"
                as={NavLink}
                aria-current={pageNumber === currentPage ? "page" : false}
                className={styles.navigationButton}
                // hack for the active style to work for query params
                // tried to use className instead of style but it requires
                // changing not only Button but also ButtonText
                style={({ isActive }: { isActive: boolean }) => {
                  const active = pageAsQueryParam
                    ? pageNumber === currentPage
                    : isActive

                  if (active) {
                    return {
                      backgroundColor: "var(--color-lev-blue-3)",
                      color: "white",
                    }
                  }
                  return undefined
                }}
                aria-label={
                  pageNumber === currentPage
                    ? t("currentPageLabel", { pageNumber })
                    : t("gotoPageLabel", { pageNumber })
                }
              >
                {pageNumber}
              </Button>
            </li>
          )
        })}

        <li>
          <Button
            variant="clear"
            icon={<Icon name="arrow-right-s-line" />}
            as={Link}
            relative="path"
            to={getToProp(currentPage + 1)}
            className={`${styles.navigationButton} ${styles.navigationIconButtons}`}
            readOnly={disableNextNavigation}
            aria-label={t("nextPageLabel")}
          />
        </li>
      </ul>
    </nav>
  )
}
