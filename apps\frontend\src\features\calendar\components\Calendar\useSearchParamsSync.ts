import { SelectStore } from "@ariakit/react"
import { useEffect } from "react"
import { useSearchParams } from "react-router-dom"

const useSearchParamsSync = (store: SelectStore, key: string) => {
  const [searchParams, setSearchParams] = useSearchParams()

  useEffect(() => {
    const value = store.getState().value

    if (!value.length && searchParams.has(key)) {
      setSearchParams((params) => {
        params.delete(key)
        return params
      })
    }

    if (value && value.length) {
      setSearchParams((params) => {
        const paramValue = Array.isArray(value) ? value.join(",") : value
        params.delete(key)
        params.set(key, encodeURIComponent(paramValue))
        return params
      })
    }
  }, [store.getState().value, key])
}

export default useSearchParamsSync
