import { MenuItem } from "@ariakit/react"
import { Meta, StoryFn } from "@storybook/react-vite"

import { useMenuStore } from "../Ariakit/hooks"
import items from "../FiltrableButtonMenu/list"
import { ButtonMenu, ButtonMenuProps } from "./ButtonMenu"
import styles from "./ButtonMenuStory.module.css"

export default {
  title: "Components/Button Menu",
  component: ButtonMenu,
} as Meta

export const ButtonMenuExample: StoryFn<ButtonMenuProps> = (args) => {
  const options = items.map((el) => ({ value: el, label: el }))

  const menu = useMenuStore({})

  return (
    <ButtonMenu
      {...args}
      menuStore={menu}
      options={options}
      onSelect={(value) => console.log(value)}
    >
      SelectItem
    </ButtonMenu>
  )
}

export const MenuButtonCustomItemExample: StoryFn<ButtonMenuProps> = (args) => {
  const options = items.map((el) => ({ value: el, label: el }))

  const menu = useMenuStore({})

  return (
    <ButtonMenu
      {...args}
      menuStore={menu}
      options={options}
      renderItem={(value) => (
        <MenuItem
          key={value}
          className={styles.menuItem}
          onClick={() => {
            menu.setOpen(false)
          }}
        >
          {value}
        </MenuItem>
      )}
    />
  )
}
