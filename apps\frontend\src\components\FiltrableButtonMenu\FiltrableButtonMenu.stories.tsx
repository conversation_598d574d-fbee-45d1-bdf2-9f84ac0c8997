import { <PERSON>a, Story } from "@storybook/react-vite"

import { ComboboxItem } from "../Ariakit/Combobox/ComboboxItem/ComboboxItem"
import { useComboboxStore, useMenuStore } from "../Ariakit/hooks"
import { useFilter } from "../Ariakit/hooks/"
import {
  FiltrableButtonMenu,
  FiltrableButtonMenuProps,
} from "./FiltrableButtonMenu"
import items from "./list"

export default {
  title: "Components/Filtrable Button Menu",
  component: FiltrableButtonMenu,
} as Meta

export const FiltrableMenuButtonDefault: Story<FiltrableButtonMenuProps> = (
  args
) => {
  const options = items.map((el) => ({ value: el, label: el }))

  const combobox = useComboboxStore()
  const menu = useMenuStore(combobox)

  const { value: comboboxValue } = combobox.useState()

  const { filteredList } = useFilter({
    defaultItems: options,
    value: comboboxValue,
  })

  return (
    <FiltrableButtonMenu
      {...args}
      menuStore={menu}
      comboboxStore={combobox}
      options={filteredList}
      onSelect={(value) => console.log(value)}
    >
      Click
    </FiltrableButtonMenu>
  )
}

export const FiltrableMenuButtonCustomItem: Story<FiltrableButtonMenuProps> = (
  args
) => {
  const options = items.map((el) => ({ value: el, label: el }))

  const combobox = useComboboxStore({
    value: "",
  })
  const menu = useMenuStore(combobox)

  const { value: comboboxValue } = combobox.useState()

  const { filteredList } = useFilter({
    defaultItems: options,
    value: comboboxValue,
  })

  return (
    <FiltrableButtonMenu
      {...args}
      menuStore={menu}
      comboboxStore={combobox}
      options={filteredList}
      renderItem={(value) => (
        <ComboboxItem
          key={value}
          onClick={() => {
            combobox.setValue("")
            menu.setOpen(false)
          }}
          setValueOnClick={false}
          focusOnHover
        >
          {value}
        </ComboboxItem>
      )}
    >
      Click
    </FiltrableButtonMenu>
  )
}
