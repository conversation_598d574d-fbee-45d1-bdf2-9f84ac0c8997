use crate::builder::TestDataBuilder;
use leviosa_domain_contracts::{<PERSON>ck<PERSON>mail<PERSON>ender, MockPdfGenerator};
use leviosa_domain::{
    billing::{
        BillingCodeNhi, BillingCodeType, CreateInvoiceForEncounterCommand,
        CreateInvoiceIssuerCommand, CreateInvoiceLineForCodeCommand, FindBillingCodeNhiQuery,
        GetInvoiceFilter, Invoice, InvoiceId, InvoiceIssuer, InvoiceIssuerId, IssueInvoiceCommand,
        IssueInvoiceHandler, NhiInvoiceTotalCalculator, UpdateInvoiceCommand,
    },
    command::{CommandPipeline, InvoiceTotalCalculationHandler, RepoCommandHandler},
    email::EmailSender,
    file_repo::MockIFileRepo,
    i18n::LanguageId,
    nhi_service::{MockNhiService, NhiSubmissionResult, SubjectInsuranceStatus},
    pdf_generator::PdfGenerator,
    query::QueryPipeline,
    repo_connection::IRepoConnection,
    subject_journal::EncounterId,
};
use std::future;
use std::sync::Arc;

impl TestDataBuilder {
    pub fn default_mock_nhi_service(&self, maximum_payable_by_subject: f64) -> MockNhiService {
        let mut mock_nhi_service = MockNhiService::new();
        mock_nhi_service
            .expect_get_subject_payment_status()
            .returning(move |_, _| {
                Box::pin(future::ready(Ok(SubjectInsuranceStatus::Insured {
                    insurance_category: "ALM".to_string(),
                    maximum_payable_by_subject,
                    insurance_percentage: 10,
                    payment_status_serial_number: 1_234_567_890,
                })))
            });
        mock_nhi_service
            .expect_submit_invoice()
            .returning(move |_, _, _| {
                Box::pin(future::ready(Ok(NhiSubmissionResult {
                    request_xml: String::from("Mock-Request-XML"),
                    request_json: String::from("Mock-Request-JSON"),
                    response_xml: String::from("Mock-Response-XML"),
                })))
            });

        mock_nhi_service
    }

    pub fn default_mock_nhi_invoice_calculator(&self) -> NhiInvoiceTotalCalculator {
        NhiInvoiceTotalCalculator::new(
            Arc::new(self.default_mock_nhi_service(30000.0)),
            "fake_token".to_string(),
        )
    }

    pub async fn invoice(&self, encounter_id: &Option<EncounterId>) -> Invoice {
        let command = CreateInvoiceForEncounterCommand {
            id: None,
            encounter_id: encounter_id.unwrap_or(self.encounter(&None, &None, &None).await.id()),
            reference: None,
        };

        CommandPipeline::handle(
            InvoiceTotalCalculationHandler::new(
                self.tx_manager(),
                self.admin_auth_data().user().unwrap(),
                self.default_mock_nhi_invoice_calculator(),
            ),
            self.admin_auth_data(),
            command,
        )
        .await
        .unwrap()
    }

    pub async fn issue_invoice(&self, id: InvoiceId) -> Invoice {
        let command = IssueInvoiceCommand { id };

        let pdf_generator = MockPdfGenerator::new();
        let email_sender = MockEmailSender::new();
        let file_repo = MockIFileRepo::new();

        let handler = IssueInvoiceHandler::new(
            self.tx_manager(),
            self.admin_auth_data().user().unwrap(),
            "fake_token".to_string(),
            LanguageId::EnGb,
            Arc::new(self.default_mock_nhi_service(30000.0)),
            &file_repo,
            &pdf_generator,
            &email_sender,
        );

        CommandPipeline::handle(handler, self.admin_auth_data(), command)
            .await
            .unwrap()
    }

    pub async fn invoice_issuer(
        &self,
        title: String,
        tax_number: Option<String>,
        address: Option<String>,
        persona_id: Option<String>,
    ) -> InvoiceIssuer {
        let command = CreateInvoiceIssuerCommand {
            title,
            tax_number,
            address,
            persona_id,
        };

        CommandPipeline::handle(
            RepoCommandHandler::new(self.tx_manager(), self.admin_auth_data().user().unwrap()),
            self.admin_auth_data(),
            command,
        )
        .await
        .unwrap()
    }

    pub async fn add_issuer_to_invoice(
        &self,
        invoice_id: InvoiceId,
        issuer_id: InvoiceIssuerId,
    ) -> Invoice {
        let handler =
            RepoCommandHandler::new(self.tx_manager(), self.admin_auth_data().user().unwrap());
        CommandPipeline::handle(
            handler,
            self.admin_auth_data(),
            UpdateInvoiceCommand {
                id: invoice_id,
                nhi_pays_all: None,
                issuer_id: Some(issuer_id),
                provider_id: None,
                payment_date: None,
                treatment_date: None,
                due_date: None,
                total_discount: None,
                total_vat: None,
                total: None,
                total_payable_by_subject: None,
                total_payable_by_insurance: None,
                reference: None,
                payer_id: None,
                payer_name: None,
                payer_email: None,
                comment: None,
                send_invoice_mail: None,
                print_invoice: None,
                payment_method: None,
                subject_discount: None,
            },
        )
        .await
        .unwrap()
    }

    pub async fn update_invoice(&self, input: UpdateInvoiceCommand) -> Invoice {
        let handler =
            RepoCommandHandler::new(self.tx_manager(), self.admin_auth_data().user().unwrap());
        CommandPipeline::handle(handler, self.admin_auth_data(), input)
            .await
            .unwrap()
    }

    pub async fn invoice_with_nhi_codes(&self) -> (Invoice, Vec<BillingCodeNhi>) {
        let auth = self.admin_auth_data();
        let auth_user = auth.check_permission(None).unwrap();

        let invoice = self.invoice(&None).await;

        let billing_codes = QueryPipeline::execute(
            self.repo_query_handler(),
            auth,
            FindBillingCodeNhiQuery {
                filter: Some("81-001-01".to_string()),
                category: None,
                limit: Some(1),
            },
        )
        .await
        .unwrap();
        let code = billing_codes.first().unwrap();
        let add_line_command = CreateInvoiceLineForCodeCommand {
            encounter_id: self.encounter(&None, &None, &None).await.id(),
            invoice_id: invoice.id(),
            billing_code_id: code.id.0,
            billing_code_type: BillingCodeType::Nhi,
            quantity: 1.0,
        };

        let handler = InvoiceTotalCalculationHandler::new(
            self.tx_manager(),
            auth_user.clone(),
            NhiInvoiceTotalCalculator::new(
                Arc::new(self.default_mock_nhi_service(30000.0)),
                "fake_token".to_string(),
            ),
        );

        CommandPipeline::handle(handler, auth, add_line_command)
            .await
            .unwrap();
        let invoice = self
            .repo_connection()
            .repos()
            .invoice_repo()
            .get(GetInvoiceFilter::Id(invoice.id()))
            .await
            .unwrap();
        (invoice, billing_codes)
    }
}
