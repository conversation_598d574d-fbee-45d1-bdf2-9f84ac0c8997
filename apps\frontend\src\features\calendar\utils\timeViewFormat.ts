import { format } from "date-fns"

export const timeViewFormat = (fromDate: Date, toDate: Date) => {
  const dayOfWeek = format(fromDate, "EEE")
  const month = format(fromDate, "MMMM")
  const day = fromDate.getDate()
  const year = format(fromDate, "yyyy")

  const fromHour = format(fromDate, "HH:mm")
  const toHour = format(toDate, "HH:mm")

  return `${dayOfWeek} ${month} ${day}, ${year} ${fromHour} - ${toHour}`
}
