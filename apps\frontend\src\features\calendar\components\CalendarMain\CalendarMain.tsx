import { useCallback, useEffect, useMemo } from "react"
import {
  Components,
  Calendar as ReactBigCalendar,
  SlotInfo,
  View,
} from "react-big-calendar"
import withDragAndDrop, {
  EventInteractionArgs,
} from "react-big-calendar/lib/addons/dragAndDrop"
import "react-big-calendar/lib/addons/dragAndDrop/styles.css"
import { useTranslation } from "react-i18next"
import { useLocation, useSearchParams } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { useLocalStorage } from "components/LocalStorageProvider/LocalStorageProvider"
import { RouteStrings } from "routes/RouteStrings"
import { calendarColorMap, color } from "styles/colors"
import { notification } from "ui"
import { getEventTitle as _getEventTitle } from "utils/getEventTitle"
import { isTypename } from "utils/isTypename"

import {
  GetAllProvidersQuery,
  namedOperations,
  useMoveEventInstanceMutation,
  useNationalHolidaysQuery,
} from "generated/graphql"

import "../../assets/Calendar.css"
import "../../assets/CalendarDragAndDrop.css"
import "../../assets/CalendarPrintView.css"
import "../../assets/EventColors.css"
import useCalendarState, {
  validateUuidsString,
} from "../../hooks/useCalendarState"
import { useEventInstanceFetcher } from "../../hooks/useEventInstanceFetcher"
import { formats } from "../../utils/formats"
import { getScrollTimeDayAndWeekView } from "../../utils/getScrollTime"
import { localizer } from "../../utils/localizer"
import useNavigateCalendar from "../../utils/navigateCalendar"
import type { CalendarEvent } from "../../utils/parser/calendarEvent"
import { splitEventByProvider } from "../../utils/parser/splitEventByProvider"
import calendarVariables from "../../utils/variables/calendarVariables"
import { useCalendar } from "../Calendar/CalendarProvider"
import { CalendarEventWrapper } from "../CalendarEventWrapper/CalendarEventWrapper"
import { CalendarToolbar } from "../CalendarToolbar/CalendarToolbar"
import { Event } from "../Event/Event"
import RBCHeader from "../RBCHeader/RBCHeader"
import RBCResourceHeader from "../RBCResourceHeader/RBCResourceHeader"

type ResourceType = {
  provider: GetAllProvidersQuery["providers"][number]
  date: Date
}

const DragAndDropBigCalendar = withDragAndDrop<
  CalendarEvent<"eventInstance" | "availability" | "holiday">,
  ResourceType
>(ReactBigCalendar)

const views: View[] = ["month", "week", "day", "work_week"]

export type CalendarComponentMap = Components<
  CalendarEvent<"eventInstance" | "availability" | "holiday">,
  ResourceType
>

const calendarComponentMap: CalendarComponentMap = {
  toolbar: CalendarToolbar,
  header: RBCHeader,
  resourceHeader: RBCResourceHeader,
  event: Event,
  eventWrapper: CalendarEventWrapper,
}

function useNationalHolidays(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1

  const { data: nationalHolidays } = useNationalHolidaysQuery({
    variables: {
      filter: {
        year,
        month,
      },
    },
  })

  return useMemo(
    () =>
      nationalHolidays?.nationalHolidays.map(({ name, date }) => ({
        title: name,
        start: new Date(date),
        end: new Date(date),
        allDay: true,
        resource: {
          id: name,
          type: "holiday" as const,
        },
      })) ?? [],
    [nationalHolidays?.nationalHolidays]
  )
}

const resourceIdAccessor = (resource: ResourceType) => resource?.provider?.id

type CalendarMainProps = {
  date: Date
  onSelectDate: (date: Date) => void
  providers?: GetAllProvidersQuery["providers"]
  eventInstances: ReturnType<typeof useEventInstanceFetcher>["eventInstances"]
  availabilityBlocks: ReturnType<
    typeof useEventInstanceFetcher
  >["availabilityBlocks"]
}

export default function CalendarMain({
  date,
  onSelectDate,
  eventInstances,
  availabilityBlocks,
}: CalendarMainProps) {
  const { t } = useTranslation()
  const navigateCalendar = useNavigateCalendar()
  const { view, visibleProviders, selectedProviders } = useCalendarState()
  const {
    globalState: { isNnMode },
  } = useGlobalState()
  const location = useLocation()
  const { allProviders } = useCalendar()

  const showMultiColumnView = visibleProviders?.length > 1 && view === "day"

  const [moveEventInstance] = useMoveEventInstanceMutation({
    onCompleted: () => {
      notification.create({
        message: t("Event has been updated"),
        status: "success",
        maxWidth: "500px",
      })

      navigateCalendar(RouteStrings.calendar)
    },
    refetchQueries: [namedOperations.Query.ProviderBox],
  })

  const holidayEvents = useNationalHolidays(date)

  const events = useMemo(
    () =>
      showMultiColumnView
        ? [...splitEventByProvider(eventInstances), ...holidayEvents]
        : [...eventInstances, ...holidayEvents],
    [eventInstances, holidayEvents, showMultiColumnView]
  )

  const handleSelectEvent = (event: CalendarEvent) => {
    if (event?.resource?.id) {
      if (event.resource.type === "holiday") return
      if (event.resource.type === "availability") return

      const filteredProvidersInEvent = event.resource.participants
        .filter(isTypename("ParticipantProvider"))
        .filter((p) => visibleProviders.includes(p.provider.id))

      navigateCalendar(RouteStrings.calendarViewEventInstance, {
        eventId: event.resource.id,
        providerId:
          filteredProvidersInEvent.length > 1 ? event.resourceId : undefined,
        replace: true,
      })

      return
    }

    navigateCalendar(RouteStrings.calendarCreateEventInstance, {
      state: { start: event.start, end: event.end },
    })
  }

  const handleSelectSlot = useCallback(
    ({ start, end, action, resourceId }: SlotInfo) => {
      // don't do anything if the action is click
      if (action === "click") {
        return
      }

      navigateCalendar(RouteStrings.calendarCreateEventInstance, {
        state: { start, end, providerId: resourceId },
      })
    },

    [view, navigateCalendar]
  )

  const handleMove = useCallback(
    ({
      event,
      start,
      end,
    }: EventInteractionArgs<CalendarEvent<"eventInstance" | "holiday">>) => {
      if (!event?.resource) return

      if (event.resource.type === "holiday") return

      if (event.resource.canceledAt !== null) return

      const { id, participants } = event.resource

      const newFromDate = new Date(start)
      const newToDate = new Date(end)

      moveEventInstance({
        variables: {
          editEventInstanceId: id,
          input: {
            fromDate: newFromDate,
            toDate: newToDate,
          },
        },
        optimisticResponse: {
          editEventInstance: {
            id,
            fromDate: newFromDate.toISOString(),
            toDate: newToDate.toISOString(),
            participants,
            __typename: "EventInstance" as const,
          },
          __typename: "Mutation" as const,
        },
      })
    },
    [moveEventInstance]
  )

  const filteredProviders = allProviders?.filter((provider) =>
    visibleProviders.includes(provider.id)
  )

  // Transform providers to ResourceType objects
  const resourcesWithDate = useMemo(
    () =>
      filteredProviders?.map((provider) => ({
        provider,
        date: new Date(date),
      })),
    [filteredProviders, date]
  )

  const tooltipAccessor = useCallback(
    (event: CalendarEvent<"eventInstance" | "availability" | "holiday">) => {
      if (event.resource.type !== "eventInstance") return event.title
      if (event.subjects && event.subjects.length > 0) {
        if (isNnMode) {
          return "-"
        }
        return event.subjects.join(", ")
      }

      return _getEventTitle(event.title, event.resource.serviceType?.name) || ""
    },
    [isNnMode]
  )

  const navigateToDate = useCallback(
    (_: unknown, date: Date) => {
      const searchParams = Object.fromEntries(
        new URLSearchParams(location.search)
      )
      searchParams.date = date.toISOString().split("T")[0]
      navigateCalendar(RouteStrings.calendar, {
        view: "day",
        search: searchParams,
      })
    },
    [location.search, navigateCalendar]
  )

  const [visibleProvidersString, setVisibleProviders] = useLocalStorage(
    "visibleCalendarProviders"
  )
  const [__, setSelectedProviders] = useLocalStorage(
    "selectedCalendarProviders"
  )
  const [searchParams] = useSearchParams()
  // Get provider from query parameter and properly decode it
  const rawUrlProvidersString = searchParams.get("provider") ?? ""
  const urlProviders = validateUuidsString(rawUrlProvidersString)

  useEffect(() => {
    // When the URL changes, update the visible providers
    if (urlProviders && visibleProvidersString !== urlProviders) {
      setVisibleProviders(urlProviders)

      // Make sure all visible providers are selected providers
      const newSelectedProviders = Array.from(
        new Set([...selectedProviders, ...urlProviders.split(",")])
      )
      setSelectedProviders(newSelectedProviders.join(","))
    }
  }, [rawUrlProvidersString, selectedProviders, visibleProvidersString])

  const handleViewChange = (newView: View) => {
    navigateCalendar(RouteStrings.calendar, {
      view: newView,
    })
  }

  return (
    <DragAndDropBigCalendar
      formats={formats}
      localizer={localizer}
      events={events}
      backgroundEvents={availabilityBlocks}
      onNavigate={onSelectDate}
      components={calendarComponentMap}
      resources={showMultiColumnView ? resourcesWithDate : undefined}
      resourceIdAccessor={resourceIdAccessor}
      onSelectEvent={handleSelectEvent}
      onSelectSlot={handleSelectSlot}
      date={date}
      defaultView={view}
      dayLayoutAlgorithm="no-overlap"
      views={views}
      onEventDrop={handleMove}
      onEventResize={handleMove}
      view={view}
      onView={handleViewChange}
      tooltipAccessor={tooltipAccessor}
      onShowMore={navigateToDate}
      scrollToTime={getScrollTimeDayAndWeekView()}
      eventPropGetter={(event) => {
        const eventType = event.resource.type
        if (eventType === "holiday") {
          return {
            className: color.levBlue.dark,
          }
        }

        let colorClassName = ""
        if (event.resource.type === "eventInstance") {
          const eventColor = event.resource.serviceType?.color
            ? calendarColorMap[event.resource.serviceType.color]
            : color.levBlue

          if (event.resource.canceledAt) {
            colorClassName = eventColor.light
            colorClassName += " rbc-event-canceled"
          } else {
            colorClassName = eventColor.dark
          }

          colorClassName += " " + color.interactive
        }

        return {
          className: colorClassName,
          style: { top: 0 },
        }
      }}
      {...calendarVariables}
    />
  )
}
