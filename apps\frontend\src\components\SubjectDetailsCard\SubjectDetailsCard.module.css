.subjectDetailsCard {
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
  padding: 4px 10px;
  border-radius: var(--radius-button-half);
  margin: -4px 10px -4px -10px;
  cursor: pointer;
}

.subjectDetailsCard:not(:has(a:hover)):hover {
  background-color: var(--color-lev-blue-on-white-hover);
}

.subjectDetailsCard:not(:has(a:hover)):focus-visible {
  background-color: var(--color-lev-blue-on-white-hover);
  outline: 2px var(--color-lev-blue) solid;
}

.subjectNameWrap {
  display: flex;
  align-items: center;
}

.subjectInfoSection {
  display: flex;
  gap: 8px;
  align-items: center;
  color: var(--color-blue-gray-violet-dark);
  flex-wrap: wrap;
}

.subjectInfoSection svg {
  flex: 0 0 16px;
  height: 16px;
}

.subjectInfoItem {
  display: flex;
  gap: 4px;
  align-items: center;
  white-space: nowrap;
  transition: background-color 0.1s ease-in-out;
  margin: -4px;
  padding: 4px 8px 4px 4px;
  border-radius: var(--radius-button-half);
}

.loading {
  --colors-loading-gradient-light: var(--color-gray-20);
  --colors-loading-gradient-dark: var(--color-blue-gray-violet-dark);
  composes: loading from "@leviosa/assets/styles/LoadingGradient.module.css";
}
