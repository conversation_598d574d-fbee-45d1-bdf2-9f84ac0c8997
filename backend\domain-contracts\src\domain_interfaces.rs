//! Domain interfaces for infrastructure layer
//!
//! This module contains all domain interfaces that infrastructure needs
//! without creating a dependency on the domain implementation.

use async_trait::async_trait;
use crate::domain_types::*;
use crate::errors::Result;

// National Registry Interface
#[async_trait]
#[mockall::automock]
pub trait DomainNationalRegistry: Send + Sync {
    async fn get_person(
        &self,
        persona_id: PersonaIdIs,
    ) -> Result<Option<DomainNationalRegistryPerson>>;
}

// External Organisation Integration Interface
#[async_trait]
#[mockall::automock]
pub trait ExternalOrganisationIntegration: Send + Sync {
    async fn sync_organisations(&self) -> Result<()>;

    async fn organisations(
        &self,
        filter: FindExternalOrganisationsQuery,
    ) -> Result<Vec<ExternalOrganisation>>;
}

// Text Message Integration Interface
#[async_trait]
#[mockall::automock]
pub trait TextMessageIntegration: Send + Sync {
    async fn send_message(&self, phone_number: &str, message: &str) -> Result<()>;
}

// Doctor Letter and Referral API Interface
#[async_trait]
#[mockall::automock]
pub trait DoctorLetterAndReferralApi: Send + Sync {
    async fn send_letter(&self, letter: OutboundDoctorsLetter) -> Result<()>;
    async fn send_referral(&self, referral: OutboundReferral) -> Result<()>;
}

// Oracle API Interface
#[async_trait]
#[mockall::automock]
pub trait OracleApi: Send + Sync {
    async fn query(&self, query: &str) -> Result<Vec<String>>;
}

// Prescription API Interface
#[async_trait]
#[mockall::automock]
pub trait PrescriptionApi: Send + Sync {
    async fn submit_prescription(&self, prescription: &str) -> Result<()>;
}

// Calendar Notification Service Interface
#[async_trait]
#[mockall::automock]
pub trait CalendarNotificationService: Send + Sync {
    async fn send_notification(&self, message: &str) -> Result<()>;
}

// Notification Service Integration Interface
#[async_trait]
#[mockall::automock]
pub trait DomainNotificationService: Send + Sync {
    async fn send_notification(&self, recipient: &str, message: &str) -> Result<()>;
}

// Auth Repository Interface
#[async_trait]
#[mockall::automock]
pub trait AuthRepo: Send + Sync {
    async fn authenticate(&self, username: &str, password: &str) -> Result<AuthenticatedUser>;
    async fn get_user(&self, user_id: &str) -> Result<Option<AuthenticatedUser>>;
}

// App Authenticator Interface
#[async_trait]
#[mockall::automock]
pub trait AppAuthenticator: Send + Sync {
    async fn authenticate(&self, token: &str) -> Result<AuthenticatedUser>;
}

// JWT Encoder Interface
#[async_trait]
#[mockall::automock]
pub trait JwtEncoder: Send + Sync {
    async fn encode(&self, claims: &AuthData) -> Result<String>;
    async fn decode(&self, token: &str) -> Result<AuthData>;
}

// File Repository Interface
#[async_trait]
#[mockall::automock]
pub trait IFileRepo: Send + Sync {
    async fn store_file(&self, content: &[u8], filename: &str) -> Result<String>;
    async fn get_file(&self, file_id: &str) -> Result<Option<Vec<u8>>>;
}

// Repository Connection Interface
#[async_trait]
#[mockall::automock]
pub trait RepoConnection: Send + Sync {
    async fn begin_transaction(&self) -> Result<()>;
    async fn commit_transaction(&self) -> Result<()>;
    async fn rollback_transaction(&self) -> Result<()>;
}
