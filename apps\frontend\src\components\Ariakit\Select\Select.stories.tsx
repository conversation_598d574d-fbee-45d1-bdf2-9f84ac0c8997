import { Meta, StoryFn } from "@storybook/react-vite"

import { useSelectStore } from "../hooks/useSelectStore/useSelectStore"
import { Select, SelectProps } from "./Select"

export default {
  title: "Ariakit/DefaultSelect",
  component: Select,
} as Meta

export const SelectExample: StoryFn<SelectProps> = (args) => {
  const select = useSelectStore({
    defaultValue: "Apple",
  })

  return <Select {...args} store={select} />
}
