import { LoaderFunctionArgs, json, redirect } from "@remix-run/node"
import { useLoaderData } from "@remix-run/react"

import Welcome from "app/pages/Welcome/Welcome"
import { getSession, getCommitSessionHeaders } from "app/sessions"

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get("Cookie"))
  const checkInData = session.get("checkInData")

  if (!checkInData) {
    return redirect("/checkin")
  }

  // Clear the checkInData from session after we've loaded it
  session.unset("checkInData")

  const headers = await getCommitSessionHeaders(session)
  return json({ checkInData }, headers)
}

export default function WelcomeRoute() {
  const { checkInData } = useLoaderData<typeof loader>()
  return <Welcome checkInData={checkInData} />
}
