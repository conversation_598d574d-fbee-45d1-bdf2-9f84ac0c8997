import { ReactNode } from "react"

import { Heading, Text } from "ui"

import styles from "./ErrorBox.module.css"

type Props = {
  title: ReactNode
  message: string
  // eslint-disable-next-line
  infoObject?: object
  infoString?: string
  fullScreen?: boolean
}

export const ErrorBox = ({
  title,
  message,
  infoObject,
  infoString,
  fullScreen,
}: Props) =>
  fullScreen ? (
    <div className={styles.wrapper}>
      <Heading className={styles.header}>{title}</Heading>
      <Text>{message}</Text>
      {infoObject && (
        <Text>
          <pre>{JSON.stringify(infoObject, null, 2)}</pre>
        </Text>
      )}
      {infoString && (
        <Text>
          <pre>{infoString}</pre>
        </Text>
      )}
    </div>
  ) : (
    <>
      <Heading className={styles.header}>{title}</Heading>
      <Text>{message}</Text>
      {infoObject && (
        <Text>
          <pre>{JSON.stringify(infoObject, null, 2)}</pre>
        </Text>
      )}
      {infoString && (
        <Text>
          <pre>{infoString}</pre>
        </Text>
      )}
    </>
  )
