import { expect } from "vitest"

import { getScrollTimeDayAndWeekView, getScrollTimeAt7 } from "./getScrollTime"

describe("getScrollTime", () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it("getScrollTimeAt7 should always return a date at 07:00", () => {
    const date = new Date(2025, 1, 1)
    vi.setSystemTime(date)
    expect(getScrollTimeAt7().getHours()).toBe(7)

    const dateWithTime = new Date(2025, 1, 1, 1, 1, 1, 1)
    vi.setSystemTime(dateWithTime)
    expect(getScrollTimeAt7().getHours()).toBe(7)
    expect(getScrollTimeAt7().getMinutes()).toBe(0)
    expect(getScrollTimeAt7().getSeconds()).toBe(0)
    expect(getScrollTimeAt7().getMilliseconds()).toBe(0)
  })

  it("getScrollTimeDayAndWeekView should return the correct date 2 hours ago", () => {
    const dateAt10 = new Date(2025, 1, 1, 10, 30)
    vi.setSystemTime(dateAt10)
    expect(getScrollTimeDayAndWeekView().getHours()).toBe(8)
    expect(getScrollTimeDayAndWeekView().getMinutes()).toBe(0)

    const dateAt13 = new Date(2025, 1, 1, 13)
    vi.setSystemTime(dateAt13)
    expect(getScrollTimeDayAndWeekView().getHours()).toBe(11)
  })

  it("getScrollTimeDayAndWeekView should return 7:00, if the hour is before 9:00", () => {
    const dateAt8 = new Date(2025, 1, 1, 8)
    vi.setSystemTime(dateAt8)
    expect(getScrollTimeDayAndWeekView().getHours()).toBe(7)

    const dateAt1 = new Date(2025, 1, 1, 1)
    vi.setSystemTime(dateAt1)
    expect(getScrollTimeDayAndWeekView().getHours()).toBe(7)
  })

  it("getScrollTimeDayAndWeekView should return 14:00, the latest", () => {
    const dateAt14 = new Date(2025, 1, 1, 14, 30)
    vi.setSystemTime(dateAt14)
    expect(getScrollTimeDayAndWeekView().getHours()).toBe(12)
    expect(getScrollTimeDayAndWeekView().getMinutes()).toBe(0)

    const dateAt17 = new Date(2025, 1, 1, 17, 30, 2, 4)
    vi.setSystemTime(dateAt17)
    expect(getScrollTimeDayAndWeekView().getHours()).toBe(14)
    expect(getScrollTimeDayAndWeekView().getMinutes()).toBe(0)
    expect(getScrollTimeDayAndWeekView().getSeconds()).toBe(0)
    expect(getScrollTimeDayAndWeekView().getMilliseconds()).toBe(0)
  })
})
