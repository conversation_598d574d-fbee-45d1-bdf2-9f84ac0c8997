/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/no-empty-function */
import "@testing-library/jest-dom"

import { PowerMenuGroup } from "../../lib/types"

const jestFunction = jest.fn()

export const testGroups: PowerMenuGroup[] = [
  {
    id: "MockGroup1",
    title: "MockGroup1",
    items: [
      {
        id: "MockGroup1Item1",
        title: "MockGroup1Item1",
        execute: () => {},
      },
      {
        id: "MockGroup1Item2",
        title: "MockGroup1Item2",
        execute: () => {},
      },
      {
        id: "MockGroup1Item3",
        title: "MockGroup1Item3",
        execute: () => {},
      },
    ],
  },
  {
    id: "MockGroup2",
    title: "MockGroup2",
    items: [
      {
        id: "MockGroup2Item1",
        title: "MockGroup2Item1",
        execute: () => {},
      },
      {
        id: "MockGroup2Item2",
        title: "MockGroup2Item2",
        execute: () => {},
      },
    ],
  },
]

export const getGroupOfParentItems = (
  handleClick: ReturnType<typeof jestFunction>
) => {
  return [
    {
      id: "Group",
      title: "Group",
      items: [
        {
          id: "Item",
          title: "Item",
          execute: handleClick,
        },
      ],
    },
  ]
}

export const getGroupOfChildItems = (
  handleClick?: ReturnType<typeof jestFunction>
) => {
  return [
    {
      id: "Group",
      title: "Group",
      items: [
        {
          id: "Item",
          title: "Item",
          hasSubMenu: true,
          execute: () => {
            return Promise.resolve([
              {
                id: "SubMockGroup",
                title: "SubMockGroup",
                items: [
                  {
                    id: "SubMockItem1",
                    title: "SubMockItem1",
                    execute: handleClick ? handleClick : () => {},
                  },
                ],
              },
            ])
          },
        },
      ],
    },
  ]
}
