use crate::auth::AuthenticatedUser;
use crate::billing::{GetInvoiceFilter, Invoice};
use crate::errors::CustomError;
use crate::file_repo::{CreateFileCommand, File, FileCategory, FileId, GetFileFilter, IFileRepo};
use crate::pdf_generator::PdfGenerator;
use crate::{
    billing::InvoiceId, command::CommandHandler, errors::Result, permissions::PermissionKey,
    repo_connection::IRepoConnection,
};

pub struct DownloadInvoicePdfCommand {
    pub id: InvoiceId,
}

pub struct DownloadInvoicePdfHandler<'a, T: IRepoConnection> {
    connection: T,
    user: AuthenticatedUser,
    token: String,
    file_repo: &'a dyn IFileRepo,
    pdf_generator: &'a dyn PdfGenerator,
}

#[async_trait::async_trait]
impl<T: IRepoConnection> CommandHandler<DownloadInvoicePdfCommand>
    for DownloadInvoicePdfHandler<'_, T>
{
    type Model = File;

    fn required_permission() -> PermissionKey {
        PermissionKey::Billing_Invoice_Create
    }

    async fn execute(&self, command: DownloadInvoicePdfCommand) -> Result<File> {
        let invoice_repo = self.connection.repos().invoice_repo();

        let invoice = invoice_repo.get(GetInvoiceFilter::Id(command.id)).await?;

        if !invoice.issued() {
            return Err(CustomError::new(
                None,
                "Invoice PDF can not be downloaded because the invoice has not been issued."
                    .to_string(),
            )
            .into());
        }

        let pdf = Self::get_invoice_pdf(
            &invoice,
            &self.user,
            &self.token,
            self.pdf_generator,
            self.file_repo,
        )
        .await?;

        Ok(pdf)
    }
}

impl<'a, T: IRepoConnection> DownloadInvoicePdfHandler<'a, T> {
    pub fn new(
        connection: T,
        user: AuthenticatedUser,
        token: String,
        file_repo: &'a dyn IFileRepo,
        pdf_generator: &'a dyn PdfGenerator,
    ) -> Self {
        Self {
            connection,
            user,
            token,
            file_repo,
            pdf_generator,
        }
    }

    pub async fn get_invoice_pdf(
        invoice: &Invoice,
        user: &AuthenticatedUser,
        token: &str,
        pdf_generator: &dyn PdfGenerator,
        file_repo: &dyn IFileRepo,
    ) -> Result<File> {
        let file_id: FileId = invoice.id().0.into();
        let file_name = format!("Invoice-{}.pdf", invoice.invoice_number());

        let pdf = match file_repo
            .get_optional(&GetFileFilter::Id(file_id), user)
            .await?
        {
            Some(pdf) => pdf,
            None => {
                let pdf = pdf_generator.invoice(leviosa_domain_types::BillingInvoiceId(invoice.id().0), token).await?;
                let metadata = file_repo
                    .create(
                        CreateFileCommand {
                            id: Some(file_id),
                            file_name: file_name.clone(),
                            category: FileCategory::Invoice,
                            content: pdf.clone(),
                        },
                        user,
                    )
                    .await?;

                File {
                    id: metadata.id,
                    metadata,
                    content: pdf,
                }
            }
        };

        Ok(pdf)
    }
}
