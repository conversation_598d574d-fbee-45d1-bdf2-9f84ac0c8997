.invoiceFooter {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

.footerSpacing {
  display: grid;
  grid-row-gap: 16px;
}

.footerSpacing > * {
  color: var(--LEV-Blue-Gray-violet-dark, #8386b8);
}

.footerPayableInfo {
  justify-items: end;
}

.footerSpacing > * > span,
.nhiPayableBySubject span {
  color: var(--Primary-Text, #181c75);
}

.switch {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: end;
}

.switchLabelWithTooltip {
  display: flex;
  gap: 4px;
}

.nhiPayableBySubject {
  margin-top: 16px;
}

.numberInput {
  margin-top: 8px;
}
