import { DashboardSelectedTagMap } from "features/dashboard/components/Dashboard/DashboardTags/DashboardTags"

/**
 * Filters dashboard rows based on selected tags.
 *
 * @param selectedTags - A map of tags to their inclusion status.
 *                       The value `1` indicates the tag should be included,
 *                       and `-1` indicates the tag should be excluded.
 * @returns A function that takes a row object with a `tags` property (an array of strings)
 *          and returns a boolean indicating whether the row should be included based on the selected tags.
 *
 * The filtering logic is as follows:
 * - If there are tags to include and tags to exclude, the row must have at least one included tag and no excluded tags.
 * - If there are only tags to include, the row must have at least one included tag.
 * - If there are only tags to exclude, the row must not have any excluded tags.
 * - If there are no tags to include or exclude, the row is included by default.
 *
 * @example
 * const selectedTags = { tag1: 1, tag2: -1, tag3: 1 };
 * const filter = filterDashboardByTag(selectedTags);
 * const row = { tags: ['tag1', 'tag4'] };
 * const result = filter(row); // true, because 'tag1' is included and 'tag2' is not present
 */
export default function filterDashboardByTag(
  selectedTags: DashboardSelectedTagMap
) {
  return function (row: { tags: string[] }) {
    const includeTags = Object.entries(selectedTags)
      .filter(([, included]) => included === 1)
      .map(([tag]) => tag)

    const excludeTags = Object.entries(selectedTags)
      .filter(([, included]) => included === -1)
      .map(([tag]) => tag)

    if (includeTags.length > 0 && excludeTags.length > 0) {
      return (
        row.tags.some((tag) => includeTags.includes(tag)) &&
        !row.tags.some((tag) => excludeTags.includes(tag))
      )
    }

    if (includeTags.length > 0) {
      return row.tags.some((tag) => includeTags.includes(tag))
    }

    if (excludeTags.length > 0) {
      return !row.tags.some((tag) => excludeTags.includes(tag))
    }

    return true
  }
}
