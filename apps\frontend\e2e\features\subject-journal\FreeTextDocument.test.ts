import { test, expect, Page } from "@playwright/test"

import { login } from "../utils/authenticationUtils"
import { openSubjectJournalForSubject } from "../utils/subjectJournalTestUtils"

/* eslint-disable playwright/no-wait-for-timeout */

test.describe("Free Text Document supplement tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/")
    await page.waitForTimeout(5000)
  })

  const menuItem = "Free text document"
  const defaultTitle = "Untitled Document"
  const title = "My document"
  const descriptionText = "This is the content of my document"

  const arrange = async (page: Page, subject: string) => {
    await login(page)
    await page.waitForTimeout(1000)
    await openSubjectJournalForSubject(page, subject)
    await page.waitForTimeout(3000)
  }

  test("Add free text doc using plus button", async ({ page }) => {
    // Arrange
    await arrange(page, "<PERSON> Carrigan")

    // Act
    const numberOfDocsBefore = await page.getByText(defaultTitle).count()

    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: menuItem }).click()
    await page.waitForTimeout(800)

    const numberOfDocsAfter = await page.getByText(defaultTitle).count()

    // Assert
    await expect(numberOfDocsAfter).toBe(numberOfDocsBefore + 1)
  })

  // Skipping test because there is a bug that you have to click three times
  // on the button if the block is not in focus - see DEV-4302
  test.skip("Cannot submit empty document", async ({ page }) => {
    // Arrange
    await arrange(page, "Paul Evans")

    // Act
    const numberOfSubmittedBefore = await page
      .locator('[class*="Supplement_confirmed"]')
      .count()

    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: menuItem }).click()
    await page.waitForTimeout(400)
    await page.getByTestId("document-submit").first().click()
    await page.waitForTimeout(400)

    const numberOfSubmittedAfter = await page
      .locator('[class*="Supplement_confirmed"]')
      .count()

    // Assert
    await expect(numberOfSubmittedAfter).toBe(numberOfSubmittedBefore + 1)
  })

  test("Values are submitted", async ({ page }) => {
    // Arrange
    await arrange(page, "Ruth Cruz")
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: menuItem }).click()

    // Act
    await page.getByTestId("document-title").locator("input").fill(title)
    await page
      .getByTestId("document-description")
      .locator("textarea")
      .fill(descriptionText)
    await page.getByTestId("document-submit").first().focus()
    await page.waitForTimeout(1500)
    await page.getByTestId("document-submit").first().click()
    await page.waitForTimeout(800)

    // Assert
    const description = page.locator('[class*="ViewFreeText_description"]')
    await expect(page.locator("text=My document")).toBeVisible()
    await expect(description).toHaveText(descriptionText)
  })

  test("Edit an existing document", async ({ page }) => {
    // Arrange
    await arrange(page, "Paul Evans")

    // create certificate
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: menuItem }).click()
    await page.getByTestId("document-title").locator("input").fill(title)
    await page
      .getByTestId("document-description")
      .locator("textarea")
      .fill(descriptionText)
    // save the content by focusing on the button
    await page.getByTestId("document-submit").first().focus()
    await page.waitForTimeout(800)

    // Act
    // navigate away and back to the page
    await openSubjectJournalForSubject(page, "Evelyn Carrigan")
    await openSubjectJournalForSubject(page, "Paul Evans")

    // edit existing certificate
    await page
      .getByTestId("document-title")
      .locator("input")
      .fill(title + " 2")
    await page
      .getByTestId("document-description")
      .locator("textarea")
      .fill(descriptionText + " 2")
    await page.getByTestId("document-submit").first().focus()
    await page.waitForTimeout(1500)
    await page.getByTestId("document-submit").first().click()
    await page.waitForTimeout(800)

    // Assert
    const description = page.locator('[class*="ViewFreeText_description"]')
    await expect(page.locator("text=My document 2")).toBeVisible()
    await expect(description).toHaveText("This is the content of my document 2")
  })

  // Skipping test because there is a bug that you have to click three times
  // on the button if the block is not in focus - see DEV-4302
  test.skip("Delete a free text doc", async ({ page }) => {
    // Arrange
    await arrange(page, "Evelyn Carrigan")
    // create a certificate
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: menuItem }).click()
    await page.waitForTimeout(500)

    const numberOfDocsBefore = await page.getByText(defaultTitle).count()

    // Act
    await page.getByTestId("document-delete").first().click()
    await page.waitForTimeout(1000)

    await page.getByRole("button", { name: "Delete" }).click()
    await page.waitForTimeout(2000)

    // Assert
    const numberOfDocsAfter = await page.getByText(defaultTitle).count()

    // Assert
    await expect(numberOfDocsAfter).toBe(numberOfDocsBefore - 1)
  })
})
