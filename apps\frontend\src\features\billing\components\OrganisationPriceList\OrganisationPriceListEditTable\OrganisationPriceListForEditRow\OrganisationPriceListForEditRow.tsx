import { CompositeItem, CompositeStore } from "@ariakit/react"
import { useState } from "react"

import { PriceList } from "../../OrganisationPriceList.context"
import styles from ".././OrganisationPriceListEditTable.module.css"
import { priceItemKeys } from "../OrganisationPriceListEditTable"
import { RenderInput } from "./RenderInput"

type OrganisationPriceListForEditRowProps = {
  priceListItem: PriceList[0]
  selectedPriceItemIds: string[]

  composite: CompositeStore

  onCheck: () => void
}

export const OrganisationPriceListForEditRow = ({
  priceListItem,
  selectedPriceItemIds,
  composite,
  onCheck,
}: OrganisationPriceListForEditRowProps) => {
  const [type, setType] = useState(priceListItem.type)

  if (priceListItem.__typename !== "BillingCodeClinicSpecific") return null

  return (
    <CompositeItem render={<tr></tr>} key={priceListItem.id} store={composite}>
      <td>
        <input
          className={styles.checkbox}
          type={"checkbox"}
          checked={selectedPriceItemIds.includes(priceListItem.id)}
          onChange={onCheck}
        />
      </td>
      {priceItemKeys
        .filter((el) => el !== "type" && el !== "id")
        .map((propertyKey) => {
          return (
            <td key={propertyKey} data-key={propertyKey}>
              {/* {key === "currency" ? (
                <SelectCurrency
                // COMEBACK when more currencies are added
                // className={styles.selectInput}
                // defaultValue={priceListItem[key] || ""}
                // onBlur={() => {
                //   if (type !== "CREATE") setType("EDIT")
                // }}
                />
              ) : (
                <Input
                  inputProps={{
                    className: styles.input,
                  }}
                  step={key === "vat" ? 0.01 : undefined}
                  className={styles.inputWrap}
                  data-key={key}
                  name={key}
                  autoComplete={"off"}
                  min={0}
                  max={maxValue}
                  type={isNumeric ? "number" : "text"}
                  label={key}
                  hideLabel
                  hideMessage
                  defaultValue={priceListItem[key] || ""}
                  onBlur={() => {
                    if (type !== "CREATE") setType("EDIT")
                  }}
                />
              )} */}
              <RenderInput
                propertyKey={propertyKey}
                onBlur={() => {
                  if (type !== "CREATE") setType("EDIT")
                }}
                priceListItem={priceListItem}
              />
            </td>
          )
        })}
      {/* Id, type properties has been used in submit logic of form */}
      <td className={styles.hiddenRow}>
        <input
          type="hidden"
          name="type"
          defaultValue={type}
          data-key="type"
          readOnly
          aria-hidden
        />
      </td>
      <td className={styles.hiddenRow}>
        <input
          type="hidden"
          name="id"
          defaultValue={priceListItem.id}
          readOnly
          aria-hidden
        />
      </td>
    </CompositeItem>
  )
}
