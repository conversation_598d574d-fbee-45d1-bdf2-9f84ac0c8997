import { PaymentStatus } from "generated/graphql"

import { Invoices } from "./EventInvoiceIndicator"

type Invoice = Invoices[0]

const statusPriority = {
  [PaymentStatus.Unpaid]: 1,
  [PaymentStatus.ClaimCreated]: 2,
  [PaymentStatus.Paid]: 3,
}

// Find invoice in invoice list with the  "lowest" status
// This is the statuses in order of priority: Draft (not issued), Unpaid, Claim created, Paid
export const getInvoiceWithLowestStatus = (
  invoices: Invoice[]
): PaymentStatus | "draft" | null => {
  let lowestInvoice: Invoice | null = null

  for (const invoice of invoices) {
    if (!invoice.issued) {
      lowestInvoice = invoice
      break
    }

    if (
      !lowestInvoice ||
      statusPriority[invoice.paymentStatus] <
        statusPriority[lowestInvoice.paymentStatus]
    ) {
      lowestInvoice = invoice
    }
  }

  if (!lowestInvoice) return null

  return !lowestInvoice.issued ? "draft" : lowestInvoice.paymentStatus
}
