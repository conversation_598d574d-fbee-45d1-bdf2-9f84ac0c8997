import { CalendarColor, ServiceTypeModality, Weekday } from "generated/graphql"

export const getExpectedDayViewOutput = [
  {
    title: "",
    start: new Date(2024, 6, 16, 10, 0, 0, 0),
    end: new Date(2024, 6, 16, 12, 0, 0, 0),
    allDay: false,
    resource: {
      __typename: "ServiceTypeAvailability",
      id: "a1",
      appointmentDurationMinutes: 30,
      isDeleted: false,
      isBookableOnline: true,
      blockRules: [
        {
          __typename: "AvailabilityBlockRule",
          id: "r1",
          weekday: Weekday.Tuesday,
          fromTime: "10:00",
          toTime: "12:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r2",
          weekday: Weekday.Wednesday,
          fromTime: "14:00",
          toTime: "16:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r3",
          weekday: Weekday.Friday,
          fromTime: "09:00",
          toTime: "11:00",
          isDeleted: false,
        },
      ],
      serviceType: {
        __typename: "ExternalServiceType",
        id: "s1",
        name: "Service 1",
        color: CalendarColor.Pink,
        description: "Service 1 description",
        modality: ServiceTypeModality.Onsite,
        deletedAt: null,
      },
      team: null,
      type: "availability",
      blockId: "r1",
    },
  },
]
