import { FormGroup } from "@ariakit/react"
import { Meta, StoryFn } from "@storybook/react-vite"

import Button from "ui/components/Button/Button"

import { useSelectStore } from "../Ariakit/hooks"
import Select, { SelectProps } from "./Select"
import list from "./list"

export default {
  title: "Form Components/Select",
  component: Select,
} as Meta

export const SelectDefault: StoryFn<SelectProps<"div", "string">> = (args) => {
  const selectStore = useSelectStore({
    defaultValue: "Apple",
    focusLoop: "vertical",
  })

  return (
    <Select
      {...args}
      options={list}
      selectStore={selectStore}
      onSelect={(value) => {
        console.log(value)
      }}
    />
  )
}

export const SelectMulti: StoryFn<SelectProps<"div", "string">> = (args) => {
  const selectStore = useSelectStore({
    defaultValue: [],
    focusLoop: "vertical",
    sameWidth: true,
  })

  return (
    <Select
      {...args}
      options={list}
      selectStore={selectStore}
      onSelect={(value) => {
        console.log(value)
      }}
    />
  )
}

export const SelectFormSubmit: StoryFn<SelectProps<"div", "string">> = (
  args
) => {
  const selectStore = useSelectStore({
    defaultValue: "",
    focusLoop: "vertical",
    sameWidth: true,
  })

  const onFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())
    // const selectedOptions = formData.getAll("fruits")

    console.log(data)
  }

  return (
    <FormGroup as="form" onSubmit={onFormSubmit}>
      <Select
        {...args}
        options={list}
        selectStore={selectStore}
        onSelect={(value) => {
          console.log(value)
        }}
      />

      <Button type="submit">Submit</Button>
    </FormGroup>
  )
}

export const SelectMultiFormSubmit: StoryFn<SelectProps<"div", "string">> = (
  args
) => {
  const selectStore = useSelectStore({
    defaultValue: [],
    focusLoop: "vertical",
    sameWidth: true,
  })

  const onFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const selectedOptions = formData.getAll("fruits")
    console.log(selectedOptions)
    console.log(data)
  }

  return (
    <FormGroup as="form" onSubmit={onFormSubmit}>
      <Select
        {...args}
        options={list}
        selectStore={selectStore}
        onSelect={(value) => {
          console.log(value)
        }}
      />

      <Button type="submit">Submit</Button>
    </FormGroup>
  )
}

SelectDefault.args = {
  label: "Test",
}
SelectDefault.parameters = {
  layout: "centered",
}

SelectMulti.args = {
  label: "Test",
}
SelectMulti.parameters = {
  layout: "centered",
}

SelectFormSubmit.args = {
  label: "Test",
  name: "fruits",
}

SelectMultiFormSubmit.args = {
  label: "Test",
  name: "fruits",
}
