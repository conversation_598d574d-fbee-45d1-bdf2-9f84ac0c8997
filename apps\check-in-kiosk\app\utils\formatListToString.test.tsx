import formatListToString from "./formatListToString"

describe("formatListToString", () => {
  it("should return empty string for empty array", () => {
    const result = formatListToString([], "and")
    expect(result).toBe("")
  })

  it("should return the single item if array has one item", () => {
    const result = formatListToString(["item1"], "and")
    expect(result).toBe("item1")
  })

  it("should join two items with the separator", () => {
    const result = formatListToString(["item1", "item2"], "and")
    expect(result).toBe("item1 and item2")
  })

  it("should join multiple items with commas and separator", () => {
    const result = formatListToString(["item1", "item2", "item3"], "and")
    expect(result).toBe("item1, item2 and item3")
  })

  it("should handle null input by returning empty string", () => {
    const result = formatListToString(null as unknown as string[], "and")
    expect(result).toBe("")
  })
})
