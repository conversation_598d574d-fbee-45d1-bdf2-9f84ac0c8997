.menuPopover {
  width: 900px;
  max-height: min(var(--popover-available-height, 450px), 450px);
  position: relative;
  padding: 0;
}

.menuSpinner {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
}

.menuButton {
  padding: 0;
  margin: 0;
  border: none;
  box-shadow: none;
  background-color: transparent;
  display: flex;
  cursor: pointer;
  transition: color 0.1s;
}
.menuButton svg {
  color: var(--color-lev-blue);
  transition:
    color 0.1s,
    fill 0.1s;
}

.menuButton:hover svg,
.menuButton[aria-expanded="true"] svg {
  fill: var(--color-lev-blue);
  color: white;
}
.menuButton:active {
  color: var(--color-lev-blue);

  svg {
    fill: var(--color-lev-blue);
  }
}

.label {
  margin-left: 8px;
}

/* Select breaks table size, which leads to action icons not being centralized. */
@media screen and (max-width: 1706px) {
  .menuButton[data-has-icon="false"] {
    width: 200px;
  }
}

.comboboxGroup {
  display: grid;
  grid-template-columns: 38px 1fr max-content; /* Add back when we add favorites 48px; */
  gap: 0 8px;
}

.comboboxGroupLabel {
  grid-column: 1 / -1;
}

.menuItem,
.emptyItem {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: subgrid;
}
.emptyItem {
  grid-template-columns: 1fr;
}
