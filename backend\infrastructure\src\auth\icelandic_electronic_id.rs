use anyhow::Result;
use async_trait::async_trait;
use base64::{engine::general_purpose::URL_SAFE_NO_PAD, Engine};
use isahc::{AsyncReadResponseExt, RequestExt};
use leviosa_domain_contracts::auth::{ElectronicId, ElectronicIdUserInfo};
use rand::Rng;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use sha2::{Digest, Sha256};
use std::{collections::HashMap, thread, time::Duration};

#[derive(Clone)]
pub struct IcelandicElectronicId {
    base_url: String,
    client_id: String,
    client_secret: String,
}

#[async_trait]
impl ElectronicId for IcelandicElectronicId {
    async fn login(&self, phone_number: &str) -> Result<ElectronicIdUserInfo> {
        let code_verifier = Self::create_code_verifier();
        let code_challenge = Self::create_code_challenge(&code_verifier);

        let step1_result = self.step_1().await?;
        let step2_result = self.step_2(phone_number, step1_result).await?;
        let step3_result = self.step_3(step2_result).await?;
        let step4_result = self.step_4(&step3_result, &code_challenge).await?;
        let step5_result = self
            .step_5(&step3_result, step4_result, &code_verifier)
            .await?;
        let step6_result = self.step_6(&step3_result, &step5_result).await?;

        Ok(ElectronicIdUserInfo {
            name: step6_result.name,
            national_register_id: step6_result.national_register_id,
        })
    }
}

impl IcelandicElectronicId {
    pub fn new(base_url: String, client_id: String, client_secret: String) -> Self {
        Self {
            base_url,
            client_id,
            client_secret,
        }
    }

    /// In Step 1 we create an authentication session and
    /// Step 1 returns an authId that identifies the session.
    async fn step_1(&self) -> Result<Step1Response> {
        let base_url = self.base_url.as_str();

        let url = format!("https://{base_url}/sso/json/realms/root/realms/audkenni/authenticate?authIndexType=service&authIndexValue=api_v201");

        let request = isahc::Request::post(&url)
            .header("content-type", "application/json")
            .header("accept-api-version", "resource=2.0,protocol=1.0")
            .body("{}")
            .map_err(anyhow::Error::new)?;

        let res = request
            .send_async()
            .await
            .map_err(anyhow::Error::new)?
            .json::<Step1Response>()
            .await
            .map_err(anyhow::Error::new)?;

        Ok(res)
    }

    /// In Step 2 we send the authId from Step 1 and the users phone number.
    /// This will trigger an authentication request on the users phone.
    /// Step 2 returns an authId that can be used to poll for the result of the authentication.
    async fn step_2(
        &self,
        phone_number: &str,
        step_1_response: Step1Response,
    ) -> Result<Step2Response> {
        let base_url = self.base_url.as_str();

        let url = format!("https://{base_url}/sso/json/realms/root/realms/audkenni/authenticate?authIndexType=service&authIndexValue=api_v201");

        let body = serde_json::json!({
            "authId": step_1_response.auth_id,
            "callbacks": populate_callbacks(&self.client_id, phone_number)
        })
        .to_string();

        let request = isahc::Request::post(&url)
            .header("content-type", "application/json")
            .header("accept-api-version", "resource=2.0,protocol=1.0")
            .body(body)
            .map_err(anyhow::Error::new)?;

        let res = request
            .send_async()
            .await
            .map_err(anyhow::Error::new)?
            .json::<Step2Response>()
            .await
            .map_err(anyhow::Error::new)?;

        Ok(res)
    }

    /// Step 3 uses the authId and callbacks from Step 2 to poll for the result of the authentication.
    async fn step_3(&self, step_2_response: Step2Response) -> Result<Step3SuccessResponse> {
        let base_url = self.base_url.as_str();

        let url = format!("https://{base_url}/sso/json/realms/root/realms/audkenni/authenticate");

        let mut body = serde_json::to_vec(&step_2_response).map_err(anyhow::Error::new)?;

        loop {
            let request = isahc::Request::post(&url)
                .header("content-type", "application/json")
                .header("accept-api-version", "resource=2.0,protocol=1.0")
                .body(body)
                .map_err(anyhow::Error::new)?;

            let res = request
                .send_async()
                .await
                .map_err(anyhow::Error::new)?
                .json::<Step3Response>()
                .await
                .map_err(anyhow::Error::new)?;

            match res {
                Step3Response::Waiting(res) => {
                    body = serde_json::to_vec(&res).map_err(anyhow::Error::new)?;
                    thread::sleep(Duration::from_millis(500));
                }
                Step3Response::Success(res) => {
                    return Ok(res);
                }
                Step3Response::Other(_) => {
                    return Err(anyhow::anyhow!("Unknown response"));
                }
            }
        }
    }

    /// In Step 4 we provide the server with a code challenge and retrieve an authentication code for step 5.
    /// The authentication code is returned in the location header. The location header is a uri that contains
    /// a query parameter with the authentication code.
    /// Step 4 requires the `token_id` from Step 3, it's sent to the server as a cookie.
    async fn step_4(
        &self,
        step_3_response: &Step3SuccessResponse,
        code_challenge: &str,
    ) -> Result<String> {
        let client_id = self.client_id.as_str();
        let base_url = self.base_url.as_str();

        // Not currently using the state and redirect_uri parameters: &state=abc123&redirect_uri=http://localhost:3000/callback
        let url_parameters = format!("?service=api_v201&client_id={client_id}&response_type=code&scope=openid%20profile&code_challenge={code_challenge}&code_challenge_method=S256");
        let url = format!(
            "https://{base_url}/sso/oauth2/realms/root/realms/audkenni/authorize{url_parameters}"
        );

        let token_id = &step_3_response.token_id;
        let cookie = format!("audsso={token_id}; audssossolb=03; audsso={token_id}");

        let request = isahc::Request::get(&url)
            .header("content-type", "application/x-www-form-urlencoded")
            .header("cookie", cookie)
            .body("")
            .map_err(anyhow::Error::new)?;

        let res = request.send_async().await.map_err(anyhow::Error::new)?;

        let location_uri = res
            .headers()
            .get("location")
            .ok_or_else(|| anyhow::anyhow!("Step 4 failed, location header missing"))?
            .to_str()
            .map_err(anyhow::Error::new)?;

        let url = reqwest::Url::parse(location_uri).map_err(anyhow::Error::new)?;
        let query_pairs = url.query_pairs().into_owned().collect::<HashMap<_, _>>();
        let code = query_pairs
            .get("code")
            .ok_or_else(|| anyhow::anyhow!("Step 4 failed, code missing"))?;

        Ok(code.into())
    }

    /// In Step 5 we provide the server with a code challenge and retrieve an authentication code for step 5.
    /// The authentication code is returned in the location header. The location header is a uri that contains
    /// a query parameter with the authentication code.
    /// Step 4 requires the ``token_id`` from Step 3, it's sent to the server as a cookie.
    async fn step_5(
        &self,
        step_3_response: &Step3SuccessResponse,
        step_4_result: String,
        code_verifier: &str,
    ) -> Result<Step5Response> {
        let base_url = self.base_url.as_str();
        let token_id = &step_3_response.token_id;

        let mut url = reqwest::Url::parse(&format!(
            "https://{base_url}/sso/oauth2/realms/root/realms/audkenni/access_token"
        ))
        .map_err(anyhow::Error::new)?;

        url.query_pairs_mut()
            .append_pair("grant_type", "authorization_code")
            .append_pair("client_id", &self.client_id)
            .append_pair("code_verifier", code_verifier)
            .append_pair("code", &step_4_result)
            .append_pair("client_secret", &self.client_secret);

        let request = isahc::Request::post(&url.to_string())
            .header("content-type", "application/x-www-form-urlencoded")
            .header(
                "cookie",
                format!("audsso={token_id}; audssossolb=03; audsso={token_id}"),
            )
            .body("")
            .map_err(anyhow::Error::new)?;

        let res = request
            .send_async()
            .await
            .map_err(anyhow::Error::new)?
            .json::<Step5Response>()
            .await
            .map_err(anyhow::Error::new)?;

        Ok(res)
    }

    /// In Step 6 we use the access token retrieved in Step 5 to get the user's information.
    async fn step_6(
        &self,
        step_3_response: &Step3SuccessResponse,
        step_5_response: &Step5Response,
    ) -> Result<Step6Response> {
        let base_url = self.base_url.as_str();
        let access_token = &step_5_response.access_token;
        let token_id = &step_3_response.token_id;

        let url = reqwest::Url::parse(&format!(
            "https://{base_url}/sso/oauth2/realms/root/realms/audkenni/userinfo"
        ))
        .map_err(anyhow::Error::new)?;

        let request = isahc::Request::post(&url.to_string())
            .header("content-type", "application/x-www-form-urlencoded")
            .header("authorization", format!("Bearer {access_token}"))
            .header(
                "cookie",
                format!("audsso={token_id}; audssossolb=03; audsso={token_id}"),
            )
            .body("")
            .map_err(anyhow::Error::new)?;

        let value = request
            .send_async()
            .await
            .map_err(anyhow::Error::new)?
            .json::<Value>()
            .await
            .map_err(anyhow::Error::new)?;

        let res = serde_json::from_value(value).map_err(anyhow::Error::new)?;

        Ok(res)
    }

    fn create_code_verifier() -> String {
        let mut rng = rand::thread_rng();
        let mut bytes = [0u8; 32];
        rng.fill(&mut bytes);
        URL_SAFE_NO_PAD.encode(bytes)
    }

    fn create_code_challenge(code_verifier: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(code_verifier);
        let result = hasher.finalize();
        URL_SAFE_NO_PAD.encode(result)
    }
}

#[derive(Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
struct Step1Response {
    auth_id: String,
}

#[derive(Deserialize, Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct Step2Response {
    auth_id: String,
    callbacks: Vec<serde_json::Value>,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(untagged)]
enum Step3Response {
    Waiting(Step2Response),
    Success(Step3SuccessResponse),
    Other(serde_json::Value),
}

#[derive(Deserialize, Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct Step3SuccessResponse {
    realm: String,
    success_url: String,
    token_id: String,
}

#[derive(Deserialize, Serialize, Debug)]
struct Step5Response {
    access_token: String,
    id_token: String,
    expires_in: i32,
    scope: String,
    token_type: String,
}

#[derive(Deserialize, Serialize, Debug)]
#[serde(rename_all = "camelCase")]
struct Step6Response {
    national_register_id: String,
    name: String,
    sub: String,
    subname: String,
}

#[allow(clippy::too_many_lines)]
pub fn populate_callbacks(client_id: &str, phone_number: &str) -> Value {
    let message_to_user = "Innskráning í Leviosa";

    json!([
            {
                "type": "NameCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Sláðu inn clientId"
                    }
                ],
                "input": [
                    {
                        "name": "IDToken1",
                        "value": client_id
                    }
                ],
                "_id": 0
            },
            {
                "type": "NameCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Sláðu inn Related Party"
                    }
                ],
                "input": [
                    {
                        "name": "IDToken2",
                        "value": ""
                    }
                ],
                "_id": 1
            },
            {
                "type": "NameCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Sláðu inn símanúmer eða kennitölu"
                    }
                ],
                "input": [
                    {
                        "name": "IDToken3",
                        "value": phone_number
                    }
                ],
                "_id": 2
            },
            {
                "type": "NameCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Sláðu inn skilaboð til notanda"
                    }
                ],
                "input": [
                    {
                        "name": "IDToken4",
                        "value": message_to_user
                    }
                ],
                "_id": 3
            },
            {
                "type": "NameCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Nota vchoice (true eða false)"
                    }
                ],
                "input": [
                    {
                        "name": "IDToken5",
                        "value": "false"
                    }
                ],
                "_id": 4
            },
            {
                "type": "NameCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Nota confirmMessage (true eða false)"
                    }
                ],
                "input": [
                    {
                        "name": "IDToken6",
                        "value": "false"
                    }
                ],
                "_id": 5
            },
            {
                "type": "NameCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Sláðu inn Hash gildi"
                    }
                ],
                "input": [
                    {
                        "name": "IDToken7",
                        "value": ""
                    }
                ],
                "_id": 6
            },
            {
                "type": "ChoiceCallback",
                "output": [
                    {
                        "name": "prompt",
                        "value": "Veldu auðkenningarleið"
                    },
                    {
                        "name": "choices",
                        "value": [
                            "sim",
                            "card",
                            "app",
                            "cardnew",
                            "cardold"
                        ]
                    },
                    {
                        "name": "defaultChoice",
                        "value": 0
                    }
                ],
                "input": [
                    {
                        "name": "IDToken8",
                        "value": 0
                    }
                ],
                "_id": 7
            }
    ])
}

#[cfg(test)]
mod tests {
    use super::ElectronicId;

    #[tokio_shared_rt::test]
    #[ignore]
    async fn debug() {
        let base_url = String::from("tjwlj.audkenni.is:443");
        let client_id = String::from("leviosaApiTest");
        let client_secret = String::from("leviosaApiTestSecret");

        let electronic_id =
            super::IcelandicElectronicId::new(base_url, client_id, client_secret.clone());

        let result = electronic_id.login("8462862").await.unwrap();

        println!("\nresult name:\n\n{:?}", result.name);
        println!(
            "\nresult national registration number:\n\n{:?}\n",
            result.national_register_id
        );
    }
}
