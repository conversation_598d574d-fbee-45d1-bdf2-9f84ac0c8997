import StiLogo from "@leviosa/assets/svg/STI_Logo.svg?react"

import { Text } from "ui"

import styles from "./PatientInvoiceSelectItemInfo.module.css"

type PatientInvoiceSelectItemInfoProps = {
  label: string
  description: string
  hasStarred: boolean
  hasNationalInsurance: boolean
  category?: string
}

export const PatientInvoiceSelectItemInfo = ({
  label,
  hasStarred,
  description,
  category,
  hasNationalInsurance,
}: PatientInvoiceSelectItemInfoProps) => {
  return (
    <>
      {hasNationalInsurance && (
        <StiLogo
          className={styles.nationalHealthInsuranceIcon}
          data-is-starred={hasStarred}
        />
      )}
      <Text className={styles.label}>
        {label}
        {category && (
          <Text secondary as="span">
            {" "}
            {category}
          </Text>
        )}
      </Text>
      {description && <Text size="small">{description}</Text>}
    </>
  )
}
