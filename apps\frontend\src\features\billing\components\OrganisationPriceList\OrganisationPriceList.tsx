import { useTranslation } from "react-i18next"
import { Link, generatePath } from "react-router-dom"

import Icon from "components/Icon/Icon"
import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { RouteStrings } from "routes/RouteStrings"
import { Button } from "ui"

import { PermissionKey } from "generated/graphql"

import { CenteredLayout } from "../CenteredLayout/CenteredLayout"
import { SearchInput } from "../SearchInput/SearchInput"
import { useOrganisationPriceList } from "./OrganisationPriceList.context"
import { OrganisationPriceListHeader } from "./OrganisationPriceListHeader/OrganisationPriceListHeader"
import { OrganisationPriceListTable } from "./OrganisationPriceListTable/OrganisationPriceListTable"
import styles from "./PriceList.module.css"

const OrganisationPriceListWithoutPermission = () => {
  const queryParams = new URLSearchParams(location.search)

  const { priceList, resetPriceList } = useOrganisationPriceList()

  const { hasPermission } = usePermissions()

  const hasBillingCodeCreatePermission = hasPermission(
    PermissionKey.BillingBillingCodeCreate
  )

  const { t } = useTranslation()

  return (
    <CenteredLayout>
      <OrganisationPriceListHeader>
        {hasBillingCodeCreatePermission && (
          <Button
            as={Link}
            icon={<Icon name={"pencil-line"} />}
            to={{
              pathname: generatePath(RouteStrings.organisationPriceListEdit),
              search: queryParams.toString(),
            }}
          >
            {t("Edit")}
          </Button>
        )}
      </OrganisationPriceListHeader>

      <SearchInput
        onChange={() => resetPriceList()}
        label={t("Search Price Item")}
        className={styles.input}
      />

      <OrganisationPriceListTable priceList={priceList} />
    </CenteredLayout>
  )
}

export const OrganisationPriceList = () => {
  return (
    <Restricted
      to={PermissionKey.BillingBillingCodeView}
      fallback={<UnauthorizedPage />}
    >
      <OrganisationPriceListWithoutPermission />
    </Restricted>
  )
}

export default OrganisationPriceList
