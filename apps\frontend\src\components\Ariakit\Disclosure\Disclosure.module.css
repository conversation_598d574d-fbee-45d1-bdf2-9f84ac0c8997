.disclosure .collapseExpandIcon {
  transition: rotate 0.4s;
}

.disclosure[aria-expanded="true"] .collapseExpandIcon {
  rotate: 180deg;
}

.disclosureContent {
  /* important prop is necessary, because of ariakit inline styles */
  display: grid !important;
  grid-template-rows: 0fr;
  transition: grid-template-rows 0.4s ease-out;
}

.disclosureContent[data-enter] {
  grid-template-rows: 1fr;
}

.disclosureContentInner {
  overflow-y: hidden;
}
