import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite"

// import { iconNames } from "@leviosa/assets"

import Grid from "../../ui/components/Layout/Grid"
import { Text } from "../../ui/components/typography/Text/Text"
import Icon from "./Icon"

const iconNames = [
  "add-box-line",
  "add-circle-line",
  "add-line",
  "alert-line",
  "arrow-down-double-line",
  "arrow-down-s-line",
  "arrow-go-back-line",
  "arrow-go-forward-line",
  "arrow-left-double-line",
  "arrow-left-s-line",
  "arrow-right-double-line",
  "arrow-right-s-line",
  "arrow-up-double-line",
  "arrow-up-s-line",
  "at-line",
  "attachment-line",
  "bold",
  "calendar-2-line",
  "capsule-line",
  "chat-2-line",
  "check-line",
  "checkbox-blank-circle-line",
  "checkbox-blank-line",
  "checkbox-circle-fill",
  "checkbox-circle-line",
  "checkbox-indeterminate-line",
  "checkbox-line",
  "clipboard-line",
  "close-circle-fill",
  "close-circle-line",
  "close-line",
  "code-line",
  "collapse-diagonal-line",
  "corner-down-right-line",
  "delete-bin-fill",
  "delete-bin-line",
  "divider",
  "download-2-line",
  "draft-line",
  "edit-line",
  "emotion-happy-line",
  "emotion-normal-line",
  "emotion-unhappy-line",
  "equal-line",
  "error-warning-line",
  "excalidraw-line",
  "expand-diagonal-line",
  "external-link-line",
  "eye-line",
  "eye-off-line",
  "file-add-line",
  "file-copy-line",
  "file-edit-line",
  "file-list-2-fill",
  "file-list-2-line",
  "file-list-line",
  "file-text-line",
  "filter-line",
  "folder-5-line",
  "folder-add-line",
  "folder-check-line",
  "folder-cloud-line",
  "folder-line",
  "folder-zip-line",
  "font-size",
  "git-branch-line",
  "global-line",
  "group-line",
  "hashtag",
  "heading1",
  "heading2",
  "heading3",
  "heart-pulse-line",
  "history-line",
  "hospital-line",
  "id-card-line",
  "image-line",
  "inbox-2-line",
  "inbox-bullet-line",
  "indent-decrease",
  "indent-increase",
  "information-line",
  "italic",
  "key-line",
  "lightbulb-line",
  "link-unlink",
  "links-line",
  "list-bullet",
  "list-check-3",
  "list-number",
  "loader-4-line",
  "lock-line",
  "logout-box-r-line",
  "mail-line",
  "mail-send-line",
  "map-pin-line",
  "men-line",
  "menu-line",
  "mic-line",
  "money-dollar-circle-fill",
  "money-dollar-circle-line",
  "money-dollar-undo-line",
  "more-2-line",
  "more-line",
  "notification-line",
  "notification-off-line",
  "other-gender",
  "paragraph",
  "pencil-line",
  "phone-line",
  "play-circle-line",
  "play-line",
  "printer-line",
  "prohibited-2-line",
  "pulse-line",
  "quote",
  "refresh-line",
  "restart-line",
  "restaurant-line",
  "ruler-line",
  "scales-2-line",
  "search-line",
  "settings-line",
  "shield-user-line",
  "sketching",
  "star-line",
  "strikethrough",
  "subscript",
  "superscript",
  "table-view",
  "team-line",
  "test-tube-line",
  "time-line",
  "underline",
  "unknown-gender",
  "user-add-line",
  "user-line",
  "women-line",
] as const

export default {
  title: "Components/Icon",
  component: Icon,
  args: {
    name: "heart-pulse-line",
  },
  argTypes: {
    name: {
      control: "select",
      options: iconNames,
    },
  },
} as Meta<typeof Icon>

export const IconNew: StoryObj<typeof Icon> = {
  args: {
    name: "heart-pulse-line",
  },
}

export const AllIcons: StoryObj<typeof Icon> = {
  render: () => (
    <Grid rowGap>
      {iconNames.map((name) => (
        <div
          key={name}
          style={{
            gridColumn: "span 2",
            display: "flex",
            gap: 16,
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Icon name={name} style={{ width: 45, height: 45 }} />
          <Text>{name}</Text>
        </div>
      ))}
    </Grid>
  ),
}
