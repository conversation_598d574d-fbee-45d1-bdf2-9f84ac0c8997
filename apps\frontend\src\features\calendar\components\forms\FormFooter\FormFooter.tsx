import { useTranslation } from "react-i18next"

import { Button } from "ui"

import styles from "./FormFooter.module.css"

type Props = {
  formId: string
  readOnly: boolean
  formType: "Create" | "Edit"
  onDelete?: () => void
  onCancel?: () => void
  isLoading: boolean
}

export const FormFooter = ({
  formId,
  readOnly,
  formType,
  onDelete,
  onCancel,
  isLoading,
}: Props) => {
  const { t } = useTranslation()

  return (
    <div className={styles.footer} data-is-readonly={readOnly}>
      {formType === "Edit" && (
        <Button
          variant="outline"
          status="error"
          onClick={onDelete}
          className={styles.deleteButton}
        >
          {t("Delete")}
        </Button>
      )}

      {!readOnly && (
        <Button variant="clear" onClick={onCancel}>
          {t("Cancel")}
        </Button>
      )}
      <Button disabled={isLoading} type="submit" variant="filled" form={formId}>
        {formType === "Create" ? t("Save") : readOnly ? t("Done") : t("Update")}
      </Button>
    </div>
  )
}
