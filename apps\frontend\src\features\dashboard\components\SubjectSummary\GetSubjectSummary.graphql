query GetSubjectSummary($subjectId: UUID!) {
  subject(id: $subjectId) {
    # All the fields in fragment are used in SS except for nationality and for sake of easy spread of fetched object for optimisticReponse, we allow it to be included. We could also set it with dummy field but this method has no implications or harm.
    # Notice though it is a mere coincidence that SS wants nearly all fields used in SubjectEdit. If Subject dramatically changes this should be revisited.
    ...SubjectEditFieldsFragment
    location {
      id
      label
    }
    subjectHealthProfile {
      id
      dietaryAllowanceId
    }

    subjectStaticData {
      ...SubjectStaticDataFragment
    }

    # For location, dietary-restriction etc.
    encounters(statuses: IN_PROGRESS) {
      id
    }
    notificationsConfig {
      id
      category
      allowed
    }
  }
}
