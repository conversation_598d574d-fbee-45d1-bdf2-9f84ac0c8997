import { Page, expect } from "@playwright/test"

/**
 * Helper functions for general testing purposes.
 */

// Platform sensitive modifier keys
const platform = process.platform
const isMac = platform === "darwin"
export const modifierKeys = isMac
  ? ["Meta", "Alt", "Shift"]
  : ["Control", "Alt", "Shift"]

// Helper function to check if the URL is correct, used in the tests.
// Parameters:
//   page: The page object
//   path: The path to check
export const CheckUrl = async (page: Page, path: string | RegExp) => {
  // await expect(page).toHaveURL(`http://localhost:3000/${url}`)

  const baseUrl = "http://localhost:3000/"
  // if path is RegExp, create a new RegExp with the base URL and the path
  if (path instanceof RegExp) {
    await expect(page).toHaveURL(new RegExp(path.source))
  }
  // if path is a string, create a new string with the base URL and the path
  else await expect(page).toHaveURL(`${baseUrl}${path}`)
}

// Helper function to simulate a key combination.
// Parameters:
//   page: The page object
//   key: The key to press
//   modifiers: The modifiers to press
export const simulateKeyCombination = async (
  page: Page,
  key: string,
  modifiers: string[]
) => {
  // Press down the modifier keys
  for (const modifier of modifiers) {
    if (modifierKeys.includes(modifier)) {
      await page.keyboard.down(modifier)
    }
  }

  // Press the main key
  await page.keyboard.press(key)

  // Release the modifier keys
  for (const modifier of modifiers) {
    if (modifierKeys.includes(modifier)) {
      await page.keyboard.up(modifier)
    }
  }
}

// Helper function to open a page from power menu.
// Parameters:
//   page: The page object
//   menuItemName: Name of menu item
//   searchInput: Array of strings to search for
export const navigateFromPowermenu = async (
  page: Page,
  menuItemName: string,
  searchInput: string[]
) => {
  // Open power menu
  await page.click('[data-testid="power-menu-button"]')
  // Wait for power menu to appear
  await page.waitForSelector("[data-testid=power-menu]")
  // Check if power menu is open
  await expect(page.locator("[data-testid=power-menu]")).toBeVisible()

  if (searchInput.length > 1) {
    let counter = 0
    for (const searchInputItem of searchInput) {
      // Search for input string
      await page.fill(
        '[data-testid="search-input"]',
        searchInputItem[counter++]
      )
      // Check if search results are visible
      await expect(
        page.locator('[data-testid="power-menu-item"]', {
          hasText: searchInputItem,
        })
      ).toBeVisible()
      // Click on the search result
      await page
        .locator('[data-testid="power-menu-item"]', {
          hasText: searchInputItem,
        })
        .click()
      // Wait for the page to finish loading
      await page.waitForLoadState()
    }
  } else if (searchInput.length === 1) {
    // Search for input string
    await page.fill('[data-testid="search-input"]', searchInput[0])
    // Check if search results are visible
    await expect(
      page
        .locator('[data-testid="power-menu-item"]', { hasText: menuItemName })
        .first()
    ).toBeVisible()
    // Click on the search result
    await page.click(
      '[data-testid="power-menu-item"] >> text="' + menuItemName + '"'
    )
    // Wait for the page to finish loading
    await page.waitForLoadState()
  }
}
