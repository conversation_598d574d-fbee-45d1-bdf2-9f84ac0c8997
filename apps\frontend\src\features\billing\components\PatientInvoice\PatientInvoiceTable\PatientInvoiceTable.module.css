.tableWrap {
  position: relative;
  margin-left: -30px;
  margin-right: -30px;
  padding-left: 30px;
  padding-right: 30px;
}

.table {
  overflow: visible;
}
.table tbody td {
  background-color: var(--color-white);
  position: relative;
  padding: 4px calc(var(--grid-gap) / 2);
}

.table th {
  padding: 17px calc(var(--grid-gap) / 2);
  /* Text label small doesn't overwrite font size as 12px */
  font-size: 12px;
}
.table :is(th, td):first-child {
  padding-left: var(--grid-gap);
}
.table :is(th, td):last-child {
  padding-right: var(--grid-gap);
}

.table.table .groupName {
  background: var(--LEV-Blue-LEV-Blue-1, #c2bdf7);
  padding: 0 16px;
}

.odd,
.even {
  transition: background-color 0.1s ease-in-out;
}

.errorPanel {
  grid-column: 1 / -1;
  margin: 8px 0 16px;
}
.errorMessage {
  margin-top: 8px;
}

/* COMEBACK remove !important styles */
.itemRow:nth-child(even) td {
  background: var(--LEV-Blue-LEV-Blue-0, #e6e6ff);
}
.itemRow:nth-child(odd) td {
  background: var(--color-white);
}
:is(.even, .odd):hover td {
  background: var(--color-lev-blue-0, #c2bdf7);
}

.code {
  min-width: 130px;
}

.nhiIcon {
  position: absolute;
  width: 16px;
  height: 16px;
  margin-top: 3px;
}

.billingCodeRow {
  position: relative;
}

.billingCodeText {
  margin-left: 28px;
  min-width: 300px;

  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.billingCodeText[data-is-nhi="false"] {
  margin-left: 0;
}

.billingCodeTitle {
  display: flex;
  gap: 4px;
}

.addNewItemButton.addNewItemButton {
  font-size: 20px;
  margin: 0 calc(-1 * var(--grid-gap));
  padding: 16px var(--grid-gap);
}

.numericRow {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.unitPriceText {
  padding: 11px 0;
}

.addButton.addButton,
.removeButton.removeButton {
  position: absolute;
  left: 0;
  padding: 4px;
  margin: -4px;
  min-height: 24px;
  font-size: 24px;
  z-index: 2;
}

.removeButton.removeButton {
  right: 0;
  left: auto;
}

.inputWrap {
  transform: translateX(8px);
  z-index: 1;
  position: relative;
}
.inputWrap.inputWrap > input {
  background-color: inherit;
  border: 1px solid transparent;
  padding: 10px 8px;
  color: var(--Primary-Text, #181c75);
  justify-self: end;
}

.patientInvoiceSelect {
  background-color: inherit;
  border: 1px solid transparent;
}

.itemRow:hover .patientInvoiceSelect {
  border: 1px solid var(--color-neutral);
  background-color: var(--color-white);
}

/* Logic for showing arrow down icon */
.arrowDownIcon {
  opacity: 0;
}

.itemRow {
  position: relative;
  z-index: 1;
}
.itemRow::after {
  content: "";
  position: absolute;
  inset: 0 -30px;
  z-index: -1;
}
.itemRow:hover .arrowDownIcon {
  opacity: 1;
}

.itemRow:hover .patientInvoiceSelect span {
  display: block;
}

.itemRow:hover .inputWrap > input {
  border: 1px solid var(--color-neutral);
  background-color: var(--color-white);
  align-content: end;
}

.inputWrap {
  width: fit-content;
}

/* Additional pixel needed for larger numbers. In larger numbers, values are hiding behind padding */
.inputWrap.inputWrap > input[name="quantity"],
.inputWrap.inputWrap > input[name="billableQuantity"] {
  width: calc(80px + 8px);
}

.inputWrap.inputWrap > input[name="unitPrice"] {
  padding-left: 0;
  width: calc(90px + 8px);
}

.inputWrap.inputWrap > input[name="discount"] {
  width: calc(50px + 16px);
}

.itemRow .inputWrap:focus-within > input {
  border: 1px solid var(--color-neutral) !important;
  background-color: var(--color-white) !important;
}
