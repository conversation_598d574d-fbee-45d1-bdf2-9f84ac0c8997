const graphqlUri = `${
  process.env.RUST_URL || "http://mss.localhost:4001"
}/graphql`

export async function fetchAPI(
  method: string,
  body: object,
  authToken?: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<any> {
  const additionalHeaders: { [key: string]: string } = {}
  if (authToken) {
    additionalHeaders["Authorization"] = `${authToken}`
  }

  const response = await fetch(graphqlUri, {
    method,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      ...additionalHeaders,
    },
    body: JSON.stringify(body),
  })

  return response.json()
}
