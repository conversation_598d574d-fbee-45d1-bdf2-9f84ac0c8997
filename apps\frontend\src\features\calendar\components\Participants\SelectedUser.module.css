.wrap {
  display: grid;
}

.selectedUser {
  margin: 10px 0px;
}

.userName {
  width: 150px;
}

.iconWrap {
  width: 40px;
  height: 40px;
  background: #e3e5f0;
  border-radius: 50%;
  margin-right: 15px;

  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.userIcon {
  width: 24px;
  height: 24px;
}

.userRsvpIcon {
  position: absolute;
  bottom: 0;
  right: -10px;
  width: 24px;
  height: 24px;

  color: var(--color-warning);
  fill: var(--color-warning-200);
  filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.25));
}

.userRsvpIcon[data-rsvp-status="ACCEPTED"] {
  color: var(--color-success);
  fill: var(--color-success-200);
}

.userRsvpIcon[data-rsvp-status="DECLINED"] {
  color: var(--color-critical);
  fill: var(--color-critical-200);
}
.participantInfo {
  display: flex;
  padding: 8px 16px 8px 0;
}
.deleteIcon {
  width: 16px;
  height: 16px;
  cursor: pointer;

  position: absolute;
  top: 0;
  left: -21px;
  top: 14px;
  color: var(--color-text);
}

.participantType {
  text-transform: uppercase;
  font-size: 12px;
  line-height: 14px;
}

.participationButtons {
  padding-right: 16px;
}

.dropdownIcon {
  width: 14px;
  height: 14px;
}

.textWithIcon {
  margin-bottom: 10px;
}
