import { FormEvent, useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import { SelectColor } from "features/organisation-management/components/SelectColor/SelectColor"
import { Button, Input, Label, Text, Textarea } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import isDefined from "utils/isDefined"

import {
  CalendarColor,
  namedOperations,
  ServiceTypeFragmentFragment,
  ServiceTypeModality,
  useCreateServiceTypeMutation,
  useDeleteServiceTypeMutation,
  useUpdateServiceTypeMutation,
} from "generated/graphql"

import styles from "./ServiceTypeModal.module.css"

export const ServiceTypeFormSchema = z.object({
  name: z.string().min(5),
  description: z.string().min(5),
  color: z.nativeEnum(CalendarColor),
  modality: z.nativeEnum(ServiceTypeModality),
})

type ServiceTypeModalProps = {
  serviceType: ServiceTypeFragmentFragment | null
  showModal: boolean
  closeModal: () => void
  onCreate?: (st: ServiceTypeFragmentFragment) => void
}

const ServiceTypeModal = ({
  serviceType,
  showModal,
  closeModal,
  onCreate,
}: ServiceTypeModalProps) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")
  const [error, setError] = useState<string | null>(null)
  const [isDialogForArchiveOpen, setIsDialogForArchiveOpen] = useState(false)
  const [createServiceType] = useCreateServiceTypeMutation()
  const [updateServiceType] = useUpdateServiceTypeMutation()
  const [deleteServiceType] = useDeleteServiceTypeMutation()

  const selectModalityStore = useSelectStore({
    defaultValue: serviceType?.modality || ServiceTypeModality.Onsite,
  })

  // Set the default value of the modality select component when the service type is set
  useEffect(() => {
    selectModalityStore.setValue(
      serviceType?.modality || ServiceTypeModality.Onsite
    )
  }, [serviceType?.modality])

  const modalityOptions = Object.values(ServiceTypeModality).map(
    (modality) => ({
      value: modality,
      label: tEnum(`ServiceTypeModality.${modality}`),
    })
  )

  const handleCloseModal = () => {
    closeModal()
    setError("")
  }

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())

    const validatedInput = ServiceTypeFormSchema.safeParse({
      name: data.name,
      description: data.description,
      color: data.color,
      modality: data.modality,
    })

    if (!validatedInput.success) {
      setError(validatedInput.error.message)
      console.error(validatedInput.error)

      return
    }

    const { name, description, color, modality } = validatedInput.data

    if (!serviceType) {
      createServiceType({
        variables: {
          input: {
            name,
            description,
            color,
            modality,
          },
        },
        onError: () => {
          setError(t("Something went wrong, please try again"))
        },
        onCompleted: (data) => {
          handleCloseModal()
          onCreate?.(data.createExternalServiceType)
        },
        refetchQueries: [namedOperations.Query.GetServiceTypes],
      })

      return
    }

    updateServiceType({
      variables: {
        input: {
          id: serviceType.id,
          name,
          description,
          color,
          modality,
        },
      },
      onError: () => {
        setError(t("Something went wrong, please try again"))
      },
      onCompleted: handleCloseModal,
    })
  }

  const isArchived = isDefined(serviceType?.deletedAt)

  const getDialogTitle = () => {
    if (isArchived) {
      return t("Archived Service")
    }
    if (serviceType) {
      return t("Edit Service")
    }
    return t("Add Service")
  }

  return (
    <Dialog
      isOpen={showModal}
      title={getDialogTitle()}
      contentClassName={styles.container}
      onClose={() => {
        handleCloseModal()
      }}
      actionsClassName={styles.dialogActions}
    >
      {isArchived && (
        <Text size="small" className={styles.archivedService}>
          {t(
            "This service has been archived and can therefore no longer be edited."
          )}
        </Text>
      )}

      <form onSubmit={handleSubmit}>
        <Input
          name="name"
          label={t("Service Name")}
          defaultValue={serviceType?.name || ""}
          readOnly={isArchived}
        />
        <Textarea
          name="description"
          label={t("Service Description")}
          defaultValue={serviceType?.description || ""}
          rows={5}
          readOnly={isArchived}
        />
        <Select
          name="modality"
          defaultValue={serviceType?.modality}
          label={t("Modality")}
          selectStore={selectModalityStore}
          options={modalityOptions}
          readOnly={isArchived}
        />
        <Label className={styles.label}>{t("Service Color")}</Label>
        <SelectColor
          name={"color"}
          defaultValue={serviceType?.color}
          disabled={isArchived}
        />
        {error && (
          <Panel status="error" className={styles.error}>
            {error}
          </Panel>
        )}
        {!isArchived && (
          <div className={styles.confirmationButtons}>
            {serviceType && !isArchived && (
              <Button
                variant="clear"
                status="error"
                icon={<Icon name="delete-bin-line" />}
                className={styles.archiveButton}
                onClick={() => setIsDialogForArchiveOpen(true)}
              >
                {t("Archive Service")}
              </Button>
            )}
            <Button
              variant="clear"
              onClick={() => {
                handleCloseModal()
              }}
            >
              {t("cancel")}
            </Button>
            <Button variant="filled" type="submit">
              {t("doSave")}
            </Button>
          </div>
        )}
      </form>
      <Dialog
        title={t("Are you sure you want to archive this service?")}
        isOpen={isDialogForArchiveOpen}
        onClose={() => {
          setIsDialogForArchiveOpen(false)
        }}
        actions={
          <>
            <Button onClick={() => setIsDialogForArchiveOpen(false)}>
              {t("Cancel")}
            </Button>
            <Button
              onClick={() => {
                if (serviceType) {
                  deleteServiceType({
                    variables: {
                      id: serviceType.id,
                    },
                    onCompleted: () => {
                      setIsDialogForArchiveOpen(false)
                      handleCloseModal()
                    },
                    onError: () => {
                      setError(t("Something went wrong, please try again"))
                    },
                    refetchQueries: [
                      namedOperations.Query.GetServiceTypes,
                      namedOperations.Query.GetAvailabilitySchedules,
                    ],
                  })
                }
              }}
              variant="filled"
            >
              {t("Archive")}
            </Button>
          </>
        }
      ></Dialog>
    </Dialog>
  )
}

export default ServiceTypeModal
