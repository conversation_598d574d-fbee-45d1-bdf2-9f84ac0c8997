import { test, expect, Page } from "@playwright/test"

import { login } from "../utils/authenticationUtils"
import { openSubjectJournalForSubject } from "../utils/subjectJournalTestUtils"

/* eslint-disable playwright/no-wait-for-timeout */

test.describe("Medical Certificates tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/", { waitUntil: "commit" })
    await page.waitForTimeout(5000)
  })

  const arrange = async (page: Page, subject: string) => {
    await login(page)
    await openSubjectJournalForSubject(page, subject)
    await page.waitForTimeout(800)
    await page.getByTestId("journal-template-tile").nth(0).click()
    await page.waitForTimeout(400)
  }

  test("Add certificate using plus button", async ({ page }) => {
    // Arrange
    arrange(page, "Ivan Hoe")

    // Act
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: "Certificate" }).click()
    await page.waitForTimeout(800)

    const numberOfCertificatesAfter = await page
      .getByText("Medical certificateDue to absence from work or school")
      .count()

    // Assert
    await expect(numberOfCertificatesAfter).toBe(1)
  })

  test("Can submit empty certificate", async ({ page }) => {
    // Arrange
    await arrange(page, "Laurie Seeley")

    // Act
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: "Certificate" }).click()

    await page.getByTestId("certificate-submit").first().click()
    await page.waitForTimeout(400)
    const numberOfSubmittedAfter = await page
      .locator('[class*="Supplement_confirmed"]')
      .count()

    // Assert
    expect(numberOfSubmittedAfter).toBe(1)
  })

  test("Values are submitted", async ({ page }) => {
    // Arrange
    await arrange(page, "Thomas Grill")

    // Act
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: "Certificate" }).click()

    await page
      .getByTestId("certificate-employer-school")
      .locator("input")
      .fill("The workplace")
    await page
      .getByTestId("certificate-from-date")
      .locator("input")
      .fill("2025-10-10")
    await page
      .getByTestId("certificate-to-date")
      .locator("input")
      .fill("2025-12-10")
    await page
      .getByTestId("certificate-description")
      .locator("textarea")
      .fill("Confirmed that they need to stay home for medical reasons.")
    await page.getByTestId("certificate-submit").first().focus()
    await page.waitForTimeout(1000)
    await page.getByTestId("certificate-submit").first().click()
    await page.waitForTimeout(800)

    // Assert
    const description = page.locator(
      '[class*="ViewMedicalCertificate_description"]'
    )
    await expect(page.locator("text=The workplace")).toBeVisible()
    await expect(page.getByText("2025/10/10 - 2025/12/10")).toBeVisible()
    await expect(description).toHaveText(
      "Confirmed that they need to stay home for medical reasons."
    )
  })

  test("Edit an existing medical certificate", async ({ page }) => {
    // Arrange
    await arrange(page, "Gail Mitchel")

    // create certificate
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: "Certificate" }).click()
    await page
      .getByTestId("certificate-employer-school")
      .locator("input")
      .fill("High school")
    await page
      .getByTestId("certificate-from-date")
      .locator("input")
      .fill("2025-05-01")
    await page
      .getByTestId("certificate-to-date")
      .locator("input")
      .fill("2025-05-04")
    await page
      .getByTestId("certificate-description")
      .locator("textarea")
      .fill("Confirmed Gail had flu and was at home.")
    await page.getByTestId("certificate-submit").first().focus()
    await page.waitForTimeout(800)

    // Act
    // navigate away and back to the page
    await openSubjectJournalForSubject(page, "Thomas Grill")
    await openSubjectJournalForSubject(page, "Gail Mitchel")

    // edit existing certificate
    await page
      .getByTestId("certificate-employer-school")
      .locator("input")
      .fill("High school 2")
    await page
      .getByTestId("certificate-from-date")
      .locator("input")
      .fill("2025-06-01")
    await page
      .getByTestId("certificate-to-date")
      .locator("input")
      .fill("2025-06-04")
    await page
      .getByTestId("certificate-description")
      .locator("textarea")
      .fill("Confirmed Gail had flu and was at home 2.")
    await page.getByTestId("certificate-submit").first().focus()
    await page.waitForTimeout(1000)
    await page.getByTestId("certificate-submit").first().click()
    await page.waitForTimeout(800)

    // Assert
    const description = page.locator(
      '[class*="ViewMedicalCertificate_description"]'
    )
    await expect(page.locator("text=High school 2")).toBeVisible()
    await expect(page.getByText("2025/06/01 - 2025/06/04")).toBeVisible()
    await expect(description).toHaveText(
      "Confirmed Gail had flu and was at home 2."
    )
  })

  test("Delete a medical certificate", async ({ page }) => {
    // Arrange
    await arrange(page, "Paul Bierly")
    // create a certificate
    await page.getByTestId("add-supplements-menu").first().click()
    await page.getByRole("option", { name: "Certificate" }).click()
    await page.waitForTimeout(500)

    // Act
    await page.getByTestId("certificate-delete").first().click()
    await page
      .getByRole("dialog")
      .getByRole("button", { name: "Delete" })
      .click()
    await page.waitForTimeout(1000)

    // Assert
    const numberOfCertificatesAfter = await page
      .getByText("Medical certificateDue to absence from work or school")
      .count()

    // Assert
    await expect(numberOfCertificatesAfter).toBe(0)
  })
})
