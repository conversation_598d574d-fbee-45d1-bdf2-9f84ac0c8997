[workspace]
members = [
    "backend/public-api",
    "backend/public-graphql",
    "backend/migration",
    "backend/domain",
    "backend/domain-contracts",
    "backend/domain-types",
    "backend/api",
    "backend/infrastructure",
    "backend/macros",
    "backend/external-ehr-client",
    "backend/testing",
    "backend/cron-jobs",
    "backend/data-import",
]
exclude = [
    "backend/hekla",
]
resolver = "2"

[workspace.package]
edition = "2024"

# Workspace metadata for parallel compilation optimization
[workspace.metadata.build-optimization]
# Independent crates that can be built in parallel (no cross-dependencies)
independent-tier-1 = [
    "backend/domain-types",
    "backend/macros",
]

# Crates that depend only on tier-1 (can be built in parallel after tier-1)
independent-tier-2 = [
    "backend/domain-contracts",
    "backend/external-ehr-client",
    "backend/testing",
]

# Crates that depend on domain-types and contracts (can be built in parallel after tier-2)
dependent-tier-3 = [
    "backend/domain",
    "backend/migration",
]

# Crates that depend on domain and infrastructure (final tier)
dependent-tier-4 = [
    "backend/infrastructure",
    "backend/public-api",
    "backend/public-graphql",
    "backend/api",
    "backend/cron-jobs",
    "backend/data-import",
]

[profile.dev]
# Disabling debug info speeds up builds a bunch,
# and we don't rely on it for debugging that much.
debug = 0

# Enable parallel compilation optimizations
codegen-units = 16
incremental = true

[workspace.dependencies]
cookie = { version = "0.18", features = ["private"] }
indexmap = "2.7.1"
async-graphql = { version = "7.0.17", default-features = false, features = [
    "uuid",
    "chrono",
    "dataloader",
    "playground",
    "boxed-trait"
] }
async-graphql-axum = "7.0.17"
axum = { version = "0.8", features = ["multipart"] }
anyhow = { version = "1.0.75", features = ["backtrace"] }
async-trait = "0.1.74"
leviosa-domain = { path = "backend/domain" }
leviosa-domain-contracts = { path = "backend/domain-contracts" }
leviosa-domain-types = { path = "backend/domain-types" }
leviosa-macros = { path = "backend/macros" }
leviosa-infrastructure = { path = "backend/infrastructure", features = [
    "sqlx",
    "sea-orm",
] }
chrono = { version = "0.4.31", features = ["serde"] }
futures = "0.3.29"
getset = "0.1.2"
serde = { version = "1.0.190", features = ["derive"] }
serde_json = "1.0.107"
sqlx = { version = "0.8.3", features = [
    "runtime-tokio-rustls",
    "postgres",
    "uuid",
    "chrono",
    "json",
] }
thiserror = "1.0.50"
tokio = { version = "1.33.0", default-features = false, features = ["full"] }
uuid = { version = "1.5.0", features = ["serde", "v4"] }
rand = "0.8.5"
base64 = "0.21.5"
reqwest = { version = "0.12", default-features = false, features = ["multipart", "rustls-tls", "json"] }
graphql_client = "0.13.0"
heck = "0.4.1"
validator = { version = "0.16.1", features = ["derive"] }
regex = "1.10.2"
paste = "1.0.14"
sentry = { version = "0.31.7", default-features = false, features = [
    "backtrace",
    "contexts",
    "panic",
    "anyhow",
    "reqwest",
    "rustls",
    "tracing",
] }
sentry-tracing = "0.31.7"
sea-orm = { version = "^1.1.4", features = [
    "sea-orm-internal",
    "sqlx-postgres",
    "sqlx-sqlite",
    "runtime-tokio-rustls",
    "macros",
    "postgres-array",
    "debug-print",
] }
insta = { version = "1.34.0", features = ["json", "redactions"] }
sea-orm-migration = { version = "^1.1.4", features = [
    "runtime-tokio-rustls",
    "sqlx-postgres",
] }
aws-config = { version = "1.5.1", default-features = false, features = [
    "rustls",
    "rt-tokio",
    "behavior-version-latest",
] }
aws-sdk-s3 = { version = "1.35.0", default-features = false, features = [
    "rustls",
    "rt-tokio",
] }
bcrypt = "0.15.0"
dotenv = "0.15.0"
envy = "0.4.2"
jsonwebtoken = "9.1.0"
proc-macro2 = "1.0.69"
quote = "1.0.33"
syn = "2.0.38"
isahc = { version = "1.7.2", features = ["json"] }
sha2 = "0.10.8"
rrule = "0.11.0"
urlencoding = "2.1.3"
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.17", features = ["json"] }
serde_with = "3.4.0"
tower-http = { version = "0.5", default-features = false, features = [
    "cors",
] }
lettre = { version = "0.11.1", default-features = false, features = [
    "builder",
    "smtp-transport",
    "rustls-tls",
] }
prefixed-api-key = { version = "0.1.0", features = ["sha2"] }
mockall = "0.13.1"
serde-xml-rs = "0.6.0"
ammonia = "4.0.0"
rstest = "0.19.0"
async-std = { version = "1.12.0", features = ["attributes"] }
csv = "1.3.0"
tokio-shared-rt = "0.1.0"
cynic = { version = "3.9.1", features = ["http-reqwest"] }
cynic-codegen = { version = "3" }
lambda_runtime = "0.13.0"
aes-gcm-siv = "0.11.1"
