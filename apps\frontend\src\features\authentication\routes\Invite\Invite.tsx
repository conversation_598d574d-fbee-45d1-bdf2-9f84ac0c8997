import { FormEvent } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams } from "react-router-dom"

import Panel from "components/Panel/Panel"
import usePasswordValidation from "hooks/usePasswordValidation"
import { RouteStrings } from "routes/RouteStrings"
import { Button, FormGrid, Heading, Input } from "ui"
import NewPasswordInput from "ui/components/Input/NewPasswordInput"

import { useAcceptInvitationMutation } from "generated/graphql"

import { useAuth } from "../../AuthProvider"
import styles from "../../Authentication.module.css"

export default function Invite() {
  const navigate = useNavigate()
  const { token } = useParams<{ token: string }>()
  const {
    passwordRef,
    repeatedPasswordRef,
    passwordStatus,
    setPasswordStatus,
    repeatedPasswordStatus,
    setRepeatedPasswordStatus,
    validatePassword,
    validateRepeatedPassword,
  } = usePasswordValidation()

  const { authenticate } = useAuth()
  const { t } = useTranslation()

  const [acceptInvitation, { loading, error }] = useAcceptInvitationMutation()

  if (!token) {
    navigate(RouteStrings.login)

    return null
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!validatePassword() || !validateRepeatedPassword()) return

    const form = e.currentTarget
    const formData = new FormData(form)
    const password = formData.get("password")

    if (typeof password !== "string") {
      setPasswordStatus({
        status: "error",
        message: t("routes:validations.passwordsNoMatch"),
      })

      return
    }

    const { data } = await acceptInvitation({
      variables: { password, invitationToken: token },
    })
    // What should we do in this case?
    if (!data) return

    if (data.acceptInvitation) {
      const { accessToken, refreshToken } = data.acceptInvitation.tokens
      authenticate({ accessToken, refreshToken }, "/")
    }
  }

  return (
    <FormGrid onSubmit={handleSubmit} className={styles.wrap}>
      <Heading as="h1" size="large">
        {t("routes:auth.invitationTitle")}
      </Heading>

      <NewPasswordInput
        label={t("routes:auth.password")}
        autoComplete="new-password"
        name="password"
        type="password"
        ref={passwordRef}
        onFocus={() => setPasswordStatus({ status: "default", message: "" })}
        onBlur={validatePassword}
        status={passwordStatus.status}
        message={passwordStatus.message}
      />

      <Input
        label={t("routes:auth.repeatPassword")}
        autoComplete="new-password"
        name="repeatPassword"
        type="password"
        ref={repeatedPasswordRef}
        onBlur={validateRepeatedPassword}
        onFocus={() =>
          setRepeatedPasswordStatus({ status: "default", message: "" })
        }
        status={repeatedPasswordStatus.status}
        message={repeatedPasswordStatus.message}
      />

      {error && <Panel status="error" children={error?.message} />}

      <Button
        className={styles.submitButton}
        disabled={loading}
        type="submit"
        size="large"
        variant="filled"
      >
        {t("routes:auth.acceptInvitation")}
      </Button>
    </FormGrid>
  )
}
