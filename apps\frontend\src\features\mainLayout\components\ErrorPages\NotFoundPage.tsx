import { Trans, useTranslation } from "react-i18next"

import Laptop404Illustration from "@leviosa/assets/illustrations/laptop404.svg?react"

import ErrorPage from "./ErrorPage"

export default function NotFoundPage() {
  const { t } = useTranslation("features", {
    keyPrefix: "mainLayout.NotFound",
  })

  const suggestions = [
    <Trans i18nKey={t("suggestion1")} components={{ bold: <b /> }} />,
    <Trans i18nKey={t("suggestion2")} components={{ bold: <b /> }} />,
    <Trans i18nKey={t("suggestion3")} components={{ bold: <b /> }} />,
  ]

  return (
    <ErrorPage
      illustration={<Laptop404Illustration />}
      heading={t("heading")}
      message={t("message")}
      suggestions={suggestions}
      subMessage={t("subMessage")}
    />
  )
}
