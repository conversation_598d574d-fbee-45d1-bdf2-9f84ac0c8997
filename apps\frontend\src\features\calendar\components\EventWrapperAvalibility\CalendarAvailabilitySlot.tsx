import { skipToken, useBackgroundQuery } from "@apollo/client"
import { PopoverDisclosure, PopoverProvider } from "@ariakit/react"
import c from "classnames"
import { Suspense } from "react"
import { View } from "react-big-calendar"
import { useParams } from "react-router-dom"

import { usePopoverStore } from "components/Ariakit"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  GetProvidersAndSubjectForAvailabilityDocument,
  GetProvidersAndSubjectForAvailabilityQuery,
  GetProvidersAndSubjectForAvailabilityQueryVariables,
} from "generated/graphql"

import CalendarAvailabilitySlotPopover from "./CalendarAvailabilitySlotPopover"
import styles from "./EventWrapperAvailability.module.css"

type CalendarAvailabilitySlotProps = {
  allowCreateEvent: boolean
  isLastItem: boolean
  height: number
  slotStartTime: Date
  slotEndTime: Date
  serviceTypeName: string
  serviceTypeId: string
  teamId?: string
  providerId: string
}

export default function CalendarAvailabilitySlot({
  allowCreateEvent,
  isLastItem,
  height,
  slotStartTime,
  slotEndTime,
  serviceTypeName,
  serviceTypeId,
  teamId,
  providerId,
}: CalendarAvailabilitySlotProps) {
  const { view } = useParams()
  const { globalData } = useGlobalState()
  const timeFormat = useTimeFormatter()
  const currentView: View = (view ?? "week") as View

  const store = usePopoverStore({
    placement: currentView === "day" ? "bottom" : "right-start",
  })

  const subject = globalData.actor?.lastSubjectInteraction?.subject
  const isOpen = store.useState().open

  const [queryRef] = useBackgroundQuery<
    GetProvidersAndSubjectForAvailabilityQuery,
    GetProvidersAndSubjectForAvailabilityQueryVariables
  >(
    GetProvidersAndSubjectForAvailabilityDocument,
    isOpen && subject?.id
      ? {
          variables: {
            fromTime: slotStartTime,
            toTime: slotEndTime,
            inputFilter: {
              ids: [providerId],
            },
            subjectId: subject.id,
          },
        }
      : skipToken
  )

  const Element = allowCreateEvent ? "button" : "div"

  // Format start and end times
  const startTimeFormatted = timeFormat(slotStartTime)
  const endTimeFormatted = timeFormat(slotEndTime)

  return (
    <PopoverProvider store={store}>
      <PopoverDisclosure
        render={<Element />}
        // Disable if renderAs is div. This is used in availability calendar where we don't need popover
        disabled={!allowCreateEvent}
        style={{
          height,
        }}
        className={c({
          [styles.availabilitySlotButton]: 1,
          [styles.lastItem]: isLastItem,
        })}
        title={`${startTimeFormatted} - ${endTimeFormatted}`}
      >
        <span className={styles.timeSlotLabel}>
          {startTimeFormatted}
          <span className={styles.timeSlotEnd}> - {endTimeFormatted}</span>
        </span>
      </PopoverDisclosure>
      <Suspense>
        {queryRef && (
          <CalendarAvailabilitySlotPopover
            slotStartTime={slotStartTime}
            slotEndTime={slotEndTime}
            store={store}
            serviceTypeName={serviceTypeName}
            serviceTypeId={serviceTypeId}
            teamId={teamId}
            queryRef={queryRef}
            providerId={providerId}
          />
        )}
      </Suspense>
    </PopoverProvider>
  )
}
