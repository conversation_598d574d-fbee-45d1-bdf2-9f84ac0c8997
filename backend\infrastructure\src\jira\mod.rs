use anyhow::Context;
use async_trait::async_trait;
use chrono::Utc;
use isahc::{AsyncReadResponseExt, Request, RequestExt};
use leviosa_domain_contracts::{
    CustomerService, FeedbackData,
    errors::Error,
};

pub struct Jira {
    host: String,
    api_key: String,
    service_desk_id: String,
    request_type_id: String,
    environment: String,
}

impl Jira {
    pub fn new(
        host: String,
        api_key: String,
        service_desk_id: String,
        request_type_id: String,
        environment: String,
    ) -> Self {
        Self {
            host,
            api_key,
            service_desk_id,
            request_type_id,
            environment,
        }
    }
}

#[async_trait]
impl CustomerService for <PERSON>ra {
    async fn create_feedback(&self, feedback: FeedbackData) -> Result<(), Error> {
        let base_url = self.host.as_str();
        let service_desk_id = self.service_desk_id.as_str();
        let request_type_id = self.request_type_id.as_str();
        let access_token = self.api_key.as_str();
        const CREATE_FEEDBACK_API: &str = "rest/servicedeskapi/request";
        let authorization = &format!("Basic {access_token}");
        let url = &format!("{base_url}/{CREATE_FEEDBACK_API}");

        let body = serde_json::json!({
            "isAdfRequest": false,
            "requestTypeId": request_type_id,
            "serviceDeskId": service_desk_id,
            "requestFieldValues": {
                "summary": format!("feedback {}", Utc::now()),
                "description": format!("{:?}\nenvironment: {}", feedback, self.environment),
            },
        })
        .to_string();

        let request = Request::post(url)
            .header("accept", "application/json")
            .header("content-type", "application/json")
            .header("authorization", authorization)
            .body(body)
            .context("Failed to create customer feedback request")?;

        let mut res = request
            .send_async()
            .await
            .context("Failed to send customer feedback request")?;

        let status = res.status();
        let text = res
            .text()
            .await
            .context("Failed to parse customer feedback response")?;

        if !status.is_success() {
            return Err(
                anyhow::anyhow!("Failed to create feedback in jira: {}, {}", status, text).into(),
            );
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use leviosa_domain_contracts::{FeedbackUser, FeedbackData};
    use leviosa_domain_types::{OrganisationId, ProviderId};

    #[tokio_shared_rt::test]
    #[ignore]
    async fn debug_jira_customer_feedback() {
        let jira_api = Jira::new(
            String::new(), // jira api key
            String::from("https://leviosa.atlassian.net"),
            String::from("4"),
            String::from("46"),
            String::from("development"),
        );

        jira_api
            .create_feedback(FeedbackData {
                user: FeedbackUser {
                    user_id: ProviderId::generate(),
                    user_name: String::from("Test User"),
                    user_email: String::from("<EMAIL>"),
                    organization_id: OrganisationId::generate(),
                    organization_name: String::from("Test Organization"),
                },
                feedback: String::from("Positive"),
                os: Some(String::from("Test OS")),
                browser: Some(String::from("Test Browser")),
                browser_version: Some(String::from("Test Browser Version")),
                app_version: String::from("1.0.0"),
            })
            .await
            .unwrap();
    }
}
