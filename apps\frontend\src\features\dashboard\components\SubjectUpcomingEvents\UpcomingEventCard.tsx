import c from "classnames"
import { useTranslation } from "react-i18next"
import { Link } from "react-router-dom"

import Icon from "components/Icon/Icon"
import OpenInJournal from "features/calendar/components/OpenInJournal/OpenInJournal"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"
import { calendarColorMap, color } from "styles/colors"
import { Button, Heading, Text } from "ui"
import { isTypename } from "utils/isTypename"

import {
  ProviderSpecialty,
  UpcomingEventFragmentFragment,
} from "generated/graphql"

import styles from "./SubjectUpcomingEvents.module.css"

type UpcomingEventCardProps = {
  eventInstance: UpcomingEventFragmentFragment
  interventionPeriodId?: string
}

export const UpcomingEventCard = ({
  eventInstance,
  interventionPeriodId,
}: UpcomingEventCardProps) => {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })
  const { t } = useTranslation()

  const getCalendarPath = useGetCalendarPathObject()

  const dateTimeFormatter = new Intl.DateTimeFormat("IS", {
    dateStyle: "medium",
    timeStyle: "short",
  })

  const getEventTitles = (serviceTypeName?: string, title?: string) => {
    return [serviceTypeName, title].filter(Boolean).join(" • ")
  }

  const getEventProviders = (
    providers: {
      name: string
      specialty: ProviderSpecialty
    }[]
  ) => {
    if (providers.length === 1)
      return `${providers[0].name} • ${tEnum(providers[0].specialty)}`

    return providers.map((p) => p.name).join(",  ")
  }

  const getEventLink = (
    eventId: string,
    eventDate: string,
    providerId: string
  ) => {
    const date = eventDate.split("T")[0]
    return getCalendarPath(RouteStrings.calendarViewEventInstance, {
      eventId,
      search: { provider: providerId, date: date },
    })
  }

  const providers = eventInstance.participants
    .filter(isTypename("ParticipantProvider"))
    .map((p) => p.provider)

  const eventLink =
    providers.length >= 1
      ? getEventLink(eventInstance.id, eventInstance.fromDate, providers[0].id)
      : null

  const eventInfo = (
    <>
      <Text size="small">
        {dateTimeFormatter.format(new Date(eventInstance.fromDate))}
      </Text>
      <Heading className={styles.eventTitle}>
        <Icon name="calendar-2-line" />
        {getEventTitles(eventInstance.serviceType?.name, eventInstance.title)}
      </Heading>
      {eventLink && <Text size="small">{getEventProviders(providers)}</Text>}
      {eventLink && (
        <Button
          as="span"
          className={c(styles.eventButton, color.dark)}
          iconEnd={<Icon name="arrow-right-s-line" />}
        >
          <Heading size="xsmall">{t("Upcoming event")}</Heading>
        </Button>
      )}
    </>
  )

  const providerIds = eventInstance.participants
    .filter(isTypename("ParticipantProvider"))
    .map((p) => p.provider.id)
  const subjectIds = eventInstance.participants
    .filter(isTypename("ParticipantSubject"))
    .map((p) => p.subject.id)

  const eventColor =
    calendarColorMap[eventInstance.serviceType?.color || "LEV_BLUE"]

  return (
    <li key={eventInstance.id} className={styles.eventWrapper}>
      {eventLink ? (
        <Link to={eventLink} className={c(styles.event, eventColor.light)}>
          {eventInfo}
        </Link>
      ) : (
        <div
          className={c(styles.event, styles.noPointerCursor, eventColor.light)}
        >
          {eventInfo}
        </div>
      )}
      <OpenInJournal
        className={styles.openJournalButton}
        start={new Date(eventInstance.fromDate)}
        end={new Date(eventInstance.toDate)}
        providerIds={providerIds}
        subjectIds={subjectIds}
        teamId={eventInstance.team?.id}
        eventId={eventInstance.id}
        encounterReason={
          eventInstance.serviceType?.name || eventInstance.title || ""
        }
        encounterId={eventInstance.encounter?.id}
        variant="filled-light"
        interventionPeriodId={interventionPeriodId}
      />
    </li>
  )
}
