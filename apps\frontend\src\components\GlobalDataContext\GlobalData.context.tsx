import { createContext, ReactNode, useContext, useState } from "react"

import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { ValueOf } from "utils/ValueOf"

export type GlobalState = {
  isNnMode: boolean
  currentTeamId: string | null // nullable b/c Provider may be unassigned to any Team
}

export const initialGlobalState: GlobalState = {
  isNnMode: false,
  currentTeamId: null,
}

type Props = {
  globalState: GlobalState
  globalData: GlobalDataWithNonNullableActor
  setGlobalState: (key: keyof GlobalState, value: ValueOf<GlobalState>) => void
}

const GlobalContext = createContext<Props>(
  /* Omit<Props, "globalData"> & { globalData: Maybe<Props["globalData"]>>  }*/
  {
    globalState: initialGlobalState,
    // @ts-expect-error because MainLayoutProviders.tsx takes care of not to invoke GlobalProvider if there is no data for it!
    globalData: null,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    setGlobalState: () => {},
  }
)

export const useGlobalState = () => {
  return useContext(GlobalContext)
}

export const GlobalProvider = ({
  globalData,
  currentTeamId,
  children,
}: { globalData: NonNullable<Props["globalData"]> } & {
  currentTeamId: string
  children?: ReactNode
}) => {
  const [globalState, setGlobalState] = useState({
    ...initialGlobalState,
    currentTeamId,
  })

  const setStateValue = (key: keyof GlobalState, value: ValueOf<GlobalState>) =>
    setGlobalState((prevState) => {
      return {
        ...prevState,
        [key]: value,
      }
    })

  return (
    <GlobalContext.Provider
      value={{
        globalState,
        globalData,
        setGlobalState: setStateValue,
      }}
    >
      {children}
    </GlobalContext.Provider>
  )
}
