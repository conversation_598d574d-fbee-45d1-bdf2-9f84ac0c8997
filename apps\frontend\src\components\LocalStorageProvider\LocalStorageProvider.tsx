import { createContext, ReactNode, useContext, useMemo } from "react"
import { DraftFunction, ImmerHook, useImmer } from "use-immer"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"

const storageKeys = [
  "calendarView",
  "visibleCalendarProviders",
  "selectedCalendarProviders",
] as const

export type LocalStorageKey = (typeof storageKeys)[number]

type LocalStorageState = {
  [key in LocalStorageKey]: string
}

const LocalStorageContext = createContext<
  | readonly [
      ImmerHook<LocalStorageState>[0],
      <K extends LocalStorageKey>(
        key: K
      ) => (
        args: LocalStorageState[K] | DraftFunction<LocalStorageState[K]>
      ) => void,
    ]
  | undefined
>(undefined)

export default function LocalStorageProvider({
  children,
}: {
  children: ReactNode
}) {
  const { globalData } = useGlobalState()
  const { actor } = globalData
  const { id: actorId } = actor
  const defaultValues = {
    calendarView: "week",
    visibleCalendarProviders: actorId,
    selectedCalendarProviders: actorId,
  }
  const lsKey = (key: LocalStorageKey) => `${key}-${actorId}`

  const initialValues = storageKeys.reduce(
    (acc, key) => {
      const storedValue = localStorage.getItem(lsKey(key))
      if (storedValue) {
        acc[key] = storedValue
      } else {
        acc[key] = defaultValues[key]
      }
      return acc
    },
    {} as Record<LocalStorageKey, string>
  )

  const [state, setState] = useImmer(initialValues)

  function setItem<K extends LocalStorageKey>(key: K) {
    return (
      arg: LocalStorageState[K] | DraftFunction<LocalStorageState[K]>
    ) => {
      setState((draft) => {
        if (typeof arg === "function") {
          arg(draft[key])
        } else {
          draft[key] = arg
        }
        localStorage.setItem(lsKey(key), draft[key])
      })
    }
  }
  return (
    <LocalStorageContext.Provider value={[state, setItem] as const}>
      {children}
    </LocalStorageContext.Provider>
  )
}

export const useLocalStorage = (key: LocalStorageKey) => {
  const lsContext = useContext(LocalStorageContext)
  if (!lsContext) {
    throw new Error(
      "useLocalStorage must be used within a LocalStorageProvider"
    )
  }

  const [state, setItem] = lsContext

  return useMemo(
    () => [state[key], setItem(key)] as const,
    [state[key], setItem, key]
  )
}
