import { useTranslation } from "react-i18next"
import { generatePath, Link } from "react-router-dom"
import { z } from "zod"

import { FormSection } from "components/FormSection/FormSection"
import Panel from "components/Panel/Panel"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { Heading, Input, notification } from "ui"
import Button from "ui/components/Button/Button"
import FormFooter from "ui/components/FormFooter/FormFooter"
import { Center } from "ui/components/Layout/Center"

import {
  useOrganisationManageQuery,
  useUpdateOrganisationConfigMutation,
} from "generated/graphql"

import { TeamsCard } from "../TeamsCard/TeamsCard"
import styles from "./OrganisationManage.module.css"

const baseSchema = z.object({
  emailSubjectPrefix: z.string(),
})

type OrganisationManageProps = {
  children: React.ReactNode
}

const OrganisationManage = ({ children }: OrganisationManageProps) => {
  const { t } = useTranslation("routes")

  const { data } = useOrganisationManageQuery()

  const [updateOrganisationConfig, { loading, error }] =
    useUpdateOrganisationConfigMutation({
      onCompleted: (data) => {
        if (data?.updateOrganisationConfig)
          notification.create({
            status: "success",
            message: t("saved"),
          })
      },
    })

  if (!data) return null

  const actor = data?.actor

  if (actor === null) throw new Error("actor is null")

  const { organisation } = actor
  const { departments, teams } = data

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = baseSchema.safeParse(data)

    if (!validatedInput.success) {
      return
    }

    updateOrganisationConfig({
      variables: {
        input: {
          id: organisation.id,
          emailSubjectPrefix: validatedInput.data.emailSubjectPrefix,
        },
      },
    })
  }

  const teamsWithoutDepartments = teams.filter(
    ({ department }) => department === null
  )

  return (
    <Center>
      <Heading size="large" as="h1">
        {organisation.name}
      </Heading>

      <form onSubmit={handleSubmit}>
        <FormSection showLine={false} iconName="settings-line">
          <Input
            label={t("manageOrg.emailSubjectPrefix")}
            defaultValue={organisation.config.emailSubjectPrefix}
            name="emailSubjectPrefix"
          />
          <FormFooter>
            <Button type="submit" disabled={loading} variant="filled">
              {t("submit")}
            </Button>
          </FormFooter>
          {error && <Panel status="error">{error.message}</Panel>}
        </FormSection>
      </form>

      <FormSection iconName={"hospital-line"}>
        <Heading size="default" as="h2">
          {t("manageOrg.orgTeams")}
        </Heading>

        <TeamsCard teams={teamsWithoutDepartments} />

        <FormFooter>
          <Button
            as={Link}
            variant="clear"
            to={RouteStrings.organisationManageCreateTeam}
          >
            {t("manageTeam.createTeam")}
          </Button>
        </FormFooter>
      </FormSection>

      <FormSection iconName={"hospital-line"}>
        <Heading size="default" as="h2">
          {t("manageOrg.departmentsAndTeams")}
        </Heading>

        <ul className={styles.departments}>
          {departments.map(({ id, name, teams }) => (
            <li key={id}>
              <Link to={getRecordPagePath(RouteStrings.departmentEdit, id)}>
                {name}
              </Link>
              <TeamsCard
                teams={teams.map(
                  ({ __typename, id, name, description, serviceType }) => ({
                    __typename,
                    id,
                    name,
                    department: null,
                    description,
                    serviceType,
                  })
                )}
                variant="spacy"
              />
            </li>
          ))}
        </ul>
        <FormFooter>
          <Button
            as={Link}
            variant="clear"
            to={generatePath(RouteStrings.organisationManageCreateDepartment, {
              organisationId: organisation.id,
            })}
          >
            {t("manageOrg.createDepartment")}
          </Button>
        </FormFooter>
      </FormSection>

      {children}
    </Center>
  )
}

export default OrganisationManage
