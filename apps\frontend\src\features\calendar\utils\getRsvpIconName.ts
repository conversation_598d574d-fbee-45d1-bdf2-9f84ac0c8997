import { IconName } from "@leviosa/assets"

import { ParticipantRsvpStatus } from "generated/graphql"

export const getRsvpIconName = (
  rsvpStatus: ParticipantRsvpStatus | null
): IconName => {
  switch (rsvpStatus) {
    case ParticipantRsvpStatus.Accepted:
      return "checkbox-circle-line"
    case ParticipantRsvpStatus.Declined:
      return "close-circle-line"
    default:
      return "error-warning-line"
  }
}
