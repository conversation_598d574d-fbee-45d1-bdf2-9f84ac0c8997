@layer components {
  .popover {
    position: relative;
    z-index: 50;
    display: flex;
    max-height: min(var(--popover-available-height, 400px), 400px);
    flex-direction: column;
    overflow: auto;
    overscroll-behavior: contain;
    border-radius: var(--radius-button-half);
    border: 2px solid var(--color-lev-blue);
    background-color: var(--color-background);
    padding: 0.5rem;
    color: var(--color-text);
    outline: 2px solid transparent;
    outline-offset: 2px;
    filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04))
      drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
  }
  .animated {
    opacity: 0;
    transition-duration: 100ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    scale: 0.95;
  }

  .animateDown {
    transform: translateY(-30px);
  }
  .animateDown[data-enter] {
    opacity: 1;
    transform: translateY(0%);
    scale: 1;
  }
  .animateDown[data-leave] {
    opacity: 0;
    transform: translateY(-30px);
  }
  .animateUp {
    transform: translateY(30px);
  }
  .animateUp[data-enter] {
    opacity: 1;
    transform: translateY(0%);
    scale: 1;
  }
  .animateUp[data-leave] {
    opacity: 0;
    transform: translateY(30px);
    scale: 0.95;
  }

  .menuHeading {
    padding: 0.5rem 1rem 0;
    margin: 0 -0.5rem;
  }

  .menuItem {
    margin: 0 -0.5rem;
  }
  .menuItemActive {
    background-color: hsla(214 97% 57% / 19%);
  }
  .menuItemButton {
    padding: 0.75rem 1rem;
    cursor: pointer;
    width: 100%;
  }

  .info {
    display: flex;
    flex-direction: row;
    padding: 16px 8px 8px;
    gap: 8px;
  }
  .infoIcon {
    width: 16px;
    height: 16px;
    margin-top: 1px;
  }
}
