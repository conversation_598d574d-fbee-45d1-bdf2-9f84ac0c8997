import { DashboardQuery } from "generated/graphql"

import { SearchSettingsType } from "./Dashboard"
import { sortDashboardData } from "./sortDashboardData"

// Mock data for testing
const mockDashboardRows = [
  {
    id: "1",
    subject: {
      name: "<PERSON><PERSON>",
      age: "45y",
      location: null,
    },
    priority: 1,
    reason: "Checkup",
    fromDate: "2021-01-01",
  },
  {
    id: "2",
    subject: {
      name: "<PERSON>",
      age: "32y",
      location: null,
    },
    priority: 2,
    reason: "Consultation",
    fromDate: "2021-05-15",
  },
  {
    id: "3",
    subject: {
      name: "<PERSON><PERSON><PERSON>",
      age: "18y",
      location: null,
    },
    priority: 3,
    reason: "Follow-up",
    fromDate: "2025-01-03",
  },
  {
    id: "4",
    subject: {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      age: "55y",
      location: null,
    },
    priority: null,
    reason: "Emergency",
    fromDate: "2023-01-04",
  },
  {
    id: "5",
    subject: {
      name: "<PERSON>",
      age: "28y",
      location: null,
    },
    priority: 0,
    reason: "Vaccination",
    fromDate: "2020-01-05",
  },
  {
    id: "6",
    subject: {
      name: "Aðal<PERSON>iður",
      age: "40y",
      location: null,
    },
    priority: 4,
    reason: "Routine",
    fromDate: "2023-12-31",
  },
] as unknown as DashboardQuery["team"]["rows"]

describe("sortDashboardData", () => {
  it('should return the original data when order is "default"', () => {
    const searchSettings: SearchSettingsType = {
      order: "default",
      column: "subject.name",
    }

    const result = sortDashboardData([...mockDashboardRows], searchSettings)

    expect(result).toEqual(mockDashboardRows)
  })

  it("should sort Icelandic names in ascending order correctly", () => {
    const searchSettings: SearchSettingsType = {
      order: "asc",
      column: "subject.name",
    }

    const result = sortDashboardData([...mockDashboardRows], searchSettings)

    // Correct Icelandic alphabetical order: 'Að', 'A', 'B', 'E', 'Þ', 'Ö'
    expect(result[0].subject.name).toBe("Aðalheiður")
    expect(result[1].subject.name).toBe("Anna Jónsdóttir")
    expect(result[2].subject.name).toBe("Björn Björnsson")
    expect(result[3].subject.name).toBe("Eva Ævisdóttir")
    expect(result[4].subject.name).toBe("Þór Þórsson")
    expect(result[5].subject.name).toBe("Örn Arnarson")
  })

  it("should sort Icelandic names in descending order correctly", () => {
    const searchSettings: SearchSettingsType = {
      order: "desc",
      column: "subject.name",
    }

    const result = sortDashboardData([...mockDashboardRows], searchSettings)

    // In reverse Icelandic alphabetical order: 'Ö', 'Þ', 'E', 'B', 'A', 'Að'
    expect(result[0].subject.name).toBe("Örn Arnarson")
    expect(result[1].subject.name).toBe("Þór Þórsson")
    expect(result[2].subject.name).toBe("Eva Ævisdóttir")
    expect(result[3].subject.name).toBe("Björn Björnsson")
    expect(result[4].subject.name).toBe("Anna Jónsdóttir")
    expect(result[5].subject.name).toBe("Aðalheiður")
  })

  it("should handle numeric sorting correctly for priority", () => {
    const searchSettings: SearchSettingsType = {
      order: "asc",
      column: "priority",
    }

    const result = sortDashboardData([...mockDashboardRows], searchSettings)

    // Priority should be sorted numerically (0, 1, 2, 3, 4, null)
    expect(result[0].priority).toBe(0)
    expect(result[1].priority).toBe(1)
    expect(result[2].priority).toBe(2)
    expect(result[3].priority).toBe(3)
    expect(result[4].priority).toBe(4)
    expect(result[5].priority).toBe(null) // null values are treated as MAX_SAFE_INTEGER
  })

  it("should handle descending numeric sorting for priority", () => {
    const searchSettings: SearchSettingsType = {
      order: "desc",
      column: "priority",
    }

    const result = sortDashboardData([...mockDashboardRows], searchSettings)

    // Priority in descending order (null, 4, 3, 2, 1, 0)
    expect(result[0].priority).toBe(null) // null values come first in descending order
    expect(result[1].priority).toBe(4)
    expect(result[2].priority).toBe(3)
    expect(result[3].priority).toBe(2)
    expect(result[4].priority).toBe(1)
    expect(result[5].priority).toBe(0)
  })

  it("should sort date strings correctly", () => {
    const searchSettings: SearchSettingsType = {
      order: "asc",
      column: "fromDate",
    }

    const result = sortDashboardData([...mockDashboardRows], searchSettings)

    // Dates should be sorted chronologically
    expect(result[0].fromDate).toBe("2020-01-05")
    expect(result[1].fromDate).toBe("2021-01-01")
    expect(result[2].fromDate).toBe("2021-05-15")
    expect(result[3].fromDate).toBe("2023-01-04")
    expect(result[4].fromDate).toBe("2023-12-31")
    expect(result[5].fromDate).toBe("2025-01-03")
  })

  it("should sort reason field with Icelandic characters correctly", () => {
    // Create custom data with Icelandic characters in the reason field
    const icelandicReasonData = [
      { ...mockDashboardRows[0], reason: "Þunglyndi" },
      { ...mockDashboardRows[1], reason: "Ofnæmi" },
      { ...mockDashboardRows[2], reason: "Astmi" },
      { ...mockDashboardRows[3], reason: "Æðaþrengsli" },
      { ...mockDashboardRows[4], reason: "Beinbrot" },
    ] as unknown as DashboardQuery["team"]["rows"]

    const searchSettings: SearchSettingsType = {
      order: "asc",
      column: "reason",
    }

    const result = sortDashboardData([...icelandicReasonData], searchSettings)

    // In Icelandic alphabetical order
    expect(result[0].reason).toBe("Astmi")
    expect(result[1].reason).toBe("Beinbrot")
    expect(result[2].reason).toBe("Ofnæmi")
    expect(result[3].reason).toBe("Þunglyndi")
    expect(result[4].reason).toBe("Æðaþrengsli")
  })

  it("should handle empty data array", () => {
    const emptyData = [] as unknown as DashboardQuery["team"]["rows"]
    const searchSettings: SearchSettingsType = {
      order: "asc",
      column: "subject.name",
    }

    const result = sortDashboardData(emptyData, searchSettings)

    expect(result).toEqual([])
  })
})
