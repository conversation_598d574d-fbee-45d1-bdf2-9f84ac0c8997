import { MockedProvider } from "@apollo/client/testing"
import { renderHook, waitFor } from "@testing-library/react"
// Mocked data that we use as result for the query
import { mockGlobalData } from "test/mocks/GlobalStateMock"

import { GlobalProvider } from "components/GlobalDataContext/GlobalData.context"
import usePermissions from "features/authentication/hooks/usePermissions"

import { GlobalDataDocument, PermissionKey } from "generated/graphql"

describe("usePermissions", () => {
  const mocks = [
    {
      request: {
        query: GlobalDataDocument,
      },
      result: {
        data: {
          globalData: mockGlobalData,
        },
      },
    },
  ]

  it("should return a function to check if user has a specific permission", async () => {
    const { AccountsProviderCreate, SubjectJournalInterventionPeriodEdit } =
      PermissionKey
    const { result } = renderHook(() => usePermissions(), {
      wrapper: ({ children }) => (
        <MockedProvider mocks={mocks} addTypename={false}>
          <GlobalProvider currentTeamId={"fakeId"} globalData={mockGlobalData}>
            {children}
          </GlobalProvider>
        </MockedProvider>
      ),
    })

    await waitFor(() => result.current)

    expect(result.current.hasPermission(AccountsProviderCreate)).toBe(true)
    expect(
      result.current.hasPermission(SubjectJournalInterventionPeriodEdit)
    ).toBe(false)
  })

  it("should return a function to check if user has all permissions", async () => {
    const {
      AccountsProviderCreate,
      AccountsProviderInvite,
      SubjectJournalInterventionPeriodEdit,
    } = PermissionKey
    const { result } = renderHook(() => usePermissions(), {
      wrapper: ({ children }) => (
        <MockedProvider mocks={mocks} addTypename={false}>
          <GlobalProvider currentTeamId={"fakeId"} globalData={mockGlobalData}>
            {children}
          </GlobalProvider>
        </MockedProvider>
      ),
    })

    await waitFor(() => result.current)

    expect(
      result.current.hasAllPermissions([
        AccountsProviderCreate,
        AccountsProviderInvite,
      ])
    ).toBe(true)
    expect(
      result.current.hasAllPermissions([
        AccountsProviderCreate,
        SubjectJournalInterventionPeriodEdit,
      ])
    ).toBe(false)
  })

  it("should return a function to check if user has some permissions", async () => {
    const {
      AccountsProviderCreate,
      AccountsProviderInvite,
      SubjectJournalInterventionPeriodEdit,
      SubjectJournalSign,
    } = PermissionKey
    const { result } = renderHook(() => usePermissions(), {
      wrapper: ({ children }) => (
        <MockedProvider mocks={mocks} addTypename={false}>
          <GlobalProvider currentTeamId={"fakeId"} globalData={mockGlobalData}>
            {children}
          </GlobalProvider>
        </MockedProvider>
      ),
    })

    await waitFor(() => result.current)

    expect(
      result.current.hasSomePermissions([
        AccountsProviderCreate,
        AccountsProviderInvite,
      ])
    ).toBe(true)
    expect(
      result.current.hasSomePermissions([
        AccountsProviderCreate,
        SubjectJournalInterventionPeriodEdit,
      ])
    ).toBe(true)
    expect(
      result.current.hasSomePermissions([
        SubjectJournalInterventionPeriodEdit,
        SubjectJournalSign,
      ])
    ).toBe(false)
  })
})
