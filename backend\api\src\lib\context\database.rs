use super::LeviosaContext;
use crate::lib::{
    context::ContextData,
    errors::{<PERSON><PERSON><PERSON>, Result},
};
use leviosa_domain_contracts::CustomError;
use sea_orm::{
    error::{DbErr, RuntimeErr, SqlxError},
    DatabaseConnection,
};
use sqlx::{pool::PoolConnection, Postgres};

impl ContextData {
    pub async fn db(&self) -> sqlx::Result<PoolConnection<Postgres>> {
        let pool = &self.database();
        pool.acquire().await
    }

    pub fn sea_db(&self) -> DatabaseConnection {
        self.sea_database().clone()
    }
}

pub trait Unique<T> {
    fn unique(self, cols: &[(&'static str, &'static str)]) -> Result<T>;
}

impl<T> Unique<T> for std::result::Result<T, DbErr> {
    fn unique(self, cols: &[(&'static str, &'static str)]) -> Result<T> {
        self.map_err(move |e| {
            match &e {
                DbErr::Query(RuntimeErr::SqlxError(SqlxError::Database(e)))
                | DbErr::Exec(RuntimeErr::SqlxError(SqlxError::Database(e))) => {
                    if let Some(constraint) = e.constraint() {
                        if let Some((_, arg_name)) =
                            cols.iter().find(|(col_name, _)| *col_name == constraint)
                        {
                            return CustomError::new(
                                Some(arg_name),
                                format!("{arg_name} must be unique"),
                            )
                            .into();
                        }
                    }
                }
                _ => {}
            }
            Error::Internal(anyhow::Error::new(e))
        })
    }
}

pub trait LeviosaDatabaseContext<'a> {
    fn db(&self) -> Result<DatabaseConnection>;
}

impl<'a, T: LeviosaContext<'a>> LeviosaDatabaseContext<'a> for T {
    fn db(&self) -> Result<DatabaseConnection> {
        let ctx = self.get()?;
        Ok(ctx.sea_database().clone())
    }
}
