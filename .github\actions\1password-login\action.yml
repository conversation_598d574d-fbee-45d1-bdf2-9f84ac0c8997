name: 'Login to 1password'
description: 'Login to 1password and return the cli to use'
inputs:
  email:
    required: true
    description: 'Email to use for login'
  password:
    required: true
    description: 'password to use for login'
  secretkey:
    required: true
    description: 'secretkey to use for login'
runs:
  using: 'composite'
  steps:
    - name: Check the OP_EMAIL variable
      shell: bash
      run: echo ${{ inputs.email }}
    - name: Fetch 1password cli
      shell: bash
      run: curl https://cache.agilebits.com/dist/1P/op2/pkg/v2.19.0/op_linux_amd64_v2.19.0.zip -o op.zip && unzip op.zip && chmod +x op && sudo mv op /usr/bin/op
    - name: Add 1password account
      shell: bash
      env:
        OP_SECRET_KEY: ${{ inputs.secretkey }}
        OP_PASSWORD: ${{ inputs.password }}
      run: echo $OP_PASSWORD | op --debug account add --address leviosa.1password.com --email ${{ inputs.email }}
    - name: Login to 1password
      shell: bash
      env:
        OP_PASSWORD: ${{ inputs.password }}
      run: sessionkey=$(echo $OP_PASSWORD | op signin --raw ) && echo OP_SESSION_leviosa=$sessionkey >> $GITHUB_ENV