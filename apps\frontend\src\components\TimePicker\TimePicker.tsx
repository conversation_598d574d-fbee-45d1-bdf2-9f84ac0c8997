import { ComboboxProps, VisuallyH<PERSON>den } from "@ariakit/react"
import c from "classnames"
import { matchSorter } from "match-sorter"
import {
  forwardRef,
  startTransition,
  useEffect,
  useId,
  useMemo,
  useState,
} from "react"
import { PatternFormat } from "react-number-format"

import {
  Combobox,
  ComboboxItem,
  ComboboxPopover,
  ComboboxProvider,
} from "components/Ariakit"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useComboboxStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { Label } from "ui"

import {
  generateTimeOptions,
  isValidTime,
  timeRegexString,
} from "../../utils/timeUtils"
import styles from "./TimePicker.module.css"

const TimePatternInput = forwardRef((props, ref) => (
  <PatternFormat
    {...props}
    getInputRef={ref}
    format="##:##"
    placeholder="HH:MM"
    pattern={timeRegexString}
  />
))

export type TimePickerProps = {
  className?: string
  minuteStep?: number
  startTime?: string
  endTime?: string
  pickEndOfStep?: boolean
  store: ReturnType<typeof useComboboxStore>
  error?: string
  scrollToTime?: string
  portal?: boolean
  required?: boolean
  id?: string
  hideLabel?: boolean
  label?: string
  inputClassName?: string
} & ComboboxProps

/**
 * Renders a time picker component.
 *
 * @component
 * @example
 * ```tsx
 * <TimePicker
 *   className="time-picker"
 *   minuteStep={20}
 *   startTime="00:00"
 *   endTime={startTime}
 *   pickEndOfStep={false}
 *   store={timePickerStore}
 *   error={errorMessage}
 *   scrollToTime="08:00"
 *   // ...rest of the props
 * />
 * ```
 *
 * @param {Object} props - The component props.
 * @param {string} [props.className=""] - The CSS class name for the component.
 * @param {number} [props.minuteStep=20] - The minute step for the time options.
 * @param {string} [props.startTime="00:00"] - The start time for the time options.
 * @param {string} [props.endTime=startTime] - The end time for the time options.
 * @param {boolean} [props.pickEndOfStep=false] - Displays options at the end of the step rather than the beginning. This is useful for time pickers that are used to set the end of a time range
 * @param {Object} props.store - The combobox store object for managing the state of the time picker.
 * @param {string} props.error - The error message to display, if any.
 * @param {string} [props.scrollToTime="08:00"] - The time to scroll to when the time picker is opened.
 *
 */
export const TimePicker = ({
  className = "",
  minuteStep = 20,
  startTime = "00:00",
  endTime = startTime,
  pickEndOfStep = false,
  store,
  error,
  scrollToTime,
  portal = true,
  required = false,
  id,
  hideLabel = true,
  label,
  inputClassName,
  ...rest
}: TimePickerProps) => {
  const allOptions = useMemo(
    () => generateTimeOptions(minuteStep, startTime, endTime, pickEndOfStep),
    [minuteStep, startTime, endTime, pickEndOfStep]
  )

  const generatedInputId = useId()

  const [showFilteredList, setShowFilteredList] = useState(false)
  const [searchValue, setSearchValue] = useState("")
  const matches = useMemo(
    () =>
      matchSorter(
        allOptions,
        searchValue.replace(":", "").replaceAll(" ", ""),
        {
          keys: ["label"],
        }
      ),
    [searchValue]
  )

  const isOpen = store.useState().open
  const currentValue = store.getState().value

  // Scroll to the `scrollToTime` option when the popover is opened
  useEffect(() => {
    if (!isOpen) return
    const scrollTo = scrollToTime || currentValue
    const popoverElement = store.getState().popoverElement
    const scrollToElement = popoverElement?.querySelector(
      `[data-value="${scrollTo}"]`
    )
    scrollToElement?.scrollIntoView({
      block: "start",
    })
  }, [scrollToTime, store, isOpen, currentValue])

  useEffect(() => {
    if (!isOpen) {
      setShowFilteredList(false)
    }
  }, [isOpen, currentValue])

  const options = showFilteredList ? matches : allOptions

  const validValue = isValidTime(currentValue) ? currentValue : undefined

  return (
    <ComboboxProvider
      setValue={(value) => {
        startTransition(() => {
          setShowFilteredList(true)
          setSearchValue(value)
        })
      }}
      store={store}
    >
      <div className={c(styles.wrap, className)}>
        <div className={styles.comboboxWrap}>
          <Label
            as="label"
            htmlFor={id || generatedInputId}
            visuallyHidden={hideLabel}
            className={required ? "labelIsRequired" : ""}
          >
            {label}
          </Label>

          <Combobox
            id={id || generatedInputId}
            render={<TimePatternInput />}
            autoSelect
            placeholder={"HH:MM"}
            pattern={timeRegexString}
            className={c(
              {
                [styles.error]: !!error,
              },
              inputClassName
            )}
            {...rest}
          />
        </div>
        <ComboboxPopover portal={portal} sameWidth className={styles.popover}>
          {options.length ? (
            options.map(({ value, label }) => (
              <ComboboxItem
                key={value}
                value={value}
                focusOnHover
                setValueOnClick={true}
                data-value={value}
                {...(value === validValue ? { "aria-selected": "true" } : {})}
                hideOnClick
              >
                {label}
              </ComboboxItem>
            ))
          ) : validValue ? (
            <ComboboxItem
              value={validValue}
              focusOnHover
              setValueOnClick={true}
              data-value={validValue}
              hideOnClick
            >
              {validValue}
            </ComboboxItem>
          ) : (
            <div className="no-results">No results found</div>
          )}
        </ComboboxPopover>
        {error && (
          <Tooltip
            tooltipContent={error}
            status="error"
            className={styles.errorTooltip}
            portal={portal}
          >
            <Icon name="error-warning-line" />
            <VisuallyHidden>Error</VisuallyHidden>
          </Tooltip>
        )}
      </div>
    </ComboboxProvider>
  )
}
