.wrap {
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: 1fr;
  justify-content: center;
  padding: 0 var(--page-padding);
}
.wrap > * {
  position: relative;
  height: calc(100vh - var(--header-height) - var(--grid-gap));
  overflow-y: scroll;
  overflow-x: hidden;
}

.wrap > * > div {
  position: relative;
  padding: 0 var(--grid-gap);
  padding-bottom: 200px;
  min-height: 100%;
}

.wrap > *:first-child > div {
  padding-left: 0;
}

.wrap > *:last-child > div {
  padding-right: 0;
}

.wrap > * > div::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 2px;
  background-color: var(--color-light-blue-3);
}

.wrap > *:first-of-type > div::before {
  display: none;
}
