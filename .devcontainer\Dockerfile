# See here for image contents: https://github.com/microsoft/vscode-dev-containers/tree/v0.233.0/containers/rust/.devcontainer/base.Dockerfile

# [Choice] Debian OS version (use bullseye on local arm64/Apple Silicon): buster, bullseye
ARG VARIANT="buster"
FROM mcr.microsoft.com/vscode/devcontainers/rust:0-${VARIANT}
RUN rustup default 1.87.0
RUN rustup component add clippy
RUN rustup component add rustfmt
RUN cargo install cargo-edit
RUN cargo install cargo-expand
RUN cargo install cargo-insta
RUN cargo install sqlx-cli
RUN sudo chown -R vscode /usr/local/cargo/

# [Optional] Uncomment this section to install additional packages.
# RUN apt-get update && export DEBIAN_FRONTEND=noninteractive \
#     && apt-get -y install --no-install-recommends <your-package-list-here>
