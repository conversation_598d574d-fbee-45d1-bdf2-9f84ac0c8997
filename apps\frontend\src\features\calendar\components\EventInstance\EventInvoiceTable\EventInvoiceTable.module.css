.wrapper {
  width: 100%;
  display: grid;
  gap: 4px;
  margin-top: -8px;
  margin-left: -10px;
}

.table tr {
  cursor: pointer;
}

.table td {
  width: 25%;
}
td.printCell {
  padding: 0;
  width: 6%;
}

.rightAlign {
  text-align: right;
}

.subject {
  max-width: 260px;
}

.table td * {
  vertical-align: middle;
}

.table a {
  color: inherit;
}

.createInvoiceButton {
  justify-self: flex-end;
}

.menuButton {
  transform: rotate(90deg);
}

.menuIcon {
  height: 1.125em;
  flex-basis: 1.125em;
}

.emailPopover {
  min-width: 360px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.emailPopoverHeading {
  color: var(--color-text-secondary);
  margin-left: 8px;
}

.inputWrapper {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 8px;
}
