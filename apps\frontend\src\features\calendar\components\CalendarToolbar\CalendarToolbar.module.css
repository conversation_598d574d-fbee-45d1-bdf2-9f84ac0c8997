.wrap {
  display: grid;
  grid-template-columns: max-content 1fr max-content auto;
  align-items: stretch;
  gap: 16px;
  margin-bottom: var(--grid-gap);
}

.wrap:has(.menuButton) {
  grid-template-columns: max-content 1fr max-content auto auto;
}

.viewSelect {
  justify-self: end;
  width: auto;
}
.label {
  align-self: center;
  /* ugly hack so the today navigation doesn't jump around */
  width: 120px;
}

.label.day {
  /* ugly hack so the today navigation doesn't jump around */
  width: 170px;
}

.subContent {
  margin-left: auto;
}

.menuButton {
  aspect-ratio: 1/1;
  display: flex;
  align-items: center;
  justify-content: center;
}
