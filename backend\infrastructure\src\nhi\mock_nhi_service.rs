use leviosa_domain_types::SubjectId;
use leviosa_domain::{
    errors::{CustomError, NotFoundError},
    nhi_service::{NhiService, NhiSubmissionResult, SubjectInsuranceStatus},
};
use async_trait::async_trait;

pub struct MockNhiService;

#[async_trait]
impl NhiService for MockNhiService {
    async fn get_subject_payment_status(
        &self,
        subject_id: SubjectId,
        _token: &str,
    ) -> leviosa_domain::errors::Result<SubjectInsuranceStatus> {
        // Mock implementation based on subject ID patterns
        let subject_id_str = subject_id.to_string();

        match subject_id_str.as_str() {
            // <PERSON> Franklin - uninsured
            id if id.contains("1511968219") => {
                tracing::info!("Mocking get_subject_payment_status for subject_id: {}", subject_id);
                Ok(SubjectInsuranceStatus::Uninsured)
            },
            // <PERSON> - not found
            id if id.contains("0212938219") => {
                tracing::info!("Mocking get_subject_payment_status for subject_id: {}", subject_id);
                Err(NotFoundError::custom("SubjectPaymentStatus", &subject_id_str, "subject_id").into())
            },
            // Yong Freddie - error
            id if id.contains("2901128219") => {
                tracing::info!("Mocking get_subject_payment_status for subject_id: {}", subject_id);
                Err(CustomError::new(None, "Failed to get payment status".to_string()).into())
            },
            // Anyone else - insured
            _ => {
                tracing::info!("Mocking get_subject_payment_status for subject_id: {}", subject_id);
                Ok(SubjectInsuranceStatus::Insured {
                    insurance_category: "ALM".to_string(),
                    maximum_payable_by_subject: 15000.0,
                    insurance_percentage: 10,
                    payment_status_serial_number: 1_234_567_890,
                })
            }
        }
    }

    async fn submit_invoice(
        &self,
        medical_bill: leviosa_domain::nhi_service::MedicalBill,
        _on_behalf_of: &str,
        _token: &str,
    ) -> leviosa_domain::errors::Result<NhiSubmissionResult> {
        tracing::info!("Mocking submit_invoice for medical_bill: {:?}", medical_bill);
        Ok(NhiSubmissionResult {
            request_xml: String::from("<xml>Mock XML</xml>"),
            request_json: String::from("{\"json\": \"Mock JSON\"}"),
            response_xml: String::from("<xml>Mock Response XML</xml>"),
        })
    }
}

pub fn get_mock_nhi_service() -> MockNhiService {
    MockNhiService
}
