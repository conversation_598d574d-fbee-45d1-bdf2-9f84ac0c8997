import Icon from "components/Icon/Icon"

import { ComboboxItem, SelectItem } from "../Ariakit"
import type { OptionsProps } from "../Select/Options"
import styles from "../Select/Select.module.css"

export const Options = <T extends string | string[]>({
  options,
  value: optionValue,
  noOptionsPlaceholder,
  onClick,
  isLoading,
  hasComboboxValue,
}: OptionsProps<T>) => {
  if (isLoading)
    return (
      <div className={styles.optionsContent}>
        <Icon name="loader-4-line" spin />
      </div>
    )

  if (!options.length && noOptionsPlaceholder !== "" && hasComboboxValue)
    return <div className={styles.optionsContent}>{noOptionsPlaceholder}</div>

  return (
    <>
      {options.map(({ value, label, subContent, direction }) => {
        return (
          <SelectItem
            key={value}
            onClick={() => {
              onClick?.(value)
            }}
            value={value}
            focusOnHover
            render={(props) => (
              <ComboboxItem
                {...props}
                data-is-multi={
                  Array.isArray(optionValue) && optionValue.includes(value)
                }
                subContent={subContent}
                direction={direction}
              >
                {label}
              </ComboboxItem>
            )}
          ></SelectItem>
        )
      })}
    </>
  )
}
