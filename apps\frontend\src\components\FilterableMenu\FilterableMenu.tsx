import { MenuProvider } from "@ariakit/react"
import c from "classnames"
import { matchSorter } from "match-sorter"
import { startTransition, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  ComboboxProvider,
  Menu,
  MenuButton,
  MenuButtonProps,
} from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { ButtonText } from "ui"

import { Option } from "../Ariakit/types/Option"
import styles from "./FilterableMenu.module.css"

export type FilterableMenuProps = {
  onSelect?: (option: Option<string>) => void
  menuStore: ReturnType<typeof useMenuStore>
  options: Option<string>[]
  placeholder?: string
  label?: string
  className?: string
  menuButton?: React.ReactNode
  renderItem?: (label: string) => React.ReactNode
  alwaysVisibleMenuOption?: Option<string>
} & Omit<MenuButtonProps, "store" | "ref" | "onSelect">

export const FilterableMenu = ({
  onSelect,
  menuStore,
  options,
  renderItem,
  icon = <Icon name="add-line" />,
  placeholder = "Search...",
  label,
  menuButton,
  alwaysVisibleMenuOption,
  className,
  ...rest
}: FilterableMenuProps) => {
  const { t } = useTranslation()

  const [searchValue, setSearchValue] = useState("")

  const matches = useMemo(() => {
    return searchValue
      ? matchSorter(options, searchValue, {
          keys: ["label"],
        })
      : options
  }, [searchValue, options])

  const menuOptions = [...matches]
  if (alwaysVisibleMenuOption) {
    menuOptions.push(alwaysVisibleMenuOption)
  }

  return (
    <ComboboxProvider
      resetValueOnHide
      value={searchValue}
      setValue={(value) => {
        startTransition(() => {
          setSearchValue(value)
        })
      }}
    >
      <MenuProvider store={menuStore}>
        <div className={c(styles.wrap, className)}>
          {menuButton || (
            <>
              <MenuButton icon={icon} className={styles.button} {...rest} />
              {label && (
                <ButtonText
                  size="small"
                  className={styles.label}
                  secondary
                  onClick={() => menuStore.setOpen(true)}
                >
                  {label}
                </ButtonText>
              )}
            </>
          )}
        </div>
        <Menu className={styles.menu}>
          <Combobox
            placeholder={placeholder}
            autoSelect
            className={styles.combobox}
          />
          <ComboboxList alwaysVisible>
            {menuOptions.length > 0 ? (
              <>
                {menuOptions.map((option) => {
                  return renderItem ? (
                    renderItem(option.value)
                  ) : (
                    <ComboboxItem
                      key={option.value}
                      value={option.label}
                      focusOnHover
                      setValueOnClick={false}
                      className={`${styles.item}`}
                      onClick={() => {
                        menuStore.setOpen(false)
                        onSelect?.(option)
                        setSearchValue("")
                      }}
                    />
                  )
                })}
              </>
            ) : (
              <ComboboxItem value={t("No results Found")} />
            )}
          </ComboboxList>
        </Menu>
      </MenuProvider>
    </ComboboxProvider>
  )
}
