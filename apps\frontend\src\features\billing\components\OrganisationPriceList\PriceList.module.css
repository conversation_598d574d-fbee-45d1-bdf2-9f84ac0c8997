.tableHead {
  white-space: nowrap;
}

.buttonStar {
  opacity: 0;
  text-align: right;
}

.buttonColumn {
  line-height: 0;
  text-align: center;
  width: 120px;
  margin-right: 24px;
}

.buttonStar[data-is-starred="true"] {
  opacity: 1;
}

.priceListRow:hover .buttonStar {
  opacity: 1;
}

.numericValue {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.input {
  width: calc(50% - var(--grid-gap) / 2);
}

.truncateText {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  box-sizing: border-box;
  overflow: hidden;
}

.isLoading {
  opacity: 0.6;
}

.billingCode {
  min-width: 110px;
}
