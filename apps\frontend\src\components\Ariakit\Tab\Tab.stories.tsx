import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite"

import "./Tab"
import { Tab, <PERSON>b<PERSON>ist, TabPanel, TabProvider } from "./Tab"

const meta: Meta<typeof Tab> = {
  title: "Ariakit/Tab",
  component: Tab,
}

export default meta

type Story = StoryObj<typeof Tab>

export const TabExample: Story = {
  render: () => {
    const defaultSelectedId = "default-selected-tab"

    return (
      <TabProvider defaultSelectedId={defaultSelectedId}>
        <div style={{ display: "grid", gap: 16 }}>
          <TabList aria-label="Groceries">
            <Tab>Fruits</Tab>
            <Tab id={defaultSelectedId}>Vegetables</Tab>
            <Tab>Meat</Tab>
          </TabList>
          <TabPanel>
            <ul>
              <li>🍎 Apple</li>
              <li>🍇 Grape</li>
              <li>🍊 Orange</li>
            </ul>
          </TabPanel>
          <TabPanel tabId={defaultSelectedId}>
            <ul>
              <li>🥕 Carrot</li>
              <li>🧅 Onion</li>
              <li>🥔 Potato</li>
            </ul>
          </TabPanel>
          <TabPanel>
            <ul>
              <li>🥩 Beef</li>
              <li>🍗 Chicken</li>
              <li>🥓 Pork</li>
            </ul>
          </TabPanel>
        </div>
      </TabProvider>
    )
  },
}
