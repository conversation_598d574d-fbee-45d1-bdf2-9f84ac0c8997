import { FormGroup } from "@ariakit/react"
import { Meta, <PERSON> } from "@storybook/react-vite"
import { addWeeks } from "date-fns"
import React, { useState } from "react"

import { Button } from "ui"

import DateInput, { DateInputProps } from "./DateInput"

export default {
  title: "Form Components/DateInput",
  component: DateInput,
} as Meta<typeof DateInput>

export const DateInputDefault: Story<DateInputProps> = (args) => {
  return (
    <DateInput
      {...args}
      onKeyUp={(e) => console.log(e)}
      value="1232-05-23T00:00:00+04:37"
      onChange={(value) => console.log(value)}
    />
  )
}

DateInputDefault.args = {
  label: "Date of Birth",
}

DateInputDefault.parameters = {
  layout: "centered",
}

export const DateInputFormSubmit: Story<DateInputProps> = (args) => {
  const onFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())
    console.log(data)
  }

  return (
    <FormGroup as="form" onSubmit={onFormSubmit}>
      <DateInput {...args} max="2023-01-12" name="birthDate" />

      <Button type="submit">Submit</Button>
    </FormGroup>
  )
}

DateInputFormSubmit.args = {
  label: "Date of Birth",
  name: "birthDate",
}

DateInputFormSubmit.parameters = {
  layout: "centered",
}

export const DateInputHandlers: Story<DateInputProps> = (args) => {
  return (
    <DateInput
      {...args}
      onBlur={(e) => console.log(e)}
      onFocus={(e) => console.log(e)}
      onMouseEnter={(e) => console.log(e)}
      onMouseLeave={(e) => console.log(e)}
      onKeyDown={(e) => console.log(e)}
      required
      onChange={(value) => console.log(value)}
      name="birthDate"
    />
  )
}

DateInputHandlers.args = {
  label: "Date of Birth",
  name: "birthDate",
}

DateInputHandlers.parameters = {
  layout: "centered",
}

export const SynchedDateInputs: Story<DateInputProps> = () => {
  const [value, setValue] = useState("2021-05-23")
  return (
    <div>
      <DateInput
        label="Week Earlier"
        value={value}
        onChange={(value) => {
          console.log(value)
          value && typeof value === "string" && setValue(value)
        }}
        name="first"
      />
      <DateInput
        label="Week Later"
        value={addWeeks(new Date(value), 1).toISOString()}
        onChange={(value) =>
          value &&
          typeof value === "string" &&
          setValue(addWeeks(new Date(value), -1).toISOString())
        }
        name="first"
      />
    </div>
  )
}
