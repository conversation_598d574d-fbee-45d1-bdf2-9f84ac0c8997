//! Adapter to bridge between domain CalendarNotificationService and contracts CalendarNotificationService

use crate::{
    calendar_notification_service::CalendarNotificationService as DomainCalendarNotificationService,
    errors::Result,
    notifications::Message,
    repos::{IOrganisationRepo, ISubjectRepo},
};
use async_trait::async_trait;
use leviosa_domain_contracts::CalendarNotificationService as ContractsCalendarNotificationService;

/// Adapter that implements domain CalendarNotificationService using contracts CalendarNotificationService
pub struct CalendarNotificationAdapter<'a> {
    contracts_service: &'a dyn ContractsCalendarNotificationService,
    token: String,
}

impl<'a> CalendarNotificationAdapter<'a> {
    pub fn new(
        contracts_service: &'a dyn ContractsCalendarNotificationService,
        token: String,
    ) -> Self {
        Self {
            contracts_service,
            token,
        }
    }
}

#[async_trait]
impl<'a> DomainCalendarNotificationService for CalendarNotificationAdapter<'a> {
    async fn send_scheduled_reminders(
        &self,
        _subject_repo: &dyn ISubjectRepo,
        _organisation_repo: &dyn IOrganisationRepo,
    ) -> Result<Vec<Message>> {
        // For now, return empty list since the contracts interface doesn't support this complex operation
        // In a full implementation, this would:
        // 1. Query repositories to find scheduled reminders
        // 2. Convert to contracts format
        // 3. Call contracts service methods
        // 4. Convert results back to domain format
        
        tracing::warn!("CalendarNotificationAdapter::send_scheduled_reminders not fully implemented - using contracts interface requires refactoring");
        Ok(vec![])
    }

    async fn create_automatic_message(
        &self,
        _input: crate::calendar_notification_service::CreateAutomaticMessageInput,
    ) -> Result<()> {
        // Similar placeholder - would need to convert input and call appropriate contracts method
        tracing::warn!("CalendarNotificationAdapter::create_automatic_message not fully implemented");
        Ok(())
    }

    async fn delete_scheduled_reminders(
        &self,
        _input: crate::calendar_notification_service::DeleteScheduledRemindersInput,
    ) -> Result<()> {
        // Similar placeholder - would need to convert input and call appropriate contracts method
        tracing::warn!("CalendarNotificationAdapter::delete_scheduled_reminders not fully implemented");
        Ok(())
    }

    async fn send_appointment_reminder(
        &self,
        event_instance_id: crate::calendar::EventInstanceId,
    ) -> Result<()> {
        // Convert domain EventInstanceId to contracts EventInstanceId
        let contracts_event_id = leviosa_domain_types::EventInstanceId(event_instance_id.0);
        
        // Call contracts service
        self.contracts_service
            .send_appointment_reminder(contracts_event_id, &self.token)
            .await
            .map_err(|e| e.into())
    }

    async fn send_appointment_confirmation(
        &self,
        event_instance_id: crate::calendar::EventInstanceId,
    ) -> Result<()> {
        // Convert domain EventInstanceId to contracts EventInstanceId
        let contracts_event_id = leviosa_domain_types::EventInstanceId(event_instance_id.0);
        
        // Call contracts service
        self.contracts_service
            .send_appointment_confirmation(contracts_event_id, &self.token)
            .await
            .map_err(|e| e.into())
    }

    async fn send_appointment_cancellation(
        &self,
        event_instance_id: crate::calendar::EventInstanceId,
    ) -> Result<()> {
        // Convert domain EventInstanceId to contracts EventInstanceId
        let contracts_event_id = leviosa_domain_types::EventInstanceId(event_instance_id.0);
        
        // Call contracts service
        self.contracts_service
            .send_appointment_cancellation(contracts_event_id, &self.token)
            .await
            .map_err(|e| e.into())
    }
}
