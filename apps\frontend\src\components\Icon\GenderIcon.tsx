import { useTranslation } from "react-i18next"

import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"

import { GenderId } from "generated/graphql"

export type GenderIconProps = {
  genderId: GenderId
}

export const genderIconNames: Record<
  GenderId.Female | GenderId.Male | GenderId.Other | GenderId.Unknown,
  IconName
> = {
  [GenderId.Female]: "women-line",
  [GenderId.Male]: "men-line",
  [GenderId.Other]: "other-gender",
  [GenderId.Unknown]: "other-gender",
}
export default function GenderIcon({ genderId }: GenderIconProps) {
  const { t } = useTranslation()

  return <Icon aria-label={t(genderId)} name={genderIconNames[genderId]} />
}
