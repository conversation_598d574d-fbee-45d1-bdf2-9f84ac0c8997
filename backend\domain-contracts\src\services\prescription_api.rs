//! Prescription API service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PrescriptionRequest {
    pub prescription_id: DrugPrescriptionId,
    pub subject_id: SubjectId,
    pub provider_id: ProviderId,
    pub medications: Vec<Medication>,
    pub pharmacy_info: Option<PharmacyInfo>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Medication {
    pub name: String,
    pub dosage: String,
    pub frequency: String,
    pub duration: String,
    pub instructions: Option<String>,
    pub quantity: i32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PharmacyInfo {
    pub name: String,
    pub address: String,
    pub phone: Option<String>,
    pub email: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PrescriptionResponse {
    pub prescription_id: String,
    pub status: PrescriptionStatus,
    pub submitted_at: DateTime<Utc>,
    pub tracking_number: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum PrescriptionStatus {
    Submitted,
    Processing,
    Filled,
    Ready,
    Dispensed,
    Cancelled,
}

/// Contract for prescription API service
#[async_trait]
#[mockall::automock]
pub trait PrescriptionApi: Send + Sync {
    /// Submit a prescription to pharmacy
    async fn submit_prescription(
        &self,
        request: PrescriptionRequest,
        token: &str,
    ) -> Result<PrescriptionResponse>;

    /// Get prescription status
    async fn get_prescription_status(
        &self,
        prescription_id: &str,
        token: &str,
    ) -> Result<PrescriptionStatus>;
}
