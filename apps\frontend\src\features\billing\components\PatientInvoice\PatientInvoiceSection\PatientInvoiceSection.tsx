import { useTranslation } from "react-i18next"

import {
  useComboboxStore,
  useFilter,
  useSelectStore,
} from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import { Grid, Heading, Input } from "ui"

import {
  GetInvoiceQuery,
  useEditInvoiceMutation,
  useGetInvoiceIssuersQuery,
  useGetProvidersQuery, // useUpdateInvoiceIssuerMutation,
} from "generated/graphql"

import { PatientInvoiceFooter } from "../PatientInvoiceFooter/PatientInvoiceFooter"
import { PatientInvoiceTable } from "../PatientInvoiceTable/PatientInvoiceTable"
import styles from "./PatientInvoiceSection.module.css"

type PatientInvoiceSectionProps = {
  invoice: GetInvoiceQuery["invoice"]
}

export const PatientInvoiceSection = ({
  invoice,
}: PatientInvoiceSectionProps) => {
  const { t } = useTranslation()

  const [editInvoice] = useEditInvoiceMutation()

  const comboboxIssuer = useComboboxStore()
  const { value: issuerValue } = comboboxIssuer.useState()
  const selectStoreIssuer = useSelectStore({
    combobox: comboboxIssuer,
    defaultValue: invoice.issuer?.id || "",
    focusLoop: "vertical",
  })

  const comboboxProvider = useComboboxStore()
  const { value: providerValue } = comboboxProvider.useState()

  const selectStoreProvider = useSelectStore({
    combobox: comboboxProvider,
    defaultValue: invoice.provider?.id || "",
    focusLoop: "vertical",
  })

  const { data: issuers } = useGetInvoiceIssuersQuery({
    variables: {
      filter: null,
    },
  })
  const { data: providersData } = useGetProvidersQuery()

  // const [updateIssuer] = useUpdateInvoiceIssuerMutation()

  const issuerOptions =
    issuers?.invoiceIssuers.map((issuer) => ({
      value: issuer.id,
      label: issuer.title,
    })) || []

  const { filteredList: filteredListIssuers } = useFilter({
    defaultItems: issuerOptions,
    value: issuerValue,
  })

  const providerOptions =
    providersData?.providers.map((provider) => ({
      value: provider.id,
      label: provider.name,
    })) || []

  const { filteredList: filteredProviderOptions } = useFilter({
    defaultItems: providerOptions,
    value: providerValue,
  })

  return (
    <>
      <Heading size="large">{t("Invoice")}</Heading>
      <Grid className={styles.inputs}>
        <FiltrableSelect
          className={styles.input}
          label={t("Provider")}
          sameWidth
          options={providerOptions}
          selectStore={selectStoreProvider}
          comboboxStore={comboboxProvider}
          filteredOptions={filteredProviderOptions}
          onSelectChange={(value) => {
            if (typeof value !== "string") return
            editInvoice({
              variables: {
                input: {
                  id: invoice.id,
                  providerId: value,
                },
              },
            })
          }}
        />

        <FiltrableSelect
          className={styles.input}
          label={t("Issuer")}
          sameWidth
          options={issuerOptions}
          selectStore={selectStoreIssuer}
          comboboxStore={comboboxIssuer}
          filteredOptions={filteredListIssuers}
          onSelectChange={(value) => {
            if (typeof value !== "string") return
            editInvoice({
              variables: {
                input: {
                  id: invoice.id,
                  issuerId: value,
                },
              },
            })
          }}
        />

        <Input
          className={styles.input}
          defaultValue={invoice.reference || ""}
          label={t("Referral Id")}
          onBlur={(e) => {
            editInvoice({
              variables: {
                input: {
                  id: invoice.id,
                  reference: e.target.value,
                },
              },
            })
          }}
        />
      </Grid>
      <PatientInvoiceTable invoice={invoice} />
      <PatientInvoiceFooter invoice={invoice} />
    </>
  )
}
