import {
  useComboboxStore,
  useFilter,
  useSelectStore,
} from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"

import { useGetTeamsForCalendarQuery } from "generated/graphql"

import styles from "./SelectTeam.module.css"

type SelectTeamProps = {
  teamId?: string
}

export const SelectTeam = ({ teamId }: SelectTeamProps) => {
  const comboboxStore = useComboboxStore()

  const { value: comboboxValue } = comboboxStore.useState()

  const { data } = useGetTeamsForCalendarQuery()

  const teamsOptions =
    data?.teams.map((team) => ({
      label: team.name,
      value: team.id,
    })) ?? []

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: teamId || "",
    focusLoop: "vertical",
  })

  const { filteredList } = useFilter({
    defaultItems: teamsOptions,
    value: comboboxValue,
  })

  return (
    <FiltrableSelect
      options={teamsOptions}
      sameWidth
      filteredOptions={filteredList}
      selectStore={selectStore}
      comboboxStore={comboboxStore}
      label="Team"
      name="teamId"
      className={styles.span6}
      hideMessage
    />
  )
}
