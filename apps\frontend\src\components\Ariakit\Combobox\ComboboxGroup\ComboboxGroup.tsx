import {
  ComboboxGroup as ComboboxGroupAriaKit,
  ComboboxGroupProps,
  ComboboxGroupLabel as ComboboxGroupLabelAriaKit,
  ComboboxGroupLabelProps,
} from "@ariakit/react"
import c from "classnames"

import { Heading } from "ui"

import styles from "./ComboboxGroup.module.css"

export const ComboboxGroup = (props: ComboboxGroupProps) => {
  return <ComboboxGroupAriaKit {...props} />
}
export const ComboboxGroupLabel = (props: ComboboxGroupLabelProps) => {
  return (
    <Heading
      size="xsmall"
      as={ComboboxGroupLabelAriaKit}
      {...props}
      className={c(styles.groupLabel, props.className)}
    />
  )
}
