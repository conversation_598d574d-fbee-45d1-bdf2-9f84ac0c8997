import c from "classnames"
import { matchSorter } from "match-sorter"
import { useMemo, useEffect, useRef } from "react"
import { useTranslation } from "react-i18next"
import { useSearchParams } from "react-router-dom"

import { Icon } from "@leviosa/components"

import { Combobox, ComboboxItem, ComboboxPopover } from "components/Ariakit"
import { useComboboxStore } from "components/Ariakit/hooks"
import useCalendarState from "features/calendar/hooks/useCalendarState"
import { Button, Text } from "ui"

import { useCalendar } from "../Calendar/CalendarProvider"
import { calendarHeaderId } from "../RBCHeader/RBCHeader"
import styles from "./ProviderPicker.module.css"

export default function ProviderPicker() {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "calendar",
  })
  const { allProviders } = useCalendar()
  const [searchParams, setSearchParams] = useSearchParams()

  const combobox = useComboboxStore({
    placement: "bottom",
    resetValueOnHide: true,
  })

  // Use a ref to store the calendar header element reference
  const calendarHeaderRef = useRef<HTMLElement | null>(null)
  // Find the calendar header element and store in ref, this is used for finalFocus after selecting a provider
  useEffect(() => {
    calendarHeaderRef.current = document.getElementById(calendarHeaderId)
  }, [])

  // Get the current search term from the combobox
  const searchTerm = combobox.useState("value") || ""

  // Get visible provider values from the store (only these affect the calendar)

  const {
    visibleProviders: visibleProviderIds,
    selectedProviders,
    removeSelectedProvider,
  } = useCalendarState()

  // Get all available providers
  const allProviderOptions = useMemo(
    () =>
      allProviders?.map((provider) => ({
        label: provider.name,
        value: provider.id,
      })) || [],
    [allProviders]
  )

  // Build the list of selected providers for display
  const providerList = useMemo(() => {
    return allProviderOptions
      .filter((provider) => selectedProviders.includes(provider.value))
      .sort((a, b) =>
        a.label.localeCompare(b.label, undefined, { sensitivity: "base" })
      )
  }, [allProviderOptions, selectedProviders])

  // Filter providers based on the search term (now handled by combobox)
  const notSelectedProviderOptions = useMemo(() => {
    const notSelectedProviders = allProviderOptions.filter(
      (provider) => !selectedProviders.includes(provider.value)
    )
    return matchSorter(notSelectedProviders, searchTerm, {
      keys: ["label"],
    })
  }, [allProviderOptions, searchTerm, selectedProviders])

  const handleProviderSelect = (providerId: string) => {
    const newSelected = [...visibleProviderIds, providerId]
    searchParams.set("provider", newSelected.join(","))
    setSearchParams(searchParams, { replace: true })
  }

  const handleRemoveProvider = (providerId: string) => {
    // If the provider is visible, we need to update searchParams
    if (visibleProviderIds.includes(providerId)) {
      const newSelected = visibleProviderIds.filter((id) => id !== providerId)
      searchParams.set("provider", newSelected.join(","))
      setSearchParams(searchParams, { replace: true })
    }
    // Setting timeout to ensure the provider is removed from the URL before the state is updated
    setTimeout(() => removeSelectedProvider(providerId), 0)
  }

  const handleToggleProviderVisibility = (providerId: string) => {
    let newSelected = [...visibleProviderIds]
    if (visibleProviderIds.includes(providerId)) {
      // Remove from visibleProviders
      newSelected = visibleProviderIds.filter((id) => id !== providerId)
    } else {
      newSelected.push(providerId)
    }
    searchParams.set("provider", newSelected.join(","))
    setSearchParams(searchParams, { replace: true })
  }

  // Check if a provider is currently visible
  const isProviderVisible = (providerId: string) => {
    return visibleProviderIds.includes(providerId)
  }

  return (
    <div className={styles.container}>
      <ul className={styles.selectedProviders}>
        {providerList.map(({ label, value }) => {
          const providerVisible = isProviderVisible(value)
          const isLastVisibleProvider =
            providerVisible && visibleProviderIds.length === 1
          const LinkOrDiv = isLastVisibleProvider ? "div" : "button"

          return (
            <li
              key={value}
              className={c(styles.providerWrapper, {
                [styles.providerHidden]: !providerVisible,
              })}
            >
              <LinkOrDiv
                className={styles.providerToggleButton}
                onClick={() => handleToggleProviderVisibility(value)}
                title={
                  providerVisible
                    ? tRoutes("hideProvider")
                    : tRoutes("showProvider")
                }
                disabled={isLastVisibleProvider}
              >
                <Text
                  size="small"
                  className={styles.providerLabel}
                  title={label} // Add title attribute to show full name on hover
                >
                  {label}
                </Text>
                {!isLastVisibleProvider && (
                  <Icon name={providerVisible ? "eye-line" : "eye-off-line"} />
                )}
              </LinkOrDiv>
              {!isLastVisibleProvider && (
                <Button
                  onClick={() => handleRemoveProvider(value)}
                  icon={!isLastVisibleProvider && <Icon name="close-line" />}
                  variant="clear"
                  size="small"
                  className={styles.removeButton}
                  title={tRoutes("removeProvider")}
                  disabled={isLastVisibleProvider}
                />
              )}
            </li>
          )
        })}
      </ul>

      <div className={styles.comboboxWrapper}>
        <Icon name="search-line" className={styles.searchIcon} />
        <Combobox
          store={combobox}
          autoSelect
          placeholder={tRoutes("addProvider")}
          aria-label={tRoutes("addProvider")}
          className={styles.combobox}
        />
      </div>

      <ComboboxPopover
        store={combobox}
        portal={true}
        sameWidth={true}
        finalFocus={calendarHeaderRef.current}
        className={styles.popover}
        hideOnInteractOutside
        hideOnEscape
      >
        {notSelectedProviderOptions.length > 0 ? (
          notSelectedProviderOptions.map(({ label, value }) => (
            <ComboboxItem
              key={value}
              value={value}
              onClick={() => handleProviderSelect(value)}
              setValueOnClick={false}
            >
              {label}
            </ComboboxItem>
          ))
        ) : (
          <Text className={styles.noResults}>{tRoutes("noProviderFound")}</Text>
        )}
      </ComboboxPopover>
    </div>
  )
}
