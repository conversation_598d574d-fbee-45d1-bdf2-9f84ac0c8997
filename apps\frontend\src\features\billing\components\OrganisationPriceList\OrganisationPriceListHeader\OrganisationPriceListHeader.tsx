import { useTranslation } from "react-i18next"

import { Heading } from "ui"

import styles from "./OrganisationPriceListHeader.module.css"

type OrganisationPriceListHeaderProps = {
  children: React.ReactNode
}

export const OrganisationPriceListHeader = ({
  children,
}: OrganisationPriceListHeaderProps) => {
  const { t } = useTranslation()

  return (
    <div className={styles.header}>
      <Heading size="large">{t("Organisation Price List")}</Heading>
      {children}
    </div>
  )
}
