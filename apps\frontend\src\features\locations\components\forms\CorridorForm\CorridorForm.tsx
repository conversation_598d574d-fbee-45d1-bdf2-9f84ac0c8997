import { ApolloError } from "@apollo/client"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import Panel from "components/Panel/Panel"
import { Button, Heading, Input } from "ui"
import { Center } from "ui/components/Layout/Center"

import stylesLocationForm from "../LocationForm.module.css"
import styles from "./CorridorForm.module.css"

const baseSchema = z.object({
  label: z.string(),
})

type CorridorFormProps = {
  label?: string
  onSubmit: (data: z.infer<typeof baseSchema>) => void
  onCancel: () => void
  error?: ApolloError
  loading: boolean
}

// Remove after InlineTextForm has been merged
export const CorridorForm = ({
  onSubmit,
  onCancel,
  loading,
  error,
  label,
}: CorridorFormProps) => {
  const { t } = useTranslation()
  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setValidationError(null)

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = baseSchema.safeParse(data)

    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    onSubmit?.(validatedInput.data)
  }

  return (
    <Panel
      as="form"
      onSubmit={handleSubmit}
      className={stylesLocationForm.panelFrom}
    >
      <Center as={Heading} size="display">
        {!label ? t("Add Corridor") : t("Edit Corridor")}
      </Center>

      <br />
      <div className={styles.wrap}>
        <Input
          label="Label"
          name="label"
          type="text"
          required={true}
          hideLabel={true}
          className={styles.inputWrap}
          defaultValue={label}
          inputProps={{
            className: styles.input,
          }}
        />

        <div className={styles.footer}>
          <Button
            size="large"
            type="submit"
            variant="filled"
            disabled={loading}
            className={styles.saveButton}
          >
            {t("Save")}
          </Button>
          <Button
            disabled={loading}
            variant={"clear"}
            size="large"
            onClick={onCancel}
          >
            {t("Cancel")}
          </Button>
        </div>
      </div>

      {(error || validationError) && (
        <Panel status="error">
          {validationError?.message}
          {error?.message}
        </Panel>
      )}
    </Panel>
  )
}
