mutation SetNhiPaysFullInvoiceAmount($invoiceId: UUID!, $nhiPaysAll: Boolean!) {
  setNhiPaysFullInvoiceAmount(invoiceId: $invoiceId, nhiPaysAll: $nhiPaysAll) {
    id
    total
    totalPayableBySubject
    totalPayableByInsurance
    totalLineItemsDiscount
    subjectDiscount
    totalPayableBySubjectBeforeDiscount
    containsNhiItems
    totalVat
    totalWithoutVat
    nhiPaysAll
    nhiPayableBySubject
  }
}
