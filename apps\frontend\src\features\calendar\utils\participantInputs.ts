import {
  EventParticipantSetInput,
  ParticipantAttendanceRequest,
  ParticipantAttendeeSource,
} from "generated/graphql"

export default ({
  participantType,
  userId,
  eventParticipantId,
}: {
  participantType: string[]
  userId: string[]
  eventParticipantId: string[]
}) => {
  const participantInputs: EventParticipantSetInput[] = []

  userId.forEach((id, index) => {
    participantInputs.push({
      // setting the attendanceRequest for everyone to Optional for now since its required but we don't use it
      attendanceRequest: ParticipantAttendanceRequest.Optional,
      attendeeRelation: eventParticipantId[index]
        ? undefined
        : {
            objId: id,
            source: participantType[index] as ParticipantAttendeeSource,
          },
      id: eventParticipantId[index] || undefined,
    })
  })

  return participantInputs
}
