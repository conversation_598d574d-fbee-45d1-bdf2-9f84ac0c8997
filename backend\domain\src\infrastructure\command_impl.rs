//! Command implementation utilities

use crate::infrastructure::auth::AuthenticatedUser;
use anyhow::Result;

/// Transaction manager interface
pub trait ITransactionManager: Send + Sync {
    type Connection;
    async fn execute_in_transaction<F, R>(&self, f: F) -> Result<R>
    where
        F: FnOnce(&Self::Connection) -> Result<R> + Send,
        R: Send;
}

/// Command pipeline for executing commands
pub struct CommandPipeline;

/// Implementation of the RepoCommandHandler for domain-specific command handlers
pub struct RepoCommandHandler<T: ITransactionManager> {
    tx_manager: T,
    user: AuthenticatedUser,
}

impl<T: ITransactionManager> RepoCommandHandler<T> {
    pub fn new(tx_manager: T, user: AuthenticatedUser) -> Self {
        Self { tx_manager, user }
    }

    pub fn tx_manager(&self) -> &T {
        &self.tx_manager
    }

    pub fn user(&self) -> &AuthenticatedUser {
        &self.user
    }
}
