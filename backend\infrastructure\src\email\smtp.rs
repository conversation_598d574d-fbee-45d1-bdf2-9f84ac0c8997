use anyhow::Context;
use async_trait::async_trait;
use lettre::message::{Attachment, MultiPart, SinglePart};
use lettre::{
    message::header::ContentType, transport::smtp::extension::ClientId, Message, SmtpTransport,
    Transport,
};
use leviosa_domain_contracts::services::{EmailSender, EmailSubmitInput, EmailSendInput};
use regex::Regex;
use std::collections::HashMap;

// Simple string interpolation utility
fn interpolate_template(template: String, params: &HashMap<String, String>) -> Result<String, anyhow::Error> {
    let rx = Regex::new("\\{([[:alpha:]_]+)\\}").map_err(|e| anyhow::anyhow!("Failed to compile regex: {}", e))?;

    let result = rx
        .replace_all(template.as_str(), |cap: &regex::Captures| {
            let key = cap.get(1).unwrap().as_str();
            params.get(key).unwrap_or_else(|| {
                panic!("failed to interpolate: unspecified parameter {key} in template")
            })
        })
        .to_string();

    Ok(result)
}

#[derive(Clone)]
pub struct SmtpEmailSender {
    default_from_email: String,
}

impl SmtpEmailSender {
    pub fn new(default_from_email: String) -> Self {
        Self { default_from_email }
    }
}

#[async_trait]
impl EmailSender for SmtpEmailSender {
    async fn submit(&self, input: EmailSubmitInput) -> leviosa_domain_contracts::errors::Result<()> {
        let from_email = input.from_email.unwrap_or(self.default_from_email.clone());
        let from_name = input.from_name.unwrap_or("Leviosa Medical".to_string());
        let to_name = input.to_name.clone();
        let to_email = input.to_email.clone();

        let email = Message::builder()
            .from(
                format!("{from_name} <{from_email}>")
                    .parse()
                    .context("Failed to parse from email address")?,
            )
            .to(format!("{to_name} <{to_email}>")
                .parse()
                .context("Failed to parse to email address")?)
            .subject(input.subject);

        let message = if input.attachments.is_empty() {
            email
                .header(ContentType::TEXT_HTML)
                .body(input.body)
                .context("Failed to build email message")?
        } else {
            let mut parts = MultiPart::mixed().singlepart(
                SinglePart::builder()
                    .header(ContentType::TEXT_HTML)
                    .body(input.body),
            );

            for attachment in input.attachments {
                let content_type = ContentType::parse(attachment.content_type.as_str()).unwrap();
                let attachment =
                    Attachment::new(attachment.filename).body(attachment.content, content_type);
                parts = parts.singlepart(attachment);
            }
            email.multipart(parts).unwrap()
        };

        let domain = self.default_from_email.split('@').collect::<Vec<&str>>()[1];

        // Open a remote connection to gmail
        let mailer = SmtpTransport::starttls_relay("smtp-relay.gmail.com")
            .context("Failed to start smtp email relay")?
            .hello_name(ClientId::Domain(String::from(domain)))
            .build();

        mailer.send(&message).context("Failed to send email").map_err(|e| anyhow::anyhow!(e))?;
        Ok(())
    }

    async fn send(&self, input: EmailSendInput) -> leviosa_domain_contracts::errors::Result<()> {
        let subject = interpolate_template(input.subject_template, &input.params)?;
        let body = interpolate_template(input.body_template, &input.params)?;

        self.submit(EmailSubmitInput {
            to_email: input.to_email,
            to_name: input.to_name,
            subject,
            body,
            attachments: input.attachments,
            from_email: input.from_email,
            from_name: input.from_name,
        }).await
    }
}

#[cfg(test)]
mod mailer_tests {
    use super::*;

    #[tokio_shared_rt::test]
    #[ignore]
    // Comment out ignore to test mail sending
    pub async fn debug_email_sending() {
        let mailer = SmtpEmailSender::new("<EMAIL>".into());

        mailer
            .submit(EmailSubmitInput {
                to_email: "<EMAIL>".to_string(),
                to_name: "Halldór Reynir Tryggvason".to_string(),
                subject: "Test subject 1".to_string(),
                body: "This is a plaintext body".to_string(),
                attachments: vec![],
                from_email: None,
                from_name: None,
            })
            .await
            .unwrap();

        mailer
            .submit(EmailSubmitInput {
                to_email: "<EMAIL>".to_string(),
                to_name: "Halldór Reynir Tryggvason".to_string(),
                subject: "Test subject 2".to_string(),
                body: "This is a html body <a href=\"https://www.leviosa.is\">Leviosa</a>"
                    .to_string(),
                attachments: vec![],
                from_email: None,
                from_name: None,
            })
            .await
            .unwrap();
    }
}
