import { ComponentProps, forwardRef } from "react"

import { Pii } from "ui/index"

import { useGlobalState } from "../GlobalDataContext/GlobalData.context"

export const PiiSensitive = forwardRef<HTMLElement, ComponentProps<typeof Pii>>(
  (props, ref) => {
    const { title, ...rest } = props

    const {
      globalState: { isNnMode },
    } = useGlobalState()

    return (
      <Pii
        ref={ref}
        isNnMode={isNnMode}
        title={!isNnMode ? title : null}
        {...rest}
      />
    )
  }
)
