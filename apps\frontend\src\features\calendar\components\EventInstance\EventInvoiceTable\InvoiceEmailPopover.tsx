import { PopoverStore } from "@ariakit/react"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"

import { Popover } from "components/Ariakit/Popover/Popover"
import Icon from "components/Icon/Icon"
import { Button, Heading, Input, notification } from "ui"

import { useSendInvoiceEmailMutation } from "generated/graphql"

import styles from "./EventInvoiceTable.module.css"

type InvoiceEmailInputFormProps = InvoiceEmailPopoverProps
const InvoiceEmailInputForm = ({
  invoiceId,
  payerEmail,
  subjectEmail,
  popoverStore,
}: InvoiceEmailInputFormProps) => {
  const { t } = useTranslation("routes", {
    keyPrefix: "billing.invoiceEmailPopover",
  })
  const defaultEmail = payerEmail || subjectEmail || ""
  const [emailAddress, setEmailAddress] = useState(defaultEmail)

  const [sendInvoice, { loading: sendingInvoiceEmail }] =
    useSendInvoiceEmailMutation()

  const handleEmailChange = ({
    target: { value },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setEmailAddress(value)
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.currentTarget)
    const email = formData.get("email") as string

    await sendInvoice({
      variables: {
        invoiceId,
        email,
        updateSubjectEmail: defaultEmail === "",
      },
      onCompleted: () => {
        notification.create({
          message: t("sendEmail.success", { email }),
          status: "success",
        })
        popoverStore.hide()
      },
      onError: () => {
        notification.create({
          message: t("sendEmail.error"),
          status: "error",
        })
      },
    })
  }

  return (
    <form className={styles.emailPopover} onSubmit={handleSubmit}>
      <Heading size="xsmall" className={styles.emailPopoverHeading}>
        {t("heading")}
      </Heading>
      <div className={styles.inputWrapper}>
        <Input
          name="email"
          label={t("inputLabel")}
          hideLabel
          value={emailAddress}
          onChange={handleEmailChange}
          hideMessage
          size="small"
          type="email"
          autoComplete="off"
          // autoComplete does not always work with 1Password, `data-1p-ignore`
          // instructs 1Password to ignore this field
          // source: https://developer.1password.com/docs/web/compatible-website-design/#ignore-offers-to-save-or-fill-specific-fields
          inputProps={{ "data-1p-ignore": true }}
        />
        <Button
          disabled={!emailAddress || sendingInvoiceEmail}
          icon={sendingInvoiceEmail && <Icon name="loader-4-line" spin />}
          type="submit"
          variant="filled"
        >
          {t("submitButton")}
        </Button>
      </div>
    </form>
  )
}

interface InvoiceEmailPopoverProps {
  invoiceId: string
  payerEmail: string | null
  subjectEmail: string | null
  popoverStore: PopoverStore
}

export const InvoiceEmailPopover = ({
  popoverStore,
  ...invoice
}: InvoiceEmailPopoverProps) => (
  <Popover store={popoverStore} portal={true}>
    <InvoiceEmailInputForm
      key={popoverStore.getState().open ? invoice.invoiceId : ""}
      popoverStore={popoverStore}
      {...invoice}
    />
  </Popover>
)
