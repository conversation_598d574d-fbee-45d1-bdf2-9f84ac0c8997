import { FormGroup } from "@ariakit/react"
import { Meta, StoryFn } from "@storybook/react-vite"

import Button from "ui/components/Button/Button"

import { useComboboxStore, useFilter, useSelectStore } from "../Ariakit/hooks"
import list from "../Select/list"
import FiltrableSelect, { SelectFiltrableProps } from "./FiltrableSelect"

export default {
  title: "Form Components/FiltrableSelect",
  component: FiltrableSelect,
} as Meta

export const FiltrableSelectDefault: StoryFn<
  SelectFiltrableProps<"div", "string">
> = (args) => {
  const comboboxStore = useComboboxStore()

  const { value: comboboxValue } = comboboxStore.useState()

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: "",
    focusLoop: "vertical",
  })

  const { filteredList } = useFilter({
    defaultItems: list,
    value: comboboxValue,
  })

  return (
    <FiltrableSelect
      {...args}
      options={list}
      sameWidth
      filteredOptions={filteredList}
      selectStore={selectStore}
      comboboxStore={comboboxStore}
      onSelectChange={(value) => {
        console.log(value)
      }}
    />
  )
}

export const FiltrableSelectMulti: StoryFn<
  SelectFiltrableProps<"div", "string">
> = (args) => {
  const comboboxStore = useComboboxStore({})

  const { value: comboboxValue } = comboboxStore.useState()

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: [],
    focusLoop: "vertical",
  })

  const { filteredList } = useFilter({
    defaultItems: list,
    value: comboboxValue,
  })

  return (
    <FiltrableSelect
      {...args}
      sameWidth
      options={list}
      filteredOptions={filteredList}
      selectStore={selectStore}
      comboboxStore={comboboxStore}
      onSelectChange={(value) => {
        console.log(value)
      }}
    />
  )
}

export const FiltrableSelectFormSubmit: StoryFn<
  SelectFiltrableProps<"div", "string">
> = (args) => {
  const comboboxStore = useComboboxStore({})

  const { value: comboboxValue } = comboboxStore.useState()

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: "",
    focusLoop: "vertical",
  })

  const { filteredList } = useFilter({
    defaultItems: list,
    value: comboboxValue,
  })

  const onFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())
    console.log(data)
  }

  return (
    <FormGroup as="form" onSubmit={onFormSubmit}>
      <FiltrableSelect
        {...args}
        options={list}
        sameWidth
        filteredOptions={filteredList}
        selectStore={selectStore}
        comboboxStore={comboboxStore}
        onSelectChange={(value) => {
          console.log(value)
        }}
      />

      <Button type="submit">Submit</Button>
    </FormGroup>
  )
}

export const FiltrableSelectMultiFormSubmit: StoryFn<
  SelectFiltrableProps<"div", "string">
> = (args) => {
  const comboboxStore = useComboboxStore({})

  const { value: comboboxValue } = comboboxStore.useState()

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: [],
    focusLoop: "vertical",
  })

  const { filteredList } = useFilter({
    defaultItems: list,
    value: comboboxValue,
  })

  const onFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const selectedOptions = formData.getAll("fruits")
    console.log(selectedOptions)
    console.log(data)
  }

  return (
    <FormGroup as="form" onSubmit={onFormSubmit}>
      <FiltrableSelect
        {...args}
        options={list}
        sameWidth
        filteredOptions={filteredList}
        selectStore={selectStore}
        comboboxStore={comboboxStore}
        onSelectChange={(value) => {
          console.log(value)
        }}
      />

      <Button type="submit">Submit</Button>
    </FormGroup>
  )
}

FiltrableSelectDefault.args = {
  label: "Test",
}
FiltrableSelectDefault.parameters = {
  layout: "centered",
}

FiltrableSelectMulti.args = {
  label: "Test",
}
FiltrableSelectMulti.parameters = {
  layout: "centered",
}

FiltrableSelectFormSubmit.args = {
  layout: "centered",
}

FiltrableSelectFormSubmit.args = {
  label: "Test",
  name: "fruits",
}

FiltrableSelectMultiFormSubmit.args = {
  label: "Test",
  name: "fruits",
}
