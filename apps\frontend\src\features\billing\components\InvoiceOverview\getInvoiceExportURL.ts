import { endOfDay } from "date-fns"

import { getTokens } from "features/authentication/utils/tokenStorage"

import { INVOICES_LIMIT } from "./InvoiceOverview"

export const getInvoiceExportURL = (
  subject: string,
  invoiceIssuer: string,
  status: string,
  date: {
    fromDate: string
    toDate: string
  },
  provider: string,
  paymentMethod: string
) => {
  const baseUrl = `${
    import.meta.env.VITE_BACKEND_API_URL || window.origin
  }/api/export/invoices`

  const invoiceUrl = new URL(baseUrl)

  const { accessToken } = getTokens() || {}

  invoiceUrl.searchParams.append("token", accessToken || "")
  invoiceUrl.searchParams.append(
    "from_treatment_date",
    new Date(date.fromDate || new Date()).toISOString()
  )
  invoiceUrl.searchParams.append(
    "to_treatment_date",
    endOfDay(new Date(date.toDate || new Date())).toISOString()
  )

  // Check each prop and add to queryParams if it exists
  if (subject) {
    invoiceUrl.searchParams.append("subject_id", subject)
  }
  if (invoiceIssuer) {
    invoiceUrl.searchParams.append("issuer_id", invoiceIssuer)
  }

  if (provider) {
    invoiceUrl.searchParams.append("provider_id", provider)
  }

  if (paymentMethod) {
    invoiceUrl.searchParams.append("payment_method", paymentMethod)
  }

  if (status) {
    if (status === "DRAFT") {
      invoiceUrl.searchParams.append("issued", "false")
    } else {
      invoiceUrl.searchParams.append("issued", "true")
      invoiceUrl.searchParams.append("payment_status", status)
    }
  }

  invoiceUrl.searchParams.append("limit", INVOICES_LIMIT.toString())

  return invoiceUrl.toString()
}
