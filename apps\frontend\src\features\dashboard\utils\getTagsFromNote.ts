/**
 * Extracts tags from a given note string.
 *
 * This function searches for tags within the provided note. Tags are defined
 * as words that start with a '#' character followed by alphanumeric characters
 * or underscores.
 *
 * @param note - The note string from which to extract tags. If the note is null,
 *               an empty array will be returned.
 * @returns An array of tags found in the note. If no tags are found, an empty
 *          array is returned.
 */
export default function getTagsFromNote(note: string | null) {
  return note?.match(/#[a-zA-Z0-9_]+/g) || []
}
