import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import { LexicalEditor } from "lexical"
import { ChangeEventHandler, useRef } from "react"

import styles from "./DataField.module.css"
import { useDataField } from "./DataFieldContext"
import { DataFieldAttributes } from "./DataFieldNode"
import { SelectFieldComponent } from "./SelectFieldComponent"

export default function DataFieldComponent({
  id,
  onChange,
}: DataFieldAttributes & {
  onChange: (value: string, editor: LexicalEditor) => void
}) {
  const { getInput, setValue, isEditorFocused } = useDataField()
  const [editor] = useLexicalComposerContext()

  const measureRef = useRef<HTMLSpanElement>(null)

  const inputData = getInput(id)

  const handleChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    setValue(id, e.target.value)
    onChange(e.target.value, editor)
  }

  const handleSelectChange = (value: string) => {
    setValue(id, value)
    onChange(value, editor)
  }

  const inputWidth = inputData?.value && inputData.value.toString().length

  if (!isEditorFocused)
    return (
      <span id={`${inputData?.value}`} className={styles.dataFieldValue}>
        {inputData?.value ?? ""}
      </span>
    )

  switch (inputData?.type) {
    case "number":
    case "string":
      return (
        <>
          <input
            onChange={handleChange}
            className={styles.input}
            type={inputData?.type}
            style={{
              width: measureRef.current
                ? `min(${measureRef.current?.scrollWidth + 12}px, 98%)`
                : inputWidth + "ch",
            }}
            value={inputData?.value}
          />
          <span aria-hidden className={styles.measureSize} ref={measureRef}>
            {inputData?.value}
          </span>
        </>
      )
    case "select":
      return (
        <SelectFieldComponent
          handleSelectChange={handleSelectChange}
          inputData={inputData}
        />
      )
    default:
      return null
  }
}
