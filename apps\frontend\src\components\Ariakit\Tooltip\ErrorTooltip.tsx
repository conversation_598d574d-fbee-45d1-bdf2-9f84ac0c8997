import {
  Tooltip as AriakitTooltip,
  TooltipAnchor,
  TooltipProps,
  TooltipProvider,
  TooltipStoreState,
} from "@ariakit/react"

import styles from "./Tooltip.module.css"

type ErrorTooltipProps = {
  error: string | null
  children: React.ReactNode
  placement?: TooltipStoreState["placement"]
  focusable?: TooltipProps["focusable"]
}

export const ErrorTooltip = ({
  error,
  children,
  placement = "top",
  focusable,
}: ErrorTooltipProps) => {
  return (
    <TooltipProvider open={!!error} placement={placement}>
      <TooltipAnchor showOnHover={false} focusable={focusable}>
        {children}
      </TooltipAnchor>
      <AriakitTooltip
        className={`${styles.tooltip} ${styles.errorTooltip}`}
        focusable={focusable}
      >
        {error}
      </AriakitTooltip>
    </TooltipProvider>
  )
}
