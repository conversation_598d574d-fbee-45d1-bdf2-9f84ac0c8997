import { useTranslation } from "react-i18next"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import { getRsvpIconName } from "features/calendar/utils/getRsvpIconName"
import { Text } from "ui"

import { EventInstanceQuery } from "generated/graphql"

import { SubjectLink } from "../SubjectLink/SubjectLink"
import styles from "./ParticipantInfo.module.css"

type ParticipantInfoProps = {
  participant: EventInstanceQuery["eventInstance"]["participants"][0]
  onDelete?: (id: string) => void
  canRemoveParticipants?: boolean
}

export const ParticipantInfo = ({
  participant,
  onDelete,
  canRemoveParticipants,
}: ParticipantInfoProps) => {
  const { t } = useTranslation()

  const { rsvpStatus, objId, __typename } = participant

  const isSubject = __typename === "ParticipantSubject"

  return (
    <>
      <div className={styles.iconWrap}>
        {onDelete && (
          <Tooltip
            tooltipContent={
              <div style={{ maxWidth: 250 }}>
                {canRemoveParticipants ? (
                  <>{t("Remove participant")}</>
                ) : (
                  "Cannot remove participants on events that have started"
                )}
              </div>
            }
          >
            <button
              aria-label={t("Remove participant")}
              className={styles.deleteButton}
              disabled={!canRemoveParticipants}
              onClick={() => onDelete(objId)}
            >
              <Icon name={"close-line"} />
            </button>
          </Tooltip>
        )}

        <Icon className={styles.userIcon} name={"user-line"} />
        <Icon
          data-rsvp-status={rsvpStatus}
          className={styles.userRsvpIcon}
          name={getRsvpIconName(rsvpStatus)}
        />
      </div>

      <div>
        <SubjectLink
          isSubject={isSubject}
          objId={objId}
          name={
            __typename === "ParticipantProvider"
              ? participant.provider.name
              : participant.subject.name
          }
        />

        <Text className={styles.participantType}>
          {isSubject ? t("Subject") : t("Provider")}
        </Text>
      </div>
    </>
  )
}
