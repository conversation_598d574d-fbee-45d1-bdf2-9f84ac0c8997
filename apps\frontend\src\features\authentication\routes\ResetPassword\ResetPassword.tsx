import { FormEvent } from "react"
import { useTranslation } from "react-i18next"
import { Link, useNavigate, useParams } from "react-router-dom"
import invariant from "tiny-invariant"

import Panel from "components/Panel/Panel"
import usePasswordValidation from "hooks/usePasswordValidation"
import { RouteStrings } from "routes/RouteStrings"
import { Button, FormGrid, Heading, Input, Text } from "ui"
import NewPasswordInput from "ui/components/Input/NewPasswordInput"

import { useResetPasswordMutation } from "generated/graphql"

import styles from "../../Authentication.module.css"

export default function ResetPassword() {
  const { token } = useParams<{ token: string }>()
  const { t } = useTranslation()
  const {
    passwordRef,
    repeatedPasswordRef,
    passwordStatus,
    setPasswordStatus,
    repeatedPasswordStatus,
    setRepeatedPasswordStatus,
    validatePassword,
    validateRepeatedPassword,
  } = usePasswordValidation()

  const [resetPassword, { loading, called, error }] = useResetPasswordMutation()

  const navigate = useNavigate()

  if (!token) {
    navigate(RouteStrings.login)

    return null
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.target as HTMLFormElement)

    const { newPassword, repeatPassword } = Object.fromEntries(
      formData.entries()
    )

    invariant(
      typeof newPassword === "string",
      t("routes:validations.passwordIsInvalid")
    )

    invariant(
      typeof repeatPassword === "string",
      t("routes:validations.passwordIsInvalid")
    )

    if (!validatePassword() || !validateRepeatedPassword()) return

    await resetPassword({
      variables: { newPassword: newPassword, code: token },
    })
  }

  const isResetSuccessful = called && !loading && !error

  return (
    <FormGrid className={styles.wrap} onSubmit={handleSubmit}>
      {!isResetSuccessful ? (
        <>
          <Heading as="h1" size="large" className={styles.title}>
            {t("routes:auth.resetPassword")}
          </Heading>
          <NewPasswordInput
            label={t("routes:auth.password")}
            name="newPassword"
            ref={passwordRef}
            onBlur={validatePassword}
            onFocus={() =>
              setPasswordStatus({ status: "default", message: "" })
            }
            status={passwordStatus.status}
            message={passwordStatus.message}
          />

          <Input
            label={t("routes:auth.repeatPassword")}
            type="password"
            name="repeatPassword"
            autoComplete="new-password"
            ref={repeatedPasswordRef}
            onBlur={validateRepeatedPassword}
            onFocus={() =>
              setRepeatedPasswordStatus({ status: "default", message: "" })
            }
            status={repeatedPasswordStatus.status}
            message={repeatedPasswordStatus.message}
          />

          {error && <Panel status="error">{error.message}</Panel>}

          <Button
            className={styles.submitButton}
            disabled={loading}
            type="submit"
            size="large"
            variant="filled"
          >
            {t("routes:auth.resetPassword")}
          </Button>
          <Link to={RouteStrings.login} className={styles.link}>
            {t("routes:auth.backToLogin")}
          </Link>
        </>
      ) : (
        <Text>
          {t("routes:auth.successPasswordChange")}{" "}
          <Link className={styles.link} to={RouteStrings.login}>
            {t("routes:auth.backToLogin")}
          </Link>
        </Text>
      )}
    </FormGrid>
  )
}
