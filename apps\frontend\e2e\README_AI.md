# Info

- e2e tests are system tests to validate mulitple layers of the application.
- Are performed by `playwright`, using `npx playwright test` and is included in a special CI workflow which automatically runs once daily and always before release CI.
- Elements included in tests require `data-testid` attribute. It is used both to identify elements to interact with and also to validate the values that the user sees. When locating UI elements, use getByTestId() instead of other locators whenever possible. Add missing `data-testid` attributes but do not make any changes to code outside of e2e folder.
