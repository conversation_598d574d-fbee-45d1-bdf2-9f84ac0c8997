import c from "classnames"
import { parseISO } from "date-fns"
import { MouseEvent, useRef } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link } from "react-router-dom"

import {
  Menu,
  MenuButton,
  MenuProvider,
  usePopoverStore,
} from "components/Ariakit"
import { MenuItem } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { useMenuStore } from "components/Ariakit/hooks"
import { useAttachmentContext } from "components/AttachmentsPreview/AttachmentsPreview"
import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { InvoiceEmailPopover } from "features/calendar/components/EventInstance/EventInvoiceTable/InvoiceEmailPopover"
import { RouteStrings } from "routes/RouteStrings"
import { notification } from "ui"
import TrLink from "ui/components/Table/TrLink"
import { formatNumberInThousand } from "utils/formatNumberInThousand"
import { printDocument } from "utils/printDocument"
import useDateFormatter from "utils/useDateFormatter"

import {
  GetInvoicesQuery,
  namedOperations,
  useCreateCreditInvoiceMutation,
  useDeleteInvoiceMutation,
} from "generated/graphql"

import { invoicePdfUrl } from "../invoicePdfUrl"
import styles from "./InvoiceOverviewTable.module.css"
import { InvoicePaymentMethodTag } from "./InvoicePaymentMethodTag/InvoicePaymentMethodTag"
import { InvoiceStatusTag } from "./InvoiceStatusTag/InvoiceStatusTag"

type TableBodyRowProps = {
  invoice: GetInvoicesQuery["invoices"][0]
}

export const TableBodyRow = ({ invoice }: TableBodyRowProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "billing" })
  const dateFormat = useDateFormatter()
  const { openAttachment } = useAttachmentContext()
  const refWrap = useRef<HTMLButtonElement>(null)

  const menu = useMenuStore({
    placement: "bottom-end",
    animated: true,
  })

  const popoverStore = usePopoverStore()

  const [createCreditInvoice] = useCreateCreditInvoiceMutation({
    onCompleted: () => {
      notification.create({
        message: t("invoiceCreditInvoiceCreation.success"),
        status: "success",
      })
    },
    onError: () => {
      notification.create({
        message: t("invoiceCreditInvoiceCreation.error"),
        status: "error",
      })
    },
    refetchQueries: [namedOperations.Query.GetInvoices],
  })

  const [deleteInvoice] = useDeleteInvoiceMutation({
    variables: {
      input: {
        id: invoice.id,
      },
    },
    onCompleted: () => {
      notification.create({
        message: t("invoiceDeletion.success"),
        status: "success",
      })
    },
    onError: () => {
      notification.create({
        message: t("invoiceDeletion.error"),
        status: "error",
      })
    },
    refetchQueries: [namedOperations.Query.GetInvoices],
  })

  const {
    id,
    subject,
    provider,
    issuer,
    total,
    issued,
    invoiceNumber,
    paymentStatus,
    treatmentDate,
    issuedAt,
    paymentMethod,
  } = invoice

  const parsedIssuedDate = issuedAt ? parseISO(issuedAt) : null
  const formattedIssuedDate = parsedIssuedDate
    ? dateFormat(parsedIssuedDate, {
        dateStyle: "short",
      })
    : ""

  const parsedTreatmentDate = treatmentDate ? parseISO(treatmentDate) : null
  const formattedTreatmentDate = parsedTreatmentDate
    ? dateFormat(parsedTreatmentDate, {
        dateStyle: "short",
      })
    : ""

  const url = invoicePdfUrl(invoice.id)

  const commonProps = {
    "aria-label": `${subject.name}: Invoice number ${invoiceNumber}`,
    className: styles.invoiceNumber,
  }

  const handleIssuedClick: React.MouseEventHandler<HTMLAnchorElement> = (e) => {
    if (openAttachment) {
      e.preventDefault()
      openAttachment(url)
    }
  }

  const handleShowInvoiceEmailPopover = () => {
    popoverStore.setAnchorElement(refWrap.current)
    popoverStore.show()
  }

  return (
    <TrLink selectors="a" className={c(styles.tableRow)}>
      <td>{formattedTreatmentDate}</td>
      <td>{formattedIssuedDate}</td>
      <td>
        {issued ? (
          <a href={url} onClick={handleIssuedClick} download {...commonProps}>
            {invoiceNumber}
          </a>
        ) : (
          <Link
            to={generatePath(RouteStrings.patientInvoice, {
              invoiceId: invoice.id,
            })}
            {...commonProps}
          >
            {invoiceNumber}
          </Link>
        )}
      </td>
      <td>{issuer?.title}</td>
      <td>{provider?.name}</td>
      <PiiSensitive as="td" children={subject.name} />
      <td className={styles.numericValue}>{formatNumberInThousand(total)}</td>
      <td onClick={(e) => e.stopPropagation()}>
        <InvoicePaymentMethodTag id={id} paymentMethod={paymentMethod} />
      </td>
      <td
        className={styles.actionRow}
        onClick={(e) => {
          e.stopPropagation()
        }}
      >
        <div className={styles.tags}>
          <InvoiceStatusTag
            id={id}
            paymentStatus={paymentStatus}
            issued={issued}
          />
          <MenuProvider store={menu}>
            <MenuButton
              variant="clear"
              store={menu}
              className={styles.menuButton}
              iconEnd={<Icon name="more-line" />}
              onClick={(e: MouseEvent) => {
                e.stopPropagation()
                popoverStore.hide()
              }}
              ref={refWrap}
            />
            <Menu className={styles.menu}>
              {issued && (
                <>
                  <MenuItem
                    className={styles.menuItem}
                    onClick={handleShowInvoiceEmailPopover}
                  >
                    <Icon name="mail-line" className={styles.menuIcon} />
                    {t("menuButton.emailInvoice")}
                  </MenuItem>
                  <MenuItem
                    className={styles.menuItem}
                    onClick={async () => {
                      const url = invoicePdfUrl(invoice.id)

                      await printDocument(url)
                    }}
                  >
                    <Icon name="printer-line" className={styles.menuIcon} />
                    {t("menuButton.printInvoice")}
                  </MenuItem>
                  <MenuItem
                    className={styles.menuItem}
                    onClick={() => {
                      createCreditInvoice({
                        variables: {
                          invoiceId: invoice.id,
                        },
                      })
                    }}
                  >
                    <Icon
                      name="money-dollar-undo-line"
                      className={styles.menuIcon}
                    />
                    {t("menuButton.createCreditInvoice")}
                  </MenuItem>
                </>
              )}

              {!issued && (
                <MenuItem
                  className={styles.menuItem}
                  onClick={() => deleteInvoice()}
                >
                  {t("menuButton.deleteDraftInvoice")}
                </MenuItem>
              )}
            </Menu>
          </MenuProvider>
          <InvoiceEmailPopover
            invoiceId={invoice.id}
            payerEmail={invoice.payerEmail}
            subjectEmail={invoice.subject.email}
            popoverStore={popoverStore}
          />
        </div>
      </td>
    </TrLink>
  )
}
