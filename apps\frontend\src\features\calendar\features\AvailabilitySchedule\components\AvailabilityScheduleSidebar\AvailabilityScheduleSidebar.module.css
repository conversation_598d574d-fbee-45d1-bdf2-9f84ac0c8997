.sidebar {
  display: flex;
  width: 400px;
  flex-shrink: 0;
  flex-direction: column;
  gap: 24px;
  position: relative;
  overflow-y: auto;
  margin: -24px -24px 0 0;
  padding: 32px 24px 0 0;
}

.sidebar hr {
  margin: 0;
}
.sidebar hr::after {
  background-color: var(--color-lev-blue-200);
}

.headingContainer {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag {
  height: fit-content;
}

.panel {
  display: grid;
  gap: 16px;
}

.panelInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.icon {
  flex-shrink: 0;
}

.scheduleSelectWrapper {
  display: grid;
  grid-template-columns: auto 36px;
  grid-gap: 8px;
}

.scheduleSettings {
  height: 36px;
  width: 36px;
  margin-top: 25px;
}

.addPeriodButton {
  margin-top: 16px;
  width: fit-content;
}

.servicesHeading {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.addServiceButton {
  width: fit-content;
  margin-top: 8px;
}

.redirectServiceButtonWrap {
  text-align: right;
}

.confirmationButtons {
  display: flex;
  justify-content: space-between;
  position: sticky;
  bottom: 0;
  padding: 0 24px;
  margin-left: -24px;
  margin-right: -24px;
  margin-top: auto;
  background-color: white;
}

.confirmationButtons::before {
  content: "";
  position: absolute;
  top: -24px;
  left: 0;
  right: 0;
  height: 24px;
  background: linear-gradient(to bottom, transparent, white);
}
