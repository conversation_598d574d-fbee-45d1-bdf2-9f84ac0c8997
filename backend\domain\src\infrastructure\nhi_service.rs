use crate::errors::Error;
use leviosa_domain_types::SubjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use async_trait::async_trait;

#[derive(Debug, Clone, Deserialize)]
pub enum SubjectInsuranceStatus {
    Uninsured,
    Insured {
        /// Categories such as "ALM", "ELLI", "BARN" that describe the subject's insurance category.
        insurance_category: String,
        /// The maximum amount the subject should pay on an invoice.
        maximum_payable_by_subject: f64,
        /// The percentage that the insurance covers for the subject.
        insurance_percentage: i32,
        /// Identifies which payment status revision the data is from.
        payment_status_serial_number: i32,
    },
    Unknown,
}

// NHI service types moved from model layer

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
pub struct MedicalBill {
    pub treatment_date: DateTime<Utc>,
    pub payed_by_patient: i64,
    pub payed_by_insurance: i64,
    /// The NHI owner is the issuer in our system.
    /// If no issuer is selected we use the organisation registration number.
    pub owner_person_id: String,
    pub clinic_registration_number: String,
    /// This is not the Leviosa issuer. This is the doctor person id.
    pub issuer_person_id: String,
    pub doctor_number: i64,
    pub receipt_number: String,
    pub codes: Vec<Code>,
    pub patient_person_id: String,
    pub radnumer_si: Option<i32>,
}

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
pub struct Code {
    pub code: String,
    pub quantity: i64,
    pub credit: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct NhiSubmissionResult {
    #[serde(rename = "RequestXml")]
    pub request_xml: String,
    #[serde(rename = "RequestJson")]
    pub request_json: String,
    #[serde(rename = "ResponseXml")]
    pub response_xml: String,
}

#[async_trait::async_trait]
#[mockall::automock]
pub trait NhiService: Send + Sync {
    async fn get_subject_payment_status(
        &self,
        subject_id: SubjectId,
        token: &str,
    ) -> Result<SubjectInsuranceStatus, Error>;

    async fn submit_invoice(
        &self,
        medical_bill: MedicalBill,
        on_behalf_of: &str,
        token: &str,
    ) -> Result<NhiSubmissionResult, Error>;
}
