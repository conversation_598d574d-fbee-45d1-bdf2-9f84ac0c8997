.wrap {
  padding: 16px;
  max-height: none;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 36px;
}

.form {
  width: 100%;
  display: grid;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--grid-gap);
}

.inputs {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.input {
  width: 100%;
}

.exportWrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 64px;
}

.exportButton {
  width: 100%;
  justify-content: center;
  margin-top: 16px;
}
