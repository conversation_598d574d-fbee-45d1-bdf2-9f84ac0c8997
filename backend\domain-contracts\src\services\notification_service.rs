//! Notification service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendCustomMessageInput {
    pub receiver_id: Uuid,
    pub sender_id: Uuid,
    pub subject: Option<String>,
    pub content: String,
    pub organisation_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: MessageId,
    pub receiver_id: SubjectId,
    pub sender_id: ProviderId,
    pub subject: Option<String>,
    pub content: String,
    pub status: MessageStatus,
    pub message_type: MessageType,
    pub category: MessageCategory,
    pub organisation_id: OrganisationId,
    pub retry_count: i32,
    pub response: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
    pub earliest_send_time: Option<DateTime<Utc>>,
    pub latest_send_time: Option<DateTime<Utc>>,
    pub sent_at: Option<DateTime<Utc>>,
    pub reference_id: Option<String>,
    pub nr_of_segments: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageStatus {
    Pending,
    Sent,
    Failed,
    Delivered,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageType {
    Sms,
    Email,
    Push,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageCategory {
    Appointment,
    Reminder,
    Custom,
    System,
}

/// Contract for notification service integration
#[async_trait]
#[mockall::automock]
pub trait NotificationService: Send + Sync {
    /// Send a custom message to a subject
    async fn send_custom_message(
        &self,
        input: SendCustomMessageInput,
    ) -> Result<Message>;

    /// Find messages for a receiver
    async fn find_messages(
        &self,
        receiver_id: Uuid,
        limit: Option<u64>,
    ) -> Result<Vec<Message>>;
}
