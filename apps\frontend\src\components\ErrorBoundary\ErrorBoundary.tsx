import { Component, ErrorInfo, ReactNode } from "react"

import UnexpectedErrorPage from "features/mainLayout/components/ErrorPages/UnexpectedErrorPage"
import { logException } from "lib/sentry/sentry"

type Props = {
  children: ReactNode
  fallback?: ReactNode
}

type State = {
  hasError: boolean
  isChunkLoadError: boolean
}

export class ErrorBoundary extends Component<Props, State> {
  state = {
    hasError: false,
    isChunkLoadError: false,
  }

  // Check if we've already attempted a refresh
  private hasAttemptedRefresh(): boolean {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.has("refreshAttempted")
  }

  static getDerivedStateFromError(error: unknown) {
    // Check if it's a chunk loading error that requires page reload
    const isChunkLoadError =
      error instanceof Error && error.name === "ChunkLoadError"

    return {
      hasError: true,
      isChunkLoadError,
    }
  }

  componentDidCatch(error: Error, errorInfo?: ErrorInfo): void {
    logException(error, { data: errorInfo })
  }

  componentDidMount(): void {
    window.addEventListener("popstate", this.resetErrorState)
    window.addEventListener("click", this.resetErrorState)
  }

  componentWillUnmount(): void {
    window.removeEventListener("popstate", this.resetErrorState)
    window.removeEventListener("click", this.resetErrorState)
  }

  // Reset error state on any user interaction
  resetErrorState = (): void => {
    if (this.state.hasError) {
      this.setState({ hasError: false, isChunkLoadError: false })
    }
  }

  componentDidUpdate(): void {
    // If we have a chunk load error and haven't attempted a refresh yet
    if (this.state.isChunkLoadError && !this.hasAttemptedRefresh()) {
      // Add query parameter and reload
      const url = new URL(window.location.href)
      url.searchParams.set("refreshAttempted", "true")
      window.location.href = url.toString()
    }
  }

  render() {
    const { children, fallback } = this.props
    const { hasError } = this.state

    if (hasError) {
      return fallback || <UnexpectedErrorPage />
    }

    return children
  }
}
