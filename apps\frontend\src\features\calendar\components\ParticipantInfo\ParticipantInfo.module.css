.iconWrap {
  width: 40px;
  height: 40px;
  background: var(--color-lev-blue-200);
  border-radius: 50%;
  margin-right: 15px;

  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.deleteButton {
  cursor: pointer;
  position: absolute;
  left: -32px;
  padding: 8px;
  color: var(--color-text);
  top: 50%;
  transform: translateY(-50%);
  border-radius: 4px;
}

.deleteButton:disabled {
  cursor: not-allowed;
  color: var(--color-legacy-gray);
  opacity: 0.6;
}
.deleteButton:focus-visible {
  box-shadow: 0 0 0 3px var(--color-lev-blue);
}

.userIcon {
  width: 24px;
  height: 24px;
}

.userRsvpIcon {
  position: absolute;
  bottom: 0;
  right: -10px;
  width: 24px;
  height: 24px;

  color: var(--color-warning);
  fill: var(--color-warning-200);
  filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.25));
}

.userRsvpIcon[data-rsvp-status="ACCEPTED"] {
  color: var(--color-success);
  fill: var(--color-success-200);
}

.userRsvpIcon[data-rsvp-status="DECLINED"] {
  color: var(--color-critical);
  fill: var(--color-critical-200);
}

.participantType {
  text-transform: uppercase;
  font-size: 12px;
  line-height: 14px;
}
