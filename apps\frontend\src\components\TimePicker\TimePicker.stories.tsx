import { Meta, <PERSON> } from "@storybook/react-vite"

import { TimePicker, TimePickerProps } from "./TimePicker"

export default {
  title: "Form Components/TimePicker",
  component: TimePicker,
} as Meta

export const TimePickerDefault: Story<TimePickerProps> = (args) => {
  return <TimePicker {...args} />
}

export const TimePickerWithHourStep: Story<TimePickerProps> = (args) => {
  return <TimePicker {...args} minuteStep={60} />
}
