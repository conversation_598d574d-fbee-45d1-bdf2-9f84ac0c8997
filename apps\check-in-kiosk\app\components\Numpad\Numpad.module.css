.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.numpadContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.row {
  display: grid;
  grid-gap: 84px;
  grid-template-columns: repeat(3, 110px);
}

.button {
  height: 80px;
  width: 100px;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
.button,
.actionButton {
  animation: slideIn 0.3s cubic-bezier(0.17, 0.84, 0.44, 1);
  animation-fill-mode: both;
}
.row:nth-child(2) .button {
  animation-delay: 75ms;
}
.row:nth-child(3) .button {
  animation-delay: 150ms;
}
.row:nth-child(4) :is(.button, .actionButton) {
  animation-delay: 225ms;
}

.button:active {
  background-color: var(--bg-accent-hover, #ede6fc);
}

.numberButton {
  font-size: 30px;
  font-weight: 600;
}

.actionButton.actionButton {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.inputContainer {
  width: 90%;
  padding: 24px 0;
  animation: fadeIn 0.4s linear;
  animation-fill-mode: both;
  animation-delay: 100ms;
}

/* Animate opacity in and translate down */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
/* Animate opacity in and translate down */
@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
