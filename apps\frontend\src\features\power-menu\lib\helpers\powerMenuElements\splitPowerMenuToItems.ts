import { PowerMenuGroup } from "../../types"

export const splitPowerMenuToItems = (powerMenuGroups: PowerMenuGroup[]) => {
  const nonFiltrableItemsOnTop = []
  const nonFiltrableItemsOnBottom = []
  const restItems = []

  for (const powerMenuGroup of powerMenuGroups) {
    if (powerMenuGroup.filtrable === false && powerMenuGroup.positionAtTop)
      nonFiltrableItemsOnTop.push(...powerMenuGroup.items)
    else if (powerMenuGroup.filtrable === false)
      nonFiltrableItemsOnBottom.push(...powerMenuGroup.items)
    else restItems.push(...powerMenuGroup.items)
  }

  return [nonFiltrableItemsOnTop, restItems, nonFiltrableItemsOnBottom]
}
