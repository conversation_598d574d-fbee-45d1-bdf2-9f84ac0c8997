import getFirstMondayOfNextMonth from "./getFirstMondayOfNextMonth"

describe("getFirstMondayOfNextMonth", () => {
  test("returns the correct date when current date is before the first Monday", () => {
    // Set the current date to a date before the first Monday of the next month
    const currentDate = new Date("2022-01-15")

    // Calculate the expected result
    const expectedDate = new Date("2022-02-07")

    // Call the function and assert the result
    expect(getFirstMondayOfNextMonth(currentDate)).toEqual(expectedDate)
  })

  test("returns the correct date when current date is on a Monday", () => {
    // Set the current date to a Monday
    const currentDate = new Date("2022-02-07")

    // Calculate the expected result
    const expectedDate = new Date("2022-03-07")

    // Call the function and assert the result
    expect(getFirstMondayOfNextMonth(currentDate)).toEqual(expectedDate)
  })

  test("returns the correct date when current date is after the first Monday", () => {
    // Set the current date to a date after the first Monday of the next month
    const currentDate = new Date("2022-02-15")

    // Calculate the expected result
    const expectedDate = new Date("2022-03-07")

    // Call the function and assert the result
    expect(getFirstMondayOfNextMonth(currentDate)).toEqual(expectedDate)
  })
})
