.wrapper {
  padding: 0;
}
/* table, use thin borders between cells */
.content {
  border-collapse: collapse;
  font-size: 18px;
}

.content thead th {
  font-weight: normal;
  padding: 16px 16px 8px;
  background-color: var(--color-gray);
}

.content td {
  text-align: center;
  white-space: nowrap;
  padding: 8px 4px;
  border: 1px solid var(--color-gray);
}

.content :is(th, td):first-child {
  padding-left: 16px;
}
.content :is(th, td):last-child {
  padding-right: 16px;
}
.content tbody tr:last-child td {
  padding-bottom: 16px;
}

.secondaryContent {
  color: var(--color-text-secondary);
}

.missing {
  color: var(--color-warning);
  /* b/c warning color is barely readable on white */
  font-weight: bold;
}
