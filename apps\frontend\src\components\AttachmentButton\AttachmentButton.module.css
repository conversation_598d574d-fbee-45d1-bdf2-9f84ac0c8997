.wrapper {
  position: relative;
  border: transparent;
  display: inline-block;
  transition:
    background-color 0.5s ease,
    color 0.5s ease;
}

.button {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  /* Considering both print and delete existence. Otherwise icons are going off */
  min-width: 200px;
  border: none;
  padding: 6px 8px;
  cursor: pointer;
  border-radius: var(--radius-button-half);
}

.actionIcons {
  position: absolute;
  right: 0;
  display: flex;
  gap: 0px;
  opacity: 1;
  top: 0px;
  right: 4px;
}

.iconButton.iconButton {
  padding: 4px;
  font-size: 20px;
}

.iconButton:active,
.wrapper:hover .actionIcons {
  opacity: 0;
}

.wrapper:hover .actionIcons {
  opacity: 1;
}

.icon {
  font-size: 20px;
  flex-shrink: 0;
}

.actions {
  display: flex;
  gap: 8px;
}

.content {
  display: grid;
  grid-row-gap: 16px;
}
