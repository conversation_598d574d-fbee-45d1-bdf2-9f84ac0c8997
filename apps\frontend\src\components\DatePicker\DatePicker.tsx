import { PopoverDisclosure } from "@ariakit/react"
import c from "classnames"
import { FocusEventHandler, useEffect, useMemo, useRef } from "react"
import { useTranslation } from "react-i18next"

import { Popover, usePopoverStore } from "components/Ariakit/Popover/Popover"
import Icon from "components/Icon/Icon"
import dateToString from "features/calendar/utils/dateToString"
import { Button, Input, InputProps } from "ui"
import { DayPicker } from "ui/components/DayPicker/DayPicker"

import styles from "./DatePicker.module.css"

export type DatePickerProps = {
  value?: string
  defaultValue?: string
  className?: string
  portal?: boolean
} & Omit<InputProps, "value" | "type">

/**
 * NOTE Value is any string that Date() can parse and may include time but will be ignored and only date value returned.
 */
export const DatePicker = ({
  className = "",
  inputProps,
  portal = false,
  value,
  onFocus,
  onBlur,
  hideMessage,
  ...rest
}: DatePickerProps) => {
  const refWrap = useRef<HTMLDivElement>(null)

  const { t } = useTranslation()

  const calendarPopover = usePopoverStore({
    placement: "bottom-start",
  })

  useEffect(() => {
    if (!refWrap?.current) return
    calendarPopover.setAnchorElement(refWrap.current)
  }, [calendarPopover])

  const selectedDate = useMemo(() => {
    const dateValue = value ? new Date(value) : undefined

    return dateValue
  }, [value])

  const handleChange = (date: Date) => {
    const dateString = dateToString(date)
    const input = refWrap.current?.querySelector("input[type=date]") as
      | HTMLInputElement
      | undefined

    if (input) {
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        window.HTMLInputElement.prototype,
        "value"
      )?.set

      nativeInputValueSetter?.call(input, dateString)

      const event = new Event("change", { bubbles: true })
      input.dispatchEvent(event)
      input.focus()
      calendarPopover.hide()
    }
  }

  const handleFocus: FocusEventHandler<HTMLInputElement> = (e) => {
    onFocus?.(e)
  }

  const handleBlur: FocusEventHandler<HTMLInputElement> = (e) => {
    onBlur?.(e)
  }

  const parsedValue = selectedDate?.toISOString().split("T")[0]

  /* Gutter of -10 because the Input has some gap below it when it has message */
  const gutter = hideMessage ? 4 : -10

  return (
    <div
      className={`${styles.wrap} ${className}`}
      data-read-only={rest?.readOnly}
      data-disabled={rest?.disabled}
      ref={refWrap}
    >
      <Input
        type="date"
        value={parsedValue}
        {...rest}
        inputProps={{
          ...inputProps,
          className: `${styles.customDateInput} ${inputProps?.className}`,
        }}
        hideMessage={hideMessage}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onClick={(e) => {
          e.preventDefault()
          calendarPopover.toggle()
        }}
      />
      <PopoverDisclosure
        disabled={rest.disabled || rest.readOnly}
        toggleOnClick
        store={{ ...calendarPopover, setAnchorElement: () => null }}
        className={c(styles.calendarIcon, {
          [styles.hasIcon]:
            rest.icon || (rest.status && rest.status !== "default"),
        })}
      >
        <Icon name="calendar-2-line" />
      </PopoverDisclosure>
      <Popover
        portal={portal}
        store={calendarPopover}
        className={styles.calendar}
        gutter={gutter}
        hideOnInteractOutside={true}
        hideOnEscape
        autoFocusOnShow={false}
      >
        <DayPicker
          // to force re-render when value changes
          key={(selectedDate || "").toString()}
          onDateSelect={handleChange}
          selectedDate={selectedDate}
          minDate={
            typeof rest.min === "string" ? new Date(rest.min) : undefined
          }
          maxDate={
            typeof rest.max === "string" ? new Date(rest.max) : undefined
          }
          footer={
            <div className={styles.footer}>
              <Button
                variant={"clear"}
                onClick={() => {
                  handleChange(new Date())
                }}
              >
                {t("Today")}
              </Button>
            </div>
          }
        />
      </Popover>
    </div>
  )
}

export default DatePicker
