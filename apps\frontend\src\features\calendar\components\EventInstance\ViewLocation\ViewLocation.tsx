import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

import { Text } from "ui"

import { useViewLocationQuery } from "generated/graphql"

import { ViewSection } from "../ViewSection/ViewSection"
import { ViewSectionConflict } from "../ViewSection/ViewSectionConflict"

type ViewLocationProps = {
  fromTime: string
  toTime: string
  isEventCancelled: boolean
}

export const ViewLocation = ({
  fromTime,
  toTime,
  isEventCancelled,
}: ViewLocationProps) => {
  const { eventId } = useParams()

  const { t } = useTranslation()

  const { data } = useViewLocationQuery({
    variables: {
      eventInstanceId: eventId || "",
      fromTime: new Date(fromTime),
      toTime: new Date(toTime),
    },
    fetchPolicy: "cache-and-network",
  })

  const location = data?.eventInstance?.location
  if (!location) return null

  const showConflict =
    location.__typename === "Room" && !location.isAvailable && !isEventCancelled

  return (
    <ViewSection iconName="map-pin-line">
      <Text>{location.label}</Text>
      {showConflict && (
        <ViewSectionConflict
          message={`${location.label} ${t("has a scheduling conflict")}`}
        />
      )}
    </ViewSection>
  )
}
