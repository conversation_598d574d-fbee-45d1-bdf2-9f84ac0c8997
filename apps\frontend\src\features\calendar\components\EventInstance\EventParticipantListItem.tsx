import { useTranslation } from "react-i18next"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import {
  SubjectDetailsCard,
  SubjectInfoSectionLoading,
  SubjectInfoSectionProps,
} from "components/SubjectDetailsCard/SubjectDetailsCard"
import { Text } from "ui"

import styles from "./ViewEventInstance.module.css"
import { ViewSectionConflict } from "./ViewSection/ViewSectionConflict"

type EventProviderParticipantListItemProps = {
  name?: string
  hasSchedulingConflict?: boolean
  type: "Provider" | "Subject"
}

export const EventProviderParticipantListItem = ({
  name,
  hasSchedulingConflict,
  type,
}: EventProviderParticipantListItemProps) => {
  const { t } = useTranslation()
  const {
    globalState: { isNnMode },
  } = useGlobalState()

  const isProvider = type === "Provider"

  return (
    <li className={styles.participant}>
      {isProvider ? (
        <Text>{name}</Text>
      ) : (
        <PiiSensitive as={Text}>{name}</PiiSensitive>
      )}

      {hasSchedulingConflict && (
        <ViewSectionConflict
          message={`${isNnMode && !isProvider ? "-" : name} ${t(
            "has a scheduling conflict"
          )}`}
        />
      )}
    </li>
  )
}

type EventSubjectParticipantListItemProps = {
  id: string
  name?: string
  hasSchedulingConflict?: boolean
  subjectData?: SubjectInfoSectionProps
}

export const EventSubjectParticipantListItem = ({
  id,
  name,
  hasSchedulingConflict,
  subjectData,
}: EventSubjectParticipantListItemProps) => {
  const { t } = useTranslation()
  const {
    globalState: { isNnMode },
  } = useGlobalState()

  return (
    <li>
      {subjectData ? (
        <SubjectDetailsCard
          id={id}
          name={name}
          {...subjectData}
          {...(hasSchedulingConflict && {
            subjectInfoIcon: (
              <ViewSectionConflict
                message={`${isNnMode ? "-" : name} ${t("has a scheduling conflict")}`}
              />
            ),
          })}
        />
      ) : (
        <SubjectInfoSectionLoading />
      )}
    </li>
  )
}
