import c from "classnames"
import { forwardRef, HTMLAttributes } from "react"

import { color } from "styles/colors"

import styles from "./Popover.module.css"

export type PopoverProps = HTMLAttributes<HTMLDivElement>

const Popover = forwardRef<HTMLDivElement, PopoverProps>(function Popover(
  { className = "", ...rest },
  ref
) {
  return (
    <div ref={ref} className={`${className} ${styles.popover}`} {...rest} />
  )
})
export default Popover

export type AnimatedPopoverProps = {
  animated?: boolean
  animationDirection?: "up" | "down"
} & PopoverProps

export const AnimatedPopover = forwardRef<HTMLDivElement, AnimatedPopoverProps>(
  function AnimatedPopover(
    { className = "", animationDirection = "down", animated = true, ...rest },
    ref
  ) {
    return (
      <Popover
        ref={ref}
        className={c(
          {
            [className]: className,
            [styles.animated]: animated,
            [styles.animateUp]: animationDirection === "up",
            [styles.animateDown]: animationDirection === "down",
          },
          color.levBlue
        )}
        {...rest}
      />
    )
  }
)
