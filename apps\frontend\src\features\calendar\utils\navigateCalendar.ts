import { useCallback } from "react"
import { View } from "react-big-calendar"
import {
  generatePath,
  NavigateOptions,
  Path,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom"

import { useLocalStorage } from "components/LocalStorageProvider/LocalStorageProvider"
import { RouteString } from "routes/RouteStrings"

type GetCalendarPathOptions = {
  view?: View
  eventId?: string
  templateId?: string
  providerId?: string
}

export const useGetCalendarPath = () => {
  const params = useParams()
  const [defaultView] = useLocalStorage("calendarView")

  // Using useCallback for react-big-calendar
  return useCallback(
    (route: RouteString, options: GetCalendarPathOptions = {}) => {
      return generatePath(route, {
        ...params,
        view: options.view || defaultView,
        eventId: options.eventId || params.eventId,
      })
    },
    [params, defaultView]
  )
}

type Options = {
  search?: Record<string, string>
} & GetCalendarPathOptions &
  NavigateOptions
export const useGetCalendarPathObject = () => {
  const getCalendarPath = useGetCalendarPath()
  const [searchParams] = useSearchParams()

  return useCallback(
    (route: RouteString, options: Options = {}): Partial<Path> => {
      const newSearchParams = new URLSearchParams(searchParams)
      if (options.search) {
        Object.entries(options.search).forEach(([key, value]) => {
          newSearchParams.set(key, value)
        })
      }
      const pathname = getCalendarPath(route, options)
      const search = newSearchParams.toString()

      return {
        pathname,
        search,
      }
    },
    [getCalendarPath, searchParams]
  )
}

export default function useNavigateCalendar() {
  const navigate = useNavigate()
  const getPathObject = useGetCalendarPathObject()

  return useCallback(
    (route: RouteString, options: Options = {}) =>
      navigate(getPathObject(route, options), options),
    [getPathObject, navigate]
  )
}
