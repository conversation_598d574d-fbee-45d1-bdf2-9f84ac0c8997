.select {
  display: flex;
  justify-content: space-between;
  align-items: center;
  grid-row: 2;
  flex-grow: 1;
  gap: 12px;

  border: 1px solid var(--color-neutral);
  border-radius: var(--radius-button-half);
  color: var(--color-initial-text);

  box-sizing: border-box;
  background: var(--color-white);

  min-height: 42px;

  font-weight: 400;
  font-size: 16px;
  line-height: 18px;
  cursor: pointer;

  padding: 11px 16px;
}

.select[aria-expanded="true"],
.select:focus-visible {
  border-color: var(--color-500);
  box-shadow:
    0 0 4px rgba(13, 16, 57, 0.1),
    0 0 20px rgba(13, 16, 57, 0.2);
  background-color: var(--color-white);
  outline: none;
}

.selectArrow {
  margin: -4px 0;
  font-size: 24px;
}

.select[aria-expanded="true"] .selectArrow {
  transform: rotate(180deg);
}
