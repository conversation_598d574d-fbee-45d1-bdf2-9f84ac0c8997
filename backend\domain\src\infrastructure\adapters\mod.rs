//! Adapters to bridge between domain service traits and contracts service traits
//! 
//! These adapters enable the inverse dependency architecture by allowing:
//! - Domain layer to continue using its existing service interfaces
//! - API layer to provide contracts implementations
//! - Smooth transition without breaking existing domain logic

pub mod calendar_notification_adapter;

pub use calendar_notification_adapter::CalendarNotificationAdapter;
