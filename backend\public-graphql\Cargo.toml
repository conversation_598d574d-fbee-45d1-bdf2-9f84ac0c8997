[package]
name = "public-graphql"
version = "0.1.0"
edition.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
leviosa-domain-contracts = { path = "../domain-contracts" }
leviosa-infrastructure = { path = "../infrastructure" }
leviosa-macros = { path = "../macros" }
async-graphql.workspace = true
anyhow.workspace = true
sea-orm.workspace = true
getset.workspace = true
dotenv.workspace = true
envy.workspace = true
serde.workspace = true
chrono.workspace = true
paste.workspace = true
uuid.workspace = true
tokio.workspace = true
tracing.workspace = true
sentry.workspace = true
serde_with.workspace = true
rand.workspace = true


