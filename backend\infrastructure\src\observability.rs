use async_trait::async_trait;
use futures::{Future, FutureExt};
use leviosa_domain_contracts::auth::AuthData;
use sentry::{SentryFutureExt, SessionMode};
use serde_json::{Map, Value};
use std::net::IpAddr;
use std::sync::Arc;
use tracing::{Instrument, Level};
use tracing_subscriber::{filter::LevelFilter, layer::SubscriberExt, util::SubscriberInitExt};

/// Initializes logging and error reporting facilities. Should be called at app startup. Returns guard that should be dropped at app shutdown.
pub fn init(log_level: Level, structured_logs: &bool, sentry_config: SentryConfig<'_>) -> Guard {
    let guard = sentry::init(sentry::ClientOptions {
        dsn: Some(sentry_config.dsn.clone()),
        release: sentry_config
            .release
            .map(|release| release.to_owned().into()),
        environment: Some(sentry_config.environment.to_owned().into()),
        sample_rate: sentry_config.sample_rate,
        traces_sample_rate: sentry_config.traces_sample_rate,
        server_name: sentry_config
            .server_name
            .map(|server_name| server_name.to_owned().into()),
        session_mode: SessionMode::Request,
        ..Default::default()
    });

    let level_filter = LevelFilter::from(log_level);
    let fmt_layer = tracing_subscriber::fmt::layer()
        .with_file(true)
        .with_line_number(true);

    let structured_fmt_layer = tracing_subscriber::fmt::layer()
        .json()
        .with_file(true)
        .with_line_number(true);

    tracing_subscriber::registry()
        .with(level_filter)
        .with(structured_logs.then(|| structured_fmt_layer))
        .with((structured_logs != &true).then_some(fmt_layer))
        .with(sentry_tracing::layer())
        .init();

    guard
}

pub type Guard = sentry::ClientInitGuard;

#[derive(Clone, Copy)]
pub struct SentryConfig<'a> {
    pub dsn: &'a sentry::types::Dsn,
    pub release: Option<&'a str>,
    pub environment: &'a str,
    pub sample_rate: f32,
    pub traces_sample_rate: f32,
    pub profiles_sample_rate: f32,
    pub server_name: Option<&'a str>,
}

pub fn with_request_span<T, F: Future<Output = T>>(
    query: String,
    operation_name: Option<String>,
    variables: Map<String, Value>,
    fut: F,
) -> impl Future<Output = T> {
    let tracing_span = tracing::span!(
        tracing::Level::INFO,
        "request",
        query,
        operation_name,
        ?variables,
    );

    let sentry_hub = sentry::Hub::new_from_top(sentry::Hub::current());
    sentry_hub.configure_scope(|scope| {
        scope.set_context(
            "request",
            sentry::protocol::Context::Other(
                [
                    ("query".into(), query.into()),
                    ("operation_name".into(), operation_name.into()),
                    (
                        "variables".into(),
                        sentry::protocol::Value::Object(variables),
                    ),
                ]
                .into(),
            ),
        );
    });
    fut.bind_hub(sentry_hub).instrument(tracing_span)
}

pub fn with_authentication_span<'a, T: 'a, F: Future<Output = T> + Send + 'a>(
    auth_data: AuthData,
    host_header: Option<String>,
    ip_addr: Option<IpAddr>,
    fut: F,
) -> impl Future<Output = T> + 'a {
    if let AuthData::User(user_auth_data, _) = auth_data {
        let tracing_span = tracing::span!(
            tracing::Level::INFO,
            "authenticated",
            user_id = %user_auth_data.user_id(),
            organisation_id = %user_auth_data.organisation_id(),
            host = ?host_header,
        );
        let fut = fut.instrument(tracing_span).boxed();
        sentry::Hub::current().configure_scope(|scope| {
            scope.set_user(Some(sentry::User {
                id: Some(user_auth_data.user_id().to_string()),
                ip_address: ip_addr.map(sentry::protocol::IpAddress::Exact),
                other: [(
                    "organisation_id".into(),
                    user_auth_data.organisation_id().to_string().into(),
                )]
                .into(),
                ..Default::default()
            }));
            if let Some(host_header) = host_header {
                scope.set_tag("host", host_header);
            }
        });
        fut
    } else {
        sentry::Hub::current().configure_scope(|scope| {
            scope.set_user(Some(sentry::User {
                ip_address: ip_addr.map(sentry::protocol::IpAddress::Exact),
                ..Default::default()
            }));
            if let Some(host_header) = host_header {
                scope.set_tag("host", host_header);
            }
        });

        fut.boxed()
    }
}

pub fn with_resolver_span<T, F: Future<Output = T>>(
    parent_type: &str,
    field_name: &str,
    fut: F,
) -> impl Future<Output = T> {
    let resolver_name = format!("{parent_type}.{field_name}");
    let tracing_span = tracing::span!(tracing::Level::INFO, "resolve", resolver = resolver_name);
    let sentry_hub = sentry::Hub::new_from_top(sentry::Hub::current());
    sentry_hub.configure_scope(|scope| {
        scope.set_tag("resolver", resolver_name);
    });
    fut.instrument(tracing_span).bind_hub(sentry_hub)
}

pub struct ObservabilityExtension;

impl async_graphql::extensions::ExtensionFactory for ObservabilityExtension {
    fn create(&self) -> Arc<dyn async_graphql::extensions::Extension> {
        Arc::new(Extension)
    }
}

struct Extension;

#[async_trait]
impl async_graphql::extensions::Extension for Extension {
    async fn resolve(
        &self,
        ctx: &async_graphql::extensions::ExtensionContext<'_>,
        info: async_graphql::extensions::ResolveInfo<'_>,
        next: async_graphql::extensions::NextResolve<'_>,
    ) -> async_graphql::ServerResult<Option<async_graphql::Value>> {
        with_resolver_span(info.parent_type, info.name, next.run(ctx, info)).await
    }

    async fn prepare_request(
        &self,
        ctx: &async_graphql::extensions::ExtensionContext<'_>,
        request: async_graphql::Request,
        next: async_graphql::extensions::NextPrepareRequest<'_>,
    ) -> async_graphql::ServerResult<async_graphql::Request> {
        with_request_span(
            request.query.clone(),
            request.operation_name.clone(),
            request
                .variables
                .iter()
                .map(|(k, v)| {
                    (
                        k.to_string(),
                        v.clone().into_json().unwrap_or_else(|_| {
                            Value::String(String::from("Serialization failed"))
                        }),
                    )
                })
                .collect(),
            next.run(ctx, request),
        )
        .await
    }
}
