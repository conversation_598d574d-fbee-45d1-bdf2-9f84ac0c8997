import "@ariakit/react"
import c from "classnames"
import { forwardRef } from "react"
import { useTranslation } from "react-i18next"

import { Menu, MenuItem, MenuProvider, MenuButton } from "components/Ariakit"
import Icon from "components/Icon/Icon"
import { IconButton, Tag, Text } from "ui"

import styles from "./DashboardTags.module.css"

const maxVisibleTags = 3

export type DashboardSelectedTagMap = {
  // -1 for exclude, 0 for not selected, 1 for include
  [tagKey: string]: -1 | 0 | 1
}

type DashboardTagsProps = {
  allTags: string[]
  selectedTags: DashboardSelectedTagMap
  onExcludeTag: (tag: string) => void
  onIncludeTag: (tag: string) => void
  onRemoveTag: (tag: string) => void
}

export const DashboardTags = ({
  allTags,
  selectedTags,
  onExcludeTag,
  onIncludeTag,
  onRemoveTag,
}: DashboardTagsProps) => {
  const selectedTagsArray = Object.entries(selectedTags)
    .filter(([, value]) => value !== 0)
    .map(([key]) => key)
  const hasSelectedTags = selectedTagsArray.length > 0

  const { t } = useTranslation("routes", { keyPrefix: "dashboard" })

  const visibleTags = selectedTagsArray.slice(0, maxVisibleTags)

  const hiddenCount =
    selectedTagsArray.length > maxVisibleTags
      ? selectedTagsArray.length - maxVisibleTags
      : 0

  const hiddenTags = selectedTagsArray.slice(maxVisibleTags)

  const restTags = allTags.filter(
    (tag) => !selectedTagsArray.some((selectedTag) => selectedTag === tag)
  )

  return (
    <div className={styles.wrap}>
      {visibleTags.map((tag) => (
        <DashboardTag
          tag={tag}
          key={tag}
          include={selectedTags[tag]}
          onExcludeTag={onExcludeTag}
          onIncludeTag={onIncludeTag}
          onRemoveTag={onRemoveTag}
        />
      ))}

      {hiddenCount > 0 && (
        <MenuProvider placement="bottom">
          <MenuButton variant="filled-light" className={styles.overflowButton}>
            +{hiddenCount}
          </MenuButton>
          <Menu className={styles.hiddenCountPopover}>
            {hiddenTags.map((tag) => (
              <MenuItem
                key={tag}
                render={(p) => (
                  <DashboardTag
                    tag={tag}
                    include={selectedTags[tag]}
                    onExcludeTag={onExcludeTag}
                    onIncludeTag={onIncludeTag}
                    onRemoveTag={onRemoveTag}
                    {...p}
                  />
                )}
              />
            ))}
          </Menu>
        </MenuProvider>
      )}

      {!allTags.length ||
        (restTags.length ? (
          <MenuProvider placement="bottom">
            <MenuButton
              className={c(styles.hashtagButton)}
              variant={!hasSelectedTags ? "outline" : "filled-light"}
              aria-label="Filter by tag"
            >
              <Icon name="hashtag" />
              <Icon name="arrow-down-s-line" />
            </MenuButton>
            <Menu className={styles.hashTagPopover}>
              {restTags.map((tag) => (
                <MenuItem key={tag} onClick={() => onIncludeTag(tag)}>
                  {tag}
                </MenuItem>
              ))}
              {allTags.length === 0 && (
                <MenuItem disabled className={styles.noTagsMessage}>
                  <Text size="large" weight="bold">
                    {t("filterByTag")}
                  </Text>
                  <Text>{t("filterByTagDescription")}</Text>
                </MenuItem>
              )}
            </Menu>
          </MenuProvider>
        ) : null)}
    </div>
  )
}

type DashboardTagProps = {
  tag: string
  include: -1 | 0 | 1
  className?: string
} & Pick<DashboardTagsProps, "onExcludeTag" | "onRemoveTag" | "onIncludeTag">

const DashboardTag = forwardRef<HTMLDivElement, DashboardTagProps>(
  (
    { tag, include, className = "", onExcludeTag, onRemoveTag, onIncludeTag },
    ref
  ) => {
    return (
      <Tag
        ref={ref}
        color={include === -1 ? "neutral" : "levBlue"}
        className={c(styles.tag, className)}
      >
        <button
          className={styles.tagButton}
          onClick={() =>
            include === 1 ? onExcludeTag(tag) : onIncludeTag(tag)
          }
        >
          <span className={styles.tagContent}>{tag}</span>
          <Icon name={include === 1 ? "eye-line" : "eye-off-line"} />
        </button>
        <IconButton iconName="close-line" onClick={() => onRemoveTag(tag)} />
      </Tag>
    )
  }
)
