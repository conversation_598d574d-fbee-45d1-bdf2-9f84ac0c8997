import { useState } from "react"
import { useTranslation } from "react-i18next"

import { Button, notification } from "ui"

import {
  useAddBedToCorridorMutation,
  useCreateRoomMutation,
  useDeleteBedFromCorridorMutation,
} from "generated/graphql"

import { BedTable } from "../BedTable/BedTable"
import { Building } from "../BuildingOverview/BuildingOverview"
import styles from "../CorridorOverview/CorridorOverview.module.css"
import { BedForm } from "../forms/BedForm/BedForm"
import { RoomForm } from "../forms/RoomForm/RoomForm"

type AddRoomToCorridorProps = {
  buildingId: string
  activeCorridor?: Building["corridors"]["corridors"][0]
}

export const AddRoomToCorridor = ({
  buildingId,
  activeCorridor,
}: AddRoomToCorridorProps) => {
  const [showRoomForm, setShowRoomFrom] = useState(false)
  const [showBedForm, setShowBedForm] = useState(false)

  const { t } = useTranslation()

  const [createRoom, { loading: loadingCreateRoom, error: createRoomError }] =
    useCreateRoomMutation({
      onCompleted: (data) => {
        if (data) {
          notification.create({
            message: t("Room has been created"),
            status: "success",
            maxWidth: "500px",
          })
        }

        setShowRoomFrom(false)
      },
    })

  const [
    addBedToCorridor,
    { loading: loadingAddBed, error: createBuildingError },
  ] = useAddBedToCorridorMutation({
    onCompleted: (data) => {
      if (data) {
        notification.create({
          message: t("Bed has been added to location"),
          status: "success",
          maxWidth: "500px",
        })
      }

      setShowBedForm(false)
    },
  })

  const [deleteBedForCorridor] = useDeleteBedFromCorridorMutation()

  if (showRoomForm)
    return (
      <RoomForm
        onSubmit={(data) => {
          createRoom({
            variables: {
              input: {
                ...data,
                capacity: data.capacity || 0,
                buildingId,
                corridorId: activeCorridor?.id,
              },
            },
          })
        }}
        onCancel={() => setShowRoomFrom(false)}
        loading={loadingCreateRoom}
        error={createRoomError}
      />
    )

  if (showBedForm)
    return (
      <BedForm
        onSubmit={(data) => {
          const { label, bedType } = data

          addBedToCorridor({
            variables: {
              input: {
                label: label,
                bedType: bedType,
                corridorId: activeCorridor?.id,
              },
            },
          })
        }}
        onCancel={() => setShowBedForm(false)}
        error={createBuildingError}
        loading={loadingAddBed}
      />
    )

  return (
    <>
      <div className={styles.buttonsWrap}>
        <Button onClick={() => setShowRoomFrom(true)}>
          {t("Add Room to Corridor")}
        </Button>

        <Button onClick={() => setShowBedForm(true)}>
          {t("Add Bed to Corridor")}
        </Button>
      </div>
      <br />
      <BedTable
        beds={activeCorridor?.beds.beds || []}
        onDelete={(bedId) => {
          deleteBedForCorridor({
            variables: {
              input: {
                locationId: bedId,
              },
            },
          })
        }}
      />
    </>
  )
}
