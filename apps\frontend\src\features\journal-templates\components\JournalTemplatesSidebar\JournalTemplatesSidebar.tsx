import { matchSorter } from "match-sorter"
import { useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate, useParams } from "react-router-dom"

import {
  TabLink,
  TabList,
  TabPanel,
  TabProvider,
} from "components/Ariakit/Tab/Tab"
import { useTabStore } from "components/Ariakit/hooks/useTabStore/useTabStore"
import Icon from "components/Icon/Icon"
import usePermissions from "features/authentication/hooks/usePermissions"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading, Input, Modal } from "ui"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateType,
  PermissionKey,
} from "generated/graphql"

import { CreateJournalTemplateForm } from "../CreateJournalTemplateForm/CreateJournalTemplateForm"
import { JournalTemplateCard } from "../JournalTemplateCard/JournalTemplateCard"
import styles from "./JournalTemplatesSidebar.module.css"

const isJournalTemplateTypeEnum = (
  value: string
): value is JournalTemplateType => {
  return Object.values(JournalTemplateType).includes(
    value as JournalTemplateType
  )
}

export const JournalTemplateSidebar = ({
  templates,
}: {
  templates: JournalTemplateFragmentFragment[]
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { hasPermission } = usePermissions()

  const { templateType } = useParams<{ templateType: string }>()

  const tabStore = useTabStore({
    selectedId: templateType,
    setSelectedId: (id) => {
      if (id && isJournalTemplateTypeEnum(id.toUpperCase())) {
        navigate(
          generatePath(RouteStrings.journalTemplates, {
            templateType: id,
          })
        )
      }
    },
  })

  const activeTab = tabStore.useState().activeId

  const [showNewTemplateModal, setShowNewTemplateModal] =
    useState<JournalTemplateType | null>(null)
  const [searchValue, setSearchValue] = useState("")

  const { inlineTemplates, documentTemplates, extendedDocumentTemplates } =
    useMemo(() => {
      const filteredList = matchSorter(templates, searchValue, {
        keys: ["name"],
      })

      return {
        inlineTemplates: filteredList.filter(
          (t) => t.templateType === JournalTemplateType.InlineTemplate
        ),
        documentTemplates: filteredList.filter(
          (t) => t.templateType === JournalTemplateType.DocumentTemplate
        ),
        extendedDocumentTemplates: filteredList.filter(
          (t) => t.templateType === JournalTemplateType.ExtendedDocumentTemplate
        ),
      }
    }, [templates, searchValue])

  const getTabLink = (
    templateType: JournalTemplateType,
    templateId?: string
  ) => {
    return generatePath(RouteStrings.journalTemplates, {
      templateType: templateType.toLowerCase(),
      templateId,
    })
  }

  const shouldShowAddNewButton = (templateType: string) => {
    if (templateType === JournalTemplateType.InlineTemplate.toLowerCase()) {
      return hasPermission(PermissionKey.SubjectJournalTemplateEdit)
    }

    if (templateType === JournalTemplateType.DocumentTemplate.toLowerCase()) {
      return hasPermission(PermissionKey.SubjectJournalDocumentTemplateEdit)
    }

    return false
  }

  const showAddNewButton = activeTab && shouldShowAddNewButton(activeTab)

  return (
    <aside className={styles.wrap}>
      <div className={styles.heading}>
        <Heading size="display">{t("Templates")}</Heading>
        {showAddNewButton && (
          <Button
            icon={<Icon name={"add-line"} />}
            onClick={() => {
              if (
                activeTab &&
                isJournalTemplateTypeEnum(activeTab?.toUpperCase())
              ) {
                setShowNewTemplateModal(
                  activeTab.toUpperCase() as JournalTemplateType
                )
              }
            }}
          >
            {t("Add new")}
          </Button>
        )}
      </div>

      <TabProvider store={tabStore}>
        <TabList aria-label="templates">
          <TabLink
            id={JournalTemplateType.InlineTemplate.toLowerCase()}
            to={getTabLink(
              JournalTemplateType.InlineTemplate,
              inlineTemplates[0]?.id
            )}
          >
            {t("Inline")}
          </TabLink>
          <TabLink
            id={JournalTemplateType.DocumentTemplate.toLowerCase()}
            to={getTabLink(
              JournalTemplateType.DocumentTemplate,
              documentTemplates[0]?.id
            )}
          >
            {t("Documents")}
          </TabLink>
          <TabLink
            id={JournalTemplateType.ExtendedDocumentTemplate.toLowerCase()}
            to={getTabLink(
              JournalTemplateType.ExtendedDocumentTemplate,
              extendedDocumentTemplates[0]?.id
            )}
          >
            {t("Customised documents")}
          </TabLink>
        </TabList>

        <Input
          label="Templates"
          hideLabel
          placeholder="Search"
          hideMessage
          size="small"
          iconStart={<Icon className={styles.searchIcon} name="search-line" />}
          onChange={(e) => setSearchValue(e.target.value)}
        />

        <TabPanel className={styles.list}>
          {inlineTemplates.map((template) => (
            <JournalTemplateCard key={template.id} template={template} />
          ))}
        </TabPanel>
        <TabPanel className={styles.list}>
          {documentTemplates.map((template) => (
            <JournalTemplateCard key={template.id} template={template} />
          ))}
        </TabPanel>
        <TabPanel className={styles.list}>
          {extendedDocumentTemplates.map((template) => (
            <JournalTemplateCard key={template.id} template={template} />
          ))}
        </TabPanel>
      </TabProvider>

      <Modal
        isOpen={!!showNewTemplateModal}
        closeOnClickOutside
        onClose={() => setShowNewTemplateModal(null)}
        title={t("New template")}
        allowOverflow
      >
        <CreateJournalTemplateForm
          onClose={() => setShowNewTemplateModal(null)}
          templateType={showNewTemplateModal}
        />
      </Modal>
    </aside>
  )
}
