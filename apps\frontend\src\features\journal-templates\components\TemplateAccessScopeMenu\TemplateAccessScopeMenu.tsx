import c from "classnames"
import { useTranslation } from "react-i18next"

import { IconName } from "@leviosa/assets"

import { <PERSON>u, MenuButton, MenuItemRadio } from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { color } from "styles/colors"
import { Button, ButtonText, notification, Text } from "ui"

import {
  TemplateAccessScope,
  useSetJournalTemplateAccessScopeMutation,
} from "generated/graphql"

import styles from "./TemplateAccessScopeMenu.module.css"

const scopeColorMap = {
  [TemplateAccessScope.PublicRead]: color.orange.light,
  [TemplateAccessScope.PublicWrite]: color.blue.light,
  [TemplateAccessScope.Private]: color.pink.light,
}

type TemplateAccessScopeMenuProps = {
  templateId: string
  currentScope: TemplateAccessScope
  canEdit: boolean
}

export const getTemplateAccessScopeIcon = (
  scope: TemplateAccessScope
): IconName => {
  switch (scope) {
    case TemplateAccessScope.PublicRead:
      return "eye-line"
    case TemplateAccessScope.PublicWrite:
      return "edit-line"
    case TemplateAccessScope.Private:
      return "lock-line"
    default:
      return "lock-line"
  }
}

export const TemplateAccessScopeMenu = ({
  templateId,
  canEdit,
  currentScope,
}: TemplateAccessScopeMenuProps) => {
  const menuStore = useMenuStore({
    defaultValues: { accessScope: currentScope },
    placement: "bottom-end",
  })
  const { t } = useTranslation()

  const [setAccessScope] = useSetJournalTemplateAccessScopeMutation()

  const menuOptions: {
    label: string
    menuLabel: string
    description: string
    value: TemplateAccessScope
    icon: IconName
  }[] = [
    {
      label: t("Public"),
      menuLabel: t("Publicly viewable"),
      description: t(
        "All your colleagues can see and use the template, but only you can edit it"
      ),
      value: TemplateAccessScope.PublicRead,
      icon: getTemplateAccessScopeIcon(TemplateAccessScope.PublicRead),
    },
    {
      label: t("Public"),
      menuLabel: t("Publicly editable"),
      description: t("All your colleagues can see, edit and use the template"),
      value: TemplateAccessScope.PublicWrite,
      icon: getTemplateAccessScopeIcon(TemplateAccessScope.PublicWrite),
    },
    {
      label: t("Private"),
      menuLabel: t("Private"),
      description: t("Only you see the template"),
      value: TemplateAccessScope.Private,
      icon: getTemplateAccessScopeIcon(TemplateAccessScope.Private),
    },
  ]

  const currentScopeOption = menuOptions.find(
    (option) => option.value === currentScope
  )

  if (!currentScopeOption) return null
  if (!canEdit) {
    return (
      <div className={styles.wrap}>
        <Button
          data-status={currentScope}
          data-testid="encounter-status-transition-button"
          className={c(styles.menuButton, scopeColorMap[currentScope])}
        >
          <Icon name={currentScopeOption.icon} />
          <ButtonText size="small">{currentScopeOption.label}</ButtonText>
        </Button>
      </div>
    )
  }

  return (
    <div className={styles.wrap}>
      <MenuButton
        store={menuStore}
        data-status={currentScope}
        data-testid="encounter-status-transition-button"
        className={c(styles.menuButton, scopeColorMap[currentScope])}
      >
        <Icon name={currentScopeOption.icon} />
        <ButtonText size="small">{currentScopeOption.label}</ButtonText>
        {canEdit && (
          <Icon
            name={
              menuStore.useState().open
                ? "arrow-up-s-line"
                : "arrow-down-s-line"
            }
          />
        )}
      </MenuButton>
      <Menu store={menuStore} className={styles.menu}>
        {menuOptions.map((option) => (
          <MenuItemRadio
            key={option.value}
            value={option.value}
            name="accessScope"
            onClick={() => {
              setAccessScope({
                variables: {
                  id: templateId,
                  accessScope: option.value,
                },
                onError: () => {
                  notification.create({
                    message: "Failed to update access scope",
                    status: "error",
                  })
                },
              })
            }}
            className={styles.menuItem}
          >
            <Icon name={option.icon} />
            <div>
              <ButtonText>{option.menuLabel}</ButtonText>
              <Text size="small">{option.description}</Text>
            </div>
          </MenuItemRadio>
        ))}
      </Menu>
    </div>
  )
}
