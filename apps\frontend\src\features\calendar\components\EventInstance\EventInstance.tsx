import { PopoverStore } from "@ariakit/react"
import c from "classnames"
import { useRef, useState } from "react"
import { useHotkeys } from "react-hotkeys-hook"

import { Popover } from "components/Ariakit/Popover/Popover"
import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import modifier from "features/power-menu/lib/hotkeys/hotkeyModifier"

import { useEventInstanceQuery } from "generated/graphql"

import EditEventInstance from "../EditEventInstance/EditEventInstance"
import styles from "../EventInstance/EventInstance.module.css"
import { EditEventInstanceHeader } from "./EditEventInstanceHeader/EditEventInstanceHeader"
import { ViewEventInstanceHeader } from "./ViewEvenInstanceHeader/ViewEventInstanceHeader"
import { ViewEventInstance } from "./ViewEventInstance"

type EventInstanceProps = {
  event: CalendarEvent<"eventInstance">
  store: PopoverStore
}

export const EventInstance = ({ event, store }: EventInstanceProps) => {
  const ref = useRef<HTMLDivElement>(null)
  const eventId = event.resource.id

  const isPopoverOpen = store.useState().open

  const [isEditMode, setIsEditMode] = useState(false)

  const { data } = useEventInstanceQuery({
    variables: {
      eventInstanceId: eventId,
    },
    fetchPolicy: "cache-and-network",
    skip: !isPopoverOpen,
  })

  useHotkeys(
    modifier + "+E",
    (event) => {
      event.preventDefault()
      setIsEditMode(true)
    },
    [setIsEditMode]
  )

  const onClose = () => {
    store.hide()
  }

  return (
    <Popover
      store={store}
      className={c(styles.popover, {
        [styles.cancelled]: event.resource.canceledAt,
      })}
      portal={true}
      autoFocusOnHide={false}
      unmountOnHide
      ref={ref}
      overlap
      onTransitionEnd={() => {
        if (!isPopoverOpen) {
          setIsEditMode(false)
        }
      }}
    >
      {!isEditMode && (
        <>
          <ViewEventInstanceHeader
            onClose={onClose}
            onEditClick={() => setIsEditMode(true)}
            event={event}
            createdAt={data?.eventInstance.createdAt}
            createdBy={data?.eventInstance.createdBy}
          />
          <div className={styles.content}>
            <ViewEventInstance
              event={event}
              eventInstance={data?.eventInstance}
              store={store}
            />
          </div>
        </>
      )}

      {isEditMode && data && (
        <>
          <EditEventInstanceHeader onClose={onClose} />
          <div className={styles.content}>
            <EditEventInstance eventInstance={data.eventInstance} />
          </div>
        </>
      )}
    </Popover>
  )
}
