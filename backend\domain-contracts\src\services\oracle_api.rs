//! Oracle API service contract

use crate::errors::Result;
use async_trait::async_trait;
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OracleQuery {
    pub query: String,
    pub parameters: Vec<OracleParameter>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OracleParameter {
    pub name: String,
    pub value: String,
    pub data_type: OracleDataType,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum OracleDataType {
    String,
    Number,
    Date,
    Boolean,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OracleResult {
    pub rows: Vec<OracleRow>,
    pub columns: Vec<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OracleRow {
    pub values: Vec<Option<String>>,
}

/// Contract for Oracle database API
#[async_trait]
#[mockall::automock]
pub trait OracleApi: Send + Sync {
    /// Execute a query against Oracle database
    async fn execute_query(
        &self,
        query: <PERSON>Query,
        token: &str,
    ) -> Result<OracleResult>;

    /// Execute a stored procedure
    async fn execute_procedure(
        &self,
        procedure_name: &str,
        parameters: Vec<OracleParameter>,
        token: &str,
    ) -> Result<OracleResult>;
}
