import React, { useState, useRef, useEffect } from "react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { Button, Label, Text, Textarea } from "ui"

import styles from "./EditFieldForm.module.css"

type EditFieldFormProps = {
  name: string
  label: string
  value?: string
  onSubmit: (value: string) => void
  isLoading?: boolean
  piiSensitive?: boolean
  placeholder: string
  hideLabel?: boolean
  testid?: string
}

export const EditFieldForm: React.FC<EditFieldFormProps> = ({
  name,
  value,
  label,
  onSubmit,
  isLoading = false,
  piiSensitive = false,
  placeholder,
  hideLabel = false,
  testid,
}) => {
  const [showTextForm, setShowTextForm] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement | null>(null)
  const formRef = useRef<HTMLFormElement | null>(null)
  const { t } = useTranslation()

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    const formData = new FormData(event.currentTarget)
    const formFieldValue = formData.get(name) as string

    onSubmit(formFieldValue)
    setShowTextForm(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.ctrlKey && event.key === "Enter") {
      event.preventDefault()
      formRef.current?.requestSubmit() // Trigger the form submission
    } else if (event.key === "Escape") {
      event.preventDefault()
      event.stopPropagation()
      setShowTextForm(false)
    }
  }

  useEffect(() => {
    if (showTextForm && textareaRef.current) {
      textareaRef.current.focus()

      textareaRef.current.setSelectionRange(
        textareaRef.current.value.length,
        textareaRef.current.value.length
      )
    }
  }, [showTextForm, textareaRef.current])

  return (
    <div>
      {!hideLabel && (
        <Label className={styles.label} data-is-edit={showTextForm}>
          {label}
        </Label>
      )}

      <div className={styles.fieldWrap}>
        {!showTextForm ? (
          <>
            {piiSensitive ? (
              <PiiSensitive
                as={Text}
                className={styles.fieldText}
                data-is-empty={!value}
                onClick={() => setShowTextForm(true)}
                role="button"
              >
                {value || placeholder}
              </PiiSensitive>
            ) : (
              <Text
                className={styles.fieldText}
                data-is-empty={!value}
                onClick={() => setShowTextForm(true)}
                role="button"
                data-testid={testid || undefined}
              >
                {value || placeholder}
              </Text>
            )}

            <Button
              onClick={() => setShowTextForm(true)}
              icon={<Icon name="edit-line" fontSize={16} />}
              variant="clear"
              className={styles.editButton}
            />
          </>
        ) : (
          <form ref={formRef} onSubmit={handleSubmit} className={styles.form}>
            {piiSensitive ? (
              <PiiSensitive
                as={Textarea}
                label={label}
                name={name}
                hideLabel
                defaultValue={value || ""}
                hideMessage
                isLoading={isLoading}
                autoGrow
                ref={textareaRef}
                onKeyDown={handleKeyDown}
              />
            ) : (
              <Textarea
                label={label}
                name={name}
                hideLabel
                defaultValue={value || ""}
                hideMessage
                isLoading={isLoading}
                autoGrow
                onKeyDown={handleKeyDown}
                ref={textareaRef}
              />
            )}

            <div className={styles.buttonsWrap}>
              <Button variant="clear" onClick={() => setShowTextForm(false)}>
                {t("Cancel")}
              </Button>
              <Button variant="filled" type="submit">
                {t("Save")}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}
