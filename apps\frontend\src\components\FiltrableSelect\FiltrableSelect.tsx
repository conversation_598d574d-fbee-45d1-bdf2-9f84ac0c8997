import { <PERSON>Item, VisuallyHidden } from "@ariakit/react"
import c from "classnames"
import { forwardRef, Ref, useId } from "react"

import { IconName } from "@leviosa/assets"

import {
  ComboboxGroup,
  ComboboxGroupLabel,
} from "components/Ariakit/Combobox/ComboboxGroup/ComboboxGroup"
import { useComboboxStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { SelectProps } from "components/Select/Select"
import { SelectIcon } from "components/Select/SelectIcon"
import { Button, Label, Text } from "ui"
import { LabelWithInlineGuide } from "ui/components/LabelWithInlineGuide/LabelWithInlineGuide"

import stylesSelect from ".././Select/Select.module.css"
import {
  Combobox,
  ComboboxList,
  Option,
  Select as SelectAriaKit,
  SelectLabel,
  SelectPopover,
  SelectPopoverProps,
} from "../Ariakit"
import { SelectedValue } from "../Select/SelectedValue"
import styles from "./FiltrableSelect.module.css"
import { Options } from "./Options"

const nonPrintableKeys = [
  "Tab",
  "Enter",
  "Escape",
  "Backspace",
  "Delete",
  "ArrowUp",
  "ArrowDown",
  "ArrowLeft",
  "ArrowRight",
  "Home",
  "End",
  "PageUp",
  "PageDown",
  " ",
  "Shift",
  "Control",
  "Alt",
  "Meta",
  "CapsLock",
  "Insert",
  "NumLock",
  "ScrollLock",
  "Pause",
  "ContextMenu",
  "Help",
  "Clear",
  "PrintScreen",
  "Escape",
]

type OptionsGroup<T extends string | string[]> = {
  label: string
  options: Option<T extends string ? T : T[number]>[]
}

export type FiltrableSelectProps<T extends string | string[]> =
  SelectProps<T> & {
    filteredOptions?: Option<T extends string ? T : T[number]>[]
    filteredOptionsGroups?: OptionsGroup<T>[]
    comboboxStore: ReturnType<typeof useComboboxStore>
    inputPlaceholder?: string
    sameWidth?: boolean
    iconStart?: IconName
    portal?: boolean
    finalFocus?: SelectPopoverProps["finalFocus"]
    className?: string
  }

export const FiltrableSelect = <T extends string | string[] = string>(
  {
    message,
    status = "default",
    size = "default",
    label,
    isLoading = false,
    disabled = false,
    hideLabel = false,
    className = "",
    icon,
    iconStart,
    options,
    filteredOptions = [],
    filteredOptionsGroups,
    placeholder = "Select Option",
    selectStore,
    noOptionsPlaceholder = "No Options Found",
    name,
    variant = "default",
    onSelectChange,
    comboboxStore,
    readOnly = false,
    inputPlaceholder = "Search...",
    isClearable = false,
    sameWidth = false,
    finalFocus = null,
    portal,
    hideMessage = false,
    inlineGuide,
    ...rest
  }: FiltrableSelectProps<T>,
  ref: Ref<HTMLButtonElement>
) => {
  const id = useId()

  const { value: selectValue, open } = selectStore.useState()

  const { value: comboboxValue } = comboboxStore.getState()

  const isMulti = Array.isArray(selectValue)

  const hasValue = isMulti ? selectValue.length > 0 : !!selectValue

  const hasClear = isClearable && hasValue

  const showFilteredGroups =
    filteredOptionsGroups && filteredOptionsGroups.length > 0

  return (
    <div
      className={`${stylesSelect.wrap} ${className}`}
      data-size={size}
      data-disabled={disabled}
      data-read-only={readOnly}
      data-status={status}
      data-is-open={open}
      data-no-options={!options.length}
      data-variant={variant}
      data-is-multi={Array.isArray(selectValue)}
      data-has-clear-icon={hasClear}
      {...rest}
    >
      <LabelWithInlineGuide inlineGuide={inlineGuide}>
        <Label
          as={SelectLabel}
          className={c(stylesSelect.label, { labelIsRequired: rest.required })}
          store={selectStore}
          visuallyHidden={hideLabel}
        >
          {label}
        </Label>
      </LabelWithInlineGuide>

      <div className={stylesSelect.selectWrapper}>
        <SelectAriaKit
          ref={ref}
          name={name}
          store={selectStore}
          className={stylesSelect.select}
          aria-describedby={message ? id : undefined}
          required={rest.required}
          onKeyDown={(event) => {
            // Prevent non-printable keys or meta keys from opening the select
            if (nonPrintableKeys.includes(event.key)) return

            selectStore.setOpen(true)
            comboboxStore.setValue(event.key)
          }}
        >
          {iconStart && <Icon name={iconStart} className={styles.iconStart} />}
          <SelectedValue
            placeholder={placeholder}
            options={options}
            selectStore={selectStore}
            handleUnselectValue={(optionValue) => {
              if (!Array.isArray(selectValue)) return

              const options = selectValue.filter(
                (value) => value !== optionValue
              )
              onSelectChange?.(options as T)
            }}
          />
          <span className={stylesSelect.icon}>
            <SelectIcon isLoading={isLoading} icon={icon} />
          </span>
        </SelectAriaKit>
        {hasClear && (
          <Button
            icon={<Icon name="close-line" />}
            className={stylesSelect.clearButton}
            onClick={() => {
              selectStore.setOpen(false)
              if (isMulti) {
                selectStore.setValue([])
                onSelectChange?.([] as unknown as T)
                return
              }

              onSelectChange?.(null)
              selectStore.setValue("")
            }}
          />
        )}
      </div>

      <SelectPopover
        store={selectStore}
        composite={false}
        className={styles.popover}
        sameWidth={sameWidth}
        portal={portal}
        finalFocus={finalFocus}
      >
        <Combobox
          store={comboboxStore}
          autoSelect
          autoFocus
          placeholder={inputPlaceholder}
          className={c({
            [styles.noOptionsCombobox]: !options.length,
          })}
        />
        <ComboboxList store={comboboxStore}>
          <SelectItem render={<VisuallyHidden />} hidden value="" />
          {showFilteredGroups ? (
            filteredOptionsGroups?.map(({ label, options }) => {
              if (options.length === 0) return null
              return (
                <ComboboxGroup key={label}>
                  <ComboboxGroupLabel>{label}</ComboboxGroupLabel>
                  <Options
                    value={selectValue}
                    options={options}
                    isLoading={isLoading}
                    noOptionsPlaceholder={noOptionsPlaceholder}
                    hasComboboxValue={comboboxValue !== ""}
                    onClick={(value) => {
                      if (Array.isArray(selectValue)) {
                        onSelectChange?.([...selectValue, value] as T)
                        return
                      }

                      onSelectChange?.(value as T)
                    }}
                  />
                </ComboboxGroup>
              )
            })
          ) : (
            <Options
              value={selectValue}
              options={filteredOptions}
              isLoading={isLoading}
              noOptionsPlaceholder={noOptionsPlaceholder}
              hasComboboxValue={comboboxValue !== ""}
              onClick={(value) => {
                if (Array.isArray(selectValue)) {
                  onSelectChange?.([...selectValue, value] as T)
                  return
                }

                onSelectChange?.(value as T)
              }}
            />
          )}
        </ComboboxList>
      </SelectPopover>
      {!hideMessage && (
        <Text size="small" id={id} className={stylesSelect.message}>
          {message || <>&nbsp;</>}
        </Text>
      )}
    </div>
  )
}

export default forwardRef(FiltrableSelect)
