.dialog.dialog {
  width: 100vw;
  height: 100vh;
  display: block;
  /* We don't have in variables */
  background: rgba(0, 0, 0, 0.7);
  border-radius: 0;
}

.content {
  width: 100vw;
  height: 100vh;
  max-height: 100vh;
  /* We don't have in variables */
  background: transparent;
  border: none;
  border-radius: 0;
}

.header {
  display: grid;
  grid-template-columns: 1fr;
  grid-auto-columns: auto;
  grid-auto-flow: column;
  gap: 12px;
  justify-content: flex-end;
  align-items: center;
  font-size: 24px;
  color: var(--color-white);
}

.dialog button,
.dialog a {
  color: var(--color-white);
  padding: 12px;
  font-size: 24px;
}

.dialog button:hover,
.dialog a:hover {
  /* We don't have in variables */
  background-color: #393939;
}
.dialog button:active,
.dialog a:active {
  background-color: var(--color-black);
}

.dialog button:disabled {
  /* We don't have in variables */
  background-color: #aeaeae;
  color: var(--color-black);
}

.content > div:first-child {
  border: none;
  /* We don't have in variables */
  background: rgba(0, 0, 0, 0.7);
  height: 80px;
  display: grid;
}

.content main {
  height: 100%;
  display: grid;
  grid-template-columns: max-content 1fr max-content;
  grid-template-rows: 1fr max-content;
  grid-template-areas: "prev content next" "bullets bullets bullets";
  gap: 24px;
  align-items: center;
  justify-content: center;
}
.next {
  grid-area: next;
}
.prev {
  grid-area: prev;
}
.document,
.image,
.video,
.audio {
  grid-area: content;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  place-self: center;
  overflow: hidden;
}

.document {
  height: 100%;
  width: clamp(800px, 100%, calc(71vh - 120px));
  max-width: 100%;
}

.audio {
  width: clamp(300px, 100%, 800px);
}

.bullets {
  display: flex;
  justify-content: center;
  gap: 4px;
  grid-area: bullets;
  padding-bottom: 24px;

  .bullet {
    width: 12px;
    height: 12px;
    /* We don't have in variables */
    background-color: #aeaeae;
    border-radius: 22px;
    cursor: pointer;
    transition:
      width 0.3s ease,
      height 0.3s ease,
      background-color 0.3s ease;
    padding: 0;
  }
}

.bullet[aria-current="true"] {
  width: 24px;
  background-color: var(--color-white);
}
