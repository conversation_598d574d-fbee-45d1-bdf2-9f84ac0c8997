import { CalendarColor, ServiceTypeModality, Weekday } from "generated/graphql"

export const getExpectedWeekViewOutput = [
  {
    title: "",
    start: new Date(2024, 6, 16, 10, 0, 0, 0),
    end: new Date(2024, 6, 16, 12, 0, 0, 0),
    allDay: false,
    resource: {
      __typename: "ServiceTypeAvailability",
      id: "a1",
      appointmentDurationMinutes: 30,
      isDeleted: false,
      isBookableOnline: true,
      blockRules: [
        {
          __typename: "AvailabilityBlockRule",
          id: "r1",
          weekday: Weekday.Tuesday,
          fromTime: "10:00",
          toTime: "12:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r2",
          weekday: Weekday.Wednesday,
          fromTime: "14:00",
          toTime: "16:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r3",
          weekday: Weekday.Friday,
          fromTime: "09:00",
          toTime: "11:00",
          isDeleted: false,
        },
      ],
      serviceType: {
        __typename: "ExternalServiceType",
        id: "s1",
        name: "Service 1",
        color: CalendarColor.Pink,
        description: "Service 1 description",
        modality: ServiceTypeModality.Onsite,
        deletedAt: null,
      },
      team: null,
      type: "availability",
      blockId: "r1",
    },
  },
  {
    title: "",
    start: new Date(2024, 6, 17, 14, 0, 0, 0),
    end: new Date(2024, 6, 17, 16, 0, 0, 0),
    allDay: false,
    resource: {
      __typename: "ServiceTypeAvailability",
      id: "a1",
      appointmentDurationMinutes: 30,
      isDeleted: false,
      isBookableOnline: true,
      blockRules: [
        {
          __typename: "AvailabilityBlockRule",
          id: "r1",
          weekday: Weekday.Tuesday,
          fromTime: "10:00",
          toTime: "12:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r2",
          weekday: Weekday.Wednesday,
          fromTime: "14:00",
          toTime: "16:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r3",
          weekday: Weekday.Friday,
          fromTime: "09:00",
          toTime: "11:00",
          isDeleted: false,
        },
      ],
      serviceType: {
        __typename: "ExternalServiceType",
        id: "s1",
        name: "Service 1",
        color: CalendarColor.Pink,
        description: "Service 1 description",
        modality: ServiceTypeModality.Onsite,
        deletedAt: null,
      },
      team: null,
      type: "availability",
      blockId: "r2",
    },
  },
  {
    title: "",
    start: new Date(2024, 6, 19, 9, 0, 0, 0),
    end: new Date(2024, 6, 19, 11, 0, 0, 0),
    allDay: false,
    resource: {
      __typename: "ServiceTypeAvailability",
      id: "a1",
      appointmentDurationMinutes: 30,
      isDeleted: false,
      isBookableOnline: true,
      blockRules: [
        {
          __typename: "AvailabilityBlockRule",
          id: "r1",
          weekday: Weekday.Tuesday,
          fromTime: "10:00",
          toTime: "12:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r2",
          weekday: Weekday.Wednesday,
          fromTime: "14:00",
          toTime: "16:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r3",
          weekday: Weekday.Friday,
          fromTime: "09:00",
          toTime: "11:00",
          isDeleted: false,
        },
      ],
      serviceType: {
        __typename: "ExternalServiceType",
        id: "s1",
        name: "Service 1",
        color: CalendarColor.Pink,
        description: "Service 1 description",
        modality: ServiceTypeModality.Onsite,
        deletedAt: null,
      },
      team: null,
      type: "availability",
      blockId: "r3",
    },
  },
  {
    title: "",
    start: new Date(2024, 6, 15, 8, 0, 0, 0),
    end: new Date(2024, 6, 15, 10, 0, 0, 0),
    allDay: false,
    resource: {
      __typename: "ServiceTypeAvailability",
      id: "a2",
      appointmentDurationMinutes: 45,
      isDeleted: false,
      isBookableOnline: true,
      blockRules: [
        {
          __typename: "AvailabilityBlockRule",
          id: "r4",
          weekday: Weekday.Monday,
          fromTime: "08:00",
          toTime: "10:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r5",
          weekday: Weekday.Thursday,
          fromTime: "15:00",
          toTime: "17:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r6",
          weekday: Weekday.Saturday,
          fromTime: "08:00",
          toTime: "10:00",
          isDeleted: false,
        },
      ],
      serviceType: {
        __typename: "ExternalServiceType",
        id: "s2",
        name: "Service 2",
        color: CalendarColor.LevBlue,
        description: "Service 2 description",
        modality: ServiceTypeModality.Onsite,
        deletedAt: null,
      },
      team: null,
      type: "availability",
      blockId: "r4",
    },
  },
  {
    title: "",
    start: new Date(2024, 6, 18, 15, 0, 0, 0),
    end: new Date(2024, 6, 18, 17, 0, 0, 0),
    allDay: false,
    resource: {
      __typename: "ServiceTypeAvailability",
      id: "a2",
      appointmentDurationMinutes: 45,
      isDeleted: false,
      isBookableOnline: true,
      blockRules: [
        {
          __typename: "AvailabilityBlockRule",
          id: "r4",
          weekday: Weekday.Monday,
          fromTime: "08:00",
          toTime: "10:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r5",
          weekday: Weekday.Thursday,
          fromTime: "15:00",
          toTime: "17:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r6",
          weekday: Weekday.Saturday,
          fromTime: "08:00",
          toTime: "10:00",
          isDeleted: false,
        },
      ],
      serviceType: {
        __typename: "ExternalServiceType",
        id: "s2",
        name: "Service 2",
        color: CalendarColor.LevBlue,
        description: "Service 2 description",
        modality: ServiceTypeModality.Onsite,
        deletedAt: null,
      },
      team: null,
      type: "availability",
      blockId: "r5",
    },
  },
  {
    title: "",
    start: new Date(2024, 6, 20, 8, 0, 0, 0),
    end: new Date(2024, 6, 20, 10, 0, 0, 0),
    allDay: false,
    resource: {
      __typename: "ServiceTypeAvailability",
      id: "a2",
      appointmentDurationMinutes: 45,
      isDeleted: false,
      isBookableOnline: true,
      blockRules: [
        {
          __typename: "AvailabilityBlockRule",
          id: "r4",
          weekday: Weekday.Monday,
          fromTime: "08:00",
          toTime: "10:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r5",
          weekday: Weekday.Thursday,
          fromTime: "15:00",
          toTime: "17:00",
          isDeleted: false,
        },
        {
          __typename: "AvailabilityBlockRule",
          id: "r6",
          weekday: Weekday.Saturday,
          fromTime: "08:00",
          toTime: "10:00",
          isDeleted: false,
        },
      ],
      serviceType: {
        __typename: "ExternalServiceType",
        id: "s2",
        name: "Service 2",
        color: CalendarColor.LevBlue,
        description: "Service 2 description",
        modality: ServiceTypeModality.Onsite,
        deletedAt: null,
      },
      team: null,
      type: "availability",
      blockId: "r6",
    },
  },
]
