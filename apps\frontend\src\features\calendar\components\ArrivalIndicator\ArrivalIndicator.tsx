import { MenuStoreState } from "@ariakit/react"
import c from "classnames"
import { ComponentProps } from "react"
import { useTranslation } from "react-i18next"

import {
  Menu,
  MenuButton,
  MenuGroup,
  MenuGroupLabel,
  MenuItem,
  MenuProvider,
} from "components/Ariakit"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import { Button } from "ui"

import {
  namedOperations,
  ParticipantAttendanceState,
  useSetParticipantsAttendanceMutation,
} from "generated/graphql"

import styles from "./ArrivalIndicator.module.css"

type MenuAttendanceItemProps = {
  hasArrived: boolean
  participantId: string
  children: React.ReactNode
} & ComponentProps<typeof MenuItem>

const MarkAttendanceItem = ({
  hasArrived,
  participantId,
  children,
  ...rest
}: MenuAttendanceItemProps) => {
  const [setParticipantsAttendance] = useSetParticipantsAttendanceMutation({
    // Remove refetching when objId is replaced with id of participant
    refetchQueries: [
      namedOperations.Query.EventInstance,
      namedOperations.Query.ProviderBox,
    ],
  })

  const handleClick = (e: {
    stopPropagation: () => void
    preventDefault: () => void
  }) => {
    e.stopPropagation()
    e.preventDefault()
    setParticipantsAttendance({
      variables: {
        attendanceRecords: [
          {
            id: participantId,
            state: hasArrived
              ? ParticipantAttendanceState.False
              : ParticipantAttendanceState.True,
          },
        ],
      },
    })
  }

  return (
    <MenuItem className={styles.menuButton} onClick={handleClick} {...rest}>
      <Icon
        className={styles.groupIcon}
        name={
          hasArrived ? "checkbox-circle-fill" : "checkbox-blank-circle-line"
        }
      />
      <span>{children}</span>
    </MenuItem>
  )
}

export default function ArrivalIndicator({
  subjectParticipants,
  placement = "bottom-end",
  className,
  wrapperClassName,
}: {
  subjectParticipants: {
    participantId: string
    attendanceState: {
      id: string
      state: ParticipantAttendanceState
    } | null
    subject: {
      __typename: "Subject"
      name: string
    }
  }[]
  placement?: MenuStoreState["placement"]
  className?: string
  wrapperClassName?: string
}) {
  const { t } = useTranslation()

  const {
    globalState: { isNnMode },
  } = useGlobalState()

  const [setParticipantsAttendance] = useSetParticipantsAttendanceMutation({
    // Remove refetching when objId is replaced with id of participant
    refetchQueries: [
      namedOperations.Query.EventInstance,
      namedOperations.Query.ProviderBox,
    ],
  })

  const handleClick = (e: { stopPropagation: () => void }) => {
    e.stopPropagation()
    setParticipantsAttendance({
      variables: {
        attendanceRecords: [
          {
            id: subjectParticipants[0].participantId,
            state: hasArrived
              ? ParticipantAttendanceState.False
              : ParticipantAttendanceState.True,
          },
        ],
      },
    })
  }

  const hasArrived = subjectParticipants.some(
    (participant) =>
      participant.attendanceState?.state === ParticipantAttendanceState.True
  )

  if (subjectParticipants.length === 0) return null

  if (subjectParticipants.length === 1) {
    const tooltipContent = hasArrived
      ? t("{{subjectName}} has arrived. Click to unmark", {
          subjectName:
            (!isNnMode && subjectParticipants[0].subject.name) || "Subject",
        })
      : t("Notify arrival")

    return (
      <Tooltip
        tooltipContent={tooltipContent}
        className={c(wrapperClassName, styles.tooltip)}
      >
        <Button
          variant="clear"
          className={c(styles.arrivalIndicator, className)}
          aria-label={
            hasArrived
              ? t("Arrived - Click to update")
              : t("Not arrived - Click to update")
          }
          icon={
            <Icon
              name={
                hasArrived
                  ? "checkbox-circle-fill"
                  : "checkbox-blank-circle-line"
              }
            />
          }
          onClick={(e: { stopPropagation: () => void }) => handleClick(e)}
        />
      </Tooltip>
    )
  }

  const arrivedSubjects = subjectParticipants.filter(
    (participant) =>
      participant.attendanceState?.state === ParticipantAttendanceState.True
  )

  const notArrivedSubjects = subjectParticipants.filter(
    (participant) =>
      participant.attendanceState?.state !== ParticipantAttendanceState.True
  )

  return (
    <Tooltip
      tooltipContent={t("Notify arrival")}
      className={c(wrapperClassName, styles.tooltip)}
    >
      <MenuProvider placement={placement}>
        <MenuButton
          variant="clear"
          className={c(styles.arrivalIndicator, className)}
          aria-label={
            hasArrived
              ? t("Arrived - Click to update")
              : t("Not arrived - Click to update")
          }
          icon={
            <Icon
              name={
                hasArrived
                  ? "checkbox-circle-fill"
                  : "checkbox-blank-circle-line"
              }
            />
          }
          onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) =>
            e.stopPropagation()
          }
        />
        <Menu portal className={styles.popover} hideOnInteractOutside>
          {arrivedSubjects.length > 0 && (
            <MenuGroup className={styles.menuGroup}>
              <MenuGroupLabel>{t("Arrived")}</MenuGroupLabel>
              {arrivedSubjects.map((participant) => (
                <MarkAttendanceItem
                  hasArrived={true}
                  participantId={participant.participantId}
                  key={participant.participantId}
                >
                  {participant.subject.name}
                </MarkAttendanceItem>
              ))}
            </MenuGroup>
          )}
          {notArrivedSubjects.length > 0 && (
            <MenuGroup className={styles.menuGroup}>
              <MenuGroupLabel>{t("Not arrived")}</MenuGroupLabel>
              {notArrivedSubjects.map((participant) => (
                <MarkAttendanceItem
                  hasArrived={false}
                  participantId={participant.participantId}
                  key={participant.participantId}
                >
                  {participant.subject.name}
                </MarkAttendanceItem>
              ))}
            </MenuGroup>
          )}
        </Menu>
      </MenuProvider>
    </Tooltip>
  )
}
