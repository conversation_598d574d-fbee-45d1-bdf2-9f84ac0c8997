.wrapper {
  height: 100vh;
  display: grid;
  grid-template-columns: 1fr minmax(320px, 400px) 1fr;
  grid-template-rows: 2fr auto 3fr;
  grid-template-areas:
    ". .     ."
    ". content ."
    ". .     .";
}

.content {
  grid-area: content;
  display: flex;
  flex-direction: column;
  padding: 48px 48px 32px;
  border-radius: 8px;
  background-color: var(--color-background);
}

.logo {
  width: 260px;
  align-self: center;
  margin-bottom: 40px;
  padding: 10px 0;
}
