import { CalendarComponentMap } from "features/calendar/components/CalendarMain/CalendarMain"
import ProviderEventsMenu from "features/calendar/components/ProviderEventsMenu/ProviderEventsMenu"
import useCalendarState from "features/calendar/hooks/useCalendarState"
import { Heading } from "ui"
import useDateFormatter from "utils/useDateFormatter"

import styles from "./RBCHeader.module.css"

export const calendarHeaderId = "calendar-heading"

const RBCHeader: CalendarComponentMap["header"] = ({ date }) => {
  const { view, visibleProviders } = useCalendarState()

  const format = useDateFormatter()

  const day = date.getDate()
  const weekday = format(date, { weekday: "short", dateStyle: undefined })

  const showProviderEventsMenu =
    visibleProviders.length === 1 && (view === "week" || view === "work_week")

  return (
    <div role="columnheader" aria-sort="none" className={styles.wrap}>
      {view !== "month" && (
        <Heading
          id={calendarHeaderId}
          as="span"
          size="display"
          className={styles.day}
        >
          {day}
        </Heading>
      )}
      <Heading id={calendarHeaderId} size="xsmall" as="span">
        {weekday}
      </Heading>
      {showProviderEventsMenu && (
        <ProviderEventsMenu
          providerId={visibleProviders[0]}
          date={date}
          className={styles.menuButton}
        />
      )}
    </div>
  )
}

export default RBCHeader
