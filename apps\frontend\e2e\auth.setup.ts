// auth.setup.ts
import { expect, test as setup } from "@playwright/test"

import { STORAGE_STATE } from "../playwright.config"

setup("authenticate", async ({ page }) => {
  await page.goto("http://localhost:3000/login")
  await page.waitForSelector("[name='email']", {
    state: "visible",
    timeout: 10000,
  })

  await page.fill("[name='email']", "<EMAIL>")
  await page.fill("[name='password']", "somepassword")
  await page.click('button:has-text("Log In")')

  await page.waitForURL("http://localhost:3000/", { timeout: 10000 })

  await expect(page).toHaveURL("http://localhost:3000/")

  // End of authentication steps.
  await page.context().storageState({ path: STORAGE_STATE })
})
