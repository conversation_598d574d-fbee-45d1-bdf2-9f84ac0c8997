{"extends": ["../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_", "ignoreRestSiblings": true}], "@typescript-eslint/no-non-null-assertion": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/no-inferrable-types": ["warn", {"ignoreParameters": true, "ignoreProperties": true}], "newline-before-return": ["warn"], "no-console": ["error", {"allow": ["warn", "error"]}], "no-warning-comments": ["error", {"terms": ["todo", "fixme"]}]}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}]}