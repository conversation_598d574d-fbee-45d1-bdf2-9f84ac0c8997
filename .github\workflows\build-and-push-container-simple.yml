name: Build, test and push container
on:
  workflow_call:
    inputs:
      dockerfile_path:
        type: string
        required: true
      build_context:
        type: string
        default: "."
      tag_prefix:
        type: string
        required: true
      env_file_path:
        type: string
        required: true
      run_options:
        type: string
        default: ""
      skip_testing:
        type: boolean
        default: false
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      SENTRY_AUTH_TOKEN:
        required: false
      SENTRY_CLINIC_PORTAL_DSN:
        required: false
jobs:
  build-test-and-push-container:
    name: Build, test and push container
    runs-on:
      group: large-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build image
        uses: docker/build-push-action@v6
        with:
          context: ${{ inputs.build_context }}
          file: ${{ inputs.dockerfile_path }}
          load: true
          tags: ${{ inputs.tag_prefix }}
          secrets: |
            SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}
            SENTRY_CLINIC_PORTAL_DSN=${{ secrets.SENTRY_CLINIC_PORTAL_DSN }}
          build-args: |
            GIT_REF_NAME=${{ github.ref_name }}
            SENTRY_CLINIC_PORTAL_DSN=${{ secrets.SENTRY_CLINIC_PORTAL_DSN }}
          cache-from: type=gha,scope=${{ inputs.tag_prefix }}
          cache-to: type=gha,mode=max,scope=${{ inputs.tag_prefix }}
          platforms: "linux/amd64"

      - name: Start container
        if: ${{ !inputs.skip_testing }}
        run: docker run --name ${{ inputs.tag_prefix }} -d --network=host --env-file ${{ inputs.env_file_path }} ${{ inputs.run_options }} ${{ inputs.tag_prefix }}

      - name: Wait for container to become healthy
        if: ${{ !inputs.skip_testing }}
        uses: ./.github/actions/wait-for-container
        with:
          container: ${{ inputs.tag_prefix }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
        
      - name: Login to AWS ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Push image to ECR
        run: |
          docker tag ${{ inputs.tag_prefix }} ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:latest
          docker tag ${{ inputs.tag_prefix }} ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:${{ github.sha }}
          docker push ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:latest
          docker push ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:${{ github.sha }}
