// Essential infrastructure modules for clean architecture
pub mod audit_logger;
pub mod auth;
pub mod calendar_notification_service;
pub mod command;
pub mod command_impl;
pub mod contracts_adapter;
pub mod customer_service;
pub mod date_ext;
pub mod db;
pub mod doctor_letter_and_referral_api;
pub mod email;
pub mod entity_event;
pub mod entity_event_impl;
pub mod errors;
pub mod external_organisation_integration;
pub mod file_repo;
pub mod national_registry_integration;
pub mod nhi_service;
pub mod notification_service_integration;
pub mod online_payment_service;
pub mod oracle_api;
pub mod pdf_generator;
pub mod prescription_api;
pub mod query;
pub mod query_impl;
pub mod repo_connection;
pub mod repo_connection_impl;
pub mod repos;
pub mod resource;
pub mod service_communicator;
pub mod set;
pub mod text_message_integration;

// Re-export the adapter module for external use
pub use contracts_adapter::*;
