// Core infrastructure modules for clean architecture demonstration
// pub mod audit_logger;  // Depends on model
// pub mod auth;  // Depends on model (accounts, permissions, team)
// pub mod calendar_notification_service;  // Depends on model
// pub mod command;  // Depends on model
// pub mod command_impl;  // Depends on model
// pub mod contracts_adapter;  // Depends on model
// pub mod customer_service;  // Depends on model (accounts)
pub mod date_ext;
pub mod db;
// pub mod doctor_letter_and_referral_api;  // Depends on model
pub mod email;  // Re-enabled with basic implementation
// pub mod entity_event;  // Depends on model (accounts)
// pub mod entity_event_impl;  // Depends on model
pub mod errors;
// pub mod external_organisation_integration;  // Depends on model
// pub mod file_repo;  // Depends on model
// pub mod national_registry_integration;  // Depends on model
pub mod nhi_service;  // Re-enabled with basic types
// pub mod notification_service_integration;  // Depends on model
pub mod online_payment_service;
// pub mod oracle_api;  // Depends on model
pub mod pdf_generator;
// pub mod prescription_api;  // Depends on model
// pub mod query;  // Depends on model
// pub mod query_impl;  // Depends on model
// pub mod repo_connection;  // Depends on model
// pub mod repo_connection_impl;  // Depends on model
// pub mod repos;  // Depends on model
pub mod resource;
pub mod service_communicator;
pub mod set;
pub mod text_message_integration;

// Re-export the adapter module for external use
// pub use contracts_adapter::*;  // Commented out - depends on model
