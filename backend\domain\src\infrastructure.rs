// Temporarily comment out modules that depend on model layer to test domain isolation
// pub mod audit_logger;
// pub mod auth;  // Has model dependencies
// pub mod calendar_notification_service;
// pub mod command;
// pub mod command_impl;
// pub mod contracts_adapter;
// pub mod customer_service;  // Has model dependencies
pub mod date_ext;
pub mod db;
// pub mod doctor_letter_and_referral_api;
// pub mod email;  // Has model dependencies
// pub mod entity_event;  // Has model dependencies
// pub mod entity_event_impl;
pub mod errors;
// pub mod external_organisation_integration;
// pub mod file_repo;
// pub mod national_registry_integration;
// pub mod nhi_service;
// pub mod notification_service_integration;
pub mod online_payment_service;
// pub mod oracle_api;
pub mod pdf_generator;
// pub mod prescription_api;
// pub mod query;
// pub mod query_impl;
// pub mod repo_connection;  // Has model dependencies
// pub mod repo_connection_impl;  // Has model dependencies
// pub mod repos;
pub mod resource;
pub mod service_communicator;
pub mod set;
pub mod text_message_integration;

// Re-export the adapter module for external use
// pub use contracts_adapter::*;
