/* eslint-disable no-console */
import c from "classnames"
import { useTranslation } from "react-i18next"

import { Table, TextWithIcon } from "ui"
import { formatNumberInDecimal } from "utils/formatNumberInDecimal"
import { formatNumberInThousand } from "utils/formatNumberInThousand"
import { isTypename } from "utils/isTypename"

// import { useLikeNationalHealthInsurancePriceItemMutation } from "generated/graphql"
// import { ButtonStar } from "../../ButtonStar/ButtonStar"
import styles from "../../OrganisationPriceList/PriceList.module.css"
import { NhiPriceList } from "../NationalHealthInsurancePriceList"

type NationalHealthInsurancePriceListTableProps = {
  billingCodes: NhiPriceList
  isLoading?: boolean
}

export const NationalHealthInsurancePriceListTable = ({
  billingCodes,
  isLoading,
}: NationalHealthInsurancePriceListTableProps) => {
  const { t } = useTranslation()

  // const [likeNationalHealthInsurancePriceListItem] =
  //   useLikeNationalHealthInsurancePriceItemMutation({
  //     onCompleted: (data) => {
  //       console.log("completed")
  //       console.log(data)
  //     },
  //   })

  return (
    <Table>
      <thead>
        <tr className={styles.tableHead}>
          <th>{t("Item ID")}</th>

          <th>{t("Item Name")}</th>

          <th className={styles.numericValue}>{t("Units")}</th>

          <th className={styles.numericValue}>{t("Units Price")}</th>

          <th>{t("Currency")}</th>

          <th className={styles.numericValue}>{t("VAT %")}</th>

          {/* <th></th> */}
        </tr>
      </thead>

      <tbody>
        {billingCodes.length === 0 && (
          <tr>
            <td colSpan={7}>
              <TextWithIcon iconName="information-line">
                {t("No items match the current filter.")}
              </TextWithIcon>
            </td>
          </tr>
        )}

        {billingCodes
          .filter(isTypename("BillingCodeNhi"))
          .map((billingCode) => (
            <tr
              key={billingCode.id}
              className={c(styles.priceListRow, {
                [styles.isLoading]: isLoading,
              })}
            >
              <td className={styles.billingCode}>{billingCode.code}</td>
              <td>
                <div className={styles.truncateText}>{billingCode.title}</div>
              </td>
              <td className={styles.numericValue}>{billingCode.units}</td>
              <td className={styles.numericValue}>
                {formatNumberInThousand(billingCode.unitPrice)}
              </td>
              <td>{billingCode.currency}</td>
              <td className={styles.numericValue}>
                {formatNumberInDecimal(billingCode.vat)}
              </td>
              {/* <td className={styles.buttonColumn}>
                <ButtonStar
                  className={styles.buttonStar}
                  onClick={() => {
                    likeNationalHealthInsurancePriceListItem({
                      variables: {
                        id: billingCode.id,
                      },
                    })
                  }}
                  // should come form filed resolver
                  isStarred={false}
                />
              </td> */}
            </tr>
          ))}
      </tbody>
    </Table>
  )
}
