import { isNumber } from "lodash"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useDebouncedCallback } from "use-debounce"

import StiLogo from "@leviosa/assets/svg/STI_Logo.svg?react"

import { useComboboxStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import Restricted from "features/authentication/components/Restricted/Restricted"
import {
  Button,
  Input,
  Label,
  notification,
  Table,
  Text,
  TextWithIcon,
} from "ui"
import NumberInput from "ui/components/Input/NumberInput/NumberInput"
import { formatNumberInThousand } from "utils/formatNumberInThousand"

import {
  EditInvoiceLineInput,
  GetInvoiceQuery,
  PermissionKey,
  useCreateInvoiceLineMutation,
  useDeleteInvoiceLineMutation,
  useEditPatientInvoiceLineMutation,
} from "generated/graphql"

import { QuantityInput } from "../../QuantityInput/QuantityInput"
import { BillingCodeSelect } from "../BillingCodeSelect/BillingCodeSelect"
import styles from "./PatientInvoiceTable.module.css"

type PatientInvoiceTableProps = {
  invoice: GetInvoiceQuery["invoice"]
}

export const PatientInvoiceTable = ({ invoice }: PatientInvoiceTableProps) => {
  const { t } = useTranslation()

  const invoiceLines = invoice?.invoiceLines

  const [hoveredRow, setHoveredRow] = useState<{
    top?: number
    invoiceLineId?: string
  }>({})
  const selectItemStore = useComboboxStore()
  const isMenuOpen = selectItemStore.useState().open

  const rowIconStyles = {
    top: hoveredRow.top ? `calc(${hoveredRow.top}px + 10px)` : undefined,
    opacity: hoveredRow.top ? 1 : 0,
    transition: hoveredRow.top ? "all 0.15s ease-in-out" : undefined,
  }

  const [editInvoiceLine, { loading: loadingEdit }] =
    useEditPatientInvoiceLineMutation({
      onError: (error) => {
        notification.create({
          message: error.message,
          status: "error",
        })
      },
    })

  const [deleteInvoiceLine, { loading: loadingDelete }] =
    useDeleteInvoiceLineMutation({
      onCompleted: () => {
        setHoveredRow({
          top: undefined,
          invoiceLineId: undefined,
        })
      },
    })
  const [createInvoiceLine, { error }] = useCreateInvoiceLineMutation()

  const onDebounceEdit = useDebouncedCallback(
    (
      invoiceLineId: string,
      fieldName: keyof EditInvoiceLineInput,
      value: number
    ) => {
      editInvoiceLine({
        variables: {
          input: {
            invoiceLineId: invoiceLineId,
            [fieldName]: value,
          },
        },
      })
    },
    500
  )
  const isLoading = loadingEdit || loadingDelete

  return (
    <div
      className={styles.tableWrap}
      onMouseLeave={() => !isMenuOpen && setHoveredRow({})}
    >
      <Restricted to={PermissionKey.BillingIssuedItemCreate}>
        {(hoveredRow.invoiceLineId || isMenuOpen) && (
          <BillingCodeSelect
            className={styles.addButton}
            style={rowIconStyles}
            externalComboboxStore={selectItemStore}
            onSelect={(billingCodeId, billingCodeType) => {
              createInvoiceLine({
                variables: {
                  input: {
                    invoiceId: invoice.id,
                    encounterId: invoice.encounter?.id || "",
                    billingCodeId,
                    quantity: 1,
                    billingCodeType,
                  },
                },
              })
            }}
          />
        )}
      </Restricted>

      <Table className={styles.table}>
        <thead>
          <tr>
            <Label as="th">{t("Item Id")}</Label>
            <Label as="th">{t("Item Name")}</Label>
            <Label className={styles.numericRow} as="th">
              {t("Quantity")}
            </Label>
            <Label className={styles.numericRow} as="th">
              {t("Billable Quantity")}
            </Label>
            <Label className={styles.numericRow} as="th">
              {t("Units")}
            </Label>
            <Label className={styles.numericRow} as="th">
              {t("Unit Price")}
            </Label>
            <Label className={styles.numericRow} as="th">
              {t("Discount %")}
            </Label>
            <Label className={styles.numericRow} as="th">
              {t("Total")}
            </Label>
          </tr>
        </thead>
        <tbody>
          {invoiceLines.length === 0 && (
            <tr>
              <td colSpan={8}>
                <BillingCodeSelect
                  label={t(
                    "This invoice has no billable items. Click to add items."
                  )}
                  className={styles.addNewItemButton}
                  onSelect={(billingCodeId, billingCodeType) => {
                    createInvoiceLine({
                      variables: {
                        input: {
                          invoiceId: invoice.id,
                          encounterId: invoice.encounter?.id || "",
                          billingCodeId,
                          quantity: 1,
                          billingCodeType,
                        },
                      },
                    })
                  }}
                />
                {error && (
                  <Panel status="warning" className={styles.errorPanel}>
                    <TextWithIcon iconName="alert-line">
                      {t(
                        "Could not create invoice line. Please try again. If the problem persists, please contact support."
                      )}
                      <Text size="small" className={styles.errorMessage}>
                        {error.message}
                      </Text>
                    </TextWithIcon>
                  </Panel>
                )}
              </td>
            </tr>
          )}

          {invoiceLines.map(
            ({
              billingCode,
              id,
              quantity,
              units,
              unitPrice,
              discount,
              billableQuantity,
              total,
            }) => {
              const code =
                billingCode?.__typename === "BillingCodeClinicSpecific"
                  ? billingCode.clinicCode
                  : billingCode.code

              const isNhi = billingCode?.__typename === "BillingCodeNhi"

              return (
                <tr
                  className={styles.itemRow}
                  key={id}
                  onMouseEnter={(e) => {
                    const currentRect = e.currentTarget.getBoundingClientRect()
                    const parentRect =
                      e.currentTarget.parentElement?.parentElement?.getBoundingClientRect()

                    if (parentRect && !isMenuOpen) {
                      const relativeTop = currentRect.top - parentRect.top
                      setHoveredRow({
                        top: relativeTop,
                        invoiceLineId: id,
                      })
                    }
                  }}
                >
                  <td className={styles.code}>{code}</td>
                  <td className={styles.billingCodeRow}>
                    {isNhi && <StiLogo className={styles.nhiIcon} />}
                    <Text
                      data-is-nhi={isNhi}
                      className={styles.billingCodeText}
                    >
                      {billingCode.title}
                    </Text>
                  </td>
                  <td className={styles.numericRow}>
                    <QuantityInput
                      name="quantity"
                      error={error?.message}
                      disabled={isLoading}
                      quantity={quantity}
                      className={styles.inputWrap}
                      onBlur={(quantity) => {
                        return editInvoiceLine({
                          variables: {
                            input: {
                              invoiceLineId: id,
                              quantity,
                            },
                          },
                        })
                      }}
                    />
                  </td>
                  <td className={styles.numericRow}>
                    <QuantityInput
                      name="billableQuantity"
                      error={error?.message}
                      disabled={isLoading}
                      quantity={billableQuantity}
                      className={styles.inputWrap}
                      allowFloat={true}
                      onBlur={(billableQuantity) => {
                        return editInvoiceLine({
                          variables: {
                            input: {
                              invoiceLineId: id,
                              billableQuantity,
                            },
                          },
                        })
                      }}
                    />
                  </td>
                  <td className={styles.numericRow}>
                    {formatNumberInThousand(units)}
                  </td>
                  <td className={styles.numericRow}>
                    {!isNhi ? (
                      <NumberInput
                        defaultValue={unitPrice}
                        label="Unit Price"
                        name="unitPrice"
                        className={styles.inputWrap}
                        hideLabel
                        hideMessage
                        allowNegative={false}
                        onValueChange={({ floatValue }) => {
                          isNumber(floatValue) &&
                            onDebounceEdit(id, "unitPrice", floatValue)
                        }}
                      />
                    ) : (
                      <Text className={styles.unitPriceText}>
                        {formatNumberInThousand(unitPrice)}
                      </Text>
                    )}
                  </td>
                  <td className={styles.numericRow}>
                    {!isNhi ? (
                      <Input
                        defaultValue={discount}
                        label="Discount"
                        hideLabel
                        hideMessage
                        name="discount"
                        type="number"
                        className={styles.inputWrap}
                        min="0"
                        max="100"
                        onChange={(e) => {
                          const clampedNumber = Math.min(
                            Math.max(parseInt(e.target.value), 0),
                            100
                          )
                          if (clampedNumber !== parseInt(e.target.value)) {
                            e.target.value = clampedNumber.toString()
                          }
                          onDebounceEdit(id, "discount", clampedNumber)
                        }}
                      />
                    ) : (
                      <Text className={styles.unitPriceText}>{discount}</Text>
                    )}
                  </td>
                  <td className={styles.numericRow}>
                    {formatNumberInThousand(total)}
                  </td>
                </tr>
              )
            }
          )}
        </tbody>
        <tfoot>
          <tr>
            <td colSpan={7}>Total: </td>

            <td className={styles.numericRow}>
              {/* Total without vat */}
              {formatNumberInThousand(invoice.total)}
            </td>
          </tr>
        </tfoot>
      </Table>

      <Restricted to={PermissionKey.BillingIssuedItemEdit}>
        <Button
          variant="clear"
          className={styles.removeButton}
          icon={<Icon name="delete-bin-line" />}
          style={rowIconStyles}
          onClick={() => {
            if (!hoveredRow.invoiceLineId) return

            deleteInvoiceLine({
              variables: {
                input: {
                  id: hoveredRow.invoiceLineId,
                },
              },
            })
          }}
        />
      </Restricted>
    </div>
  )
}
