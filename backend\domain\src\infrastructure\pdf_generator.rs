// Re-export from contracts
// PDF generator interface - moved to domain for isolation
use async_trait::async_trait;
use crate::errors::Result;

#[async_trait]
pub trait PdfGenerator: Send + Sync {
    async fn generate_pdf(&self, html: &str) -> Result<Vec<u8>>;
    async fn invoice(&self, invoice_id: leviosa_domain_types::BillingInvoiceId) -> Result<Vec<u8>>;
    async fn free_text_document(&self, certificate_id: leviosa_domain_types::MedicalCertificateId) -> Result<Vec<u8>>;
    async fn medical_certificate(&self, certificate_id: leviosa_domain_types::MedicalCertificateId) -> Result<Vec<u8>>;
}
