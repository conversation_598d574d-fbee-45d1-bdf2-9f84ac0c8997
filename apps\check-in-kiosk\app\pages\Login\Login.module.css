.container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100vh;
}

.loginLeftContainer {
  background: var(--Background-Blue-5, #faf7fe);
  padding: 50px 40px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-auto-flow: row;
}

.logo {
  width: 195px;
}

.heading {
  align-self: center;
  color: var(--color-lev-blue-3);
  font-family: "LeMonde Livre Classic";
  font-size: 57px;
  font-weight: 400;
  z-index: 1;
  position: relative;
  width: fit-content;
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 200ms;
}

.signature {
  position: absolute;
  right: -24px;
  bottom: 24px;
}

.loginRightContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 110px;
  gap: 8px;
  position: relative;
}

.languageSwitcher {
  position: absolute;
  top: 33px;
  right: 26px;
}

.loginHeading {
  font-size: 40px;
  font-weight: 400;
}

.loginInformation {
  color: var(--text-primary, #000);
  text-align: center;
  font-family: "Mark Pro";
  font-size: 18px;
  font-weight: 450;
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  z-index: 2;

  display: flex;
  gap: 32px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--color-white);
}

/* fadeIn animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
