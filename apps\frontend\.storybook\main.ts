import type { StorybookConfig } from "@storybook/react-vite"
import { dirname, join } from "path"

const config: StorybookConfig = {
  stories: ["../**/*.@(mdx|stories.@(js|jsx|ts|tsx))"],
  addons: ["@chromatic-com/storybook", "@storybook/addon-docs"],

  framework: {
    name: "@storybook/react-vite",
    options: {
      builder: {
        viteConfigPath: "vite.config.js",
      },
    },
  },

  staticDirs: ["../public"],

  typescript: {
    reactDocgen: "react-docgen-typescript",
  },
}

export default config

// To customize your Vite configuration you can use the viteFinal field.
// Check https://storybook.js.org/docs/react/builders/vite#configuration
// and https://nx.dev/recipes/storybook/custom-builder-configs

function getAbsolutePath(value: string) {
  return dirname(require.resolve(join(value, "package.json")))
}
