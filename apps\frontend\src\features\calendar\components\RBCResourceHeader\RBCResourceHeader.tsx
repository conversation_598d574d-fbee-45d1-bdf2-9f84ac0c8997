import { useTranslation } from "react-i18next"

import { Label, Text } from "ui"
import { ProviderBadgeHoverInfo } from "ui/components/ProviderBadgeMenu/ProviderBadgeHoverInfo/ProviderBadgeHoverInfo"

import { CalendarComponentMap } from "../CalendarMain/CalendarMain"
import ProviderEventsMenu from "../ProviderEventsMenu/ProviderEventsMenu"
import styles from "./RBCResourceHeader.module.css"

const RBCResourceHeader: CalendarComponentMap["resourceHeader"] = ({
  resource,
}) => {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })

  const provider = resource.provider
  const date = resource.date

  return (
    <div className={styles.wrap}>
      <div className={styles.providerWrap}>
        <ProviderBadgeHoverInfo className={styles.badge} {...provider} />

        <Text size="small" weight="bold">
          {provider.name}
        </Text>
        <Label size="small" secondary>
          {tEnum(provider.specialty)}
        </Label>
      </div>
      <ProviderEventsMenu
        providerId={provider.id}
        date={date}
        className={styles.menuButton}
      />
    </div>
  )
}

export default RBCResourceHeader
