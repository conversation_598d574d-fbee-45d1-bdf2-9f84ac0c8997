import { PopoverProvider, PopoverDisclosure } from "@ariakit/react"
import { useEffect, useRef } from "react"
import { View } from "react-big-calendar"
import { generatePath, useMatch, useParams } from "react-router-dom"

import { usePopoverStore } from "components/Ariakit"
import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import { RouteStrings } from "routes/RouteStrings"
import isElementVisible from "utils/isElementVisible"

import { EventInstance } from "../EventInstance/EventInstance"

type EventWrapperEventInstanceProps = {
  children: React.ReactNode
  event: CalendarEvent<"eventInstance">
  eventProviderId?: string
}

export const EventWrapperEventInstance = ({
  children,
  event,
}: EventWrapperEventInstanceProps) => {
  const navigateCalendar = useNavigateCalendar()

  const eventId = event.resource.id
  const {
    eventId: routeEventId,
    view,
    providerId: routeProviderId,
  } = useParams()
  const currentView: View = (view ?? "week") as View

  const currentEventPath = generatePath(
    RouteStrings.calendarViewEventInstance,
    {
      view: currentView,
      eventId,
      providerId: routeProviderId ? event.resourceId : undefined,
    }
  )
  const match = useMatch(currentEventPath)

  const store = usePopoverStore({
    placement: currentView === "day" ? "bottom" : "right-start",
    setOpen: (open) => {
      if (!open) {
        navigateCalendar(RouteStrings.calendar, { replace: true })
      }
      // opening is handled by handleSelectEvent in CalendarMain
    },
    open: !!match,
  })

  const ref = useRef<HTMLButtonElement>(null)
  const anchor = store.useState().anchorElement
  const isPopoverOpen = store.useState().open

  useEffect(() => {
    if (ref.current?.firstChild) {
      //Sets a reference to a DOM element which is positioning popover relative to the rbcEvent.
      const rbcEvent = ref.current.querySelector(".rbc-event") as HTMLDivElement
      store.setAnchorElement(rbcEvent)
    }
  }, [ref.current, store.setAnchorElement])

  useEffect(() => {
    // Scroll to the event if it is not visible
    if (!anchor) return
    const isVisible = isElementVisible(anchor)
    if (routeEventId === eventId && !isVisible && isPopoverOpen) {
      anchor.scrollIntoView({ behavior: "auto", block: "center" })
    }
  }, [anchor, match])

  return (
    <PopoverProvider store={store}>
      <PopoverDisclosure
        store={{ ...store, setAnchorElement: () => undefined }}
        data-event-id={eventId}
        ref={ref}
        render={<div />}
      >
        {children}
      </PopoverDisclosure>
      {store.getState().open && <EventInstance store={store} event={event} />}
    </PopoverProvider>
  )
}
