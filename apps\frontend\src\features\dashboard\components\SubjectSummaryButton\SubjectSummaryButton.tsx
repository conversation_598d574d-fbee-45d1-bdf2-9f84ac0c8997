import {
  Popover,
  PopoverDisclosure,
  PopoverProvider,
  usePopoverStore,
} from "@ariakit/react"
import { Ref, useEffect } from "react"
import { useLocation } from "react-router-dom"

import Icon from "components/Icon/Icon"
import SubjectSummary from "features/dashboard/components/SubjectSummary/SubjectSummary"
import { Button } from "ui"

import { SubjectBasicInfoFragmentFragment } from "generated/graphql"

import styles from "./SubjectSummaryButton.module.css"

export type SubjectSummaryButtonProps = SubjectBasicInfoFragmentFragment & {
  hasCriticalCodes: boolean
}

export default function SubjectSummaryButton({
  hasCriticalCodes,
  ...subject
}: SubjectSummaryButtonProps) {
  const store = usePopoverStore()
  const location = useLocation()

  useEffect(() => {
    if (store.getState().open) store.hide()
  }, [location.pathname, store])

  return (
    <PopoverProvider store={store}>
      <PopoverDisclosure
        render={(props) => (
          <Button
            aria-label="Show subject summary"
            size="large"
            className={styles.subjectSummaryButton}
            {...props}
            icon={<Icon name="id-card-line" />}
            status={hasCriticalCodes ? "error" : undefined}
            ref={props.ref as Ref<HTMLButtonElement>}
          />
        )}
      />

      <Popover
        gutter={12}
        unmountOnHide
        hideOnEscape={(e) =>
          // if active element is a textarea then don't close the popover
          !(e.target instanceof HTMLElement && e.target.tagName === "TEXTAREA")
        }
        hideOnInteractOutside
        portal
      >
        <SubjectSummary {...subject} onRequestClose={() => store.hide()} />
      </Popover>
    </PopoverProvider>
  )
}
