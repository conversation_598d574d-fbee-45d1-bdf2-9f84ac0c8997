import { useTranslation } from "react-i18next"

import { Button, notification } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"

import { usePublishAvailabilityScheduleMutation } from "generated/graphql"

type PublishAvailabilityScheduleDialogProps = {
  scheduleId: string
  isOpen: boolean
  onClose: () => void
}

const PublishAvailabilityScheduleDialog = ({
  scheduleId,
  isOpen,
  onClose,
}: PublishAvailabilityScheduleDialogProps) => {
  const { t } = useTranslation()
  const [publishSchedule] = usePublishAvailabilityScheduleMutation()
  return (
    <Dialog
      title={t("Are you sure you want to publish this schedule?")}
      isOpen={isOpen}
      onClose={onClose}
      actions={
        <>
          <Button onClick={onClose}>{t("Cancel")}</Button>
          <Button
            onClick={() => {
              publishSchedule({
                variables: {
                  input: {
                    id: scheduleId,
                  },
                },
                onCompleted: () => {
                  onClose()
                },
                onError: (e) => {
                  onClose()
                  notification.create({
                    status: "error",
                    message: e.message,
                  })
                },
              })
            }}
            variant="filled"
          >
            {t("Publish")}
          </Button>
        </>
      }
    >
      {t("After publishing the availability schedule, no changes can be made")}
    </Dialog>
  )
}

export default PublishAvailabilityScheduleDialog
