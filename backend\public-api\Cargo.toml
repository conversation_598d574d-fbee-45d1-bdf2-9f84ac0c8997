[package]
name = "public-api"
version = "0.1.0"
edition.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
public-graphql = { path = "../public-graphql" }
leviosa-domain-contracts = { path = "../domain-contracts" }
async-graphql.workspace = true
async-graphql-axum.workspace = true
axum.workspace = true
serde.workspace = true
serde_json.workspace = true
tokio.workspace = true
tower-http.workspace = true
getset.workspace = true
dotenv.workspace = true
envy.workspace = true
chrono.workspace = true
uuid.workspace = true
rand.workspace = true
sea-orm.workspace = true
tracing.workspace = true

[dev-dependencies]
insta.workspace = true
graphql_client.workspace = true
tokio-shared-rt.workspace = true
migration = { path = "../migration" }
leviosa-macros = { path = "../macros" }
leviosa-testing = { path = "../testing" }
sqlx.workspace = true