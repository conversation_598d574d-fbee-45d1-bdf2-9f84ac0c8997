import { useNavigate } from "@remix-run/react"

import { useTimeout } from "@leviosa/utils"

import ErrorMessage from "app/components/ErrorMessage/ErrorMessage"

import styles from "./Error.module.css"

type ErrorProps = {
  message?: string
}

const Error = ({ message }: ErrorProps) => {
  const navigate = useNavigate()

  // Navigate back to checkin / login screen after 20 seconds
  useTimeout(() => {
    navigate(-1)
  }, 20000)

  return (
    <div className={styles.container}>
      <ErrorMessage message={message} />
    </div>
  )
}

export default Error
