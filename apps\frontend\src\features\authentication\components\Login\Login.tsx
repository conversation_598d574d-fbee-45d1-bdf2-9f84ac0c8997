import { lazy, useEffect } from "react"
import { useNavigate } from "react-router-dom"

import { useAuth } from "features/authentication/AuthProvider"

import {
  AuthenticationMethod,
  useGetAuthenticationMethodQuery,
} from "generated/graphql"

const UserPassLogin = lazy(() => import("../UserPassLogin/UserPassLogin"))
const ElectronicIdLogin = lazy(
  () => import("../ElectronicIdLogin/ElectronicIdLogin")
)

export default function Login() {
  const { data, loading } = useGetAuthenticationMethodQuery()
  const { isAuthorized } = useAuth()
  const navigate = useNavigate()

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthorized) {
      navigate("/")
    }
  }, [isAuthorized, navigate])

  if (loading) {
    return <div style={{ height: 300 }} data-testid="login-loading" />
  }
  if (!data) {
    return null
  }

  const {
    authenticationMethod: { method, session },
  } = data

  const authMethod = session ? session.sessionAuthenticationMethod : method

  switch (authMethod) {
    case AuthenticationMethod.Password:
      return <UserPassLogin session={session} />
    case AuthenticationMethod.ElectronicId:
      return <ElectronicIdLogin session={session} />
    default:
      return null
  }
}
