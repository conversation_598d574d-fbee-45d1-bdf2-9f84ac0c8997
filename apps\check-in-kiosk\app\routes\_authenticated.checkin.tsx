import { ActionFunctionArgs, redirect } from "@remix-run/node"
import { useActionData, useNavigation } from "@remix-run/react"
import kennitala from "kennitala"
import { useEffect, useState } from "react"

import CheckIn from "app/pages/CheckIn/CheckIn"
import {
  destroySession,
  getSession,
  getCommitSessionHeaders,
} from "app/sessions"
import { fetchAPI } from "app/utils/api"

export const action = async ({ request }: ActionFunctionArgs) => {
  const session = await getSession(request.headers.get("Cookie"))
  const requestData = await request.formData()
  const personaIdInput = requestData.get("input") as string

  const isValidPersonaId = kennitala.isValid(personaIdInput)
  if (!isValidPersonaId) {
    return new Response("invalidIdNumber", { status: 400 })
  }

  try {
    const body = {
      query: `
        mutation KioskCheckIn($input: KioskCheckInInput!) {
          kioskCheckIn(input: $input) {
            fromTime
            subjectName
            providerNames
            locationLabel
          }
        }
      `,
      variables: {
        input: {
          personaId: personaIdInput,
        },
      },
    }

    const accessToken = session.get("accessToken")

    if (!accessToken) {
      return redirect("/", {
        headers: {
          "Set-Cookie": await destroySession(session),
        },
      })
    }

    const responseData = await fetchAPI("POST", body, accessToken)

    if (responseData.errors) {
      throw responseData.errors
    }

    if (!responseData.data && !responseData.errors) {
      // No error from the server, but was not able to successfully check in the subject
      throw new Error("Failed to check in")
    }

    // Store the check-in data in session
    session.set("checkInData", responseData.data.kioskCheckIn)

    const headers = await getCommitSessionHeaders(session)
    return redirect("/welcome", headers)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    if (
      e[0]?.extensions.code === "ERR_NOT_FOUND" &&
      (e[0]?.extensions.resourceKind === "EventInstance" ||
        e[0]?.extensions.resourceKind === "Subject")
    ) {
      return redirect("/no-appointment")
    }

    // Inserting the message here so we get a more user-friendly error message in sentry (otherwise it would be Unexpected Server Error)
    throw new Error(e[0]?.message || "Failed to check in")
  }
}

export default function CheckInRoute() {
  const actionData = useActionData<typeof action>()
  const navigation = useNavigation()
  const [showError, setShowError] = useState(true)

  useEffect(() => {
    if (navigation.state === "submitting") {
      setShowError(true)
    }
  }, [navigation.state])

  if (
    actionData === "invalidIdNumber" &&
    navigation.state !== "submitting" &&
    showError
  ) {
    return (
      <CheckIn
        validationError="invalidIdNumber"
        onFormUpdate={() => setShowError(false)}
      />
    )
  }

  return <CheckIn />
}
