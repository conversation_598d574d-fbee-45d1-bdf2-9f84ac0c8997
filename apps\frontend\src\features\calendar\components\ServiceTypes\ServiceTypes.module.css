.container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  align-content: start;
  align-items: baseline;
}

.serviceTypes {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.serviceType {
  width: 100%;
  display: grid;
  grid-auto-flow: column;
  align-items: center;
  grid-template-columns: 24px 1fr auto;
  grid-gap: 8px;
}

.color {
  height: 24px;
  width: 24px;
  border-radius: 8px;
  border: 1px dashed var(--color-700);
}

.settingsButton.settingsButton {
  margin-left: -3px;
  margin-top: 8px;
  justify-self: start;

  span {
    /* Move the icon in line with the color boxes */
    font-size: 16px;
    margin-left: -7px;
    margin-right: 4px;
  }
}

.noServicesPanel {
  grid-column: 1 / -1;
}
