import { useTranslation } from "react-i18next"

import useFocusOnMount from "hooks/useFocusOnMount"
import { PhoneNumberInput } from "ui"

type ElectronicIdLoginFormProps = {
  children: React.ReactNode
}
export default function ElectronicIdLoginForm({
  children,
}: ElectronicIdLoginFormProps) {
  const { t } = useTranslation()
  const firstInputRef = useFocusOnMount<HTMLInputElement>()

  return (
    <>
      <PhoneNumberInput
        id="login-electronic"
        label={t("routes:auth.phone")}
        name="phone"
        type="tel"
        ref={firstInputRef}
        minLength={7}
        maxLength={7}
        autoComplete="tel"
      />

      {children}
    </>
  )
}
