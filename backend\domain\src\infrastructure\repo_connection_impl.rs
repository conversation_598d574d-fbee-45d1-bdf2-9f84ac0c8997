//! Implementation of the repository connection interfaces from domain-contracts

use crate::infrastructure::{
    auth::AuthenticatedUser, command_impl::ITransactionManager, repo_connection::IRepoConnection,
};
use anyhow::Result;
use sea_orm::{DatabaseConnection, DatabaseTransaction};

/// Interface for repository transaction
pub trait IRepoTransaction: Send + Sync {
    type Connection;
    fn connection(&self) -> &Self::Connection;
}

/// Implementation of the repository connection
pub struct RepoConnection {
    connection: DatabaseConnection,
    user: AuthenticatedUser,
}

impl RepoConnection {
    pub fn new(connection: DatabaseConnection, user: AuthenticatedUser) -> Self {
        Self { connection, user }
    }
}

/// Implementation of the repository transaction
pub struct RepoTransaction {
    connection: DatabaseTransaction,
    user: AuthenticatedUser,
}

/// Implementation of the transaction manager
pub struct TransactionManager {
    connection: DatabaseConnection,
    user: AuthenticatedUser,
}

impl TransactionManager {
    pub fn new(connection: DatabaseConnection, user: AuthenticatedUser) -> Self {
        Self { connection, user }
    }
}

impl IRepoConnection for RepoConnection {
    fn connection(&self) -> &impl sea_orm::ConnectionTrait {
        &self.connection
    }

    fn repos<'a>(&'a self) -> Box<dyn crate::infrastructure::repos::IRepos<'a> + 'a> {
        Box::new(crate::infrastructure::repos::Repos {
            connection: &self.connection,
            user: &self.user,
        })
    }
}

impl IRepoTransaction for RepoTransaction {
    type Connection = DatabaseTransaction;

    fn connection(&self) -> &Self::Connection {
        &self.connection
    }
}

impl ITransactionManager for TransactionManager {
    type Connection = DatabaseTransaction;

    async fn execute_in_transaction<F, R>(&self, f: F) -> Result<R>
    where
        F: FnOnce(&Self::Connection) -> Result<R> + Send,
        R: Send,
    {
        // This is a simplified implementation
        // In a real implementation, you'd start a transaction and execute the function
        todo!("Implement transaction execution")
    }
}
