//! Doctor letter and referral API contract

use crate::errors::Result;
use async_trait::async_trait;
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum DoctorLetterAndReferralApiError {
    #[error("Communication error: {0}")]
    CommunicationError(String),
    #[error("Invalid response: {0}")]
    InvalidResponse(String),
    #[error("Authentication failed")]
    AuthenticationFailed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutboundDoctorsLetter {
    pub id: OutboundDoctorsLetterId,
    pub subject_id: SubjectId,
    pub provider_id: ProviderId,
    pub content: String,
    pub recipient_info: RecipientInfo,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OutboundReferral {
    pub id: OutboundReferralId,
    pub subject_id: SubjectId,
    pub provider_id: ProviderId,
    pub content: String,
    pub recipient_info: RecipientInfo,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RecipientInfo {
    pub name: String,
    pub email: Option<String>,
    pub address: Option<String>,
    pub phone: Option<String>,
}

/// Contract for doctor letter and referral API
#[async_trait]
#[mockall::automock]
pub trait DoctorLetterAndReferralApi: Send + Sync {
    /// Send an outbound doctor's letter
    async fn send_doctors_letter(
        &self,
        letter: OutboundDoctorsLetter,
        token: &str,
    ) -> std::result::Result<(), DoctorLetterAndReferralApiError>;

    /// Send an outbound referral
    async fn send_referral(
        &self,
        referral: OutboundReferral,
        token: &str,
    ) -> std::result::Result<(), DoctorLetterAndReferralApiError>;
}
