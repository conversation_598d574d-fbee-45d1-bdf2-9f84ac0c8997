import { ApolloProvider } from "@apollo/client"
import "@reykjavik/webtools/fixIcelandicLocale"
import dayjs from "dayjs"
import duration from "dayjs/plugin/duration"
import localizedFormat from "dayjs/plugin/localizedFormat"
import relativeTime from "dayjs/plugin/relativeTime"
import utc from "dayjs/plugin/utc"
import { Suspense } from "react"
import { I18nextProvider } from "react-i18next"
import { BrowserRouter } from "react-router-dom"

import ScrollToHashElement from "components/ScrollToHashElement/ScrollToHashElement"
import { AuthProvider } from "features/authentication/AuthProvider"
import AppRoutes from "routes/AppRoutes"
import { Loading } from "ui"

import { ErrorBoundary } from "../components/ErrorBoundary/ErrorBoundary"
import { i18nInstance } from "../i18n"
import { apolloClient } from "../lib/apollo/apolloClient"

// Initialise Dayjs plugins globally
dayjs.extend(utc)
dayjs.extend(duration)
dayjs.extend(relativeTime)
dayjs.extend(localizedFormat)

export const App = () => (
  <I18nextProvider i18n={i18nInstance}>
    <ErrorBoundary>
      <ApolloProvider client={apolloClient}>
        <BrowserRouter>
          <AuthProvider>
            <Suspense fallback={<Loading large />}>
              <AppRoutes />
            </Suspense>
          </AuthProvider>
          <ScrollToHashElement />
        </BrowserRouter>
      </ApolloProvider>
    </ErrorBoundary>
  </I18nextProvider>
)
