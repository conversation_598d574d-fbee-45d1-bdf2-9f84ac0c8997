import "@ariakit/react"
import { <PERSON>a, StoryObj } from "@storybook/react-vite"

import { Text } from "ui"

import { Tooltip, TooltipStatus } from "./Tooltip"
import styles from "./Tooltip.module.css"

const statusVariants: TooltipStatus[] = ["info", "success", "error", "warning"]

const meta: Meta<typeof Tooltip> = {
  title: "Ariakit/Tooltip",
  component: Tooltip,
  argTypes: {
    placement: {
      control: { type: "select" },
      options: ["top", "right", "bottom", "left"],
      description: "Tooltip placement direction",
      defaultValue: "top",
    },
    status: {
      control: { type: "select" },
      options: statusVariants,
      description: "Status variant of tooltip",
      defaultValue: "info",
    },
    tooltipContent: {
      control: "text",
      description: "Content to display in tooltip",
    },
  },
}

export default meta
type Story = StoryObj<typeof Tooltip>

export const Default: Story = {
  args: {
    tooltipContent: "Interactive tooltip with controls",
    placement: "top",
    status: "info",
    children: "Anchor",
  },
  render: (args) => <Tooltip {...args} />,
}

export const Variants: Story = {
  parameters: {
    controls: { disable: true },
  },
  render: () => (
    <div style={{ display: "flex", gap: "20px" }}>
      {statusVariants.map((status) => (
        <Tooltip key={status} tooltipContent={status} status={status}>
          <Text
            className={styles[status]}
            style={{
              color: "var(--tooltip-text)",
            }}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Text>
        </Tooltip>
      ))}
    </div>
  ),
}

export const Placements: Story = {
  parameters: {
    controls: { disable: true },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        gap: "60px",
        padding: "40px",
      }}
    >
      <Tooltip tooltipContent="Top placement" placement="top">
        Top
      </Tooltip>
      <div style={{ display: "flex", gap: "120px" }}>
        <Tooltip tooltipContent="Left placement" placement="left">
          Left
        </Tooltip>
        <Tooltip tooltipContent="Right placement" placement="right">
          Right
        </Tooltip>
      </div>
      <Tooltip tooltipContent="Bottom placement" placement="bottom">
        Bottom
      </Tooltip>
    </div>
  ),
}
