import { useTranslation } from "react-i18next"

import { usePersistedPreference } from "hooks/usePersistedPreference"
import { Button, Checkbox, TextWithIcon } from "ui"
import { Dialog, DialogProps } from "ui/components/Dialog/Dialog"

import styles from "./AttachmentButton.module.css"

const sensitiveDownloadAgreementKey = "AttachmentDontShowAgain"

type ConfirmSensitiveDownloadDialogProps = {
  fileUrl: string
} & Omit<DialogProps, "title" | "actions" | "children">

export const useSensitiveDownloadAgreement = () => {
  const [value, setValue] = usePersistedPreference<string>({
    key: sensitiveDownloadAgreementKey,
    defaultValue: "false",
  })
  const agreed = value === "true"
  const setAgreed = (v: boolean) => setValue(v ? "true" : "false")
  return [agreed, setAgreed] as const
}

export default function ConfirmSensitiveDownloadDialog({
  onClose,
  fileUrl,
  ...rest
}: ConfirmSensitiveDownloadDialogProps) {
  const { t } = useTranslation()
  const [hasAgreedSensitive, setHasAgreedSensitiveLocal] =
    useSensitiveDownloadAgreement()

  return (
    <Dialog
      title={t("Download Attachment")}
      onClose={onClose}
      actions={
        <div className={styles.actions}>
          <Button onClick={onClose}>{t("Cancel")}</Button>
          <Button as="a" href={fileUrl} download onClick={onClose}>
            {t("I understand")}
          </Button>
        </div>
      }
      {...rest}
    >
      <div className={styles.content}>
        <TextWithIcon iconName="information-line">
          {t(
            "You are now exporting potentially sensitive information. Please handle it accordingly."
          )}
        </TextWithIcon>
        <Checkbox
          label={t("Do not show again")}
          checked={hasAgreedSensitive}
          onChange={(e) => {
            setHasAgreedSensitiveLocal(e.target.checked)
          }}
        />
      </div>
    </Dialog>
  )
}
