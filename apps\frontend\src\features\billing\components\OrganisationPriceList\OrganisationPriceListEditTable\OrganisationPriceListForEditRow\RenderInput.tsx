import { Input, Textarea } from "ui"
import NumberInput from "ui/components/Input/NumberInput/NumberInput"
import { getValueWithoutThousandSeparator } from "utils/getValueWithoutThousandSeparator"

import { PriceList } from "../../OrganisationPriceList.context"
import styles from ".././OrganisationPriceListEditTable.module.css"
import { PriceListItemKeys } from "../OrganisationPriceListEditTable"
import { SelectCurrency } from "../SelectCurrency/SelectCurrency"

type RenderInputProps = {
  propertyKey: keyof PriceListItemKeys
  onBlur: () => void
  priceListItem: PriceList[0]
}

type MaxValueKeys = Pick<PriceListItemKeys, "vat" | "units" | "unitPrice">

const maxValues = {
  vat: 100,
  units: 9999,
  unitPrice: 9999999,
} satisfies MaxValueKeys

const isMaxValueKey = (
  key: keyof PriceListItemKeys
): key is keyof MaxValueKeys => key in maxValues

export const RenderInput = ({
  propertyKey,
  onBlur,
  priceListItem,
}: RenderInputProps) => {
  if (priceListItem.__typename !== "BillingCodeClinicSpecific") return null

  const isNumeric =
    propertyKey === "units" ||
    propertyKey === "unitPrice" ||
    propertyKey === "vat"

  const maxValue = isMaxValueKey(propertyKey)
    ? maxValues[propertyKey]
    : undefined

  if (propertyKey === "currency")
    return (
      <SelectCurrency
      // COMEBACK when more currencies are added
      // className={styles.selectInput}
      // defaultValue={priceListItem[key] || ""}
      // onBlur={() => {
      //   if (type !== "CREATE") setType("EDIT")
      // }}
      />
    )

  if (propertyKey === "title")
    return (
      <Textarea
        textareaProps={{
          className: `${styles.input}`,
        }}
        className={`${styles.inputWrap} ${styles.textareaWrap} `}
        data-key={propertyKey}
        name={propertyKey}
        autoComplete={"off"}
        label={propertyKey}
        hideLabel
        rows={1}
        hideMessage
        defaultValue={priceListItem[propertyKey] || ""}
        onBlur={() => {
          onBlur()
        }}
        autoGrow
      />
    )

  if (isNumeric) {
    return (
      <NumberInput
        defaultValue={priceListItem[propertyKey] || ""}
        allowLeadingZeros
        name={propertyKey}
        label=""
        autoComplete={"off"}
        decimalScale={propertyKey === "vat" ? 2 : 0}
        max={maxValue}
        min={0}
        onBlur={(e) => {
          const value = getValueWithoutThousandSeparator(e.target.value)

          if (value < 0 || value > maxValues[propertyKey]) {
            e.target.setCustomValidity(
              `Value cannot exceed ${maxValues[propertyKey]}`
            )

            return
          }

          e.target.setCustomValidity("")
          onBlur()
        }}
        inputProps={{
          className: `${styles.input + " " + styles.inputNumber}`,
        }}
        className={styles.inputWrap}
        hideMessage
        hideLabel
        data-key={propertyKey}
      />
    )
  }

  return (
    <Input
      inputProps={{
        className: styles.input,
      }}
      className={styles.inputWrap}
      data-key={propertyKey}
      name={propertyKey}
      autoComplete={"off"}
      type={"text"}
      label={propertyKey}
      hideLabel
      hideMessage
      defaultValue={priceListItem[propertyKey] || ""}
      onBlur={() => {
        onBlur()
      }}
    />
  )
}
