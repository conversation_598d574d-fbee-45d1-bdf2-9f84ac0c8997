use crate::accounts::{OrganisationId, Provider, ProviderId};
use crate::errors::{AuthorizationError, CustomError, Result};
use crate::permissions::{Permission, PermissionKey};
use crate::team::{Team, TeamId};
use getset::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Getters};
use sea_orm::DatabaseConnection;
use std::convert::TryFrom;

use super::{AccessToken, AuthScope, JwtEncoder};
use leviosa_domain_contracts::auth::ApiKeyManager;

#[derive(Clone)]
pub enum AuthData {
    Anon,
    // The authenticated user and the access token or api key used to authenticate them
    User(AuthenticatedUser, String),
}

impl AuthData {
    pub fn from_header(header: Option<&str>, jwt: &dyn JwtEncoder) -> Result<Self> {
        let header = header.unwrap_or_default();
        if !header.is_empty() {
            Self::from_access_token(header, jwt)
        } else {
            Ok(Self::Anon)
        }
    }

    pub fn from_access_token(access_token: &str, jwt: &dyn JwtEncoder) -> Result<Self> {
        let user_auth_data = jwt
            .decode_access(access_token)
            .map_err(AuthorizationError::MalformedToken)?
            .try_into()?;

        Ok(Self::User(user_auth_data, access_token.to_string()))
    }

    pub fn from_user(
        user: &AuthenticatedUser,
        jwt: &dyn JwtEncoder,
        token_lifetime_seconds: u32,
    ) -> Result<Self> {
        let token = jwt.encode_access(
            &AuthenticatedUser::new(
                user.user_id(),
                user.organisation_id(),
                vec![],
                user.permissions().clone(),
            ),
            token_lifetime_seconds,
        )?;

        Ok(AuthData::User(user.clone(), token))
    }

    pub async fn from_api_key(
        db: &DatabaseConnection,
        api_key_manager: &dyn ApiKeyManager,
        api_key: Option<&str>,
    ) -> Result<Self> {
        let api_key = api_key.unwrap_or_default();
        if !api_key.is_empty() {
            let api_key = api_key_manager
                .split(api_key)
                .map_err(|err| CustomError::new(None, format!("PrefixApiKeyError: {}", err)))?;

            let auth_user =
                Provider::get_by_api_key(db, api_key.short_token(), api_key.long_token_hash())
                    .await?;

            let permissions = Permission::provider_permissions(
                db.get_postgres_connection_pool(),
                &AuthScope::Unchecked,
                auth_user.id,
            )
            .await?;

            let sqlx_db = db.get_postgres_connection_pool();
            let team_ids: Vec<TeamId> = Team::provider_teams(sqlx_db, auth_user.id)
                .await?
                .iter()
                .map(|t| t.id())
                .collect();

            let user_auth_data: AuthenticatedUser = AuthenticatedUser {
                user_id: auth_user.id,
                organisation_id: auth_user.organisation_id,
                team_ids,
                permissions,
            };

            return Ok(Self::User(user_auth_data, api_key.api_key().to_string()));
        }

        Ok(Self::Anon)
    }

    pub fn check_permission(&self, permission: Option<PermissionKey>) -> Result<AuthenticatedUser> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(user_auth_data, _) => {
                if !permission.is_none_or(|p| user_auth_data.permissions.contains(&p)) {
                    return Err(AuthorizationError::Unauthorized.into());
                }
                Ok(user_auth_data.clone())
            }
        }
    }

    pub fn check_if_authenticated(&self) -> Result<AuthenticatedUser> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(user_auth_data, _) => Ok(user_auth_data.clone()),
        }
    }

    pub fn user(&self) -> Result<AuthenticatedUser> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(user_auth_data, _) => Ok(user_auth_data.clone()),
        }
    }

    pub fn token(&self) -> Result<String> {
        match self {
            AuthData::Anon => Err(AuthorizationError::NotAuthenticated.into()),
            AuthData::User(_, token) => Ok(token.clone()),
        }
    }
}

#[derive(Debug, Clone, Getters, CopyGetters, PartialEq)]
pub struct AuthenticatedUser {
    #[getset(get_copy = "pub")]
    pub user_id: ProviderId,
    #[getset(get_copy = "pub")]
    pub organisation_id: OrganisationId,
    #[getset(get = "pub")]
    pub team_ids: Vec<TeamId>,
    #[getset(get = "pub")]
    permissions: Vec<PermissionKey>,
}

impl AuthenticatedUser {
    pub fn new(
        user_id: ProviderId,
        organisation_id: OrganisationId,
        team_ids: Vec<TeamId>,
        permissions: Vec<PermissionKey>,
    ) -> Self {
        Self {
            user_id,
            organisation_id,
            team_ids,
            permissions,
        }
    }
}

impl TryFrom<AccessToken> for AuthenticatedUser {
    type Error = anyhow::Error;

    fn try_from(value: AccessToken) -> std::result::Result<Self, Self::Error> {
        let permissions: std::result::Result<Vec<PermissionKey>, Self::Error> = value
            .permissions
            .into_iter()
            .map(PermissionKey::try_from)
            .collect();

        Ok(Self {
            user_id: value.user_id.into(),
            organisation_id: value.organisation_id.into(),
            team_ids: value.team_ids.into_iter().map(Into::into).collect(),
            permissions: permissions?,
        })
    }
}
