use async_trait::async_trait;
use leviosa_domain_contracts::services::{ServiceCommunicator, ServiceCommunicatorError};
use reqwest::{Response, StatusCode};
use serde_json::Value;

#[derive(Debug, Clone)]
pub struct HttpServiceCommunicator {
    client: reqwest::Client,
    service_url: String,
}

impl HttpServiceCommunicator {
    pub fn new(service_url: String) -> Self {
        Self {
            client: reqwest::Client::new(),
            service_url,
        }
    }

    async fn get(&self, path: &str, token: &str) -> Result<Response, ServiceCommunicatorError> {
        let request_url = format!("{}{}", self.service_url, path);

        let res = self
            .client
            .get(request_url)
            .header("Authorization", token)
            .send()
            .await;

        Self::handle_response(path, res).await
    }

    async fn post(
        &self,
        path: &str,
        token: &str,
        body: String,
    ) -> Result<Response, ServiceCommunicatorError> {
        let request_url = format!("{}{}", self.service_url, path);
        let res = self
            .client
            .post(request_url)
            .header("Authorization", token)
            .body(body)
            .send()
            .await;

        Self::handle_response(path, res).await
    }

    async fn handle_response(
        path: &str,
        response: Result<Response, reqwest::Error>,
    ) -> Result<Response, ServiceCommunicatorError> {
        match response {
            Ok(response) => match response.status() {
                StatusCode::OK => Ok(response),
                StatusCode::NOT_FOUND => {
                    let response_text = response.text().await;
                    if let Ok(response_text) = response_text {
                        Err(ServiceCommunicatorError::NotFound(response_text))
                    } else {
                        Err(ServiceCommunicatorError::NotFound(format!(
                            "Service returned 404, path: {path}"
                        )))
                    }
                }
                code => Err(ServiceCommunicatorError::InvalidResponse(format!(
                    "Failed to get json,\nstatus:{code}\npath: {path}\nerror: {:?}",
                    response.text().await.unwrap_or_default()
                ))),
            },
            Err(e) => Err(ServiceCommunicatorError::ErrorCommunicating(format!(
                "Failed to get json,\npath: {path}\nerror: {e}"
            ))),
        }
    }

    async fn parse_json_from_response(
        response: Response,
    ) -> Result<Value, ServiceCommunicatorError> {
        match response.json().await {
            Ok(value) => Ok(value),
            Err(e) => Err(ServiceCommunicatorError::InvalidResponse(format!(
                "Failed to parse json: {e}"
            ))),
        }
    }
}

#[async_trait]
impl ServiceCommunicator for HttpServiceCommunicator {
    async fn get_bytes(
        &self,
        path: &str,
        token: &str,
    ) -> Result<Vec<u8>, ServiceCommunicatorError> {
        let response = self.get(path, token).await;

        match response {
            Ok(response) => match response.status() {
                StatusCode::OK => match response.bytes().await {
                    Ok(bytes) => Ok(bytes.to_vec()),
                    Err(e) => Err(ServiceCommunicatorError::InvalidResponse(format!(
                        "Failed read bytes: {e}"
                    ))),
                },
                StatusCode::NOT_FOUND => {
                    let response_text = response.text().await;
                    if let Ok(response_text) = response_text {
                        Err(ServiceCommunicatorError::NotFound(response_text))
                    } else {
                        Err(ServiceCommunicatorError::NotFound(
                            "Service returned 404".to_string(),
                        ))
                    }
                }
                _ => Err(ServiceCommunicatorError::InvalidResponse(format!(
                    "Failed to get bytes: {:?}",
                    response.status()
                ))),
            },
            Err(e) => Err(ServiceCommunicatorError::ErrorCommunicating(format!(
                "Failed to get bytes: {e}"
            ))),
        }
    }
    async fn get_json(&self, path: &str, token: &str) -> Result<Value, ServiceCommunicatorError> {
        let response = self.get(path, token).await?;
        Self::parse_json_from_response(response).await
    }

    async fn post_json(
        &self,
        path: &str,
        token: &str,
        body: Value,
    ) -> Result<Value, ServiceCommunicatorError> {
        let body = serde_json::to_string(&body);
        match body {
            Ok(body) => {
                let response = self.post(path, token, body).await?;
                Self::parse_json_from_response(response).await
            }
            Err(e) => Err(ServiceCommunicatorError::InvalidInput(format!(
                "Failed to serialize json: {e}"
            ))),
        }
    }
}
