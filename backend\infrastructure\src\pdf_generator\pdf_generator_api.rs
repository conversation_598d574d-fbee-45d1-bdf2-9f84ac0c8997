use async_trait::async_trait;
use leviosa_domain_types::{InvoiceId, MedicalCertificateId};
use leviosa_domain::{
    errors::{CustomError, Error},
    // services::{PdfGenerator, ServiceCommunicator},  // services module not available
    pdf_generator::PdfGenerator,
    service_communicator::ServiceCommunicator,
};

#[derive(Debug, Clone)]
pub struct PdfGeneratorApi<T: ServiceCommunicator> {
    communicator: T,
}

impl<T: ServiceCommunicator> PdfGeneratorApi<T> {
    pub fn new(communicator: T) -> Self {
        Self { communicator }
    }
}
#[async_trait]
impl<T: ServiceCommunicator> PdfGenerator for PdfGeneratorApi<T> {
    async fn medical_certificate(
        &self,
        id: MedicalCertificateId,
        token: &str,
    ) -> Result<Vec<u8>, Error> {
        let path = format!("/generate/medical-certificate/pdf/{id}");

        let result = self.communicator.get_bytes(path.as_str(), token).await;

        return match result {
            Ok(bytes) => Ok(bytes),
            Err(e) => {
                Err(CustomError::new(None, format!("Failed to get certificate PDF: {e:?}")).into())
            }
        };
    }

    async fn free_text_document(
        &self,
        id: MedicalCertificateId,
        token: &str,
    ) -> Result<Vec<u8>, Error> {
        let path = format!("/generate/free-text/pdf/{id}");

        let result = self.communicator.get_bytes(path.as_str(), token).await;

        return match result {
            Ok(bytes) => Ok(bytes),
            Err(e) => Err(CustomError::new(
                None,
                format!("Failed to get free text document PDF: {e:?}"),
            )
            .into()),
        };
    }

    async fn invoice(&self, id: InvoiceId, token: &str) -> Result<Vec<u8>, Error> {
        let path = format!("/generate/invoice/pdf/{id}");

        let result = self.communicator.get_bytes(path.as_str(), token).await;

        return match result {
            Ok(bytes) => Ok(bytes),
            Err(e) => {
                Err(CustomError::new(None, format!("Failed to get invoice PDF: {e:?}")).into())
            }
        };
    }
}
