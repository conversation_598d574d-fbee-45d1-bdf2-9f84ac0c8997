import format from "date-fns/format"
import getDay from "date-fns/getDay"
import enUS from "date-fns/locale/en-US"
import parse from "date-fns/parse"
import startOfWeek from "date-fns/startOfWeek"
import { dateFnsLocalizer } from "react-big-calendar"

const locales = {
  "en-US": enUS,
}

export const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek: (date: number | Date) => startOfWeek(date, { weekStartsOn: 1 }),
  getDay,
  locales,
})
