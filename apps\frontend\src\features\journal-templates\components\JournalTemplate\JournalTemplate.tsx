import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"

import { MenuButton } from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import { FilterableMenu } from "components/FilterableMenu/FilterableMenu"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import usePermissions from "features/authentication/hooks/usePermissions"
import ClinicalCodingForm from "features/subject-journal/components/ClinicalCodingForm/ClinicalCodingForm"
import DrugPrescriptionBlock from "features/subject-journal/components/DrugPrescription/DrugPrescriptionBlock"
import { Button, Heading } from "ui"
import ClinicalCodingCard from "ui/components/ClinicalCodingCard/ClinicalCodingCard"
import { Dialog } from "ui/components/Dialog/Dialog"

import {
  ClinicalCodingCriticalType,
  ClinicalCodingType,
  JournalTemplateFragmentFragment,
  JournalTemplateStatus,
  JournalTemplateType,
  LanguageId,
  Permission<PERSON>ey,
  TemplateAccessScope,
  useAddClinicalCodingToJournalEntryTemplateMutation,
  useAddDrugPrescriptionToJournalEntryTemplateMutation,
  useDeleteJournalBlockTemplateMutation,
  useGetJournalTemplateQuery,
} from "generated/graphql"

import { DocumentTemplate } from "./DocumentTemplate"
import { ExtendedDocumentTemplate } from "./ExtendedDocumentTemplate"
import { InlineTemplate } from "./InlineTemplate"
import styles from "./JournalTemplate.module.css"
import { NoTemplateAvailable } from "./NoTemplateAvailable"
import { PublishTemplateButton } from "./PublishTemplateButton/PublishTemplateButton"

const orderedCodingTypes = [
  ClinicalCodingType.Diagnosis,
  ClinicalCodingType.Operation,
  ClinicalCodingType.Allergy,
  ClinicalCodingType.Behavior,
  ClinicalCodingType.TreatmentRestriction,
]

type JournalTemplateProps = {
  templateId: string
}

const TemplateContent = ({
  template,
}: {
  template: JournalTemplateFragmentFragment
}) => {
  switch (template.templateType) {
    case JournalTemplateType.InlineTemplate:
      return <InlineTemplate template={template} />
    case JournalTemplateType.DocumentTemplate:
      return <DocumentTemplate template={template} />
    case JournalTemplateType.ExtendedDocumentTemplate:
      return <ExtendedDocumentTemplate template={template} />
    default:
      return null
  }
}

export const JournalTemplate = ({ templateId }: JournalTemplateProps) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "journalTemplates",
  })
  const { hasPermission } = usePermissions()

  const [showClinicalCodingForm, setShowClinicalCodingForm] =
    useState<ClinicalCodingType | null>(null)
  const [blockToDelete, setBlockToDelete] = useState<string | null>(null)
  const { globalData } = useGlobalState()

  const actorId = globalData.actor.id

  const menuStore = useMenuStore({
    placement: "right-start",
  })
  const menuOptions = orderedCodingTypes.map((coding) => {
    return {
      label: tEnum(`ClinicalCoding_CodingType.${coding}`),
      value: tEnum(`ClinicalCoding_CodingType.${coding}`),
      onSelect: () => {
        setShowClinicalCodingForm(coding)
      },
    }
  })

  // Reset showClinicalCodingForm when templateId changes
  useEffect(() => {
    setShowClinicalCodingForm(null)
  }, [templateId])

  const [deleteJournalBlockTemplate] = useDeleteJournalBlockTemplateMutation()

  const [addDrugPrescriptionToJournalEntryTemplate] =
    useAddDrugPrescriptionToJournalEntryTemplateMutation()

  const [addClinicalCodingToJournalEntryTemplate] =
    useAddClinicalCodingToJournalEntryTemplateMutation()

  const { data: journalTemplateData, loading } = useGetJournalTemplateQuery({
    variables: {
      journalTemplateId: templateId,
    },
  })

  menuOptions.unshift({
    label: tRoutes("drugPrescription"),
    value: tRoutes("drugPrescription"),
    onSelect: () => {
      addDrugPrescriptionToJournalEntryTemplate({
        variables: {
          input: {
            journalTemplateId: templateId,
          },
        },
      })
    },
  })

  const handleDelete = (id: string) => {
    deleteJournalBlockTemplate({
      variables: {
        blockTemplateId: id,
      },
      onCompleted: () => {
        setBlockToDelete(null)
      },
    })
  }

  const template = journalTemplateData?.journalTemplate

  if (loading) {
    return <div className={styles.wrap} />
  }

  if (!template || template.status === JournalTemplateStatus.Archived) {
    return <NoTemplateAvailable />
  }

  const isInlineTemplate =
    template.templateType === JournalTemplateType.InlineTemplate

  const isDocumentTemplate =
    template.templateType === JournalTemplateType.DocumentTemplate

  const isExtendedDocumentTemplate =
    template.templateType === JournalTemplateType.ExtendedDocumentTemplate

  const isCreatedByActor = template.createdBy.id === actorId

  const canEditInlineTemplate =
    hasPermission(PermissionKey.SubjectJournalTemplateEdit) && isCreatedByActor

  const canEditDocumentTemplate = hasPermission(
    PermissionKey.SubjectJournalDocumentTemplateEdit
  )

  const canEditExtendedDocumentTemplate =
    template.createdBy.id === actorId ||
    template.accessScope === TemplateAccessScope.PublicWrite

  const canEditTemplate =
    (isInlineTemplate && canEditInlineTemplate) ||
    (isDocumentTemplate && canEditDocumentTemplate) ||
    (isExtendedDocumentTemplate && canEditExtendedDocumentTemplate)

  const showSupplementsWrapper = canEditTemplate || !!template.blocks.length

  return (
    <div className={styles.wrap} key={templateId}>
      <TemplateContent template={template} />
      {showSupplementsWrapper && (
        <div className={styles.supplementsWrapper}>
          <Heading size="xsmall">{t("supplementaryMaterials")}</Heading>
          {showClinicalCodingForm && (
            <div className={styles.block}>
              <ClinicalCodingForm
                referenceType={showClinicalCodingForm}
                onSelectOption={(option, _, critical, description) => {
                  addClinicalCodingToJournalEntryTemplate({
                    variables: {
                      input: {
                        journalTemplateId: templateId,
                        code: {
                          codeId: option,
                          codingType: showClinicalCodingForm,
                        },
                        criticalType: critical
                          ? ClinicalCodingCriticalType.Normal
                          : ClinicalCodingCriticalType.NotCritical,
                        description,
                      },
                    },
                    onCompleted: () => {
                      setShowClinicalCodingForm(null)
                    },
                  })
                }}
                onCancel={() => setShowClinicalCodingForm(null)}
              />
            </div>
          )}

          <div className={styles.supplements}>
            {template.blocks.map((block) => {
              switch (block.__typename) {
                case "DrugPrescriptionTemplate":
                  return (
                    <DrugPrescriptionBlock
                      key={block.id}
                      drugPrescription={block}
                      canEdit={canEditTemplate}
                      languageId={LanguageId.Is}
                      journalTemplateId={templateId}
                      onDelete={
                        // If the block is not extended from another template, it can be deleted
                        canEditTemplate && !block.extendedFromBlockTemplate?.id
                          ? () => setBlockToDelete(block.id)
                          : undefined
                      }
                    />
                  )
                case "ClinicalCodingTemplate":
                  return (
                    <ClinicalCodingCard
                      closeableCode={false}
                      key={block.id}
                      {...block}
                      notes={[]}
                      onDelete={
                        // If the block is not extended from another template, it can be deleted
                        canEditTemplate && !block.extendedFromBlockTemplate?.id
                          ? () => setBlockToDelete(block.id)
                          : undefined
                      }
                      showDescription={false}
                    />
                  )
                default:
                  return null
              }
            })}
            {canEditTemplate && (
              <div className={styles.addSupplements}>
                <FilterableMenu
                  menuStore={menuStore}
                  options={menuOptions}
                  onSelect={(option: {
                    label: string
                    value: string
                    onSelect?: () => void
                  }) => {
                    menuStore.setOpen(false)
                    option.onSelect?.()
                  }}
                  menuButton={
                    <MenuButton icon={<Icon name="add-line" />}>
                      {tRoutes("addSupplements")}
                    </MenuButton>
                  }
                />
              </div>
            )}
          </div>
        </div>
      )}
      {template.status === JournalTemplateStatus.Draft && canEditTemplate && (
        <footer className={styles.footer}>
          <PublishTemplateButton
            id={templateId}
            templateType={template.templateType}
            canEdit={canEditTemplate}
          />
        </footer>
      )}

      <Dialog
        isOpen={blockToDelete !== null}
        onClose={() => setBlockToDelete(null)}
        title={tRoutes("deleteConfirmation")}
        actions={
          <>
            <Button
              onClick={() => {
                setBlockToDelete(null)
              }}
            >
              {tRoutes("cancel")}
            </Button>
            <Button
              status="error"
              onClick={() => blockToDelete && handleDelete(blockToDelete)}
            >
              {tRoutes("delete")}
            </Button>
          </>
        }
      ></Dialog>
    </div>
  )
}
