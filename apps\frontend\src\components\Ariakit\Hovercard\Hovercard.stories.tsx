import "@ariakit/react"
import { HovercardProvider } from "@ariakit/react"
import { Meta } from "@storybook/react-vite"

import { Hovercard, HovercardAnchor, useHovercardStore } from "./Hovercard"

export default {
  title: "Ariakit/Hovercard",
  component: Hovercard,
} as Meta

export const HovercardExample = (args) => {
  const hovercardStore = useHovercardStore()

  return (
    <HovercardProvider>
      <HovercardAnchor {...args} store={hovercardStore}>
        Hover me
      </HovercardAnchor>
      <Hovercard store={hovercardStore}>
        Some information to show inside the hovercard
      </Hovercard>
    </HovercardProvider>
  )
}
