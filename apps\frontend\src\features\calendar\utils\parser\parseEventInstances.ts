import { differenceInHours } from "date-fns"

import { isTypename } from "utils/isTypename"

import { EventInstancesQuery } from "generated/graphql"

import { CalendarEvent } from "./calendarEvent"

export const parseEventInstances = (
  eventInstances?: EventInstancesQuery["eventInstances"],
  selectedEventIds?: string[]
): CalendarEvent<"eventInstance">[] => {
  if (!eventInstances) return []

  return eventInstances.map((eventInstance) => {
    const startDate = new Date(eventInstance.fromDate)
    const endDate = new Date(eventInstance.toDate)

    const timeDifferenceInHours = differenceInHours(endDate, startDate)
    const allDay = timeDifferenceInHours >= 24

    const subjects = eventInstance.participants
      .filter(isTypename("ParticipantSubject"))
      .map((p) => p.subject.name)

    return {
      start: startDate,
      end: endDate,
      title: eventInstance.title,
      resource: {
        ...eventInstance,
        type: "eventInstance" as const,
      },
      allDay: allDay,
      selectedEventIds,
      subjects,
    }
  })
}

export default parseEventInstances
