import c from "classnames"
import { ReactNode } from "react"

import { Heading, Text } from "ui"

import styles from "./ErrorPage.module.css"

export interface ErrorPageProps {
  illustration: ReactNode
  heading: string
  message: string
  suggestions?: ReactNode[]
  subMessage?: string
}

export default function ErrorPage({
  illustration,
  heading,
  message,
  suggestions = [],
  subMessage,
}: ErrorPageProps) {
  return (
    <div className={styles.container}>
      {illustration}
      <div className={styles.content}>
        <Heading size="display" className={styles.heading}>
          {heading}
        </Heading>
        <Text size="large">{message}</Text>
        {suggestions.length > 0 && (
          <ul className={c(styles.bulletList, "with-bullets")}>
            {suggestions.map((suggestion, index) => (
              <Text as="li" size="large" key={index}>
                {suggestion}
              </Text>
            ))}
          </ul>
        )}
        {subMessage && <Text size="large">{subMessage}</Text>}
      </div>
    </div>
  )
}
