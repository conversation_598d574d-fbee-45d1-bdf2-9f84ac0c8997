import { isWithinInterval } from "date-fns"
import { useTranslation } from "react-i18next"
import {
  generatePath,
  useNavigate,
  useLocation,
  matchPath,
} from "react-router-dom"
import { v4 as uuid } from "uuid"

import Icon from "components/Icon/Icon"
import usePermissions from "features/authentication/hooks/usePermissions"
import { PrivateRoutes, RouteStrings } from "routes/RouteStrings"
import { Button, ButtonOwnProps, notification } from "ui"

import {
  EncounterCreateInput,
  EncounterStatus,
  namedOperations,
  PermissionKey,
  useCreateEncounterMutation,
  useSetSubjectJournalFocusedItemMutation,
} from "generated/graphql"

type OpenInJournalProps = {
  start: Date
  end: Date
  subjectIds: string[]
  providerIds: string[]
  teamId?: string
  eventId?: string
  className?: string
  encounterId?: string
  variant?: ButtonOwnProps["variant"]
  interventionPeriodId?: string
  encounterReason: string
  size?: "default" | "large" | "small"
  isEventCancelled?: boolean
}

const OpenInJournal = ({
  start,
  end,
  subjectIds,
  providerIds,
  teamId,
  eventId,
  encounterReason,
  encounterId,
  interventionPeriodId,
  className,
  variant,
  size,
  isEventCancelled,
}: OpenInJournalProps) => {
  const { hasPermission } = usePermissions()
  const [setSubjectJournalFocusedItem] =
    useSetSubjectJournalFocusedItemMutation()
  const { t } = useTranslation()

  const navigate = useNavigate()
  const { pathname } = useLocation()
  const isInSubjectJournal = matchPath(
    { path: PrivateRoutes.subjectJournal },
    pathname
  )

  const navigateToSubjectJournal = (subjectId: string) => {
    const subjectJournalPath = generatePath(RouteStrings.subjectJournal, {
      subjectId,
    })

    navigate(subjectJournalPath)
  }

  const [createEncounter] = useCreateEncounterMutation({
    onError: (error) => {
      notification.create({
        status: "error",
        message: error.message,
      })
    },
    refetchQueries: isInSubjectJournal
      ? [namedOperations.Query.GetSubjectJournal]
      : [
          namedOperations.Query.GetSubjectSummaryEncounters,
          namedOperations.Query.DashboardStatusCountHack,
        ],
  })

  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  if (!canViewSubjectJournal) {
    return null
  }

  const hasSingleSubject = subjectIds?.length === 1

  if (!hasSingleSubject || !teamId) {
    return null
  }

  if (
    !encounterId &&
    (!hasPermission(PermissionKey.SubjectJournalEncounterEdit) ||
      !hasPermission(PermissionKey.SubjectJournalEncounterView))
  ) {
    return null
  }

  // Only show open journal button for cancelled events if there is an encounter
  if (isEventCancelled && !encounterId) {
    return null
  }

  const subjectId = subjectIds[0] || ""

  const isEventOnGoing = isWithinInterval(new Date(), {
    start: new Date(start),
    end: new Date(end),
  })

  const encounterInput: EncounterCreateInput = {
    fromDate: start,
    reason: encounterReason,
    priority: null,
    note: "",
    responsibleProviders: providerIds,
    subjectId,
    primaryTeamId: teamId,
    status: isEventOnGoing
      ? EncounterStatus.InProgress
      : EncounterStatus.Planned,
    interventionPeriodId: interventionPeriodId || null,
    eventInstanceId: eventId,
  }

  const buttonText = isInSubjectJournal
    ? encounterId
      ? t("Open encounter")
      : t("Create encounter")
    : t("Open journal")

  return (
    <Button
      className={className}
      variant={variant}
      size={size}
      iconEnd={<Icon name="arrow-right-s-line" />}
      onClick={async (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation()
        let navigateToEncounterId = encounterId

        if (!navigateToEncounterId) {
          // Encounter does not exist, create a new one
          const newEncounterId = uuid()
          await createEncounter({
            variables: {
              createEncounterId: newEncounterId,
              input: {
                ...encounterInput,
              },
            },
          })
          navigateToEncounterId = newEncounterId
        }

        await setSubjectJournalFocusedItem({
          variables: {
            journalEntryBlockId: null,
            encounterId: navigateToEncounterId,
          },
        })

        if (!isInSubjectJournal) {
          navigateToSubjectJournal(subjectId)
        }
      }}
    >
      {buttonText}
    </Button>
  )
}

export default OpenInJournal
