import { GetParticipantsQuery, InputMaybe } from "generated/graphql"

export default (
  subjects: GetParticipantsQuery["subjects"] | undefined,
  subjectsIds: InputMaybe<readonly string[]>
) => {
  if (subjectsIds?.length !== 1 || subjects === undefined) return ""

  const participant = subjects.find(({ id }) => id === subjectsIds[0])

  return `${participant?.name} (${participant?.personaId.slice(
    0,
    6
  )}-${participant?.personaId.slice(6)})`
}
