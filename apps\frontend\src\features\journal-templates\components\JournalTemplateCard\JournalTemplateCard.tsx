import c from "classnames"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, NavLink, useNavigate } from "react-router-dom"

import { Menu, MenuButton, MenuProvider } from "components/Ariakit"
import { MenuItem } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { useMenuStore } from "components/Ariakit/hooks"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import usePermissions from "features/authentication/hooks/usePermissions"
import { RouteStrings } from "routes/RouteStrings"
import { Button, ButtonText, Heading, Label, notification, Tag, Text } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateStatus,
  JournalTemplateType,
  namedOperations,
  PermissionKey,
  TemplateAccessScope,
  useArchiveJournalTemplateMutation,
} from "generated/graphql"

import styles from "./JournalTemplateCard.module.css"

const getAccessScopeIcon = (
  templateType: JournalTemplateType,
  scope: TemplateAccessScope
) => {
  if (templateType !== JournalTemplateType.ExtendedDocumentTemplate) return null

  switch (scope) {
    case TemplateAccessScope.PublicRead:
      return "eye-line"
    case TemplateAccessScope.PublicWrite:
      return "edit-line"
    case TemplateAccessScope.Private:
      return "lock-line"
    default:
      return null
  }
}

type JournalTemplateCardProps = {
  template: JournalTemplateFragmentFragment
}

export const JournalTemplateCard = ({ template }: JournalTemplateCardProps) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "journalTemplates",
  })
  const menuStore = useMenuStore()
  const { hasPermission } = usePermissions()

  const { globalData } = useGlobalState()

  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null)

  const actor = globalData.actor

  const specialtyText = tEnum(
    `ProviderSpecialty.${template.createdBy.specialty}`
  )

  const navigate = useNavigate()

  const [archiveJournalTemplate] = useArchiveJournalTemplateMutation({
    onCompleted: () => {
      navigate(
        generatePath(RouteStrings.journalTemplates, {
          templateType: template.templateType?.toLowerCase(),
        })
      )
    },
    onError: () => {
      notification.create({
        status: "error",
        message: tRoutes("deleteTemplateError"),
      })
    },
    refetchQueries: [namedOperations.Query.GetJournalTemplates],
  })

  const canArchive =
    template.createdBy.id === actor.id &&
    hasPermission(PermissionKey.SubjectJournalTemplateEdit)

  const handleDeleteTemplate = (id: string) => {
    setTemplateToDelete(null)
    archiveJournalTemplate({
      variables: {
        id,
      },
    })
  }

  const isDocumentTemplate =
    template.templateType === JournalTemplateType.DocumentTemplate
  const isExtendedDocumentTemplate =
    template.templateType === JournalTemplateType.ExtendedDocumentTemplate
  const isInlineTemplate =
    template.templateType === JournalTemplateType.InlineTemplate

  // Determine conditions for showing status based on template type and permissions
  const canEditDocumentTemplate = hasPermission(
    PermissionKey.SubjectJournalDocumentTemplateEdit
  )
  const isCurrentUserCreator = actor.id === template.createdBy.id

  const showStatusTag =
    (isDocumentTemplate && canEditDocumentTemplate) || // Show if a document template and the user can edit it
    (isExtendedDocumentTemplate && isCurrentUserCreator) || // Show if extended template created by current user
    isInlineTemplate // Always show for inline templates

  const accessScopeIcon = getAccessScopeIcon(
    template.templateType,
    template.accessScope
  )

  return (
    <NavLink
      className={styles.template}
      data-testid="journal-template-card"
      to={generatePath(RouteStrings.journalTemplates, {
        templateType: template.templateType?.toLowerCase(),
        templateId: template.id,
      })}
    >
      <div className={styles.templateInfo}>
        {template.templateType !== JournalTemplateType.InlineTemplate && (
          <div className={styles.creator}>
            <Label>{template.createdBy.name} •</Label>
            <Label secondary>{specialtyText}</Label>
          </div>
        )}
        <Heading className={styles.name}>{template.name}</Heading>
        <Text size="small" className={styles.description}>
          {template.description}
        </Text>
      </div>

      <MenuProvider store={menuStore}>
        <MenuButton
          className={c(styles.menuButton, {
            [styles.hidden]: !canArchive,
          })}
          variant="clear"
          icon={<Icon name="more-line" />}
          onClick={(e: React.MouseEvent) => {
            e.preventDefault()
            menuStore.toggle()
          }}
        />
        <Menu>
          <MenuItem
            className={styles.menuItemDelete}
            onClick={() => setTemplateToDelete(template.id)}
          >
            {t("Delete")}
          </MenuItem>
        </Menu>
      </MenuProvider>

      <div className={styles.tags}>
        {template.documentType && (
          <Tag color="blue" size="small" weight="bold">
            {tEnum(`DocumentType.${template.documentType}.label`)}
          </Tag>
        )}

        {showStatusTag && (
          <Tag
            color={
              template.status === JournalTemplateStatus.Draft
                ? "neutral"
                : "levGreen"
            }
            className={styles.statusTag}
            size="small"
          >
            {accessScopeIcon &&
              template.status === JournalTemplateStatus.Published && (
                <Icon name={accessScopeIcon} />
              )}
            <ButtonText size="small">
              {tEnum(`JournalTemplateStatus.${template.status}`)}
            </ButtonText>
          </Tag>
        )}
      </div>
      <Dialog
        isOpen={!!templateToDelete}
        onClose={() => setTemplateToDelete(null)}
        title={tRoutes("deleteTemplate")}
        actions={
          <>
            <Button
              onClick={() => {
                setTemplateToDelete(null)
              }}
              variant="clear"
            >
              {tRoutes("keepTemplate")}
            </Button>
            <Button
              status="error"
              onClick={() => {
                if (!templateToDelete) return
                handleDeleteTemplate(templateToDelete)
              }}
            >
              {t("doDelete")}
            </Button>
          </>
        }
      >
        <Text>{tRoutes("deleteTemplateConfirmation")}</Text>
      </Dialog>
    </NavLink>
  )
}
