.modal {
  width: 730px;
}

.modalContent {
  display: grid;
  gap: 24px;
  max-height: none;
}

.events {
  display: grid;
  gap: 16px;
  overflow-y: auto;
  max-height: 50vh;
}

@media (max-height: 720px) {
  .events {
    max-height: 25vh;
  }
}

.eventsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.event {
  display: grid;
  gap: 8px;

  align-items: flex-start;
}

.event > span {
  top: 3px;
}

.eventInfo {
  display: flex;
  gap: 4px;
  flex-direction: column;
}

.selectedEventsLabel {
  color: var(--color-pink-600);
}

.subjectsContainer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
}

.tooltipIcon {
  border-radius: var(--radius-button-half);
  height: 24px;
  width: 24px;
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background-color: var(--color-warning-200);
  color: var(--color-warning-800);
  margin-left: 4px;
}

.tooltip {
  padding: 8px;
}

.panel.panel {
  padding: 8px 16px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
