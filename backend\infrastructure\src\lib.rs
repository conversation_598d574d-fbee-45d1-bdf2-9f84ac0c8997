#![deny(unused_must_use)]
#![warn(clippy::all)]
#![warn(clippy::pedantic)]
#![warn(clippy::todo)]
#![allow(clippy::module_name_repetitions)]
#![allow(clippy::must_use_candidate)]
#![allow(clippy::missing_errors_doc)]
#![allow(clippy::missing_panics_doc)]
#![allow(clippy::new_without_default)]
#![allow(unstable_name_collisions)]
#![allow(clippy::items_after_statements)]

// pub mod auth;  // Some auth modules depend on missing domain modules
pub mod db;
pub mod email;
// pub mod hekla;  // Depends on missing domain modules
// pub mod oracle_api;  // Depends on missing domain modules

mod env_parser;
pub use env_parser::EnvParser;

pub mod jira;

// pub mod audit_logger;  // Depends on missing domain modules
pub mod nhi;
// pub mod observability;  // Depends on missing domain modules
pub mod pdf_generator;
pub mod service_communicator;
pub use nhi::EmptyNhiService;
pub use nhi::NhiServiceApi;
pub use pdf_generator::PdfGeneratorApi;
// pub mod calendar_notification_integration;  // Depends on missing domain modules
// pub mod files;  // Depends on missing domain modules
pub mod messaging;
// pub mod national_registry_integration;  // Depends on missing domain modules
// pub mod notification_integration;  // Depends on missing domain modules
// pub mod notifications;  // Depends on missing domain modules
pub mod online_payment;
