import { format } from "date-fns"
import { useTranslation } from "react-i18next"

import { Text } from "ui"

import { EventRepeatInterval } from "generated/graphql"

import styles from "./EventInstanceForm.module.css"

type Props = {
  isRepeatIntervalSelected: boolean
  untilDate?: string
  repeatIntervalValue?: string
  fromTime: string
  toTime: string
}

export const RepeatIntervalText = ({
  isRepeatIntervalSelected,
  repeatIntervalValue,
  untilDate,
  fromTime,
  toTime,
}: Props) => {
  const { t } = useTranslation()

  if (!isRepeatIntervalSelected || !untilDate || !repeatIntervalValue)
    return null

  let text = ""

  if (repeatIntervalValue === EventRepeatInterval.EveryWeekday) {
    text = t("every weekday")
  } else if (repeatIntervalValue === EventRepeatInterval.Weekly) {
    text = t("every week")
  } else if (repeatIntervalValue === EventRepeatInterval.Monthly) {
    text = t("every month")
  }

  return (
    <Text className={styles.repeatIntervalText}>
      {t(`Occurs`)}

      <Text as="span" weight="bold">
        {" "}
        {text}
      </Text>

      {t(" from ")}
      <Text as="span" weight="bold">
        {fromTime}
      </Text>
      {t(" to ")}
      <Text as="span" weight="bold">
        {toTime}
      </Text>

      {t(` until ${format(new Date(untilDate), "EEEE, dd MMMM yyyy")} `)}
    </Text>
  )
}
