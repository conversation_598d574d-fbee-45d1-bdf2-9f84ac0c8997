.eventList {
  display: grid;
  gap: 16px;
}

.eventWrapper {
  position: relative;
}

/* default colors is  lev blue */

/* BLUE */
.eventWrapper .event {
  padding: 12px 16px;
  border-radius: var(--radius-button-half);
  display: grid;
  gap: 12px;
  position: relative;
  cursor: pointer;
}

.eventButton {
  position: absolute;
  right: 0;
  border: 0;
  border-radius: 0px var(--radius-button-half);
}

.eventTitle {
  display: flex;
  gap: 8px;
  align-items: center;
}

.eventTitle > svg {
  flex-shrink: 0;
  margin-top: 5px;
}

.openJournalButton.openJournalButton {
  border-radius: var(--radius-button-half) 0px;
  width: fit-content;
  position: absolute;
  bottom: 0;
  right: 0;
}

.event:hover:not(:has(.eventButton:hover)) .openJournalButton {
  border: 1px solid var(--color-lev-blue);
  border-radius: var(--radius-button-half) 0px;
  background: var(--color-lev-blue);
  color: var(--color-white);
}

.noPointerCursor.noPointerCursor {
  cursor: default;
}
