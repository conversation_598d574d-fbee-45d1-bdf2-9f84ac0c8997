import c from "classnames"
import { useState, useRef } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link, useNavigate } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading } from "ui"
import { Text } from "ui"
import { OptionsMenu } from "ui/components/OptionsMenu/OptionsMenu"
import { useClickOutside } from "ui/lib/useClickOutside"
import { getTeamPathFromServiceType } from "utils/getTeamPathFromServiceType"
import { renderDepartmentAndTeam } from "utils/renderDepartmentAndTeam"

import { ServiceType } from "generated/graphql"

import headerStyles from "../Header/Header.module.css"
import styles from "./DepartmentTeamBox.module.css"

type DepartmentBoxProps = {
  teamsAndDepartments: GlobalDataWithNonNullableActor["actor"]["teamsAndDepartments"]
}

export const DepartmentTeamBox = ({
  teamsAndDepartments,
}: DepartmentBoxProps) => {
  const {
    globalState: { currentTeamId },
    setGlobalState,
  } = useGlobalState()

  // happens only when GlobalContext is init and id hasn't been set
  if (currentTeamId === null) return null

  const currentTeamAndDepartment = teamsAndDepartments.find(
    (team) => team.id === currentTeamId
  )

  const [isOptionsMenuOpen, setOptionsMenuOpen] = useState(false)

  const navigate = useNavigate()
  const { t } = useTranslation()

  const menuRef = useRef<HTMLButtonElement>(null)
  useClickOutside(menuRef, () => {
    if (isOptionsMenuOpen) setOptionsMenuOpen(false)
  })

  if (!currentTeamAndDepartment)
    // essentially same as teamsAndDepartments.length === 0
    return (
      <div className={headerStyles.boxCommon}>
        <div className={headerStyles.boxTitle}>
          {t("routes:manageTeam.providerNotInAnyTeam")}
        </div>
      </div>
    )

  return (
    <div
      className={c({
        [headerStyles.boxCommon]: 1,
        [headerStyles.active]: location.pathname.startsWith("/team"),
      })}
    >
      <Link
        to={getTeamPathFromServiceType(
          currentTeamAndDepartment.serviceType,
          currentTeamAndDepartment.id
        )}
        className={c(styles.header, headerStyles.boxLink)}
      >
        <div className={`${headerStyles.boxTitle} ${styles.boxTitle} `}>
          <Text className={styles.departmentName} size="small">
            {currentTeamAndDepartment.department?.name}
          </Text>
          <Heading className={styles.name}>
            {currentTeamAndDepartment.name}
          </Heading>
        </div>
      </Link>
      <div className={styles.departmentButtons}>
        <Button
          variant="clear"
          icon={<Icon name={"information-line"} className={styles.icon} />}
          className={headerStyles.secondaryAction}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            e.preventDefault()

            const path = generatePath(RouteStrings.team, {
              teamId: currentTeamAndDepartment.id,
            })

            navigate(path)
          }}
        />
        <button
          className={headerStyles.dropdownButton}
          type="button"
          onClick={() => {
            setOptionsMenuOpen(!isOptionsMenuOpen)
          }}
          ref={menuRef}
        >
          <Icon name="arrow-down-s-line" className={styles.icon} />
        </button>
      </div>
      <OptionsMenu
        onClose={() => setOptionsMenuOpen(false)}
        isOpen={isOptionsMenuOpen}
        preventDefault
        options={teamsAndDepartments.map(
          ({ id, name, serviceType, department }) => ({
            id: `${id}:${serviceType}`,
            label: renderDepartmentAndTeam(name, department?.name),
            selected: id === currentTeamAndDepartment.id,
          })
        )}
        onChange={(values) => {
          const [teamId, serviceType] = values[0].split(":") as [
            teamId: string,
            serviceType: ServiceType,
          ]
          setGlobalState("currentTeamId", teamId)
          // COMEBACK - discuss with Gunnar navigation to team page
          navigate(getTeamPathFromServiceType(serviceType, teamId))
        }}
      />
    </div>
  )
}
