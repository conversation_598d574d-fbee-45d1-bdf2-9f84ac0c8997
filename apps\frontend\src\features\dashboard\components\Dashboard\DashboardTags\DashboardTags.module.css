.wrap {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tag.tag {
  border-radius: var(--radius-button-half);
  display: flex;
  align-items: center;
  gap: 4px;
  transition:
    color 0.2s,
    background 0.2s;
  padding: 0;
}

.tag:has(.tagButton:focus-visible) {
  border: 2px solid var(--color-lev-blue);
  margin: -2px;
}
.tagButton:focus-visible {
  outline: none;
}

.tagButton {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
  padding: 4px 8px 4px 8px;
  align-self: stretch;
  border-radius: var(--radius-button);
  margin-right: -8px;
  cursor: pointer;
}
.tagContent {
  flex: 0 1 auto;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 225px;
  /* Tag component sets line-height to 1
   * which causes descendants to be cut off here */
  line-height: 1.3;
}
.tagButton svg {
  flex: 0 0 auto;
}

.hiddenCountPopover {
  display: flex;
  gap: 8px;
  justify-content: stretch;
  padding: 4px;
}

.hashtagButton,
.overflowButton {
  display: flex;
  gap: 2px;
  height: 32px;
  padding: 4px 8px;
}
.overflowButton {
  padding-left: 12px;
  padding-right: 12px;
}
.noTagsMessage {
  max-width: 300px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
