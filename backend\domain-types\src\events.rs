//! Event types for domain entities

use serde::{Deserialize, Serialize};

// Base trait for all events
pub trait DomainEvent: Send + Sync + Clone + std::fmt::Debug {}

// Placeholder event types - these will be replaced with actual event definitions
// when we move the real events from domain

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct JournalEntryEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for JournalEntryEvent {}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EncounterEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for EncounterEvent {}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NoteEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for NoteEvent {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OutboundReferralEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for OutboundReferralEvent {}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OutboundDoctorsLetterEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for OutboundDoctorsLetterEvent {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrugPrescriptionEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for DrugPrescriptionEvent {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InterventionPeriodEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for InterventionPeriodEvent {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClinicalCodingEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for ClinicalCodingEvent {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JournalEntryAttachmentEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for JournalEntryAttachmentEvent {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InboundDataEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for InboundDataEvent {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InboundEntryEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for InboundEntryEvent {}

// Template events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrugPrescriptionTemplateEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

impl DomainEvent for DrugPrescriptionTemplateEvent {}
