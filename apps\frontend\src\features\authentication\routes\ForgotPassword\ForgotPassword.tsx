import { FormEvent } from "react"
import { useTranslation } from "react-i18next"
import { <PERSON> } from "react-router-dom"
import invariant from "tiny-invariant"

import { RouteStrings } from "routes/RouteStrings"
import { FormGrid, Heading, Input } from "ui"
import { Button } from "ui"
import { notification } from "ui/components/Notification"

import { useRequestPasswordResetMutation } from "generated/graphql"

import styles from "../../Authentication.module.css"

export default function ForgotPassword() {
  const { t } = useTranslation()

  const [requestPasswordReset, { loading }] = useRequestPasswordResetMutation({
    onCompleted: (data) => {
      if (!data) return
      notification.create({
        status: "success",
        message: t("routes:auth.resetPasswordSuccess"),
        duration: 4000,
      })
    },
  })

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.target as HTMLFormElement)
    await handleApiRequest(formData)
  }

  const handleApiRequest = async (formData: FormData) => {
    const { email } = Object.fromEntries(formData.entries())

    invariant(typeof email === "string", t("routes:validations.invalidEmail"))

    await requestPasswordReset({ variables: { email: email } })
  }

  return (
    <FormGrid onSubmit={handleSubmit} className={styles.wrap}>
      <Heading as="h1" size="large" className={styles.title}>
        {t("routes:auth.forgotPassword")}
      </Heading>

      <Input
        label={t("routes:auth.email")}
        type="email"
        name="email"
        autoComplete="username"
      />

      <Button
        className={styles.submitButton}
        disabled={loading}
        type="submit"
        size="large"
        variant="filled"
      >
        {t("routes:auth.resetPassword")}
      </Button>

      <Link to={RouteStrings.login} className={styles.link}>
        {t("routes:auth.backToLogin")}
      </Link>
    </FormGrid>
  )
}
