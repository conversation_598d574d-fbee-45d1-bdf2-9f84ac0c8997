import { useEffect, useState } from "react"
import { Trans, useTranslation } from "react-i18next"
import { NumberFormatValues } from "react-number-format"
import { useDebouncedCallback } from "use-debounce"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import { notification, Text } from "ui"
import NumberInput from "ui/components/Input/NumberInput/NumberInput"
import Switch from "ui/components/Switch/Switch"
import { formatNumberInThousand } from "utils/formatNumberInThousand"
import isDev from "utils/isDev"

import {
  GetInvoiceQuery,
  useCreateInvoiceDiscountForSubjectMutation,
  useSetNhiPaysFullInvoiceAmountMutation,
} from "generated/graphql"

import styles from "./PatientInvoiceFooter.module.css"

const domusLaeknarId = "a5610425-ccb7-4c97-b3bc-d1deabb0db14"
const mssTestingId = "22f08242-b83d-4db9-adc3-f831eff84ec1"

// temporary solution for Domus
const isValidOrganisationForNHISwitch = (orgId: string) => {
  return [domusLaeknarId, mssTestingId].includes(orgId) || isDev
}

const DELAY = 500

type PatientDiscountShareProps = Pick<
  GetInvoiceQuery["invoice"],
  "id" | "nhiPayableBySubject" | "subjectDiscount" | "totalPayableBySubject"
> & {
  isDisabled: boolean
}

const PatientDiscountShare = ({
  id: invoiceId,
  isDisabled,
  nhiPayableBySubject,
  subjectDiscount,
}: PatientDiscountShareProps) => {
  const subjectDiscountNum = subjectDiscount ?? 0
  const [isPatientDiscountChecked, setIsPatientDiscountChecked] = useState(
    subjectDiscountNum > 0
  )
  const [value, setValue] = useState(subjectDiscountNum)

  const { t } = useTranslation("generic")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "billing.invoiceFooter",
  })

  useEffect(() => {
    if (isDisabled) {
      setIsPatientDiscountChecked(false)
      setValue(0)
    } else {
      setValue(subjectDiscountNum)
      setIsPatientDiscountChecked(subjectDiscountNum > 0)
    }
  }, [isDisabled])

  const [createInvoiceDiscount] = useCreateInvoiceDiscountForSubjectMutation({
    onError: (error) => {
      notification.create({
        message: error.message,
        status: "error",
      })
    },
  })

  const onDebounceCreateInvoiceDiscount = useDebouncedCallback(
    (invoiceId: string, discount: number) => {
      createInvoiceDiscount({
        variables: {
          invoiceId,
          discount,
        },
      })
    },
    DELAY
  )

  const togglePatientDiscountChecked = ({
    target: { checked },
  }: React.ChangeEvent<HTMLInputElement>) => {
    const resetDiscountAmount = checked ? (nhiPayableBySubject ?? 0) : 0
    setIsPatientDiscountChecked(checked)
    setValue(resetDiscountAmount)

    createInvoiceDiscount({
      variables: {
        invoiceId,
        discount: resetDiscountAmount,
      },
    })
  }

  const handleSetValue = ({ floatValue = 0 }: NumberFormatValues) => {
    setValue(floatValue)
    onDebounceCreateInvoiceDiscount(invoiceId, floatValue)
  }

  return (
    <div>
      <div className={styles.switch}>
        <Text as="label" htmlFor="discount-patient-share-switch">
          {tRoutes("discountPatientsShare")}
        </Text>
        <Switch
          id="discount-patient-share-switch"
          checked={isPatientDiscountChecked}
          onChange={togglePatientDiscountChecked}
          disabled={isDisabled}
        />
      </div>

      {isPatientDiscountChecked && (
        <>
          <Text className={styles.nhiPayableBySubject}>
            <Trans
              i18nKey={tRoutes("nhiPayableBySubject")}
              components={{ amount: <span /> }}
              values={{
                value: formatNumberInThousand(nhiPayableBySubject),
              }}
            />
          </Text>

          <NumberInput
            data-testid="patient-discount-input"
            allowNegative={false}
            className={styles.numberInput}
            label="patient discount amount"
            hideLabel
            hideMessage
            onValueChange={handleSetValue}
            value={value}
            suffix={` ${t("currency")}`}
            {...(value > (nhiPayableBySubject ?? 0) && {
              hideMessage: false,
              message: tRoutes("discountPatientsShareInputError"),
              status: "error",
            })}
          />
        </>
      )}
    </div>
  )
}

type NHIPaysAllProps = Pick<GetInvoiceQuery["invoice"], "id" | "nhiPaysAll">

const NHIPaysAll = ({ id: invoiceId, nhiPaysAll }: NHIPaysAllProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "billing.invoiceFooter",
  })

  const [setNhiPaysFullInvoiceAmount] = useSetNhiPaysFullInvoiceAmountMutation({
    onError: (error) => {
      notification.create({
        message: error.message,
        status: "error",
      })
    },
  })

  const handleNhiPaysAllChange = ({
    target: { checked },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setNhiPaysFullInvoiceAmount({
      variables: {
        invoiceId,
        nhiPaysAll: checked,
      },
    })
  }

  return (
    <div className={styles.switch}>
      <Text
        as="label"
        htmlFor="nhi-pays-all-switch"
        className={styles.switchLabelWithTooltip}
      >
        {tRoutes("nhiPaysAll")}
        <Tooltip tooltipContent={tRoutes("nhiPaysAllTooltip")} portal={false}>
          <Icon name="information-line" />
        </Tooltip>
      </Text>

      <Switch
        id="nhi-pays-all-switch"
        checked={nhiPaysAll}
        onChange={handleNhiPaysAllChange}
      />
    </div>
  )
}

type PatientInvoiceFooterProps = {
  invoice: GetInvoiceQuery["invoice"]
}

export const PatientInvoiceFooter = ({
  invoice: {
    id,
    containsNhiItems,
    nhiPayableBySubject,
    nhiPaysAll,
    subjectDiscount,
    total,
    totalVat,
    totalDiscount,
    totalPayableByInsurance,
    totalPayableBySubject,
  },
}: PatientInvoiceFooterProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "billing.invoiceFooter" })

  const {
    globalData: {
      actor: {
        organisation: { id: orgId },
      },
    },
  } = useGlobalState()

  const showNHIPaysAllSwitch =
    containsNhiItems &&
    (totalPayableByInsurance ?? 0) > 0 &&
    isValidOrganisationForNHISwitch(orgId)

  const showPatientDiscountShareSwitchAndInput =
    containsNhiItems && (nhiPayableBySubject ?? 0) >= 0

  return (
    <div className={styles.invoiceFooter}>
      <div className={styles.footerSpacing}>
        <Text>
          <Trans
            i18nKey={t("totalVAT")}
            components={{ amount: <span /> }}
            values={{ value: formatNumberInThousand(totalVat) }}
          />
        </Text>
        <Text>
          <Trans
            i18nKey={t("totalDiscount")}
            components={{ amount: <span /> }}
            values={{ value: formatNumberInThousand(totalDiscount) }}
          />
        </Text>
        <Text>
          <Trans
            i18nKey={t("totalInvoice")}
            components={{ amount: <span /> }}
            values={{ value: formatNumberInThousand(total) }}
          />
        </Text>
      </div>
      <div className={`${styles.footerSpacing} ${styles.footerPayableInfo}`}>
        {showNHIPaysAllSwitch && <NHIPaysAll id={id} nhiPaysAll={nhiPaysAll} />}

        {showPatientDiscountShareSwitchAndInput && (
          <PatientDiscountShare
            id={id}
            nhiPayableBySubject={nhiPayableBySubject}
            subjectDiscount={subjectDiscount}
            totalPayableBySubject={totalPayableBySubject}
            isDisabled={nhiPaysAll}
          />
        )}

        {/* only displayed in credit invoiced */}
        {(subjectDiscount ?? 0) < 0 && (
          <Text>
            <Trans
              i18nKey={t("discountPatientsShareCreditInvoice")}
              components={{ amount: <span /> }}
              values={{ value: formatNumberInThousand(subjectDiscount) }}
            />
          </Text>
        )}

        <Text>
          <Trans
            i18nKey={t("totalPayableByPatient")}
            components={{ amount: <span /> }}
            values={{ value: formatNumberInThousand(totalPayableBySubject) }}
          />
        </Text>
        <Text>
          <Trans
            i18nKey={t("totalPayableByInsurance")}
            components={{ amount: <span /> }}
            values={{ value: formatNumberInThousand(totalPayableByInsurance) }}
          />
        </Text>
      </div>
    </div>
  )
}
