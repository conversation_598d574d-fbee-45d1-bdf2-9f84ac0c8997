import { createContext, ReactN<PERSON>, useContext } from "react"
import { useImmerReducer } from "use-immer"
import { v4 } from "uuid"

type StringDataField = {
  type: "string"
  id: string
  label: string
  value?: string
}

type NumberDataField = {
  type: "number"
  id: string
  label: string
  value?: number
}

export type SelectDataField = {
  type: "select"
  id: string
  label: string
  options: { label: string; value: string }[]
  value?: string
  display?: "select"
  multiSelect?: boolean
}

type DataField = StringDataField | NumberDataField | SelectDataField

type StateType = Record<string, DataField>
type ActionType<T extends DataField> = [key: T["id"], value: T["value"]]

type Props = {
  getInput: (id: string) => DataField | null
  setValue: (id: string, newValue: string) => void
  isEditorFocused: boolean
}

const DataFieldContext = createContext<Props>({
  getInput: () => null,
  setValue: () => undefined,
  isEditorFocused: true,
})

const initialState = {
  // Grunnupplýsingar template
  aa: {
    type: "string" as const,
    id: "aa",
    label: "Input 1",
    value: "Some initial text",
  },
  bb: {
    type: "number" as const,
    id: "bb",
    label: "Number",
    value: 6914689,
  },
  cc: {
    type: "select" as const,
    id: "cc",
    label: "Select",
    options: [
      {
        label: "Option 1",
        value: "option-1",
      },
    ],
  },
  // Dislocated Shoulder template
  side: {
    type: "select" as const,
    id: "side",
    label: "Side of Body",
    options: [
      {
        label: "Left",
        value: "left",
      },
      {
        label: "Right",
        value: "right",
      },
    ],
    display: "select" as const,
  },
  painScore: {
    type: "select" as const,
    id: "painScore",
    label: "Pain Score",
    options: [
      {
        label: "1 of 10",
        value: "1 of 10",
      },
      {
        label: "2 of 10",
        value: "2 of 10",
      },
      {
        label: "3 of 10",
        value: "3 of 10",
      },
      {
        label: "4 of 10",
        value: "4 of 10",
      },
      {
        label: "5 of 10",
        value: "5 of 10",
      },
      {
        label: "6 of 10",
        value: "6 of 10",
      },
      {
        label: "7 of 10",
        value: "7 of 10",
      },
      {
        label: "8 of 10",
        value: "8 of 10",
      },
      {
        label: "9 of 10",
        value: "9 of 10",
      },
      {
        label: "10 of 10",
        value: "10 of 10",
      },
    ],
    display: "select" as const,
  },
  previousDislocations: {
    type: "number" as const,
    id: "previousDislocations",
    label: "Previous Dislocations",
    value: 0,
  },
  relief: {
    type: "string" as const,
    id: "relief",
    label: "Activity that provides relief",
    value: "",
  },
  worsened: {
    type: "string" as const,
    id: "worsened",
    label: "Activity that worsens the pain",
    value: "",
  },
  pain: {
    type: "select" as const,
    id: "pain",
    label: "Pain",
    options: [
      {
        label: "suddenly",
        value: "suddenly",
      },
      {
        label: "gradually",
        value: "gradually",
      },
    ],
  },
  painDuration: {
    type: "number" as const,
    id: "previousDislocations",
    label: "Previous Dislocations",
    value: 0,
  },
  painDurationUnit: {
    type: "select" as const,
    id: "painDurationUnit",
    label: "Pain Duration Unit",
    options: [
      {
        label: "minutes",
        value: "minutes",
      },
      {
        label: "hours",
        value: "hours",
      },
      {
        label: "days",
        value: "days",
      },
      {
        label: "other",
        value: "other",
      },
    ],
  },
  painDescription: {
    type: "select" as const,
    id: "painDescription",
    label: "Pain Description",
    options: [
      {
        label: "sharp",
        value: "sharp",
      },
      {
        label: "dull",
        value: "dull",
      },
      {
        label: "burning",
        value: "burning",
      },
      {
        label: "aching",
        value: "aching",
      },
    ],
  },
  painCondition: {
    type: "select" as const,
    id: "painCondition",
    label: "Pain Condition",
    options: [
      {
        label: "constant",
        value: "constant",
      },
      {
        label: "colicky",
        value: "colicky",
      },
      {
        label: "intermittent",
        value: "intermittent",
      },
    ],
  },
  painLocation: {
    type: "select" as const,
    id: "painLocation",
    label: "Pain Location",
    options: [
      {
        label: "left",
        value: "on left side",
      },
      {
        label: "right",
        value: "on right side",
      },
      {
        label: "central",
        value: "on middle side",
      },
      {
        label: "epigastrial",
        value: "epigastrial",
      },
    ],
  },

  painRadiation: {
    type: "select" as const,
    id: "painRadiation",
    label: "Pain Radiation",
    options: [
      {
        label: "left arm",
        value: "left arm",
      },
      {
        label: "right arm",
        value: "right arm",
      },
      {
        label: "neck",
        value: "neck",
      },
      {
        label: "throat",
        value: "throat",
      },
      {
        label: "face",
        value: "face",
      },
    ],
  },
  painStatus: {
    type: "select" as const,
    id: "painStatus",
    label: "Pain Status",
    options: [
      {
        label: "worsened",
        value: "worsened",
      },
      {
        label: "improved",
        value: "improved",
      },
      {
        label: "unaffected",
        value: "unaffected",
      },
    ],
  },
  painMovementStatus: {
    type: "select" as const,
    id: "painMovementStatus",
    label: "Pain Movement Status",
    options: [
      {
        label: "increases",
        value: "increases",
      },
      {
        label: "is unchanged",
        value: "is unchanged",
      },
    ],
  },
  activeMovementStatus: {
    type: "string" as const,
    id: "activeMovementStatus",
    label: "Active Movement Status",
    value: "walking",
  },
  painCondition2: {
    type: "select" as const,
    id: "painCondition2",
    label: "Pain Condition 2",
    options: [
      {
        label: "worse",
        value: "worse",
      },
      {
        label: "better",
        value: "better",
      },
    ],
  },
  activeMovement2: {
    type: "select" as const,
    id: "activeMovement2",
    label: "Active Movement 2",
    options: [
      {
        label: "lying down",
        value: "lying down",
      },
      {
        label: "other",
        value: "other",
      },
    ],
  },
  nauseaCondition: {
    type: "select" as const,
    id: "nauseaCondition",
    label: "Nausea Condition",
    options: [
      {
        label: "No associated nausea",
        value: "No",
      },
      {
        label: "Patient has had nausea",
        value: "Yes",
      },
    ],
  },
  postopOther: {
    type: "string" as const,
    id: "postopother",
    label: "Other",
    value: "",
  },
  postopPain: {
    type: "select" as const,
    id: "postopPain",
    label: "Pain after surgery",
    options: [
      {
        label: "improving",
        value: "improving",
      },
      {
        label: "unchanged",
        value: "unchanged",
      },
      {
        label: "worsening",
        value: "worsening",
      },
    ],
  },
  freeText1: {
    type: "string" as const,
    id: "freeText1",
    label: "frjáls texti",
    value: "",
  },
  freeText2: {
    type: "string" as const,
    id: "freeText2",
    label: "frjáls texti",
    value: "",
  },
  freeText3: {
    type: "string" as const,
    id: "freeText3",
    label: "frjáls texti",
    value: "",
  },
  freeText4: {
    type: "string" as const,
    id: "freeText4",
    label: "frjáls texti",
    value: "",
  },
  freeText5: {
    type: "string" as const,
    id: "freeText5",
    label: "frjáls texti",
    value: "",
  },
  freeText6: {
    type: "string" as const,
    id: "freeText6",
    label: "frjáls texti",
    value: "",
  },
  freeText7: {
    type: "string" as const,
    id: "freeText7",
    label: "frjáls texti",
    value: "",
  },
  vitalSigns_1: {
    type: "string" as const,
    id: "vitalSigns_1",
    label: "lífsmörk",
    value: "",
  },
  vitalSigns_2: {
    type: "string" as const,
    id: "vitalSigns_2",
    label: "lífsmörk",
    value: "",
  },
  vitalSigns_3: {
    type: "string" as const,
    id: "vitalSigns_3",
    label: "lífsmörk",
    value: "",
  },
  vitalSigns_4: {
    type: "string" as const,
    id: "vitalSigns_4",
    label: "hiti",
    value: "",
  },
  vitalSigns_5: {
    type: "string" as const,
    id: "vitalSigns_5",
    label: "mettun",
    value: "",
  },
  boolean_IS: {
    type: "select" as const,
    id: "boolean_IS",
    label: "nei/já",
    options: [
      {
        label: "nei",
        value: "nei",
      },
      {
        label: "já",
        value: "já",
      },
    ],
  },
} as StateType

export const useDataField = () => useContext(DataFieldContext)

/* Leviosa custom, "Fields". */
export const DataFieldProvider = <T extends DataField>({
  children,
  isEditorFocused = true,
}: {
  children?: ReactNode
  isEditorFocused?: boolean
}) => {
  // hard-coded data for now but later on, we'll get this from the backend
  const [data, setData] = useImmerReducer<StateType, ActionType<T>>(
    (draft, action) => {
      const [id, value] = action
      if (!draft[id])
        draft[id] = {
          type: "string" as const,
          id: v4(),
          label: "Input",
        }
      draft[id].value = value
    },
    initialState
  )

  const getInput = (id: string) => {
    return data[id]
  }

  const setValue = <T extends DataField>(id: T["id"], value: T["value"]) =>
    setData([id, value])

  return (
    <DataFieldContext.Provider value={{ getInput, setValue, isEditorFocused }}>
      {children}
    </DataFieldContext.Provider>
  )
}
