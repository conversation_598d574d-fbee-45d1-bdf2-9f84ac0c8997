use async_trait::async_trait;
use base64::Engine;
use leviosa_domain::{
    errors::{CustomError, NotFoundError},
    online_payment_service::{CheckoutStatus, CreateCustomerResponse, Customer, OnlinePaymentService, PaymentCheckout, PaymentCheckoutResponse},
    service_communicator::{ServiceCommunicator, ServiceCommunicatorError},
};
use uuid::Uuid;

#[derive(Debug, Clone)]
pub struct VerifonePaymentService<T: ServiceCommunicator> {
    communicator_checkout: T,
    communicator_customer: T,
    username: String,
    password: String,
}

impl<T: ServiceCommunicator> VerifonePaymentService<T> {
    pub fn new(
        communicator_checkout: T,
        communicator_customer: T,
        username: String,
        password: String,
    ) -> Self {
        Self {
            communicator_checkout,
            communicator_customer,
            username,
            password,
        }
    }
}

#[async_trait]
impl<T: ServiceCommunicator> OnlinePaymentService for VerifonePaymentService<T> {
    // Required methods from the base trait
    async fn create_payment(&self, _request: leviosa_domain::online_payment_service::PaymentRequest) -> leviosa_domain::errors::Result<leviosa_domain::online_payment_service::PaymentResponse> {
        todo!("Implement create_payment for Verifone")
    }

    async fn get_payment_status(&self, _payment_id: &str) -> leviosa_domain::errors::Result<leviosa_domain::online_payment_service::PaymentStatus> {
        todo!("Implement get_payment_status for Verifone")
    }

    async fn process_webhook(&self, _payload: &str, _signature: &str) -> leviosa_domain::errors::Result<leviosa_domain::online_payment_service::PaymentStatus> {
        todo!("Implement process_webhook for Verifone")
    }

    // Verifone-specific methods
    async fn api_auth_header(&self) -> Result<String, leviosa_domain::errors::Error> {
        let credentials = format!("{}:{}", self.username, self.password);
        let encoded_credentials = base64::engine::general_purpose::STANDARD.encode(credentials);
        let auth_header = format!("Basic {encoded_credentials}");

        Ok(auth_header)
    }

    async fn get_checkout(&self, checkout_id: Uuid) -> Result<CheckoutStatus, leviosa_domain::errors::Error> {
        let token = self.api_auth_header().await?;

        let path = format!("/{checkout_id}");
        let result = self
            .communicator_checkout
            .get_json(path.as_str(), &token)
            .await;

        return match result {
            Ok(json) => {
                let checkout_status = serde_json::from_value(json);
                match checkout_status {
                    Ok(checkout_status) => Ok(checkout_status),
                    Err(e) => Err(CustomError::new(
                        None,
                        format!("Failed to parse checkout status: {e:?}"),
                    )
                    .into()),
                }
            }
            Err(ServiceCommunicatorError::NotFound(_)) => Err(NotFoundError::custom(
                "CheckoutStatus",
                &checkout_id.to_string(),
                "checkout_id",
            )
            .into()),
            Err(e) => {
                Err(CustomError::new(None, format!("Failed to get checkout status: {e:?}")).into())
            }
        };
    }

    async fn create_checkout(
        &self,
        payment_checkout: PaymentCheckout,
    ) -> Result<PaymentCheckoutResponse, leviosa_domain::errors::Error> {
        let token = self.api_auth_header().await?;

        let path = String::new();
        let checkout_json = serde_json::to_value(payment_checkout);
        if checkout_json.is_err() {
            return Err(CustomError::new(
                None,
                format!("Failed to serialize checkout request: {checkout_json:?}"),
            )
            .into());
        }
        let checkout_json = checkout_json.unwrap();

        let result = self
            .communicator_checkout
            .post_json(path.as_str(), &token, checkout_json)
            .await;

        return match result {
            Ok(result) => {
                let checkout_response = serde_json::from_value(result);

                match checkout_response {
                    Ok(checkout_response) => Ok(checkout_response),
                    Err(e) => Err(CustomError::new(
                        None,
                        format!("Failed to parse checkout response: {e:?}"),
                    )
                    .into()),
                }
            }
            Err(e) => Err(CustomError::new(
                None,
                format!("Failed to post checkout request: {e:?}"),
            )
            .into()),
        };
    }

    async fn create_customer(&self, customer: Customer) -> Result<CreateCustomerResponse, leviosa_domain::errors::Error> {
        let token = self.api_auth_header().await?;

        let path = String::new();
        let customer_json = serde_json::to_value(customer);
        if customer_json.is_err() {
            return Err(CustomError::new(
                None,
                format!("Failed to serialize customer request: {customer_json:?}"),
            )
            .into());
        }
        let customer_json = customer_json.unwrap();

        let result = self
            .communicator_customer
            .post_json(path.as_str(), &token, customer_json)
            .await;

        return match result {
            Ok(result) => {
                let customer_response = serde_json::from_value(result);

                match customer_response {
                    Ok(customer_response) => Ok(customer_response),
                    Err(e) => Err(CustomError::new(
                        None,
                        format!("Failed to parse checcustomerkout response: {e:?}"),
                    )
                    .into()),
                }
            }
            Err(e) => Err(CustomError::new(
                None,
                format!("Failed to post customer request: {e:?}"),
            )
            .into()),
        };
    }
}
