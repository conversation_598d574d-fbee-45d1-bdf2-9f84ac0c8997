/* eslint-disable no-console */
import { Table } from "ui"
import { formatNumberInDecimal } from "utils/formatNumberInDecimal"
import { formatNumberInThousand } from "utils/formatNumberInThousand"
import { isTypename } from "utils/isTypename"

import { EmptyOrganisationPriceListRow } from "../EmptyOrganisationPriceListRow/EmptyOrganisationPriceListRow"
import { PriceList } from "../OrganisationPriceList.context"
import { OrganisationPriceListTableRowHeader } from "../OrganisationPriceListTableRowHeader/OrganisationPriceListTableRowHeader"
import styles from "../PriceList.module.css"

type OrganisationPriceListTableProps = {
  priceList: PriceList
}

export const OrganisationPriceListTable = ({
  priceList,
}: OrganisationPriceListTableProps) => {
  // const { t } = useTranslation()

  // const [likeOrganisationPriceListItem] = useLikeOrganisationPriceItemMutation({
  //   onCompleted: (data) => {
  //     console.log("completed")
  //     console.log(data)
  //   },
  // })

  return (
    <Table>
      <thead>
        <tr>
          <OrganisationPriceListTableRowHeader />
          {/* <th></th> */}
        </tr>
      </thead>

      <tbody>
        {priceList.length === 0 && <EmptyOrganisationPriceListRow />}
        {priceList
          .filter(isTypename("BillingCodeClinicSpecific"))
          .map((priceListItem) => {
            return (
              <tr key={priceListItem.id} className={styles.priceListRow}>
                <td>{priceListItem.code || ""}</td>
                <td>
                  <div className={styles.truncateText}>
                    {priceListItem.title}
                  </div>
                </td>
                <td className={styles.numericValue}>
                  {formatNumberInThousand(priceListItem.units)}
                </td>
                <td className={styles.numericValue}>
                  {formatNumberInThousand(priceListItem.unitPrice)}
                </td>
                <td>{priceListItem.currency}</td>
                <td className={styles.numericValue}>
                  {formatNumberInDecimal(priceListItem.vat)}
                </td>
                {/* <td className={styles.buttonColumn}>
                  <ButtonStar
                    className={styles.buttonStar}
                    onClick={() => {
                      likeOrganisationPriceListItem({
                        variables: {
                          id: priceListItem.id,
                        },
                      })
                    }}
                    isStarred={false}
                  />
                </td> */}
              </tr>
            )
          })}
      </tbody>
    </Table>
  )
}
