import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import Popover from "components/Popover/Popover"
import { ToolbarItemWithTooltip } from "components/ToolbarItemWithTooltip/ToolbarItemWithTooltip"
import { Button, Text } from "ui"

import styles from "./EditOrganisationPriceListToolbar.module.css"

type EditOrganisationPriceListToolbarProps = {
  selectedPriceItemIds: string[]
  onClose: () => void
  onArchive: () => void
}

export const EditOrganisationPriceListToolbar = ({
  selectedPriceItemIds,
  onClose,
  onArchive,
}: EditOrganisationPriceListToolbarProps) => {
  const { t } = useTranslation()

  if (!selectedPriceItemIds.length) return null

  return (
    <Popover className={styles.wrap}>
      <Text>{selectedPriceItemIds.length} items selected</Text>
      <Button
        onClick={onArchive}
        variant="clear"
        status="error"
        icon={<Icon name="delete-bin-line" />}
      >
        {t("Archive")}
      </Button>

      <ToolbarItemWithTooltip
        onClick={onClose}
        icon={<Icon name="close-line" />}
        tooltipContent={t("Close")}
      />
    </Popover>
  )
}
