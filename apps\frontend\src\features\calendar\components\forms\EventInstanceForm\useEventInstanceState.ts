import { format, parse, parseISO } from "date-fns"
import { useState } from "react"

import { useComboboxStore } from "components/Ariakit/hooks"

import { convertToDateTimeLocal } from "../../CalendarTimePicker/convertToDateTimeLocal"
import { roundUpToNextQuarterHour } from "../../CalendarTimePicker/roundUpToNextQuarterHour"

const formatDateTime = (date: Date) => format(date, "yyyy-MM-dd HH:mm:ss")

export const dateFormat = "yyyy-MM-dd"

export const useEventInstanceState = (fromDate?: string, toDate?: string) => {
  const currentDateTime = roundUpToNextQuarterHour()

  const currentDate = formatDateTime(currentDateTime)

  const parsedFromDate = parse(
    fromDate ? formatDateTime(parseISO(fromDate)) : currentDate,
    `${dateFormat} HH:mm:ss`,
    new Date()
  )

  const parsedToDate = parse(
    toDate ? formatDateTime(parseISO(toDate)) : currentDate,
    `${dateFormat} HH:mm:ss`,
    new Date()
  )

  if (!toDate) {
    parsedToDate.setHours(parsedToDate.getHours() + 1)
  }

  const comboboxFromTime = useComboboxStore({
    defaultValue: format(parsedFromDate, "HH:mm"),
  })

  const comboboxToTime = useComboboxStore({
    defaultValue: format(parsedToDate, "HH:mm"),
  })

  const [fromDateState, setFromDateState] = useState<string>(
    format(parsedFromDate, dateFormat)
  )

  const [toDateState, setToDateState] = useState<string>(
    format(parsedToDate, dateFormat)
  )

  const comboboxFromTimeValue = comboboxFromTime.useState("value")
  const comboboxToTimeValue = comboboxToTime.useState("value")

  const fromDateTimeLocal = convertToDateTimeLocal(
    fromDateState,
    comboboxFromTimeValue
  )

  const toDateTimeLocal =
    fromDateState <= toDateState
      ? convertToDateTimeLocal(toDateState, comboboxToTimeValue)
      : convertToDateTimeLocal(fromDateState, comboboxToTimeValue)

  return {
    fromDateState,
    toDateState,
    comboboxFromTime,
    comboboxToTime,
    setFromDateState,
    setToDateState,
    fromDateTimeLocal,
    toDateTimeLocal,
  }
}
