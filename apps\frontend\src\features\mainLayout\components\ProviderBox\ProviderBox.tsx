import { endOfDay, startOfDay } from "date-fns"
import { useState } from "react"
import { generatePath, Link } from "react-router-dom"
import { useInterval } from "react-use"

import PowerMenuIcon from "@leviosa/assets/svg/PowerMenuIcon.svg?react"

import Icon from "components/Icon/Icon"
import usePermissions from "features/authentication/hooks/usePermissions"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { usePowerMenu } from "features/power-menu/PowerMenu.context"
import { PrivateRoutes, RouteStrings } from "routes/RouteStrings"
import { Button } from "ui"
import { ProviderBadgeMenu } from "ui/components/ProviderBadgeMenu/ProviderBadgeMenu"

import { PermissionKey, useProviderBoxQuery } from "generated/graphql"

import UpcomingEvents from "../UpcomingEvents/UpcomingEvents"
import styles from "./ProviderBox.module.css"

const fiveMinutesInMilliseconds = 300000
const thirtySecondsInMilliseconds = 30000

type ProviderBoxProps = {
  actor: GlobalDataWithNonNullableActor["actor"]
}

export const ProviderBox = ({ actor }: ProviderBoxProps) => {
  const { open } = usePowerMenu()
  const { hasPermission } = usePermissions()

  const [currentDate, setCurrentDate] = useState(new Date())
  const getCalendarPath = useGetCalendarPathObject()

  useInterval(() => {
    setCurrentDate(new Date())
  }, fiveMinutesInMilliseconds)

  // We want to poll for new events every 30 seconds to be able to provide the most up-to-date information
  const { data } = useProviderBoxQuery({
    variables: {
      id: actor.id,
      inputFilter: {
        participantProviderId: [actor.id],
        fromDate: startOfDay(currentDate),
        toDate: endOfDay(currentDate),
        includeCanceled: false,
      },
    },
    pollInterval: thirtySecondsInMilliseconds,
  })

  const journalEntries = data?.provider?.journalEntries || []

  const upcomingEvents =
    data?.events.filter(({ toDate }) => new Date(toDate) > currentDate) || []

  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  const unhandledItemsCount =
    journalEntries.length + (data?.listItems.totalCount || 0)

  return (
    <div className={styles.wrapper}>
      {canViewSubjectJournal ? (
        <UpcomingEvents upcomingEvents={upcomingEvents} />
      ) : (
        <Button
          className={styles.invoiceOverviewButton}
          as={Link}
          // size="large"
          variant="clear"
          to={generatePath(RouteStrings.invoiceOverview)}
          icon={<Icon name="money-dollar-circle-line" />}
          data-testid="invoice-overview-header-button"
        />
      )}
      <div>
        <Button
          className={styles.providerBoxButton}
          as={Link}
          size="large"
          variant="clear"
          to={getCalendarPath(RouteStrings.calendar)}
          icon={<Icon name="calendar-2-line" fontSize={24} />}
          data-testid="provider-box-calendar"
        >
          {upcomingEvents.length}
        </Button>

        <Button
          as={Link}
          className={styles.providerBoxButton}
          size="large"
          variant="clear"
          to={PrivateRoutes.worklist}
          icon={<Icon name="list-check-3" fontSize={24} />}
        >
          {unhandledItemsCount}
        </Button>
      </div>

      {/* Needs div b/c Aria component returns 2x DOM elements and breaks css grid */}
      <div className={styles.providerBadgeInfo}>
        <ProviderBadgeMenu {...actor} />
      </div>

      <button
        aria-label="Power menu"
        data-testid="power-menu-button"
        onClick={open}
        className={styles.powerMenuButton}
        type="button"
      >
        <PowerMenuIcon />
      </button>
    </div>
  )
}
