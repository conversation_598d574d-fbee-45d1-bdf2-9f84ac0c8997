use super::{CustomerService, FeedbackData};
// use crate::errors::Error; // Unused
use async_trait::async_trait;
use std::sync::RwLock;

pub struct CustomerServiceMock {
    request_history: RwLock<Vec<FeedbackData>>,
}

impl CustomerServiceMock {
    pub fn new() -> Self {
        Self {
            request_history: RwLock::new(vec![]),
        }
    }

    pub fn request_history(&self) -> Vec<FeedbackData> {
        self.request_history
            .read()
            .expect("Customer Feedback History Is Poisoned")
            .clone()
    }
}

#[async_trait]
impl CustomerService for CustomerServiceMock {
    async fn create_feedback(&self, feedback: FeedbackData) -> crate::errors::Result<()> {
        let mut auth = self.request_history.write().unwrap();
        (*auth).push(feedback);

        Ok(())
    }
}
