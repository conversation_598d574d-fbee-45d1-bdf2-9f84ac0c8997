import {
  addDays,
  addMonths,
  endOfDay,
  endOfMonth,
  startOfDay,
  startOfMonth,
  startOfWeek,
  subMonths,
} from "date-fns"
import { View } from "react-big-calendar"

export default (currentDate: Date, view: View) => {
  const startOfMonthDate = startOfMonth(currentDate)
  const endOfMonthDate = endOfMonth(currentDate)

  if (view === "month") {
    return {
      fromDate: subMonths(startOfMonthDate, 1),
      toDate: addMonths(endOfMonthDate, 1),
    }
  }

  if (view === "week" || view === "work_week") {
    const fromDate = startOfWeek(currentDate, { weekStartsOn: 1 })

    const toDate =
      view === "work_week"
        ? endOfDay(addDays(fromDate, 4)) // End of week is Friday
        : endOfDay(addDays(fromDate, 6)) // End of week is Sunday

    return {
      fromDate,
      toDate,
    }
  }

  if (view === "day") {
    return {
      fromDate: startOfDay(currentDate),
      toDate: endOfDay(currentDate),
    }
  }

  // Return month view for month view
  return {
    fromDate: startOfMonthDate,
    toDate: endOfMonthDate,
  }
}
