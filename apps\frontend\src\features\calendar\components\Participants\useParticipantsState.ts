import { useImmer } from "use-immer"

import { Participant } from "./Participants"

export type UseParticipantsState = {
  selectedParticipants: Participant[]

  onSelect: (option: Participant) => void
  onUpdate: <K extends keyof Participant>(
    userId: Participant["userId"],
    property: K,
    value: Participant[K]
  ) => void
  onRemove: (participant: Participant) => void
}

export const useParticipantsState = (participants?: Participant[]) => {
  const [selectedParticipants, setSelectedParticipants] = useImmer<
    Participant[]
  >(participants ?? [])

  const onSelect = (option: Participant) => {
    const hasOption = selectedParticipants.find(
      (v) => v.userId === option.userId
    )

    if (!hasOption) {
      setSelectedParticipants((draft) => {
        draft.push(option)
      })
    }
  }

  const onUpdate = <K extends keyof Participant>(
    userId: Participant["userId"],
    property: K,
    value: Participant[K]
  ) => {
    setSelectedParticipants((draft) => {
      const userIndex = draft.findIndex((u) => u.userId === userId)
      if (userIndex !== -1) {
        draft[userIndex][property] = value
      }
    })
  }

  const onRemove = (participant: Participant) => {
    const participants = selectedParticipants.filter(
      (u) => u.userId !== participant.userId
    )

    setSelectedParticipants(participants)
  }

  return {
    selectedParticipants,
    onSelect,
    onUpdate,
    onRemove,
    setSelectedParticipants,
  }
}
