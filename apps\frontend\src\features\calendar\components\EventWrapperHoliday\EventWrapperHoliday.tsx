import c from "classnames"

import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import { color } from "styles/colors"
import { ButtonText } from "ui"

import styles from "./EventWrapperHoliday.module.css"

type EventWrapperHolidayProps = {
  event: CalendarEvent<"holiday">
}

export const EventWrapperHoliday = ({ event }: EventWrapperHolidayProps) => {
  return (
    <ButtonText
      size="small"
      className={c(
        styles.wrap,
        color.levGreen.dark,
        "rbc-event",
        "rbc-event-holiday"
      )}
    >
      {event.title}
    </ButtonText>
  )
}
