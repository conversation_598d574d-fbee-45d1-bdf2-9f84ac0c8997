import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

import { Button, notification } from "ui"

import { useRemoveSessionCookieMutation } from "generated/graphql"

import styles from "./RemoveSessionButton.module.css"

export const RemoveSessionButton = () => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "auth" })
  const navigate = useNavigate()

  const [removeSessionCookie, { loading: isRemovingSession }] =
    useRemoveSessionCookieMutation({
      onCompleted: () => {
        navigate(0)
      },
      onError: () => {
        notification.create({
          message: tRoutes("sessionCookieRemovalError"),
          status: "error",
        })
      },
    })

  const handleRemoveSession = () => {
    removeSessionCookie()
  }

  return (
    <Button
      className={styles.removeSessionButton}
      onClick={handleRemoveSession}
      size="large"
      variant="clear"
      loading={isRemovingSession}
    >
      {tRoutes("loginAsSomeoneElse")}
    </Button>
  )
}
