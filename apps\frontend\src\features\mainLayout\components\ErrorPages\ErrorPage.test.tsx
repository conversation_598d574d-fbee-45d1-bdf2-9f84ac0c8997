import { render, screen } from "@testing-library/react"
import { describe, it, expect, vi } from "vitest"

import ErrorPage from "./ErrorPage"

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => (key === "goBack" ? "Go Back" : key),
  }),
}))

describe("ErrorPage", () => {
  const defaultProps = {
    illustration: <div data-testid="test-illustration">Test Illustration</div>,
    heading: "Error Title",
    message: "Error Message",
    suggestions: ["Try refreshing", "Check your connection"],
    subMessage: "Additional details about the error",
  }

  it("renders with all elements", () => {
    render(<ErrorPage {...defaultProps} />)

    expect(screen.getByTestId("test-illustration")).toBeInTheDocument()
    expect(screen.getByText("Error Title")).toBeInTheDocument()
    expect(screen.getByText("Error Message")).toBeInTheDocument()
    expect(screen.getByText("Try refreshing")).toBeInTheDocument()
    expect(screen.getByText("Check your connection")).toBeInTheDocument()
    expect(
      screen.getByText("Additional details about the error")
    ).toBeInTheDocument()
  })

  it("renders without suggestions when not provided", () => {
    render(<ErrorPage {...defaultProps} suggestions={[]} />)

    expect(screen.queryByText("Try refreshing")).not.toBeInTheDocument()
    expect(screen.queryByText("Check your connection")).not.toBeInTheDocument()
  })

  it("renders without subMessage when not provided", () => {
    render(<ErrorPage {...defaultProps} subMessage={undefined} />)

    expect(
      screen.queryByText("Additional details about the error")
    ).not.toBeInTheDocument()
  })
})
