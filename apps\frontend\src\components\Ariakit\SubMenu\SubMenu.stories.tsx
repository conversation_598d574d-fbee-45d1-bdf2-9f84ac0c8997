import { <PERSON>a, <PERSON> } from "@storybook/react-vite"

import Icon from "../../Icon/Icon"
import { Menu, MenuProvider } from "../Menu/Menu"
import { MenuButton } from "../Menu/MenuButton/MenuButton"
import { MenuItem } from "../Menu/MenuItem/MenuItem"
import { SubMenu } from "./SubMenu"

export default {
  title: "Ariakit/SubMenu",
  component: SubMenu,
} as Meta

export const SubMenuExample: Story = () => {
  return (
    <MenuProvider>
      <MenuButton iconEnd={<Icon name="arrow-down-s-line" />}>
        <span className="label">Edit</span>
      </MenuButton>

      <Menu hasSubmenu>
        <MenuItem>Item 1</MenuItem>
        <MenuItem>Item 2</MenuItem>
        <SubMenu label="Item 3">
          <MenuItem>Item 3.1</MenuItem>
          <MenuItem>Item 3.2</MenuItem>
          <MenuItem>Item 3.3</MenuItem>
        </SubMenu>
        <MenuItem>Item 4</MenuItem>
        <SubMenu label="Item 5">
          <MenuItem>Item 5.1</MenuItem>
          <MenuItem>Item 5.2</MenuItem>
          <MenuItem>Item 5.3</MenuItem>
          <SubMenu label="Item 5.4">
            <MenuItem>Item 5.4.1</MenuItem>
            <MenuItem>Item 5.4.2</MenuItem>
            <MenuItem>Item 5.4.3</MenuItem>
          </SubMenu>
        </SubMenu>
      </Menu>
    </MenuProvider>
  )
}
