export const getSearchParams = (searchParams: URLSearchParams) => {
  const providerSearchParam = searchParams.get("provider")
  const subjectSearchParam = searchParams.get("subject")
  const locationSearchParam = searchParams.get("location")

  const defaultProviderValue =
    (providerSearchParam &&
      decodeURIComponent(providerSearchParam)?.split(",")) ||
    []

  const defaultSubjectValue =
    (subjectSearchParam &&
      decodeURIComponent(subjectSearchParam)?.split(",")) ||
    []

  const defaultLocationValue =
    (locationSearchParam &&
      decodeURIComponent(locationSearchParam)?.split(",")) ||
    []

  return {
    defaultProviderValue,
    defaultSubjectValue,
    defaultLocationValue,
  }
}
