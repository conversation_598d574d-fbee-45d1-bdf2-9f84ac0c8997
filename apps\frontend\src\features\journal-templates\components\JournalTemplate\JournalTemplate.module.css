.wrap {
  background-color: var(--color-lev-blue-on-white-hover);
  border-radius: var(--radius-button-half);
  min-width: 660px;
  padding: 8px 16px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding-bottom: 24px;
  margin-bottom: 60px;
  height: fit-content;
}

.header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.topHeading {
  display: flex;
  justify-content: space-between;
  margin: -8px -16px 8px -16px;
}

.heading {
  display: grid;
  align-items: center;
  margin-top: 16px;
  gap: 8px;
}

.headingIcon {
  height: 24px;
  width: 24px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 7px;
}

.subHeading {
  display: flex;
  align-items: center;
  gap: 16px;
}

.tagsContainer {
  display: flex;
}

/* Styles for tags in template header when there are more than 1 grouped together */
.tagsContainer > *:nth-child(1):not(:only-child) {
  margin-right: -16px;
  padding-right: 24px;
}

.updatedAt {
  align-items: center;
  line-height: 0;
}

.creator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--color-brand-primary-blue);
  border-radius: var(--radius-button-half);
  background-color: var(--color-blue-primary-on-white-active);
  padding: 4px 10px;
}

.creator div {
  color: var(--color-brand-primary-blue);
}

.footer {
  display: flex;
  justify-content: flex-end;
}

.saveStatus {
  position: relative;
  margin-top: -16px;
}

.block {
  padding: 12px;
  background-color: var(--color-white);
  border-radius: var(--radius-button-half);
  box-shadow: 0px 0px 10px 0px rgba(198, 201, 219, 0.4);
}

.addSupplements {
  display: flex;
  gap: 8px;
  align-items: center;
}

.supplementsWrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.supplements {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.editorWrapper {
  position: relative;
  display: grid;
  gap: 32px;
}

.section {
  display: grid;
  gap: 8px;
}

.sectionContent {
  background-color: white;
  min-height: 36px;
  border-radius: var(--radius-button-half);
  padding: 8px 16px;
}

.viewSection {
  display: grid;
  gap: 8px;
}

.deleteSectionButton {
  color: var(--color-brand-primary-blue);
}

.noContent {
  color: var(--color-grey-d2);
}

.clickable {
  cursor: pointer;
}

.status {
  padding: 8px 16px;
  width: fit-content;
  height: fit-content;
  border-radius: 0 var(--radius-button-half);
  margin-left: auto;
}

.documentType {
  padding: 8px 16px;
  width: fit-content;
  height: fit-content;
  border-radius: var(--radius-button-half) 0;
}

.sectionHeading {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sectionHeading > button {
  flex-shrink: 0;
}

.switch {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-self: flex-end;
  margin: 0 8px;
}

.visuallyHidden {
  position: absolute;
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  overflow: hidden;
  white-space: nowrap;
}

.emptyState {
  height: calc(100% - 24px);
  align-items: center;
  justify-content: center;
}
