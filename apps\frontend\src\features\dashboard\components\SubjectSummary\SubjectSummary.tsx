import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link } from "react-router-dom"

import { Tab, <PERSON>bList, TabPanel, TabProvider } from "components/Ariakit/Tab/Tab"
import Icon from "components/Icon/Icon"
import { PersonSummaryOverviewList } from "components/PersonSummaryOverviewList/PersonSummaryOverviewList"
import Restricted from "features/authentication/components/Restricted/Restricted"
import EncounterCardList from "features/dashboard/components/EncounterCardList/EncounterCardList"
import { usePrintSubjectLabel } from "features/mainLayout/components/PrintSubjectLabel/PrintSubjectLabelContext"
import { usePersistedPreference } from "hooks/usePersistedPreference"
import { RouteStrings } from "routes/RouteStrings"
import {
  Button,
  Loading,
  notification,
  PersonSummary,
  PhoneNumberInput,
} from "ui"
import ClinicalCodingList from "ui/components/ClinicalCodingList/ClinicalCodingList"
import {
  ContactInformation,
  ContactInformationItem,
} from "ui/components/ContactInformation/ContactInformation"
import { InlineTextForm } from "ui/components/InlineTextForm/InlineTextForm"

import {
  PermissionKey,
  SubjectBasicInfoFragmentFragment,
  useGetSubjectInsuranceStatusQuery,
  useGetSubjectSummaryQuery,
  useUpdateSubjectMutation,
} from "generated/graphql"

import { AddressView } from "../AddressView/AddressView"
import { OtherInformation } from "../OtherInformation/OtherInformation"
import { StaticInformation } from "../StaticInformation/StaticInformation"
import SubjectCommunication from "../SubjectCommunication/SubjectCommunication"
import styles from "./SubjectSummary.module.css"

type SubjectSummaryTabs =
  | "contact"
  | "clinical-codings"
  | "encounters"
  | "communications"
  | "static-information"

export type SubjectSummaryProps = SubjectBasicInfoFragmentFragment & {
  onRequestClose: () => void
}

const SubjectSummary = ({
  id: subjectId,
  age,
  gender,
  name,
  personaId,
  phoneNumber,
  onRequestClose,
}: SubjectSummaryProps) => {
  const { data, loading, error } = useGetSubjectSummaryQuery({
    variables: { subjectId },
  })

  const { data: insuranceStatusData, error: insuranceStatusError } =
    useGetSubjectInsuranceStatusQuery({
      variables: { subjectId },
    })

  const { t } = useTranslation()
  const { t: tSS } = useTranslation("features", { keyPrefix: "subjectSummary" })
  const { setShowPrintSubjectLabel } = usePrintSubjectLabel()

  const [selectedTab, setSelectedTab] =
    usePersistedPreference<SubjectSummaryTabs>({
      key: "subjectSummaryTab",
      defaultValue: "encounters",
    })

  const [showEmailForm, setShowEmailForm] = useState(false)
  const [showPhoneNumberForm, setShowPhoneNumberForm] = useState(false)

  const [updateSubject] = useUpdateSubjectMutation()

  const {
    address,
    birthDate,
    email,
    encounters,
    location,
    notificationsConfig,
    other,
    subjectHealthProfile,
    subjectStaticData,
  } = data?.subject ?? {}

  const insuranceStatus = insuranceStatusData?.subjectInsuranceStatus

  const countryCode = "+354"

  useEffect(() => {
    if (error && !data) {
      notification.create({
        message: tSS("errorFetchingSSData"),
        status: "error",
      })
    }
  }, [error, data])

  const handleTabChange = (tabId: string | null | undefined) => {
    if (tabId) {
      setSelectedTab(tabId as SubjectSummaryTabs)
    }
  }

  return (
    <PersonSummary
      name={name}
      personaId={personaId}
      onRequestClose={onRequestClose}
      headerContent={
        <Button
          onClick={() => setShowPrintSubjectLabel(true)}
          icon={<Icon name="printer-line" />}
          variant="clear"
          className={styles.printButton}
        />
      }
    >
      <PersonSummaryOverviewList
        birthDate={birthDate ?? ""}
        locationId={location?.id}
        gender={gender}
        encounterId={encounters?.[0] ? encounters[0].id : undefined}
        age={age}
        healthProfileId={subjectHealthProfile?.id}
        dietaryAllowanceId={subjectHealthProfile?.dietaryAllowanceId}
      />

      <TabProvider
        defaultSelectedId={selectedTab || "encounters"}
        setSelectedId={handleTabChange}
      >
        <TabList aria-label="Details">
          <Tab id="contact">{tSS("tabContact")}</Tab>

          <Restricted to={PermissionKey.SubjectJournalJournalEntryView}>
            <Tab id="clinical-codings">{tSS("tabClinicalSummary")}</Tab>
          </Restricted>

          <Restricted
            toAny={[
              PermissionKey.SubjectJournalJournalEntryView,
              PermissionKey.BillingInvoiceCreate,
            ]}
          >
            <Tab id="static-information">{tSS("tabBackground")}</Tab>
          </Restricted>

          <Restricted
            toAny={[
              PermissionKey.SubjectJournalJournalEntryView,
              PermissionKey.CalendarView,
            ]}
          >
            <Tab id="encounters">{tSS("tabEncounters")}</Tab>
          </Restricted>
          <Tab id="communications">{tSS("tabCommunications")}</Tab>
        </TabList>

        <Restricted to={PermissionKey.SubjectJournalJournalEntryView}>
          <TabPanel tabId="clinical-codings" className={styles.tabPanel}>
            <ClinicalCodingList
              subjectId={subjectId}
              onClick={onRequestClose}
            />
          </TabPanel>
        </Restricted>

        <Restricted
          toAny={[
            PermissionKey.SubjectJournalJournalEntryView,
            PermissionKey.BillingInvoiceCreate,
          ]}
        >
          <TabPanel tabId="static-information" className={styles.tabPanel}>
            <StaticInformation
              subjectId={subjectId}
              subjectStaticData={subjectStaticData || null}
              insuranceStatus={insuranceStatus}
              insuranceStatusError={insuranceStatusError?.message || null}
            />
          </TabPanel>
        </Restricted>

        <Restricted
          toAny={[
            PermissionKey.SubjectJournalJournalEntryView,
            PermissionKey.CalendarView,
          ]}
        >
          <TabPanel tabId="encounters" className={styles.tabPanel}>
            <EncounterCardList subjectId={subjectId} />
          </TabPanel>
        </Restricted>

        <TabPanel tabId="communications" className={styles.tabPanel}>
          <SubjectCommunication
            subjectId={subjectId}
            subjectPhoneNumber={phoneNumber}
            subjectNotificationConfig={notificationsConfig || []}
          />
        </TabPanel>

        <TabPanel tabId="contact" className={styles.tabPanel}>
          <div className={styles.contactInformationWrapper}>
            <ContactInformation>
              <ContactInformationItem icon={"mail-line"} label={t("Email")}>
                {showEmailForm ? (
                  <InlineTextForm
                    value={email || ""}
                    label={t("clickToEdit")}
                    onSubmit={({ value }) => {
                      // this check is to make TS happy when doing the optimistic response
                      if (data) {
                        updateSubject({
                          variables: {
                            id: subjectId,
                            input: {
                              email: {
                                set: value,
                              },
                            },
                          },
                          optimisticResponse: {
                            __typename: "Mutation",
                            updateSubject: {
                              ...data.subject,
                              email: value,
                            },
                          },
                        })
                        setShowEmailForm(false)
                      }
                    }}
                    onCancel={() => {
                      setShowEmailForm(false)
                    }}
                    size={"small"}
                    actionIcons
                  />
                ) : (
                  <div className={styles.contactInformationItem}>
                    {!email && loading && <Loading showText={false} />}
                    {!email && !loading && t("n/a")}
                    {email && !loading && (
                      <a href={`mailto:${email}`}>{email}</a>
                    )}
                    <Button
                      onClick={() => setShowEmailForm(true)}
                      disabled={!data}
                      icon={<Icon name="pencil-line" fontSize={16} />}
                      variant="clear"
                      className={styles.editEmailButton}
                    />
                  </div>
                )}
              </ContactInformationItem>
              <ContactInformationItem icon={"phone-line"} label={t("Phone")}>
                {showPhoneNumberForm ? (
                  <InlineTextForm
                    InputComponent={PhoneNumberInput}
                    value={phoneNumber || ""}
                    label={t("clickToEdit")}
                    onSubmit={({ value }) => {
                      // this check is to make TS happy when doing the optimistic response
                      if (data) {
                        const number = Number(value)
                        if (value && isNaN(number)) {
                          notification.create({
                            status: "error",
                            message: tSS("errorPhoneNumberNotValid"),
                          })
                          return
                        }

                        updateSubject({
                          variables: {
                            id: subjectId,
                            input: {
                              phoneNumber: {
                                set: value || null,
                              },
                            },
                          },
                          optimisticResponse: {
                            __typename: "Mutation",
                            updateSubject: {
                              // the GQL document guarantees we're fetching the whole Subject object
                              ...data.subject,
                              phoneNumber: value,
                            },
                          },
                        })
                        setShowPhoneNumberForm(false)
                      }
                    }}
                    onCancel={() => {
                      setShowPhoneNumberForm(false)
                    }}
                    size={"small"}
                    actionIcons
                  />
                ) : (
                  <div className={styles.contactInformationItem}>
                    <a
                      href={`tel:${countryCode}${phoneNumber}`}
                    >{`${countryCode} ${phoneNumber}`}</a>
                    <Button
                      onClick={() => setShowPhoneNumberForm(true)}
                      disabled={!data}
                      icon={<Icon name="pencil-line" fontSize={16} />}
                      variant="clear"
                      className={styles.editPhoneNumberButton}
                    />
                  </div>
                )}
              </ContactInformationItem>

              <ContactInformationItem
                icon={"map-pin-line"}
                label={t("Address")}
              >
                <AddressView address={address || null} />
              </ContactInformationItem>
            </ContactInformation>

            {/* <ContactInformation>
            <ContactInformationItem
              icon={"UsersGroupRounded"}
              label={t("Relatives")}
            >
              n/a
            </ContactInformationItem>
          </ContactInformation> */}

            <ContactInformation>
              <ContactInformationItem label={t("Other")}>
                <OtherInformation id={subjectId} other={other || null} />
              </ContactInformationItem>
            </ContactInformation>

            <Link
              to={generatePath(RouteStrings.subjectEdit, { subjectId })}
              onClick={onRequestClose}
            >
              {tSS("editContactInfo")}
            </Link>
          </div>
        </TabPanel>
      </TabProvider>
    </PersonSummary>
  )
}

export default SubjectSummary
