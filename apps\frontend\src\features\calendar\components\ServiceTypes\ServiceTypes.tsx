import { MenuProvider } from "@ariakit/react"
import c from "classnames"
import { useTranslation } from "react-i18next"
import { generatePath, Link } from "react-router-dom"

import { Menu, MenuButton, MenuItem } from "components/Ariakit"
import Icon from "components/Icon/Icon"
import useCalendarState from "features/calendar/hooks/useCalendarState"
import { RouteStrings } from "routes/RouteStrings"
import { calendarColorMap } from "styles/colors"
import { Button, Heading, Text } from "ui"

import { ServiceTypeFragmentFragment } from "generated/graphql"

import styles from "./ServiceTypes.module.css"

type ServiceTypeList = {
  id: ServiceTypeFragmentFragment["id"]
  name: ServiceTypeFragmentFragment["name"]
  color: ServiceTypeFragmentFragment["color"]
}[]

export type ServiceTypesProps = {
  providers: {
    name: string
    id: string
  }[]
  serviceTypes: ServiceTypeList
  className?: string
}

const ServiceTypes = ({
  providers,
  serviceTypes,
  className,
}: ServiceTypesProps) => {
  const { t } = useTranslation()

  const { view } = useCalendarState()
  if (view === "month") return null

  return (
    <div className={c(styles.container, className)}>
      <Heading as="h2">{t("Services")}</Heading>

      {serviceTypes.length > 0 ? (
        <ul className={styles.serviceTypes}>
          {serviceTypes.map(({ id, name, color }) => {
            return (
              <li key={id} className={styles.serviceType}>
                <div
                  className={c(styles.color, calendarColorMap[color].light)}
                />
                <Text size="small">{name}</Text>
                {/* <Switch />} */}
              </li>
            )
          })}
        </ul>
      ) : null}

      {providers?.length && !serviceTypes.length ? (
        <Text size="small" className={styles.noServicesPanel}>
          {providers.length === 1
            ? `${
                providers[0]?.name
              } does not have availability schedule set up for
          this
          ${view === "day" ? " day" : " week"}.`
            : `The selected providers do not have availability schedules set up for ${
                view === "day" ? " day" : " week"
              }`}
        </Text>
      ) : null}

      {providers?.length === 1 ? (
        <Button
          as={Link}
          to={generatePath(RouteStrings.calendarSchedule, {
            providerId: providers[0].id,
          })}
          variant="clear"
          size="small"
          icon={<Icon name="settings-line" />}
          className={styles.settingsButton}
        >
          {t("Manage Schedule")}
        </Button>
      ) : (
        <MenuProvider animated>
          <MenuButton
            variant="clear"
            size="small"
            icon={<Icon name="settings-line" />}
            className={styles.settingsButton}
          >
            {t("Manage Schedules")}
          </MenuButton>
          <Menu>
            {providers.map(({ id, name }) => (
              <MenuItem
                render={
                  <Link
                    to={generatePath(RouteStrings.calendarSchedule, {
                      providerId: id,
                    })}
                  />
                }
                key={id}
              >
                {name}
              </MenuItem>
            ))}
          </Menu>
        </MenuProvider>
      )}
    </div>
  )
}

export default ServiceTypes
