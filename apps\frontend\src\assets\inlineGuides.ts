export enum InlineGuideId {
  CREATE_TEAM = "CREATE_TEAM",
  CLINICALCODING_ALLERGY_WARNING = "CLINICALCODING_ALLERGY_WARNING",
  DRUG_INTENTION_SHORTHAND = "DRUG_INTENTION_SHORTHAND",
  DRUGORDER_COMMENTTODISPENSER = "DRUGORDER_COMMENTTODISPENSER",
  DRUG_R_MARKED = "DRUG_R_MARKED",
  DEFAULT_ISSUER = "DEFAULT_ISSUER",
}

type InlineGuide = Record<InlineGuideId, { content: string }>

/* COMEBACK; misses localisation */
export const inlineGuides: InlineGuide = {
  [InlineGuideId.CLINICALCODING_ALLERGY_WARNING]: {
    content: `Registration of allergies is very basic currently because of lack of Snomed codes from EL.`,
  },
  [InlineGuideId.CREATE_TEAM]: {
    content: `A Team cannot be deleted once created. Be careful about deciding a Team's purpose before creating one and select service carefully.
Name and description can be changed later.`,
  },
  [InlineGuideId.DRUG_INTENTION_SHORTHAND]: {
    content: `## Shorthand
Shorthand allows you to quickly create drug instructions and automatically calculates needed amount of packages and max daily units. Examples:
 - 1x3: 1 tablet 3 times a day.
 - 1x1/8h: 1 tablet every 8 hours.
 - 1x1/1w: 1 table once a week.
 - 1x3 prn: 1 tablet as needed, max. 3 per day.
 - 1/4h prn: 1 tablet as needed, at min. 4 hours interval.

`,
  },
  // NOTE has instructions specific for VNR drugs and which may be irrelevant outside of Iceland.
  [InlineGuideId.DRUGORDER_COMMENTTODISPENSER]: {
    content: `Use for custom message to the pharmacist. It is hidden to patient (but recorded in Subject-Journal thus not entirely private).`,
  },
  [InlineGuideId.DRUG_R_MARKED]: {
    content: "Brand substitution not permitted",
  },
  [InlineGuideId.DEFAULT_ISSUER]: {
    content:
      "Select which legal entity will be selected by default when this provider issues an invoice. If no legal entity is selected, the provider will be set as the invoice issuer.",
  },
}
