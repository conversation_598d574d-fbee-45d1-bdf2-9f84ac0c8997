import { PaymentStatus } from "generated/graphql"

import { Invoices } from "./EventInvoiceIndicator"
import { getInvoiceWithLowestStatus } from "./getInvoiceWithLowestStatus"

describe("getInvoiceWithLowestStatus", () => {
  const invoices: Invoices = [
    {
      __typename: "Invoice",
      id: "I1",
      issued: true,
      paymentStatus: PaymentStatus.Paid,
    },
    {
      __typename: "Invoice",
      id: "I2",
      issued: true,
      paymentStatus: PaymentStatus.Unpaid,
    },
    {
      __typename: "Invoice",
      id: "I3",
      issued: true,
      paymentStatus: PaymentStatus.ClaimCreated,
    },
    {
      __typename: "Invoice",
      id: "I4",
      issued: false,
      paymentStatus: PaymentStatus.Unpaid,
    },
  ]

  it("should return draft", () => {
    const result = getInvoiceWithLowestStatus(invoices)
    expect(result).toEqual("draft")
  })

  it("should return the lowest payment status if all are issued = unpaid", () => {
    const issuedInvoices = invoices.slice(0, 3)
    const result = getInvoiceWithLowestStatus(issuedInvoices)
    expect(result).toEqual(PaymentStatus.Unpaid)
  })

  it("should return null if the invoice list is empty", () => {
    const result = getInvoiceWithLowestStatus([])
    expect(result).toBeNull()
  })

  it("should return the payment status (paid) for the invoice if there is only one", () => {
    const singleInvoice = [invoices[0]]
    const result = getInvoiceWithLowestStatus(singleInvoice)
    expect(result).toEqual(PaymentStatus.Paid)
  })

  it("should return the payment status claim created", () => {
    const invoices: Invoices = [
      {
        __typename: "Invoice",
        id: "1",
        issued: true,
        paymentStatus: PaymentStatus.ClaimCreated,
      },
      {
        __typename: "Invoice",
        id: "2",
        issued: true,
        paymentStatus: PaymentStatus.Paid,
      },
    ]
    const result = getInvoiceWithLowestStatus(invoices)
    expect(result).toEqual(PaymentStatus.ClaimCreated)
  })

  it("should handle invoices with the same payment status correctly", () => {
    const sameStatusInvoices: Invoices = [
      {
        __typename: "Invoice",
        id: "1",
        issued: true,
        paymentStatus: PaymentStatus.Unpaid,
      },
      {
        __typename: "Invoice",
        id: "2",
        issued: true,
        paymentStatus: PaymentStatus.Unpaid,
      },
    ]
    const result = getInvoiceWithLowestStatus(sameStatusInvoices)
    expect(result).toEqual(PaymentStatus.Unpaid)
  })
})
