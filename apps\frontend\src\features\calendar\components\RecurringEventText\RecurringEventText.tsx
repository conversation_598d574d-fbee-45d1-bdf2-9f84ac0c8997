import { format } from "date-fns"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Label } from "ui"

import { EventRepeatInterval, EventInstanceQuery } from "generated/graphql"

import styles from "./RecurringEventText.module.css"

type RecurringEventTextProps = {
  eventRecurrence: EventInstanceQuery["eventInstance"]["eventRecurrence"]
}

export const RecurringEventText = ({
  eventRecurrence,
}: RecurringEventTextProps) => {
  const { t } = useTranslation()

  if (eventRecurrence === null) return null

  const { toDate, repeatInterval } = eventRecurrence

  const formattedDate = format(new Date(toDate), "MMMM d, yyyy")

  const getContent = (): string => {
    switch (repeatInterval) {
      case EventRepeatInterval.Weekly:
        return t("calendar.repeatIntervalWeek", { date: formattedDate })
      case EventRepeatInterval.EveryWeekday:
        return t("calendar.repeatIntervalWeekDay", { date: formattedDate })
      case EventRepeatInterval.Monthly:
        return t("calendar.repeatIntervalMonth", { date: formattedDate })
      default:
        return ""
    }
  }

  const intervalText = getContent()

  return (
    <span className={styles.wrap}>
      <Icon name="history-line" />
      <Label size="small">{intervalText}</Label>
    </span>
  )
}
