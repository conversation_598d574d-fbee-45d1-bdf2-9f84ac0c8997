use super::{InvoiceIdGQL, InvoiceObject};
use crate::accounts::user::controllers::ProviderObject;
use crate::billing::{controllers::BillingCodeInterface, enums::BillingCodeTypeGQL};
use crate::lib::context::{<PERSON>osaAuthContext, LeviosaContext};
use crate::lib::{
    context::LeviosaLoaderContext,
    errors::Result,
    graphql::{gql_id, graphql_wrapper, IntoGQL},
};
use crate::subject_journal::controllers::{EncounterIdGQL, EncounterObject};
use crate::subject_journal::loaders::EncounterKey;
use async_graphql::{Context, InputObject, Object};
use chrono::{DateTime, Utc};
use leviosa_domain::billing::{
    BillingCodeClinicSpecificId, BillingCodeNhiId, CreateInvoiceLineForCodeCommand,
    DeleteInvoiceLineCommand, InvoiceId, InvoiceLine, InvoiceLineId, MoveInvoiceLineCommand,
    NhiInvoiceTotalCalculator,
};
use leviosa_domain::command::{CommandPipeline, InvoiceTotalCalculationHandler};
use uuid::Uuid;

gql_id!(InvoiceLineId);
graphql_wrapper!(InvoiceLine);

#[derive(Default)]
pub struct InvoiceLineMutations;

#[Object]
impl InvoiceLineMutations {
    async fn create_invoice_line(
        &self,
        ctx: &Context<'_>,
        input: CreateInvoiceLineInput,
    ) -> Result<Vec<InvoiceLineObject>> {
        let command = CreateInvoiceLineForCodeCommand {
            encounter_id: input.encounter_id.into(),
            invoice_id: input.invoice_id.into(),
            billing_code_id: input.billing_code_id,
            billing_code_type: input.billing_code_type.into(),
            quantity: input.quantity,
        };

        let handler = InvoiceTotalCalculationHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            NhiInvoiceTotalCalculator::new_with_contracts(ctx.get()?.nhi_service(), ctx.auth_data()?.token()?),
        );

        let invoice_lines = CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(invoice_lines.into_gql())
    }

    async fn delete_invoice_line(
        &self,
        ctx: &Context<'_>,
        input: DeleteInvoiceLineInput,
    ) -> Result<InvoiceLineObject> {
        let command = DeleteInvoiceLineCommand {
            id: input.id.into(),
        };

        let invoice_line = CommandPipeline::handle(
            InvoiceTotalCalculationHandler::new(
                ctx.tx_manager()?,
                ctx.auth_data()?.user()?,
                NhiInvoiceTotalCalculator::new_with_contracts(ctx.get()?.nhi_service(), ctx.auth_data()?.token()?),
            ),
            &ctx.auth_data()?,
            command,
        )
        .await?;

        Ok(invoice_line.into_gql())
    }

    // Moves invoice line between invoices
    async fn move_invoice_line(
        &self,
        ctx: &Context<'_>,
        input: MoveInvoiceLineInput,
    ) -> Result<InvoiceLineObject> {
        let command = MoveInvoiceLineCommand {
            id: input.id.into(),
            invoice_id: input.invoice_id.into(),
        };
        let handler = InvoiceTotalCalculationHandler::new(
            ctx.tx_manager()?,
            ctx.auth_data()?.user()?,
            NhiInvoiceTotalCalculator::new_with_contracts(ctx.get()?.nhi_service(), ctx.auth_data()?.token()?),
        );

        let invoice = CommandPipeline::handle(handler, &ctx.auth_data()?, command).await?;

        Ok(invoice.into_gql())
    }
}

#[Object(name = "InvoiceLine")]
impl InvoiceLineObject {
    pub async fn id(&self) -> InvoiceLineIdGQL {
        self.0.id().into()
    }

    pub async fn invoice(&self, ctx: &Context<'_>) -> Result<InvoiceObject> {
        let invoice = ctx
            .db_loader()?
            .load_one::<InvoiceId>(self.0.invoice_id().into())
            .await?
            .ok_or_else(|| anyhow::anyhow!("Invoice not found"))?;

        Ok(invoice.into_gql())
    }

    pub async fn encounter(&self, ctx: &Context<'_>) -> Result<Option<EncounterObject>> {
        if self.0.encounter_id().is_some() {
            let encounter = ctx
                .db_loader()?
                .load_one(EncounterKey(self.0.encounter_id().unwrap()))
                .await?
                .ok_or_else(|| anyhow::anyhow!("Encounter not found"))?;
            Ok(Some(encounter.into_gql()))
        } else {
            Ok(None)
        }
    }

    pub async fn billing_code_type(&self) -> BillingCodeTypeGQL {
        self.0.billing_code_type().into()
    }

    pub async fn billing_code(&self, ctx: &Context<'_>) -> Result<BillingCodeInterface> {
        match self.0.billing_code_type().into() {
            BillingCodeTypeGQL::Nhi => {
                let billing_code = ctx
                    .db_loader()?
                    .load_one::<BillingCodeNhiId>(self.0.billing_code_id().into())
                    .await?
                    .ok_or_else(|| anyhow::anyhow!("Billing code not found"))?;
                Ok(BillingCodeInterface::BillingCodeNhi(
                    billing_code.into_gql(),
                ))
            }
            BillingCodeTypeGQL::ClinicSpecific => {
                let billing_code = ctx
                    .db_loader()?
                    .load_one::<BillingCodeClinicSpecificId>(self.0.billing_code_id().into())
                    .await?
                    .ok_or_else(|| anyhow::anyhow!("Billing code not found"))?;
                Ok(BillingCodeInterface::BillingCodeClinicSpecific(
                    billing_code.into_gql(),
                ))
            }
        }
    }

    pub async fn unit_price(&self) -> f64 {
        self.0.unit_price()
    }

    pub async fn units(&self) -> f64 {
        self.0.units()
    }

    pub async fn quantity(&self) -> f64 {
        self.0.quantity()
    }

    pub async fn billable_quantity(&self) -> f64 {
        self.0.billable_quantity()
    }

    pub async fn vat(&self) -> f64 {
        self.0.vat()
    }

    pub async fn discount(&self) -> f64 {
        self.0.discount()
    }

    pub async fn total(&self) -> f64 {
        self.0.total()
    }

    pub async fn created_by(&self, ctx: &Context<'_>) -> Result<ProviderObject> {
        let provider = ctx
            .db_loader()?
            .load_one(self.0.created_by_id())
            .await?
            .ok_or_else(|| anyhow::anyhow!("Provider not found"))?;
        Ok(provider.into_gql())
    }

    pub async fn created_at(&self) -> DateTime<Utc> {
        self.0.created_at()
    }

    pub async fn updated_by(&self, ctx: &Context<'_>) -> Result<ProviderObject> {
        let provider = ctx
            .db_loader()?
            .load_one(self.0.updated_by_id())
            .await?
            .ok_or_else(|| anyhow::anyhow!("Provider not found"))?;
        Ok(provider.into_gql())
    }

    pub async fn updated_at(&self) -> DateTime<Utc> {
        self.0.updated_at()
    }

    pub async fn deleted_by(&self, ctx: &Context<'_>) -> Result<Option<ProviderObject>> {
        if let Some(provider_id) = self.0.deleted_by_id() {
            let provider = ctx
                .db_loader()?
                .load_one(provider_id)
                .await?
                .ok_or_else(|| anyhow::anyhow!("Provider not found"))?;
            Ok(Some(provider.into_gql()))
        } else {
            Ok(None)
        }
    }

    pub async fn deleted_at(&self) -> Option<DateTime<Utc>> {
        self.0.deleted_at()
    }
}

#[derive(InputObject)]
pub struct CreateInvoiceLineInput {
    encounter_id: EncounterIdGQL,
    invoice_id: InvoiceIdGQL,
    billing_code_id: Uuid,
    billing_code_type: BillingCodeTypeGQL,
    quantity: f64,
}

#[derive(InputObject)]
struct DeleteInvoiceLineInput {
    id: InvoiceLineIdGQL,
}

#[derive(InputObject)]
struct MoveInvoiceLineInput {
    id: InvoiceLineIdGQL,
    invoice_id: InvoiceIdGQL,
}
