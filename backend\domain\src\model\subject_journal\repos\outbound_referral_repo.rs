use super::SaveBlock;
use crate::{
    auth::{AuthenticatedUser, CheckTenant},
    entity_event::SaveEvent,
    errors::{NotFoundError, Result},
    subject_journal::{
        JournalEntryBlockStatus, JournalEntryBlockType, JournalEntryId, OutboundReceiver,
        OutboundReceiverId, OutboundReferral, OutboundReferralEvent, OutboundReferralId,
        entities::{
            journal_entry_block::{
                Column as JournalEntryBlockColumn, Entity as JournalEntryBlockEntity,
            },
            outbound_receiver::{
                ActiveModel as OutboundReceiverActiveModel, Column as OutboundReceiverColumn,
                Entity as OutboundReceiverEntity,
            },
            outbound_referral::{
                ActiveModel as OutboundReferralActiveModel, Entity as OutboundReferralEntity,
            },
        },
    },
};
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, EntityTrait, Query<PERSON>ilter, Set,
    sea_query::OnConflict,
};
#[async_trait::async_trait]
pub trait IOutboundReferralRepo: Send + Sync {
    async fn save(
        &self,
        model: OutboundReferral,
        events: Vec<OutboundReferralEvent>,
    ) -> Result<OutboundReferral>;
    async fn get(&self, id: OutboundReferralId) -> Result<OutboundReferral>;
    async fn get_receivers(&self, ids: Vec<OutboundReceiverId>) -> Result<Vec<OutboundReceiver>>;
    async fn save_receivers(&self, models: Vec<OutboundReceiver>) -> Result<Vec<OutboundReceiver>>;
    async fn get_receiver(&self, id: OutboundReceiverId) -> Result<OutboundReceiver>;
    async fn save_receiver(&self, models: OutboundReceiver) -> Result<OutboundReceiver>;
    async fn get_by_journal_entry_ids(
        &self,
        ids: &[JournalEntryId],
    ) -> Result<Vec<OutboundReferral>>;
}

pub struct OutboundReferralRepo<'a, C: ConnectionTrait> {
    pub connection: &'a C,
    pub user: &'a AuthenticatedUser,
}

impl<C: ConnectionTrait> SaveEvent for OutboundReferralRepo<'_, C> {
    type Event = OutboundReferralEvent;
}

impl<C: ConnectionTrait> SaveBlock for OutboundReferralRepo<'_, C> {}

impl<'a, C: ConnectionTrait> OutboundReferralRepo<'a, C> {
    pub fn new(connection: &'a C, user: &'a AuthenticatedUser) -> Self {
        Self { connection, user }
    }
}

#[async_trait::async_trait]
impl<C: ConnectionTrait> IOutboundReferralRepo for OutboundReferralRepo<'_, C> {
    async fn get(&self, id: OutboundReferralId) -> Result<OutboundReferral> {
        let (block, referral) = JournalEntryBlockEntity::find_by_id(id.0)
            .find_also_related(OutboundReferralEntity)
            .one(self.connection)
            .await?
            .ok_or_else(|| NotFoundError::new::<OutboundReferral>(id))?;

        self.user.check_tenant(&block.organisation_id)?;

        let referral = referral.ok_or_else(|| NotFoundError::new::<OutboundReferral>(id))?;

        let receiver = self.get_receiver(referral.receiver_id.into()).await?;

        Ok((referral, block.into(), receiver).into())
    }

    async fn save(
        &self,
        model: OutboundReferral,
        events: Vec<OutboundReferralEvent>,
    ) -> Result<OutboundReferral> {
        let active_model = OutboundReferralActiveModel {
            id: Set(model.id().into()),
            block_type: Set(model.block_type()),
            content: Set(model.content().clone()),
            receiver_id: Set(model.receiver().id().0),
        };

        self.save_events(self.connection, self.user, &events)
            .await?;

        let receiver = self.save_receiver(model.receiver().clone()).await?;
        let block = Self::save_block(self, self.connection, model.block().clone()).await?;

        let referral = match OutboundReferralEntity::find_by_id(model.id().0)
            .one(self.connection)
            .await?
        {
            Some(_) => active_model.update(self.connection).await?,
            None => active_model.insert(self.connection).await?,
        };

        Ok((referral, block, receiver).into())
    }

    async fn get_receivers(&self, ids: Vec<OutboundReceiverId>) -> Result<Vec<OutboundReceiver>> {
        let receivers = OutboundReceiverEntity::find()
            .filter(OutboundReceiverColumn::Id.is_in(ids.iter().map(|id| id.0)))
            .all(self.connection)
            .await?;

        Ok(receivers.into_iter().map(Into::into).collect())
    }

    async fn get_by_journal_entry_ids(
        &self,
        ids: &[JournalEntryId],
    ) -> Result<Vec<OutboundReferral>> {
        let result = JournalEntryBlockEntity::find()
            .filter(
                JournalEntryBlockColumn::JournalEntryId
                    .is_in(ids.iter().map(|id| id.0))
                    .and(JournalEntryBlockColumn::Status.ne(JournalEntryBlockStatus::Deleted))
                    .and(
                        JournalEntryBlockColumn::BlockType
                            .eq(JournalEntryBlockType::OutboundReferral),
                    ),
            )
            .find_also_related(OutboundReferralEntity)
            .all(self.connection)
            .await?;

        let receivers = OutboundReceiverEntity::find()
            .filter(
                OutboundReceiverColumn::Id.is_in(result.iter().filter_map(|r| {
                    r.1.as_ref()
                        .map(|outbound_referral| outbound_referral.receiver_id)
                })),
            )
            .all(self.connection)
            .await?;

        // assign each reciever to the corresponding outbound_referral using the result from the previous query
        let mut outbound_referral_list = Vec::new();

        for (block, outbound_referral) in result {
            self.user.check_tenant(&block.organisation_id)?;

            let outbound_referral = outbound_referral.ok_or_else(|| {
                NotFoundError::by_key::<OutboundReferral>(
                    &block.journal_entry_id.to_string(),
                    "journal_entry_id",
                )
            })?;

            let associated_receiver = receivers
                .iter()
                .find(|r| r.id == outbound_referral.receiver_id)
                .cloned()
                .ok_or_else(|| {
                    NotFoundError::by_key::<OutboundReceiver>(
                        &outbound_referral.id.to_string(),
                        "outbound_referral_id",
                    )
                })?;

            outbound_referral_list
                .push((outbound_referral, block.into(), associated_receiver.into()).into());
        }

        Ok(outbound_referral_list)
    }

    async fn save_receivers(&self, models: Vec<OutboundReceiver>) -> Result<Vec<OutboundReceiver>> {
        let ids = models.iter().map(|m| m.id()).collect();
        // Upsert (insert or update) the incoming models
        OutboundReceiverEntity::insert_many(models.into_iter().map(|model| {
            OutboundReceiverActiveModel {
                id: Set(model.id().into()),
                communication_status: Set(model.communication_status()),
                communication_response: Set(model.communication_response().clone()),
                send_error: Set(model.send_error().clone()),
                external_organisation_id: Set(model.external_organisation_id().map(|id| id.0)),
                external_provider_name: Set(model.external_provider_name().clone()),
            }
        }))
        .on_conflict(
            OnConflict::columns(vec![OutboundReceiverColumn::Id])
                .update_column(OutboundReceiverColumn::ExternalOrganisationId)
                .update_column(OutboundReceiverColumn::ExternalProviderName)
                .update_column(OutboundReceiverColumn::SendError)
                .update_column(OutboundReceiverColumn::CommunicationStatus)
                .update_column(OutboundReceiverColumn::CommunicationResponse)
                .clone(),
        )
        .exec(self.connection)
        .await?;

        let receivers = self.get_receivers(ids).await?;

        Ok(receivers)
    }

    async fn get_receiver(&self, id: OutboundReceiverId) -> Result<OutboundReceiver> {
        let receiver = OutboundReceiverEntity::find_by_id(id)
            .one(self.connection)
            .await?
            .ok_or_else(|| NotFoundError::new::<OutboundReceiver>(id))?;

        Ok(receiver.into())
    }

    async fn save_receiver(&self, model: OutboundReceiver) -> Result<OutboundReceiver> {
        OutboundReceiverEntity::insert(OutboundReceiverActiveModel {
            id: Set(model.id().into()),
            communication_status: Set(model.communication_status()),
            communication_response: Set(model.communication_response().clone()),
            send_error: Set(model.send_error().clone()),
            external_organisation_id: Set(model.external_organisation_id().map(|id| id.0)),
            external_provider_name: Set(model.external_provider_name().clone()),
        })
        .on_conflict(
            OnConflict::columns(vec![OutboundReceiverColumn::Id])
                .update_column(OutboundReceiverColumn::ExternalOrganisationId)
                .update_column(OutboundReceiverColumn::ExternalProviderName)
                .update_column(OutboundReceiverColumn::SendError)
                .update_column(OutboundReceiverColumn::CommunicationStatus)
                .update_column(OutboundReceiverColumn::CommunicationResponse)
                .clone(),
        )
        .exec(self.connection)
        .await?;

        self.get_receiver(model.id()).await
    }
}
