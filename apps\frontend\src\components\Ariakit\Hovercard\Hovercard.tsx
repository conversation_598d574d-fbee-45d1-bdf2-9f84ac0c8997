import {
  HovercardAnchor as AriakitHovercardAnchor,
  <PERSON><PERSON><PERSON> as AriakitHovercard,
  HovercardProps,
  useHovercardStore as useAriakitHovercardStore,
  HovercardStoreProps,
} from "@ariakit/react"
import { ComponentProps, forwardRef, Ref } from "react"

import { Button } from "ui"

import { AnimatedPopover, AnimatedPopoverProps } from "../../Popover/Popover"

export {
  PopoverArrow,
  PopoverDescription,
  PopoverHeading,
} from "@ariakit/react"
export type { PopoverAnchorProps, PopoverProps } from "@ariakit/react"

export const HovercardAnchor = ({
  ...props
}: ComponentProps<typeof AriakitHovercardAnchor>) => {
  return (
    <AriakitHovercardAnchor
      render={
        props.render
          ? props.render
          : (p) => <Button {...p} ref={p.ref as Ref<HTMLButtonElement>} />
      }
      {...props}
    />
  )
}

export const Hovercard = forwardRef<
  HTMLDivElement,
  HovercardProps & AnimatedPopoverProps
>(function Popover(props, ref) {
  return (
    <AriakitHovercard
      render={<AnimatedPopover animated={props.animated} />}
      ref={ref}
      gutter={4}
      unmountOnHide
      {...props}
    />
  )
})

export const useHovercardStore = (props?: HovercardStoreProps) =>
  useAriakitHovercardStore({
    animated: true,
    ...props,
  })
