import { faker } from "@faker-js/faker"
import { expect, test } from "@playwright/test"
import kennitala from "kennitala"

test.describe("Register Subject", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate
    await page.goto("/subject/register")
    await page.waitForLoadState()
    // eslint-disable-next-line playwright/no-wait-for-timeout
    await page.waitForTimeout(2000)
  })

  const birthDate = new Date()
  const validPersonaId = kennitala.generatePerson(birthDate)
  const badPersonaId = "121134"

  test.skip("Should register new subject", async ({ page }) => {
    // Arrange
    const subjectName = faker.name.firstName()
    const personaIdInput = page.locator('input[name="personaId"]')

    // Act
    // Fill persona ID field
    await personaIdInput.fill(validPersonaId)

    // Check if this persona ID has been registered before
    // If it has, the inputs will be disabled (for example the Day input)
    let subjectExists = await page.locator('[aria-label="Day"]').isDisabled()
    // Init new persona Id in case subject exists
    let newPersonaId = kennitala.generatePerson(birthDate)
    // Retry until non-existing persona ID is found. Don't loop if the newPersonaId is the same as the original validPersonaId
    // eslint-disable-next-line
    while (newPersonaId !== validPersonaId && subjectExists) {
      // Refresh the page
      await page.goto("/subject/register")
      await page.waitForLoadState()
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page.waitForTimeout(2000)
      // Set new persona Id
      await personaIdInput.fill(newPersonaId)
      // Check this if new subject exists
      subjectExists = await page.locator('[aria-label="Day"]').isDisabled()
      // Generate next new persona Id in case we loop
      newPersonaId = kennitala.generatePerson(birthDate)
    }

    // Fill name field
    await page.locator('input[name="name"]').fill(subjectName)
    // Set Gender value
    await page.locator('select[name="gender"]').selectOption("OTHER")
    // Set Nationality value
    await page.locator('select[name="nationality"]').selectOption("BE")
    // Submit
    await page.getByTestId("personal-info-submit").click()

    // Assert
    await page.waitForLoadState()

    // Error panel is not shown
    await expect(
      page.locator('div[class*="RegisterSubject_errorPanel"]')
    ).toBeHidden()
  })

  test.skip("Should warn about invalid persona id", async ({ page }) => {
    // Arrange
    const subjectName = faker.name.firstName()
    const personaIdInput = page.locator('input[name="personaId"]')

    // Act
    // Fill persona ID field
    await personaIdInput.fill(badPersonaId)
    // Fill name field
    await page.locator('input[name="name"]').fill(subjectName)
    // Submit
    await page.getByTestId("personal-info-submit").click()
    await page.waitForLoadState()
    // Click inside name field to add focus to form
    await page.locator('input[name="name"]').click()

    // Assert
    // Validation error is shown
    await expect(
      page.locator('div[class*="Input_message"]', {
        hasText: "Persona ID is invalid",
      })
    ).toBeVisible()
  })
})
