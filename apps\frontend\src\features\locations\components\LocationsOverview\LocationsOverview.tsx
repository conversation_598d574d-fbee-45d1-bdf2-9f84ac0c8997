import { useSuspenseQuery } from "@apollo/client"
import { useParams } from "react-router-dom"

import { BuildingOverview } from "features/locations/components/BuildingOverview/BuildingOverview"
import { isTypename } from "utils/isTypename"

import {
  GetBuildingsDocument,
  GetBuildingsQuery,
  GetBuildingsQueryVariables,
} from "generated/graphql"

import { BuildingsOverview } from "../BuildingsOverview/BuildingsOverview"
import { CorridorOverview } from "../CorridorOverview/CorridorOverview"
import styles from "./LocationsOverview.module.css"

export default function LocationsOverview() {
  const { data } = useSuspenseQuery<
    GetBuildingsQuery,
    GetBuildingsQueryVariables
  >(GetBuildingsDocument)

  const { buildingId } = useParams<{ buildingId?: string }>()
  const { corridorId } = useParams<{ corridorId?: string }>()

  const buildings = data?.locations.filter(isTypename("Building")) ?? []

  const activeBuilding = buildingId
    ? buildings?.find((building) => building.id === buildingId)
    : buildings[0]

  const activeCorridor = corridorId
    ? activeBuilding?.corridors?.corridors?.find(
        (corridor) => corridor.id === corridorId
      )
    : activeBuilding?.corridors.corridors[0]

  return (
    <div className={styles.wrap}>
      <section>
        <div>
          <BuildingsOverview
            locations={data?.locations || []}
            activeBuilding={activeBuilding}
          />
        </div>
      </section>

      <section>
        <div>
          <BuildingOverview
            building={activeBuilding}
            activeCorridor={activeCorridor}
          />
        </div>
      </section>

      {activeCorridor && activeBuilding?.id && (
        <section>
          <div>
            <CorridorOverview
              activeCorridor={activeCorridor}
              buildingId={activeBuilding.id}
            />
          </div>
        </section>
      )}
    </div>
  )
}
