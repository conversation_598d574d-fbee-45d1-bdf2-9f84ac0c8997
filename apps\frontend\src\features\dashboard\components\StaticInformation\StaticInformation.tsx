import { useTranslation } from "react-i18next"

import Restricted from "features/authentication/components/Restricted/Restricted"
import { Label } from "ui"
import { formatNumberInThousand } from "utils/formatNumberInThousand"

import {
  GetSubjectInsuranceStatusQuery,
  GetSubjectSummaryQuery,
  PermissionKey,
  useUpsertSubjectStaticDataMutation,
} from "generated/graphql"

import { EditFieldForm } from "../EditFieldForm/EditFieldForm"
import styles from "./StaticInformation.module.css"

type StaticInformationProps = {
  subjectId: string
  subjectStaticData: GetSubjectSummaryQuery["subject"]["subjectStaticData"]
  insuranceStatus?: GetSubjectInsuranceStatusQuery["subjectInsuranceStatus"]
  insuranceStatusError?: string | null
}

export const StaticInformation = ({
  subjectStaticData,
  subjectId,
  insuranceStatus,
  insuranceStatusError,
}: StaticInformationProps) => {
  const { t } = useTranslation()

  const [updateSubjectStaticData] = useUpsertSubjectStaticDataMutation()

  const isInsured = insuranceStatus?.isInsured

  const insuranceStatusValue = insuranceStatusError
    ? t("Failed to fetch insurance status")
    : insuranceStatus
      ? isInsured
        ? `${formatNumberInThousand(insuranceStatus.paymentStatus || 0)} ISK`
        : t("Uninsured")
      : t("Failed to fetch insurance status")

  return (
    <div className={styles.wrap}>
      <div className={styles.twoColumn}>
        <div>
          <Label>{t("Insurance status")}</Label>
          {insuranceStatusValue}
        </div>
        {isInsured && (
          <div>
            <Label>{t("Insurance category")}</Label>
            {insuranceStatus?.subjectStatus}
          </div>
        )}
      </div>
      <Restricted to={PermissionKey.SubjectJournalJournalEntryView}>
        <EditFieldForm
          name={t("general")}
          value={subjectStaticData?.generic || ""}
          label={t("General")}
          piiSensitive
          placeholder={t("Click to add general information")}
          onSubmit={(newValue) => {
            updateSubjectStaticData({
              variables: {
                input: {
                  subjectId,
                  generic: newValue,
                },
              },
            })
          }}
        />

        <EditFieldForm
          name={t("medication")}
          value={subjectStaticData?.medication || ""}
          label={t("Medications")}
          piiSensitive
          placeholder={t("Click to add medications")}
          onSubmit={(newValue) => {
            updateSubjectStaticData({
              variables: {
                input: {
                  subjectId,
                  medication: newValue,
                },
              },
            })
          }}
        />

        <EditFieldForm
          name={t("familyhistory")}
          value={subjectStaticData?.familyHistory || ""}
          label={t("Family history")}
          piiSensitive
          placeholder={t("Click to add family history")}
          onSubmit={(newValue) => {
            updateSubjectStaticData({
              variables: {
                input: {
                  subjectId,
                  familyHistory: newValue,
                },
              },
            })
          }}
        />

        <EditFieldForm
          name={t("socialhistory")}
          value={subjectStaticData?.socialHistory || ""}
          label={t("Social history")}
          piiSensitive
          placeholder={t("Click to add social history")}
          onSubmit={(newValue) => {
            updateSubjectStaticData({
              variables: {
                input: {
                  subjectId,
                  socialHistory: newValue,
                },
              },
            })
          }}
        />

        <EditFieldForm
          name={t("habits")}
          value={subjectStaticData?.habits || ""}
          label={t("Habits")}
          piiSensitive
          placeholder={t("Click to add habits")}
          onSubmit={(newValue) => {
            updateSubjectStaticData({
              variables: {
                input: {
                  subjectId,
                  habits: newValue,
                },
              },
            })
          }}
        />
      </Restricted>
    </div>
  )
}
