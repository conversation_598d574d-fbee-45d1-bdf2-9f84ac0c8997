import {
  ComboboxItem as ComboboxItemAriakit,
  ComboboxItemProps as ComboboxItemAriakitProps,
} from "@ariakit/react"
import { forwardRef } from "react"

import MenuListItem, {
  MenuListItemOwnProps,
} from "components/MenuListItem/MenuListItem"

import styles from "./ComboboxItem.module.css"

export type ComboboxItemProps = ComboboxItemAriakitProps & MenuListItemOwnProps

export const ComboboxItem = forwardRef<HTMLDivElement, ComboboxItemProps>(
  (props, ref) => {
    const {
      className = "",
      subContent,
      subContentClassName,
      direction,
      ...rest
    } = props
    return (
      <ComboboxItemAriakit
        ref={ref}
        render={
          props.render
            ? props.render
            : (p) => (
                <MenuListItem
                  {...p}
                  subContent={subContent}
                  subContentClassName={subContentClassName}
                  direction={direction}
                />
              )
        }
        className={`${styles.menuItem} ${className}`}
        {...rest}
      />
    )
  }
)
