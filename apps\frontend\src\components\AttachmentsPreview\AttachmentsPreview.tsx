import React, { <PERSON>actN<PERSON>, useContext, useRef, useState } from "react"
import { useHotkeys } from "react-hotkeys-hook"
import { useTranslation } from "react-i18next"

import modifier from "features/power-menu/lib/hotkeys/hotkeyModifier"
import { Heading, IconButton, Modal } from "ui"
import { IconAnchor } from "ui/components/IconButton/IconAnchor"
import {
  FileType,
  getFileTypeFromName,
  isPreviewSupported,
} from "utils/attachmentIconMapper"
import { printDocument } from "utils/printDocument"

import ConfirmSensitiveDownloadDialog, {
  useSensitiveDownloadAgreement,
} from "../AttachmentButton/ConfirmSensitiveDownloadDialog"
import styles from "./AttachmentsPreview.module.css"

type SupportedFileType = Extract<
  FileType,
  "document" | "audio" | "video" | "image"
>

const AudioPlayer = (props: JSX.IntrinsicElements["audio"]) => {
  const ref = useRef<HTMLAudioElement>(null)
  useHotkeys("space", (e) => {
    e.preventDefault()
    ref.current?.paused ? ref.current?.play() : ref.current?.pause()
  })
  useHotkeys("m", (e) => {
    e.preventDefault()
    if (ref.current) ref.current.muted = !ref.current?.muted
  })
  useHotkeys(`${modifier}+right`, (e) => {
    e.preventDefault()
    if (ref.current) ref.current.currentTime += 5
  })
  useHotkeys(`${modifier}+left`, (e) => {
    e.preventDefault()
    if (ref.current) ref.current.currentTime -= 5
  })

  return <audio ref={ref} controls {...props} />
}
const VideoPlayer = (props: JSX.IntrinsicElements["video"]) => {
  const ref = useRef<HTMLVideoElement>(null)
  useHotkeys("space", (e) => {
    e.preventDefault()
    ref.current?.paused ? ref.current?.play() : ref.current?.pause()
  })
  useHotkeys(`${modifier}+right`, (e) => {
    e.preventDefault()
    if (ref.current) ref.current.currentTime += 5
  })
  useHotkeys(`${modifier}+left`, (e) => {
    e.preventDefault()
    if (ref.current) ref.current.currentTime -= 5
  })

  return <video ref={ref} controls {...props} />
}

export const PreviewFileType = ({
  fileType,
  url,
}: {
  fileType: SupportedFileType
  url: string
}) => {
  switch (fileType) {
    case "document":
      return (
        <iframe
          src={url + "#toolbar=0&navpanes=0&scrollbar=0"}
          className={styles.document}
        />
      )

    case "audio":
      return <AudioPlayer src={url} className={styles.audio} />

    case "video":
      return <VideoPlayer src={url} className={styles.video} />

    default:
      return <img src={url} alt="attachment" className={styles.image} />
  }
}

const AttachmentContext = React.createContext<{
  openAttachment?: (fileUrl: string) => void
}>({})

export const useAttachmentContext = () => useContext(AttachmentContext)
export type Attachment = {
  url: string
  name: string
}
export type AttachmentsPreviewProps = {
  attachments: Attachment[]
  children: ReactNode
}
export const AttachmentsPreview = ({
  attachments,
  children,
}: AttachmentsPreviewProps) => {
  const { t } = useTranslation()
  const [showSensitiveModal, setShowSensitiveModal] = useState(false)
  const [hasAgreedSensitive] = useSensitiveDownloadAgreement()

  const supportedAttachments = attachments.filter(({ name }) =>
    isPreviewSupported(name)
  )
  // Create a state for the active attachmentId
  const [currentIndex, setCurrentIndex] = useState(-1)
  function handleClickNext() {
    currentIndex !== -1 &&
      setCurrentIndex((currentIndex + 1) % supportedAttachments.length)
  }
  function handleClickPrev() {
    currentIndex !== -1 &&
      setCurrentIndex(
        (currentIndex - 1 + supportedAttachments.length) %
          supportedAttachments.length
      )
  }
  function handleClose() {
    setCurrentIndex(-1)
  }
  function openAttachment(fileUrl: string) {
    const index = supportedAttachments.findIndex(({ url }) => url === fileUrl)
    setCurrentIndex(index)
  }
  useHotkeys("right", handleClickNext, [currentIndex])
  useHotkeys("left", handleClickPrev, [currentIndex])

  const url = supportedAttachments[currentIndex]?.url
  const name = supportedAttachments[currentIndex]?.name || ""

  // We've already made sure that the fileType is supported
  const currentFileType = getFileTypeFromName(name) as SupportedFileType
  const canPrint =
    (currentFileType === "document" || currentFileType === "image") &&
    isPreviewSupported(name)

  return (
    <AttachmentContext.Provider value={{ openAttachment }}>
      {children}
      <Modal
        header={
          <header className={styles.header}>
            <Heading as="h3">{name}</Heading>
            {canPrint && (
              <IconAnchor
                aria-label={t("Print attachment")}
                iconName={"printer-line"}
                onClick={() => printDocument(url)}
              />
            )}
            <IconAnchor
              aria-label={t("Download attachment")}
              iconName="download-2-line"
              href={url}
              download
              onClick={(e) => {
                if (!hasAgreedSensitive) {
                  e.preventDefault()
                  setShowSensitiveModal(true)
                  return
                }
              }}
            />
            <IconButton
              aria-label={t("Close modal")}
              iconName={"close-line"}
              onClick={handleClose}
            />
          </header>
        }
        isOpen={currentIndex >= 0}
        contentClassName={styles.content}
        className={styles.dialog}
        onClose={() => setCurrentIndex(-1)}
        animation="fade"
        animationDuration={100}
      >
        <main
          onClick={(e) => {
            if (e.target === e.currentTarget) handleClose()
          }}
        >
          {supportedAttachments.length > 1 && (
            <>
              <IconButton
                aria-label={t("Next attachment")}
                iconName="arrow-right-s-line"
                className={styles.next}
                onClick={handleClickNext}
              />
              <IconButton
                aria-label={t("Previous attachment")}
                iconName="arrow-left-s-line"
                className={styles.prev}
                onClick={handleClickPrev}
              />
            </>
          )}

          <PreviewFileType fileType={currentFileType} url={url} />

          {supportedAttachments.length > 1 && (
            <ul className={styles.bullets}>
              {supportedAttachments.map(({ url }, index) => (
                <li key={url}>
                  <button
                    aria-current={currentIndex === index}
                    onClick={() => setCurrentIndex(index)}
                    className={styles.bullet}
                  />
                </li>
              ))}
            </ul>
          )}
        </main>
        <ConfirmSensitiveDownloadDialog
          isOpen={showSensitiveModal}
          onClose={() => setShowSensitiveModal(false)}
          fileUrl={url}
        />
      </Modal>
    </AttachmentContext.Provider>
  )
}
