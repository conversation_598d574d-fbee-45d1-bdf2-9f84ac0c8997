[package]
name = "composition-root"
version = "0.1.0"
edition.workspace = true

[dependencies]
# All layers for dependency injection
leviosa-domain-contracts = { path = "../domain-contracts" }
leviosa-domain-types = { path = "../domain-types" }
leviosa-domain = { path = "../domain" }
leviosa-infrastructure = { path = "../infrastructure" }
leviosa-api = { path = "../api" }

# Required for dependency injection
async-trait.workspace = true
tokio.workspace = true
anyhow.workspace = true
tracing.workspace = true
