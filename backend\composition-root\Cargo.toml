[package]
name = "composition-root"
version = "0.1.0"
edition.workspace = true

[dependencies]
# All layers for dependency injection

leviosa-domain-types = { path = "../domain-types" }
leviosa-domain = { path = "../domain" }
leviosa-infrastructure = { path = "../infrastructure" }

# Required for dependency injection
async-trait.workspace = true
tokio.workspace = true
anyhow.workspace = true
tracing.workspace = true
