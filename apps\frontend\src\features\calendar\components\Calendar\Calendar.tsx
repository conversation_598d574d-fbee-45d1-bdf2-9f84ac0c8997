import { sortBy, uniqBy } from "lodash"
import {
  Profiler,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react"
import { View } from "react-big-calendar"
import { useNavigate, useParams, useSearchParams } from "react-router-dom"

import Restricted from "features/authentication/components/Restricted/Restricted"
import { CalendarProvider } from "features/calendar/components/Calendar/CalendarProvider"
import CalendarMain from "features/calendar/components/CalendarMain/CalendarMain"
import CalendarSidebar from "features/calendar/components/CalendarSidebar/CalendarSidebar"
import useCalendarState from "features/calendar/hooks/useCalendarState"
import { useEventInstanceFetcher } from "features/calendar/hooks/useEventInstanceFetcher"
import calculateDateRange from "features/calendar/utils/calculateDateRange"
import NotFoundPage from "features/mainLayout/components/ErrorPages/NotFoundPage"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { handleProfilerRender } from "utils/handleProfilerRender"

import { PermissionKey } from "generated/graphql"

import styles from "./Calendar.module.css"

const isValidView = (value?: string): value is View => {
  if (!value) return false
  return ["month", "week", "work_week", "day"].includes(value)
}

type CalendarWithoutPermissionCheckProps = {
  children: ReactNode
  onSelectDate: (date: Date) => void
  date: Date
}

const CalendarWithoutPermissionCheck = ({
  children,
  date,
  onSelectDate,
}: CalendarWithoutPermissionCheckProps) => {
  const [includeCancelledEvents, setIncludeCancelledEvents] = useState(false)

  const { view, visibleProviders: providerIds } = useCalendarState()

  const dates = calculateDateRange(date, view)

  const { eventInstances, availabilityBlocks, providers } =
    useEventInstanceFetcher(
      {
        ...dates,
        participantProviderId: providerIds?.length ? providerIds : null,
        includeCanceled: includeCancelledEvents,
      },
      view
    )

  const serviceTypes = useMemo(() => {
    const uniqueServiceTypes = uniqBy(
      availabilityBlocks.map((el) => el.resource.serviceType),
      "id"
    )
    return sortBy(uniqueServiceTypes, "name")
  }, [availabilityBlocks])

  return (
    <div className={styles.container}>
      <CalendarSidebar
        serviceTypes={serviceTypes}
        selectedProviders={providers || []}
        date={date}
        onSelectDate={onSelectDate}
        showCancelledEvents={includeCancelledEvents}
        onToggleCancelledEvents={() =>
          setIncludeCancelledEvents((prev) => !prev)
        }
      />
      <Profiler id="CalendarMain" onRender={handleProfilerRender}>
        <CalendarMain
          date={date}
          eventInstances={eventInstances}
          availabilityBlocks={availabilityBlocks}
          onSelectDate={onSelectDate}
        />
      </Profiler>
      {children}
    </div>
  )
}

type CalendarProps = {
  children: ReactNode
}

export default function Calendar(props: CalendarProps) {
  const [searchParams, setSearchParams] = useSearchParams()
  const navigate = useNavigate()
  const { view } = useParams<{ view: string }>()

  const dateSearchParam = searchParams.get("date") || ""

  // Initialize the date state either from URL query params or to today's date
  const [date, setDate] = useState<Date>(
    dateSearchParam ? new Date(dateSearchParam) : new Date()
  )
  const rawUrlProvidersString = searchParams.get("provider") ?? ""
  const { visibleProviders } = useCalendarState()

  useEffect(() => {
    if (dateSearchParam) {
      setDate(new Date(dateSearchParam))
    }
    // If date query param is not present add current date as query param
    if (!dateSearchParam || !rawUrlProvidersString) {
      setSearchParams(
        (searchParams) => {
          if (!searchParams.has("date")) {
            const currentDate = new Date().toISOString().split("T")[0]
            searchParams.set("date", currentDate)
          }
          if (!searchParams.has("provider")) {
            searchParams.set("provider", visibleProviders.join(","))
          }
          return searchParams
        },
        { replace: true }
      )
    }
  }, [
    dateSearchParam,
    rawUrlProvidersString,
    setSearchParams,
    visibleProviders,
  ])

  // Update the date query parameter when a new date is selected
  const handleSelectDate = useCallback(
    (selectedDate: Date) => {
      const newDateString = selectedDate.toISOString().split("T")[0]

      const searchParams = new URLSearchParams(location.search)

      searchParams.set("date", newDateString)

      // Update the URL with the new date without reloading the page
      navigate(
        {
          pathname: location.pathname,
          search: searchParams.toString(),
        },
        {
          replace: true,
        }
      )
    },
    [location.pathname, location.search, navigate, setDate]
  )

  // If view is not a valid View type, show not found page
  if (!isValidView(view)) {
    return <NotFoundPage />
  }

  return (
    <Restricted to={PermissionKey.CalendarView} fallback={<UnauthorizedPage />}>
      <CalendarProvider>
        <CalendarWithoutPermissionCheck
          date={date}
          onSelectDate={handleSelectDate}
          {...props}
        />
      </CalendarProvider>
    </Restricted>
  )
}
