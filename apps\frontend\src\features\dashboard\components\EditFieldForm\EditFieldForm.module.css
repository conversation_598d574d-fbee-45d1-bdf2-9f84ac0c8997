.fieldWrap {
  display: flex;
  gap: 8px;
  align-items: center;
}

.label[data-is-edit="true"] {
  margin-bottom: 4px;
}

.editButton {
  opacity: 0;
  transition: opacity 0.2s;
  padding: 4px;
  align-self: start;
}

.fieldWrap:hover .editButton {
  opacity: 1;
}

.fieldText {
  padding: 4px 8px;
  margin-left: -8px;
  border-radius: var(--radius-button-half);
  white-space: pre-wrap;
  width: 100%;
  cursor: pointer;
}

.fieldText[data-is-empty="true"] {
  color: var(--color-gray-60);
}

.fieldWrap:hover .fieldText {
  background: var(--color-blue-additional-light);
}

.form {
  width: 100%;
}

.buttonsWrap {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}
