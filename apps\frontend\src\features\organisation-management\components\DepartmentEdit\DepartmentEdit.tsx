import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate, useParams } from "react-router-dom"

import { useSelectStore } from "components/Ariakit/hooks"
import { FormSection } from "components/FormSection/FormSection"
import Select from "components/Select/Select"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { FormGrid, Grid, Heading, Input, notification } from "ui"
import Button from "ui/components/Button/Button"
import FormFooter from "ui/components/FormFooter/FormFooter"

import {
  DepartmentEditQuery,
  useDepartmentEditQuery,
  useSetDepartmentOwnerMutation,
  useUpdateDepartmentMutation,
} from "generated/graphql"

import { TeamCreate } from "../TeamCreate/TeamCreate"
import { TeamsCard } from "../TeamsCard/TeamsCard"

type DepartmentEditProps = {
  departmentId: string
  config: GlobalDataWithNonNullableActor["config"]
  data: DepartmentEditQuery
}

type FormState = {
  name: string
  externalEhrId: string | null
}

// Unlike other ownership entities, there is only DepartmentEdit, not DepartmentView. B/c it is only for moderators to work with. Department is a data entity primarily to collect Teams and serve as location placeholder in Organisation.
const DE = ({
  departmentId,
  config: { leviosaKindId },
  data: { department, providers },
}: DepartmentEditProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "manageDepartment" })
  const [tg] = useTranslation()

  const navigate = useNavigate()

  const [wantsCreateTeam, setWantsCreateTeam] = useState(false)

  const [formState, setFormState] = useState<FormState>({
    name: department.name,
    externalEhrId: department.externalEhrId,
  })

  const [updateDepartment, { loading }] = useUpdateDepartmentMutation({
    onCompleted: (data) => {
      if (data?.updateDepartment)
        notification.create({
          status: "success",
          message: tg("success"),
        })
    },
  })
  const [setDepartmentOwner] = useSetDepartmentOwnerMutation()

  const handleChangeFormState = (
    keyValuePair: Partial<Pick<FormState, keyof FormState>>
  ) => setFormState((prev) => ({ ...prev, ...keyValuePair }))

  const ownerStore = useSelectStore({
    defaultValue: department.owner.id,
  })

  const submitForm = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    updateDepartment({
      variables: {
        input: {
          id: departmentId,
          name: formState.name,
          externalEhrId: { set: formState.externalEhrId },
          // ownerId: ownerStore.getState().value,
        },
      },
    })

    const stateOwnerId = ownerStore.getState().value as string
    if (stateOwnerId !== department.owner.id)
      setDepartmentOwner({
        variables: { departmentId, ownerId: stateOwnerId },
      })
  }

  return (
    <Grid rowGap={4}>
      <FormGrid onSubmit={submitForm} autoComplete="off">
        <FormSection showLine={false} iconName={"settings-line"}>
          <Input
            label={tg("Name")}
            value={formState.name}
            onChange={({ target }) =>
              handleChangeFormState({ name: target.value })
            }
          />

          <Select
            label={tg("owner")}
            selectStore={ownerStore}
            options={providers.map(({ id, name }) => ({
              value: id,
              label: name,
            }))}
          />

          {leviosaKindId === "LITE" && (
            <Input
              label={t("externalId")}
              value={formState.externalEhrId || ""}
              onChange={({ target }) =>
                handleChangeFormState({ externalEhrId: target.value })
              }
            />
          )}
        </FormSection>

        <FormFooter>
          <Button onClick={() => navigate(-1)}>{tg("cancel")}</Button>
          <Button type="submit" disabled={loading} variant="filled">
            {tg("submit")}
          </Button>
        </FormFooter>

        <FormSection showLine={false} iconName={"group-line"}>
          <Heading size="default" as="h2">
            {t("teams")}
          </Heading>
          <TeamsCard teams={department.teams} />

          {/* COMEBACK this case shows that FormFooter perhaps is better named as 'ControlsFooter' ? Since there is no form here just need for consistency in styling of controls in a footer */}
          <FormFooter>
            <Button variant="clear" onClick={() => setWantsCreateTeam(true)}>
              {t("createNewTeam")}
            </Button>
          </FormFooter>
        </FormSection>
      </FormGrid>

      {wantsCreateTeam && (
        <TeamCreate
          onClose={() => setWantsCreateTeam(false)}
          departmentId={departmentId}
        />
      )}
    </Grid>
  )
}

export default function DepartmentEdit(
  props: Omit<DepartmentEditProps, "departmentId" | "data">
) {
  const { departmentId } = useParams<{ departmentId: string }>()

  const { data } = useDepartmentEditQuery({
    variables: {
      departmentId: departmentId || "",
    },
  })

  if (!departmentId || !data) return null

  return <DE {...props} departmentId={departmentId} data={data} />
}
