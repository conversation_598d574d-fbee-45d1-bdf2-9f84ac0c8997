use crate::lib::{
    context::{<PERSON>osaAuth<PERSON>ontext, <PERSON>osa<PERSON>ontext, LeviosaDatabaseContext},
    errors::Result,
    graphql::{graphql_wrapper, IntoGQL},
};
use async_graphql::{Context, InputObject, Object};
use leviosa_domain::{
    accounts::{Organisation, Provider},
    auth::AuthScope,
};
use leviosa_domain_contracts::{FeedbackData, FeedbackUser};
use leviosa_domain_types::{OrganisationId, ProviderId};

use tokio::join;

use super::FeedbackValuesGQL;

graphql_wrapper!(FeedbackData);

#[Object(name = "FeedbackData")]
impl FeedbackDataObject {
    async fn feedback(&self) -> FeedbackValuesGQL {
        // Convert String feedback to FeedbackValuesGQL
        match self.0.feedback.as_str() {
            "Positive" => FeedbackValuesGQL::POSITIVE,
            "Negative" => FeedbackValuesGQL::NEGATIVE,
            _ => Fe<PERSON>backV<PERSON>uesGQL::NEUTRAL,
        }
    }

    async fn comment(&self) -> Option<String> {
        // Contracts FeedbackData doesn't have comment field, return None
        None
    }

    async fn os(&self) -> Option<String> {
        self.0.os.clone()
    }

    async fn browser(&self) -> Option<String> {
        self.0.browser.clone()
    }

    async fn browser_version(&self) -> Option<String> {
        self.0.browser_version.clone()
    }

    async fn app_version(&self) -> f64 {
        // Parse String app_version to f64, default to 0.0 if parsing fails
        self.0.app_version.parse().unwrap_or(0.0)
    }
}

#[derive(InputObject)]
pub struct LeaveFeedbackInput {
    pub feedback: FeedbackValuesGQL,
    pub comment: Option<String>,
    pub os: Option<String>,
    pub browser: Option<String>,
    pub browser_version: Option<String>,
    pub app_version: f64,
}

#[derive(Default)]
pub struct FeedbackMutations;

#[Object]
impl FeedbackMutations {
    async fn leave_feedback(
        &self,
        ctx: &Context<'_>,
        input: LeaveFeedbackInput,
    ) -> Result<FeedbackDataObject> {
        let auth_user = ctx.check_permission(None)?;
        let db = ctx.db()?;

        let (user, org) = join!(
            Provider::get(&db, &AuthScope::Unchecked, auth_user.user_id),
            Organisation::get(&db, auth_user.organisation_id, &AuthScope::Unchecked)
        );
        let user = user?;
        let org = org?;

        let feedback = FeedbackData {
            feedback: match input.feedback {
                FeedbackValuesGQL::POSITIVE => "Positive".to_string(),
                FeedbackValuesGQL::NEGATIVE => "Negative".to_string(),
                FeedbackValuesGQL::NEUTRAL => "Neutral".to_string(),
            },
            os: input.os,
            browser: input.browser,
            browser_version: input.browser_version,
            app_version: input.app_version.to_string(),
            user: FeedbackUser {
                user_id: ProviderId(user.id().0),
                user_name: user.name().clone(),
                user_email: user.email().clone(),
                organization_id: OrganisationId(org.id().0),
                organization_name: org.name().clone(),
            },
        };

        ctx.customer_service()
            .create_feedback(feedback.clone())
            .await?;

        Ok(feedback.into_gql())
    }
}
