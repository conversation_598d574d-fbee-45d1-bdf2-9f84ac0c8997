import {
  ApolloError,
  skipToken,
  useBackgroundQuery,
  useReadQuery,
} from "@apollo/client"
import { Suspense } from "react"
import { useTranslation } from "react-i18next"

import { Menu, MenuGroup, MenuItem } from "components/Ariakit"
import { SubMenu } from "components/Ariakit/SubMenu/SubMenu"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import { ButtonText, Text } from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  GetDatesWithOpenSlotsDocument,
  GetDatesWithOpenSlotsQuery,
  GetDatesWithOpenSlotsQueryVariables,
  useGetOpenSlotsSuspenseQuery,
} from "generated/graphql"

import styles from "./OptionIntervalMenu.module.css"

const maxOptions = 15

export type EventData = {
  serviceTypeId: string
  providerId: string
  subjectId: string
}

type SlotProps = {
  fromTime: string
  toTime: string
  handleSelectSlot: (fromTime: string, toTime: string) => Promise<void>
  getMutationResult: (fromTime: string) => {
    loading: boolean
    error?: ApolloError
    called: boolean
  }
}

const Slot = ({
  fromTime,
  toTime,
  handleSelectSlot,
  getMutationResult,
}: SlotProps) => {
  const formatTime = useTimeFormatter()

  const { loading, called, error } = getMutationResult(fromTime)

  return (
    <MenuItem
      onClick={() => handleSelectSlot(fromTime, toTime)}
      hideOnClick={false}
      className={styles.slot}
    >
      <span>{formatTime(new Date(fromTime))}</span>
      {loading && <Icon name="loader-4-line" spin />}
      {called && !loading && error && (
        <Tooltip tooltipContent={error.message}>
          <Icon name="error-warning-line" className={styles.error} />
        </Tooltip>
      )}
      {called && !loading && !error && (
        <Icon name="error-warning-line" className={styles.success} />
      )}
    </MenuItem>
  )
}

type AvailableSlotsMenuProps = {
  date: string
  eventData: EventData
  handleSelectSlot: SlotProps["handleSelectSlot"]
  getMutationResult: SlotProps["getMutationResult"]
}

function AvailableSlotsMenu({
  date,
  eventData,
  ...rest
}: AvailableSlotsMenuProps) {
  const { providerId, serviceTypeId } = eventData
  const { data } = useGetOpenSlotsSuspenseQuery({
    variables: {
      filter: { fromDate: date, toDate: date, providerId, serviceTypeId },
    },
  })

  return (
    <>
      {data?.openTimeSlots.length ? (
        data.openTimeSlots[0].times.map((slot) => (
          <Slot key={slot.fromTime} {...slot} {...rest} />
        ))
      ) : (
        // Todo: Add translation
        <MenuItem>No available appointments on {date}</MenuItem>
      )}
    </>
  )
}

export type QueryRef = NonNullable<
  ReturnType<
    typeof useBackgroundQuery<
      GetDatesWithOpenSlotsQuery,
      GetDatesWithOpenSlotsQueryVariables
    >
  >[0]
>

type AvailableDatesProps = {
  fromDate: string
  toDate: string
  queryRef: QueryRef
  eventData: EventData
  handleSelectSlot: SlotProps["handleSelectSlot"]
  getMutationResult: SlotProps["getMutationResult"]
}

function AvailableDates({
  fromDate,
  toDate,
  queryRef,
  ...rest
}: AvailableDatesProps) {
  const format = useDateFormatter()
  const { data } = useReadQuery(queryRef)
  const { t } = useTranslation()

  const openDates =
    data?.datesWithOpenTimeSlots
      .filter((date) => date >= fromDate && date <= toDate)
      .slice(0, maxOptions) || []

  return (
    <>
      {openDates.length ? (
        openDates.map((date) => (
          <SubMenu
            key={date}
            label={
              <>
                <Text size="small">
                  {format(new Date(date), {
                    weekday: "long",
                    dateStyle: undefined,
                  })}
                </Text>
                <ButtonText>{format(new Date(date))}</ButtonText>
              </>
            }
          >
            <Suspense fallback={<MenuItem>{format(new Date(date))}</MenuItem>}>
              <AvailableSlotsMenu date={date} {...rest} />
            </Suspense>
          </SubMenu>
        ))
      ) : (
        <MenuItem>{t(`No available appointments`)}</MenuItem>
      )}
    </>
  )
}

type OptionIntervalMenuProps = {
  serviceTypeId: string
  providerId: string
  subjectId: string
  isOpen: boolean
  handleSelectSlot: SlotProps["handleSelectSlot"]
  getMutationResult: SlotProps["getMutationResult"]
  children: React.ReactNode
  optionIntervals: {
    label: string
    fromDate: string
    toDate: string
  }[]
}

export default function OptionIntervalMenu({
  serviceTypeId,
  providerId,
  subjectId,
  isOpen,
  optionIntervals,
  children,
  ...rest
}: OptionIntervalMenuProps) {
  const { t } = useTranslation()

  const [queryRef] = useBackgroundQuery<
    GetDatesWithOpenSlotsQuery,
    GetDatesWithOpenSlotsQueryVariables
  >(
    GetDatesWithOpenSlotsDocument,
    isOpen
      ? {
          variables: {
            filter: {
              serviceTypeId,
              providerId,
            },
          },
        }
      : skipToken
  )

  return (
    <Menu portal sameWidth>
      <MenuGroup>
        {children}
        {optionIntervals.map(({ label, fromDate, toDate }) => (
          <SubMenu key={fromDate} label={label}>
            <Suspense fallback={<MenuItem>{t("Loading")}</MenuItem>}>
              {queryRef && (
                <AvailableDates
                  fromDate={fromDate}
                  toDate={toDate}
                  queryRef={queryRef}
                  eventData={{
                    serviceTypeId,
                    providerId,
                    subjectId,
                  }}
                  {...rest}
                />
              )}
            </Suspense>
          </SubMenu>
        ))}
      </MenuGroup>
    </Menu>
  )
}
