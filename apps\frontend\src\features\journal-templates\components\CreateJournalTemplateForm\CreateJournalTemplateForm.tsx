import { FormEvent, useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"
import { v4 as uuid } from "uuid"
import { z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import { RouteStrings } from "routes/RouteStrings"
import { Button, FormGrid, Input, Text } from "ui"

import {
  DocumentType,
  JournalTemplateType,
  LanguageId,
  namedOperations,
  useCreateJournalTemplateMutation,
} from "generated/graphql"

import styles from "./CreateJournalTemplateForm.module.css"

const schema = z.object({
  name: z.string().min(3),
  description: z.string().optional(),
  documentType: z.nativeEnum(DocumentType).optional(),
})

type CreateJournalTemplateFormProps = {
  onClose: () => void
  templateType: JournalTemplateType | null
}

export const CreateJournalTemplateForm = ({
  onClose,
  templateType,
}: CreateJournalTemplateFormProps) => {
  const nameRef = useRef<HTMLInputElement>(null)

  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const navigate = useNavigate()
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")

  useEffect(() => {
    if (!nameRef.current) return
    setTimeout(() => {
      nameRef.current?.focus()
    })
  }, [])

  const [createJournalTemplate, { loading }] = useCreateJournalTemplateMutation(
    {
      onCompleted: (data) => {
        if (data) {
          onClose?.()
          if (!data.createJournalTemplate.id) return
          navigate(
            generatePath(RouteStrings.journalTemplates, {
              templateType: templateType?.toLowerCase(),
              templateId: data.createJournalTemplate.id,
            })
          )
        }
      },
      refetchQueries: [namedOperations.Query.GetJournalTemplates],
    }
  )

  const documentTypeStore = useSelectStore({})

  const selectedDocumentType = documentTypeStore.useState().value

  const documentTypeOptions = Object.values(DocumentType).map(
    (value: string) => ({
      label: tEnum(`DocumentType.${value}.label`),
      value,
    })
  )

  if (!templateType) return null

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setValidationError(null)

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = schema.safeParse(data)
    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    const templateId = uuid()
    const sectionId = uuid()

    createJournalTemplate({
      variables: {
        id: templateId,
        input: {
          name: validatedInput.data.name,
          description: validatedInput.data.description,
          contentLanguageId: LanguageId.Is,
          documentType: validatedInput.data.documentType || null,
          templateType,
        },
        sectionId: sectionId,
        sectionInput: {
          journalTemplateId: templateId,
          isResolutionNote: false,
          content: "",
        },
      },
    })
  }

  return (
    <FormGrid onSubmit={handleSubmit} className={styles.form}>
      <Input
        ref={nameRef}
        label={t("Name")}
        name="name"
        required={true}
        autoFocus={true}
        minLength={3}
        autoComplete="off"
      />

      {templateType === JournalTemplateType.DocumentTemplate && (
        <>
          <Select
            label={t("Document type")}
            selectStore={documentTypeStore}
            options={documentTypeOptions}
            name="documentType"
            required={true}
            sameWidth
            placeholder={t("Select type")}
            className={styles.selectDocumentType}
          />

          {selectedDocumentType && (
            <div className={styles.selectedDocumentType}>
              <Text size="small">
                {tEnum(`DocumentType.${selectedDocumentType}.description`)}
              </Text>
            </div>
          )}
        </>
      )}

      <Input label={t("Description")} name={"description"} />

      {validationError && (
        <Panel status="error">{validationError?.message}</Panel>
      )}

      <div className={styles.submitRow}>
        <Button
          type="submit"
          variant="filled"
          size="large"
          disabled={loading}
          data-testid="intervention-period-submit"
        >
          {t("Create")}
        </Button>
        <Button
          size="large"
          disabled={loading}
          onClick={onClose}
          data-testid="intervention-period-cancel"
        >
          {t("Cancel")}
        </Button>
      </div>
    </FormGrid>
  )
}
