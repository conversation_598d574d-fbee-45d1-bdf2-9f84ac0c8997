use chrono::Utc;

use crate::{
    auth::AuthenticatedUser,
    command::CommandHandler,
    entity_event::HandleEntityCommand,
    errors::Result,
    file_repo::{CreateFileCommand, File, FileCategory, FileId, GetFileFilter, IFileRepo},
    pdf_generator::PdfGenerator,
    permissions::PermissionKey,
    repo_connection::{IRepoConnection, IRepoTransaction, ITransactionManager},
    subject_journal::{
        DownloadMedicalCertificateCommand, GetJournalEntryFilter, MedicalCertificate,
    },
};

pub struct DownloadMedicalCertificateHandler<'a, T: ITransactionManager> {
    tx_manager: T,
    user: AuthenticatedUser,
    file_repo: &'a dyn IFileRepo,
    pdf_generator: &'a dyn PdfGenerator,
    token: &'a str,
}

impl<'a, T: ITransactionManager> DownloadMedicalCertificateHandler<'a, T> {
    pub fn new(
        tx_manager: T,
        user: AuthenticatedUser,
        file_repo: &'a dyn IFileRepo,
        pdf_generator: &'a dyn PdfGenerator,
        token: &'a str,
    ) -> Self {
        Self {
            tx_manager,
            user,
            file_repo,
            pdf_generator,
            token,
        }
    }
}

#[async_trait::async_trait]
impl<T: ITransactionManager> CommandHandler<DownloadMedicalCertificateCommand>
    for DownloadMedicalCertificateHandler<'_, T>
{
    type Model = File;

    fn required_permission() -> PermissionKey {
        PermissionKey::SubjectJournal_JournalEntry_Edit
    }

    async fn execute(&self, command: DownloadMedicalCertificateCommand) -> Result<File> {
        let tx = self.tx_manager.transaction().await?;

        let result = {
            let medical_certificate_repo = tx.repos().medical_certificate_repo();
            let journal_entry_repo = tx.repos().journal_entry_repo();

            let medical_certificate = medical_certificate_repo.get(command.id).await?;

            // This checks the Journal Entry tenant
            journal_entry_repo
                .get(GetJournalEntryFilter::Id(
                    medical_certificate.block().entry_id().into(),
                ))
                .await?;

            let (medical_certificate, event) =
                medical_certificate.handle(command.clone(), &self.user)?;

            let url = match self.get_pdf(&medical_certificate, self.token).await {
                Ok(url) => url,
                Err(e) => {
                    tracing::error!("Failed to get file: {:?}", e);
                    return Err(e);
                }
            };

            medical_certificate_repo
                .save(medical_certificate, event)
                .await?;

            url
        };

        tx.commit().await?;

        Ok(result)
    }
}

impl<T: ITransactionManager> DownloadMedicalCertificateHandler<'_, T> {
    async fn get_pdf(&self, certificate: &MedicalCertificate, token: &str) -> Result<File> {
        let file_id: FileId = certificate.id().0.into();
        let cert_type = certificate.cert_type();
        let file_name = if cert_type == "Free-text" {
            format!(
                "FreeTextDocument-{}.pdf",
                Utc::now().format("%d.%m.%Y-%H:%M")
            )
        } else {
            format!(
                "MedicalCertificate-{}.pdf",
                Utc::now().format("%d.%m.%Y-%H:%M")
            )
        };

        let pdf = match self
            .file_repo
            .get_optional(&GetFileFilter::Id(file_id), &self.user)
            .await?
        {
            Some(pdf) => pdf,
            None => {
                let pdf = if cert_type == "Free-text" {
                    self.pdf_generator
                        .free_text_document(leviosa_domain_types::MedicalCertificateId(certificate.id().0), token)
                        .await?
                } else {
                    self.pdf_generator
                        .medical_certificate(leviosa_domain_types::MedicalCertificateId(certificate.id().0), token)
                        .await?
                };

                let file_metadata = self
                    .file_repo
                    .create(
                        CreateFileCommand {
                            id: Some(file_id),
                            file_name,
                            category: FileCategory::MedicalCertificate,
                            content: pdf.clone(),
                        },
                        &self.user,
                    )
                    .await?;

                File {
                    id: file_id,
                    content: pdf,
                    metadata: file_metadata,
                }
            }
        };

        Ok(pdf)
    }
}
