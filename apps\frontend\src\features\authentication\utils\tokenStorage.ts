export type Tokens = {
  accessToken: string
  refreshToken: string
}

const ACCESS_TOKEN = "access_token"
const REFRESH_TOKEN = "refresh_token"
const initAccessToken = localStorage.getItem(ACCESS_TOKEN)
const initRefreshToken = localStorage.getItem(REFRESH_TOKEN)

const tokens: { current: Tokens | null } = {
  current:
    initAccessToken && initRefreshToken
      ? { accessToken: initAccessToken, refreshToken: initRefreshToken }
      : null,
}

export const getTokens = () => tokens.current

export const setTokens = (newTokens: Tokens) => {
  tokens.current = newTokens
  localStorage.setItem(ACCESS_TOKEN, newTokens.accessToken)
  localStorage.setItem(REFRESH_TOKEN, newTokens.refreshToken)
}

export const clearTokens = () => {
  localStorage.removeItem(ACCESS_TOKEN)
  localStorage.removeItem(REFRESH_TOKEN)
}
