import c from "classnames"
import { add } from "date-fns"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import {
  generatePath,
  useLocation,
  useNavigate,
  useParams,
} from "react-router-dom"
import { useImmer } from "use-immer"

import { <PERSON>u, MenuButton, MenuItem, MenuProvider } from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import useSelectStore from "components/Ariakit/hooks/useSelectStore/useSelectStore"
import DatePicker from "components/DatePicker/DatePicker"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import PersonaIdInput from "components/PersonaIdInput/PersonaIdInput"
import Select from "components/Select/Select"
import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import dateLocalReadableFormat from "features/calendar/utils/dateLocalReadableFormat"
import useLocalStorage from "hooks/useLocalStorage"
import { RouteStrings } from "routes/RouteStrings"
import {
  Button,
  Checkbox,
  Heading,
  Input,
  notification,
  Text,
  Textarea,
} from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import { printDocument } from "utils/printDocument"

import {
  GetInvoiceQuery,
  PermissionKey,
  useDeleteInvoiceMutation,
  useEditInvoiceMutation,
  useIssueInvoiceMutation,
} from "generated/graphql"

import { paymentMethods } from "../../InvoiceOverview/InvoiceOverviewSidebar/InvoiceOverviewSidebar"
import { invoicePdfUrl } from "../../InvoiceOverview/invoicePdfUrl"
import styles from "./PatientInvoiceInformationPayment.module.css"

type PaymentForm = {
  isPayerDifferent: boolean
}

type PaymentFormErrorMessage = {
  email: string | null
}

type PatientInvoiceInformationPaymentProps = {
  invoice: GetInvoiceQuery["invoice"]
}

export const PatientInvoiceInformationPayment = ({
  invoice,
}: PatientInvoiceInformationPaymentProps) => {
  const [editInvoice] = useEditInvoiceMutation()

  const [deleteInvoice] = useDeleteInvoiceMutation()

  const { globalData } = useGlobalState()

  const [isPrintInvoice, setPrintInvoice] = useLocalStorage(
    `${globalData.actor.id}-printInvoice`,
    false
  )

  const location = useLocation()

  const subject = invoice.subject

  const hasPayerInfo = !!invoice.payerId || !!invoice.payerName
  const [paymentForm, setPaymentForm] = useImmer<PaymentForm>({
    isPayerDifferent: hasPayerInfo || false,
  })

  const navigate = useNavigate()

  const menu = useMenuStore({
    placement: "bottom-end",
    animated: true,
  })

  const { invoiceId } = useParams<{ invoiceId: string }>()

  const [paymentFormErrorMessage, setPaymentFormErrorMessage] =
    useImmer<PaymentFormErrorMessage>({
      email: null,
    })

  const [isDialogForSaveOpen, setIsDialogForSaveOpen] = useState<boolean>(false)
  const [isDialogForDeleteOpen, setIsDialogForDeleteOpen] =
    useState<boolean>(false)

  const selectStore = useSelectStore({
    defaultValue: invoice.paymentMethod || "card",
    focusLoop: "vertical",
  })

  const { t } = useTranslation()
  const { hasPermission } = usePermissions()

  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  const [issueInvoice] = useIssueInvoiceMutation({
    onCompleted: async () => {
      if (isPrintInvoice) {
        const url = invoicePdfUrl(invoice.id)

        await printDocument(url)

        // Wait for the print dialog to open before redirecting
        await new Promise((resolve) => setTimeout(resolve, 1000))
      }

      const queryParams = new URLSearchParams(location.search)

      const returnTo = queryParams.get("returnTo")

      if (returnTo) {
        navigate(decodeURIComponent(returnTo))
      } else if (canViewSubjectJournal) {
        navigate(
          generatePath(RouteStrings.subjectJournal, {
            subjectId: subject?.id || "",
          })
        )
      } else {
        navigate(RouteStrings.invoiceOverview)
      }

      notification.create({
        status: "success",
        message: t("Invoice has been submitted"),
      })
    },
    onError: (e) => {
      notification.create({
        status: "error",
        message: e.message,
      })
    },
  })

  const treatmentDate = invoice.treatmentDate
    ? dateLocalReadableFormat(invoice.treatmentDate).split("T")[0]
    : ""

  const paymentDate = invoice.paymentDate
    ? dateLocalReadableFormat(invoice.paymentDate).split("T")[0]
    : ""

  const dueDate = invoice.dueDate
    ? dateLocalReadableFormat(invoice.dueDate).split("T")[0]
    : ""

  return (
    <>
      <div className={c(styles.fullColumn, styles.header)}>
        <Heading>{t("Invoice Information & Payment")}</Heading>
        <MenuProvider store={menu}>
          <MenuButton
            variant="clear"
            store={menu}
            className={styles.menuButton}
            iconEnd={<Icon name="more-line" />}
            onClick={(e: { stopPropagation: () => void }) => {
              e.stopPropagation()
            }}
          ></MenuButton>
          <Menu>
            <MenuItem
              onClick={() => {
                setIsDialogForDeleteOpen(true)
              }}
            >
              {t("Delete draft invoice")}
            </MenuItem>
          </Menu>
        </MenuProvider>
      </div>

      <DatePicker
        hideMessage
        label={t("Arrival Date (Encounter Date)")}
        value={treatmentDate}
        onChange={(e) => {
          editInvoice({
            variables: {
              input: {
                id: invoice.id,
                treatmentDate: new Date(e.target.value),
              },
            },
          })
        }}
      />

      <DatePicker
        hideMessage
        label={t("Payment Date")}
        value={paymentDate}
        onChange={(e) => {
          editInvoice({
            variables: {
              input: {
                id: invoice.id,
                paymentDate: new Date(e.target.value),
                dueDate: add(new Date(e.target.value), { days: 14 }),
              },
            },
          })
          editInvoice({
            variables: {
              input: {
                id: invoice.id,
                paymentDate: new Date(e.target.value),
              },
            },
          })
        }}
      />

      <DatePicker
        hideMessage
        label={t("Due Date")}
        value={dueDate}
        onChange={(e) => {
          editInvoice({
            variables: {
              input: {
                id: invoice.id,
                dueDate: new Date(e.target.value),
              },
            },
          })
        }}
      />

      <Select
        selectStore={selectStore}
        options={paymentMethods}
        label={t("Payment method")}
        hideMessage
        onSelectChange={(value) => {
          editInvoice({
            variables: {
              input: {
                id: invoice.id,
                paymentMethod: value,
              },
            },
            onError: (error) => {
              notification.create({
                status: "error",
                message: error.message || t("Failed to update payment method"),
              })
            },
          })
        }}
      />

      <Checkbox
        checked={paymentForm.isPayerDifferent}
        label={t("Payer is different from patient")}
        className={styles.fullColumn}
        onChange={(e) => {
          setPaymentForm((draft) => {
            draft.isPayerDifferent = e.target.checked
          })

          if (!e.target.checked) {
            editInvoice({
              variables: {
                input: {
                  id: invoice.id,
                  payerId: "",
                  payerName: "",
                },
              },
            })
          }
        }}
      />

      {paymentForm.isPayerDifferent && (
        <>
          <PersonaIdInput
            label={t("Payer Id")}
            autoComplete="off"
            defaultValue={invoice.payerId || ""}
            onBlur={(e) => {
              editInvoice({
                variables: {
                  input: {
                    id: invoice.id,
                    payerId: e.target.value,
                  },
                },
              })
            }}
          />
          <Input
            label={t("Payer's full name")}
            autoComplete="off"
            defaultValue={invoice.payerName || ""}
            onBlur={(e) => {
              editInvoice({
                variables: {
                  input: {
                    id: invoice.id,
                    payerName: e.target.value,
                  },
                },
              })
            }}
            hideMessage
          />
        </>
      )}

      <Textarea
        label={t("Comments")}
        className={styles.fullColumn}
        defaultValue={invoice.comment || ""}
        onBlur={(e) => {
          editInvoice({
            variables: {
              input: {
                id: invoice.id,
                comment: e.target.value,
              },
            },
          })
        }}
        rows={5}
      />

      <Checkbox
        checked={isPrintInvoice}
        className={styles.fullColumn}
        label={t("Print invoice")}
        onChange={(e) => {
          setPrintInvoice(e.target.checked)
        }}
      />

      <Checkbox
        label={t("Send invoice via email")}
        className={styles.fullColumn}
        checked={invoice.sendInvoiceMail || false}
        onChange={(e) => {
          const isChecked = e.target.checked
          const email = isChecked
            ? invoice.payerEmail || subject?.email || ""
            : ""

          editInvoice({
            variables: {
              input: {
                id: invoice.id,
                sendInvoiceMail: isChecked,
                payerEmail: email,
              },
            },
          })

          setPaymentFormErrorMessage((draft) => {
            draft.email = null
          })
        }}
      />

      {invoice.sendInvoiceMail && (
        <Input
          label={t("Email address")}
          type="email"
          defaultValue={invoice.payerEmail || subject?.email || ""}
          onBlur={(e) => {
            if (!e.target.checkValidity()) {
              setPaymentFormErrorMessage((draft) => {
                draft.email = t("Email address is not valid")
              })

              return
            }

            setPaymentFormErrorMessage((draft) => {
              draft.email = null
            })

            editInvoice({
              variables: {
                input: {
                  id: invoice.id,
                  payerEmail: e.target.value,
                },
              },
            })
          }}
          status={paymentFormErrorMessage.email ? "error" : undefined}
          message={
            paymentFormErrorMessage.email
              ? paymentFormErrorMessage.email
              : undefined
          }
        />
      )}

      <Restricted to={PermissionKey.BillingIssuedItemCreate}>
        <div className={styles.fullColumn}>
          <Button
            variant="filled"
            disabled={invoice.invoiceLines.length === 0}
            onClick={() => setIsDialogForSaveOpen(true)}
          >
            {t("Submit")}
          </Button>
        </div>
      </Restricted>

      <Dialog
        isOpen={isDialogForSaveOpen}
        title={t("Are you sure that you want to submit the invoice?")}
        onClose={() => setIsDialogForSaveOpen(false)}
        actions={
          <>
            <Button
              variant="clear"
              onClick={() => {
                setIsDialogForSaveOpen(false)
              }}
            >
              {t("Save as draft")}
            </Button>
            <Button
              onClick={() => {
                setIsDialogForSaveOpen(false)

                issueInvoice({
                  variables: {
                    issueInvoiceId: invoiceId || "",
                  },
                })
              }}
              variant="filled"
            >
              {t("Submit")}
            </Button>
          </>
        }
      >
        <Text>{t("Invoice cannot be edited after it is submitted")}</Text>
      </Dialog>

      <Dialog
        isOpen={isDialogForDeleteOpen}
        title={t("Delete draft invoice")}
        onClose={() => setIsDialogForDeleteOpen(false)}
        actions={
          <>
            <Button
              variant="clear"
              onClick={() => {
                setIsDialogForDeleteOpen(false)
              }}
            >
              {t("Keep invoice")}
            </Button>
            <Button
              onClick={() => {
                setIsDialogForDeleteOpen(false)

                deleteInvoice({
                  variables: {
                    input: {
                      id: invoice.id,
                    },
                  },
                  onError: () => {
                    notification.create({
                      status: "error",
                      message: t(
                        "Something went wrong, could not delete invoice"
                      ),
                    })
                  },
                  onCompleted: () => {
                    navigate(generatePath(RouteStrings.invoiceOverview))
                  },
                })
              }}
              status="error"
            >
              {t("Delete")}
            </Button>
          </>
        }
      >
        <Text>
          This will delete this draft invoice for {invoice.subject.name}. This
          cannot be undone.
        </Text>
      </Dialog>
    </>
  )
}
