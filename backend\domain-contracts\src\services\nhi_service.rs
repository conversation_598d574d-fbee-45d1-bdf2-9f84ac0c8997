//! NHI (National Health Insurance) service contract

use crate::errors::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use leviosa_domain_types::*;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum SubjectInsuranceStatus {
    Insured {
        insurance_category: String,
        maximum_payable_by_subject: f64,
        insurance_percentage: i32,
        payment_status_serial_number: i64,
    },
    Uninsured,
    Unknown,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MedicalBill {
    pub invoice_id: InvoiceId,
    pub subject_id: SubjectId,
    pub provider_id: ProviderId,
    pub organisation_id: OrganisationId,
    pub billing_codes: Vec<BillingCode>,
    pub total_amount: i64, // Amount in cents
    pub service_date: DateTime<Utc>,
    pub receipt_number: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BillingCode {
    pub code: String,
    pub description: String,
    pub amount: i64, // Amount in cents
    pub quantity: i32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NhiSubmissionResult {
    #[serde(rename = "RequestXml")]
    pub request_xml: String,
    #[serde(rename = "RequestJson")]
    pub request_json: String,
    #[serde(rename = "ResponseXml")]
    pub response_xml: String,
}

/// Contract for NHI service integration
#[async_trait]
#[mockall::automock]
pub trait NhiService: Send + Sync {
    /// Get the insurance payment status for a subject
    async fn get_subject_payment_status(
        &self,
        subject_id: SubjectId,
        token: &str,
    ) -> Result<SubjectInsuranceStatus>;

    /// Submit a medical bill to NHI for processing
    async fn submit_invoice(
        &self,
        medical_bill: MedicalBill,
        on_behalf_of: &str,
        token: &str,
    ) -> Result<NhiSubmissionResult>;
}
