//! Calendar notification service contract

use crate::errors::Result;
use async_trait::async_trait;
use leviosa_domain_types::*;

/// Contract for calendar notification service
#[async_trait]
#[mockall::automock]
pub trait CalendarNotificationService: Send + Sync {
    /// Send appointment reminder notification
    async fn send_appointment_reminder(
        &self,
        event_instance_id: EventInstanceId,
        token: &str,
    ) -> Result<()>;

    /// Send appointment confirmation notification
    async fn send_appointment_confirmation(
        &self,
        event_instance_id: EventInstanceId,
        token: &str,
    ) -> Result<()>;

    /// Send appointment cancellation notification
    async fn send_appointment_cancellation(
        &self,
        event_instance_id: EventInstanceId,
        token: &str,
    ) -> Result<()>;
}
