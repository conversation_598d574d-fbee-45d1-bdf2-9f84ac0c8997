import { useEffect } from "react"

import {
  useComboboxStore,
  useFilter,
  useSelectStore,
} from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import { usePersistedPreference } from "hooks/usePersistedPreference"

import { useGetServiceTypesQuery } from "generated/graphql"

import styles from "./SelectServiceType.module.css"
import { SetDefaultServiceType } from "./SetDefaultServiceType"

type SelectServiceTypeProps = {
  serviceTypeId?: string
  required?: boolean
  hideMessage?: boolean
  useDefaultServiceType?: boolean
  onChange?: (value: string) => void
}

export const SelectServiceType = ({
  serviceTypeId,
  required = false,
  hideMessage = false,
  useDefaultServiceType = false,
  onChange,
}: SelectServiceTypeProps) => {
  const comboboxStore = useComboboxStore()

  const { value: comboboxValue } = comboboxStore.useState()

  const { data: serviceTypes, loading } = useGetServiceTypesQuery()

  const [defaultServiceType] = usePersistedPreference<string>({
    key: "defaultServiceType",
  })

  const serviceTypeOptions = !loading
    ? [
        ...(required ? [] : [{ label: "None", value: "none" }]),
        ...(serviceTypes?.externalServiceTypes?.map(({ name, id }) => ({
          label: name,
          value: id,
        })) || []),
      ]
    : []

  const defaultValue =
    serviceTypeOptions.length > 0
      ? serviceTypeId ||
        (useDefaultServiceType ? defaultServiceType : null) ||
        "none"
      : undefined

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue,
    focusLoop: "vertical",
  })

  const { value: selectValue } = selectStore.useState()

  useEffect(() => {
    if (serviceTypeId) {
      selectStore.setValue(serviceTypeId)
      return
    }
    if (useDefaultServiceType && defaultServiceType) {
      selectStore.setValue(defaultServiceType)
    }
  }, [serviceTypeId, defaultServiceType, useDefaultServiceType, selectStore])

  const { filteredList } = useFilter({
    defaultItems: serviceTypeOptions,
    value: comboboxValue,
  })

  const value = selectValue ? String(selectValue) : null

  return (
    <FiltrableSelect
      options={serviceTypeOptions}
      sameWidth
      filteredOptions={filteredList}
      selectStore={selectStore}
      comboboxStore={comboboxStore}
      label="Service"
      name="serviceTypeId"
      className={styles.span5}
      required={required}
      hideMessage={hideMessage}
      onSelectChange={(value) => {
        if (typeof value !== "string") return

        onChange?.(value)
      }}
      message={
        useDefaultServiceType && <SetDefaultServiceType serviceTypeId={value} />
      }
    />
  )
}
