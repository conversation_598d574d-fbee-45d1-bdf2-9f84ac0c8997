.panel.panel {
  border-color: var(--color-blue-primary-on-white-active);
  white-space: pre-wrap;
}

.container {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  grid-gap: 8px;
}

.container > svg {
  margin-top: 2px;
}

.editButton {
  color: var(--color-brand-primary-blue);
  height: fit-content;
  align-self: flex-start;
}

.modal {
  width: 500px;
}

.modalForm {
  display: grid;
  gap: 16px;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 24px;
}

.textButton,
.textButton:hover {
  all: unset;
  color: var(--color-gray-60);
  width: fit-content;
  cursor: pointer;
}

.textButton:hover {
  color: var(--color-gray-70);
}
