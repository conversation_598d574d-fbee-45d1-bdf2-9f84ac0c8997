import {
  Combobox as ComboboxAriaKit,
  ComboboxProps as ComboboxPropsAriakit,
  ComboboxProvider as ComboboxProviderAriaKit,
  ComboboxProviderProps,
} from "@ariakit/react"
import { forwardRef } from "react"

import styles from "./Combobox.module.css"

export type ComboboxProps = ComboboxPropsAriakit

export const Combobox = forwardRef<HTMLInputElement, ComboboxProps>(
  ({ className = "", ...rest }, ref) => {
    return (
      <ComboboxAriaKit
        {...rest}
        className={`${styles.combobox} ${className}`}
        ref={ref}
      />
    )
  }
)

export const ComboboxProvider = (props: ComboboxProviderProps) => (
  <ComboboxProviderAriaKit animated {...props} />
)
