import { Trans, useTranslation } from "react-i18next"

import UserIllustration from "@leviosa/assets/illustrations/user.svg?react"

import ErrorPage from "./ErrorPage"

export default function UnauthorizedPage() {
  const { t } = useTranslation("features", {
    keyPrefix: "mainLayout.Unauthorized",
  })

  const suggestions = [
    <Trans i18nKey={t("suggestion1")} components={{ bold: <b /> }} />,
    <Trans i18nKey={t("suggestion2")} components={{ bold: <b /> }} />,
    <Trans i18nKey={t("suggestion3")} components={{ bold: <b /> }} />,
  ]

  return (
    <ErrorPage
      illustration={<UserIllustration />}
      heading={t("heading")}
      message={t("message")}
      suggestions={suggestions}
      subMessage={t("subMessage")}
    />
  )
}
