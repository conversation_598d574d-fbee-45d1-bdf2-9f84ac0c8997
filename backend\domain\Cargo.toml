[package]
name = "leviosa-domain"
version = "0.1.0"
edition.workspace = true


# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dependencies]

leviosa-domain-types = { path = "../domain-types" }
leviosa-domain-contracts = { path = "../domain-contracts" }
leviosa-macros = { path = "../macros" }
anyhow.workspace = true
chrono.workspace = true
getset.workspace = true
sqlx.workspace = true
thiserror.workspace = true
uuid.workspace = true
sea-orm.workspace = true
serde.workspace = true
serde_json.workspace = true
tracing.workspace = true
async-trait.workspace = true
validator.workspace = true
regex.workspace = true
base64.workspace = true
rand.workspace = true
paste.workspace = true
mockall.workspace = true
futures.workspace = true
ammonia.workspace = true
csv.workspace = true
rrule.workspace = true

[dev-dependencies]
migration = { path = "../migration" }
leviosa-testing = { path = "../testing" }
dotenv.workspace = true
envy.workspace = true
tokio.workspace = true
insta.workspace = true
rstest.workspace = true
async-std.workspace = true
tokio-shared-rt.workspace = true