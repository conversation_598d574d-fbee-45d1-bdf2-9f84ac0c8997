//! Composition Root for Dependency Injection
//!
//! This crate is responsible for creating all concrete implementations
//! and wiring them together. It's the only crate that depends on both
//! domain and infrastructure layers.

use std::sync::Arc;
use leviosa_domain_contracts::services::*;

/// Service container that holds all service implementations
pub struct ServiceContainer {
    pub email_sender: Arc<dyn EmailSender>,
    pub pdf_generator: Arc<dyn PdfGenerator>,
    pub customer_service: Arc<dyn CustomerService>,
    pub nhi_service: Arc<dyn NhiService>,
    pub online_payment_service: Arc<dyn OnlinePaymentService>,
    pub audit_logger: Arc<dyn AuditLogger>,
    pub text_message_integration: Arc<dyn TextMessageIntegration>,
    pub calendar_notification_service: Arc<dyn CalendarNotificationService>,
    pub doctor_letter_api: Arc<dyn DoctorLetterAndReferralApi>,
    pub external_organisation_service: Arc<dyn ExternalOrganisationIntegration>,
    pub oracle_api: Arc<dyn OracleApi>,
    pub prescription_api: Arc<dyn PrescriptionApi>,
    pub file_repo: Arc<dyn FileRepo>,
    pub national_registry_service: Arc<dyn NationalRegistry>,
    pub notification_service: Arc<dyn NotificationService>,
    pub service_communicator: Arc<dyn ServiceCommunicator>,
}

impl ServiceContainer {
    /// Create a new service container with production implementations
    pub fn new_production() -> Self {
        // For now, use mock implementations until we have proper production setup
        Self::new_mock()
    }

    /// Create a new service container with mock implementations for testing
    pub fn new_mock() -> Self {
        Self {
            email_sender: Arc::new(MockEmailSender::new()),
            pdf_generator: Arc::new(MockPdfGenerator::new()),
            customer_service: Arc::new(MockCustomerService::new()),
            nhi_service: Arc::new(leviosa_infrastructure::nhi::MockNhiService),
            online_payment_service: Arc::new(MockOnlinePaymentService::new()),
            audit_logger: Arc::new(MockAuditLogger::new()),
            text_message_integration: Arc::new(SimpleTextMessageMock::new()),
            calendar_notification_service: Arc::new(MockCalendarNotificationService::new()),
            doctor_letter_api: Arc::new(MockDoctorLetterAndReferralApi::new()),
            external_organisation_service: Arc::new(MockExternalOrganisationIntegration::new()),
            oracle_api: Arc::new(MockOracleApi::new()),
            prescription_api: Arc::new(MockPrescriptionApi::new()),
            file_repo: Arc::new(MockFileRepo::new()),
            national_registry_service: Arc::new(MockNationalRegistry::new()),
            notification_service: Arc::new(MockNotificationService::new()),
            service_communicator: Arc::new(MockServiceCommunicator::new()),
        }
    }

    /// Create a new service container with empty implementations (no-op)
    pub fn new_empty() -> Self {
        Self {
            email_sender: Arc::new(EmptyEmailSender::new()),
            pdf_generator: Arc::new(leviosa_infrastructure::pdf_generator::EmptyPdfGenerator),
            customer_service: Arc::new(EmptyCustomerService::new()),
            nhi_service: Arc::new(leviosa_infrastructure::nhi::EmptyNhiService),
            online_payment_service: Arc::new(EmptyOnlinePaymentService::new()),
            audit_logger: Arc::new(EmptyAuditLogger::new()),
            text_message_integration: Arc::new(EmptyTextMessageIntegration::new()),
            calendar_notification_service: Arc::new(EmptyCalendarNotificationService::new()),
            doctor_letter_api: Arc::new(EmptyDoctorLetterAndReferralApi::new()),
            external_organisation_service: Arc::new(EmptyExternalOrganisationIntegration::new()),
            oracle_api: Arc::new(EmptyOracleApi::new()),
            prescription_api: Arc::new(EmptyPrescriptionApi::new()),
            file_repo: Arc::new(EmptyFileRepo::new()),
            national_registry_service: Arc::new(EmptyNationalRegistry::new()),
            notification_service: Arc::new(EmptyNotificationService::new()),
            service_communicator: Arc::new(EmptyServiceCommunicator::new()),
        }
    }
}
