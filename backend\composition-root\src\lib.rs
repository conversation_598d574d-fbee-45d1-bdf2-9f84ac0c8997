//! Composition Root for Dependency Injection
//!
//! This crate is responsible for creating all concrete implementations
//! and wiring them together. It's the only crate that depends on both
//! domain and infrastructure layers.

use std::sync::Arc;
use leviosa_domain_contracts::services::*;

/// Service container that holds all service implementations
pub struct ServiceContainer {
    pub email_sender: Arc<dyn EmailSender>,
    pub pdf_generator: Arc<dyn PdfGenerator>,
    pub customer_service: Arc<dyn CustomerService>,
    pub nhi_service: Arc<dyn NhiService>,
    pub online_payment_service: Arc<dyn OnlinePaymentService>,
    pub audit_logger: Arc<dyn AuditLogger>,
}

impl ServiceContainer {
    /// Create a new service container with mock implementations for testing
    pub fn new_mock() -> Self {
        Self {
            email_sender: Arc::new(MockEmailSender::new()),
            pdf_generator: Arc::new(MockPdfGenerator::new()),
            customer_service: Arc::new(MockCustomerService::new()),
            nhi_service: Arc::new(leviosa_infrastructure::nhi::MockNhiService),
            online_payment_service: Arc::new(MockOnlinePaymentService::new()),
            audit_logger: Arc::new(MockAuditLogger::new()),
        }
    }

    /// Create a new service container with empty implementations (no-op)
    pub fn new_empty() -> Self {
        // For now, use mock implementations as empty implementations
        Self::new_mock()
    }
}
