import {
  addDays,
  differenceInHours,
  endOfDay,
  isWithinInterval,
  max,
  min,
  startOfDay,
  startOfWeek,
} from "date-fns"
import { View } from "react-big-calendar"

import isNotNull from "utils/isNotNull"

import {
  AvailabilityScheduleFragmentFragment,
  Weekday,
} from "generated/graphql"

const mergeDateAndTime = (date: Date, time: string) => {
  const [hours, minutes] = time.split(":").map(Number)
  // What about timezone?
  const mergedDate = new Date(date)
  mergedDate.setHours(hours, minutes, 0, 0)
  return mergedDate
}

export type ServiceTypeAvailabilities =
  AvailabilityScheduleFragmentFragment["serviceTypeAvailabilities"][0] & {
    blockId: string
  }

const weekdays = [
  Weekday.Monday,
  Weekday.Tuesday,
  Weekday.Wednesday,
  Weekday.Thursday,
  Weekday.Friday,
  Weekday.Saturday,
  Weekday.Sunday,
]
const getDateOfRuleDay = (currentDate: Date, weekday: Weekday) => {
  const start = startOfWeek(currentDate, {
    weekStartsOn: 1,
  })

  const daysFromStartOfWeek = weekdays.indexOf(weekday)
  return addDays(start, daysFromStartOfWeek)
}

function getAvailabilityBlocksForDay(
  serviceTypeAvailability: AvailabilityScheduleFragmentFragment["serviceTypeAvailabilities"][0],
  date: Date
) {
  // Adjust for the fact that JS Date uses 0 for Sunday and the weekdays uses 0 for Monday
  const currentWeekday = weekdays[(date.getDay() + 6) % 7]

  return serviceTypeAvailability.blockRules
    .map(({ weekday, fromTime, toTime, id }) => {
      if (currentWeekday !== weekday || !fromTime || !toTime) return null

      const start = mergeDateAndTime(date, fromTime)
      const end = mergeDateAndTime(date, toTime)

      const timeDifferenceInHours = differenceInHours(start, end)
      const allDay = timeDifferenceInHours >= 24

      return {
        title: "",
        start,
        end,
        allDay,
        resource: {
          ...serviceTypeAvailability,
          type: "availability" as const,
          blockId: id,
        },
      }
    })
    .filter(isNotNull)
}

/**
 * Generates availability blocks for a given week based on the service type availability and date range.
 *
 * @param serviceTypeAvailability - The availability schedule fragment for a specific service type.
 * @param date - The reference date to calculate the week.
 * @param scheduleFromDate - The start date of the schedule range (inclusive).
 * @param scheduleToDate - The end date of the schedule range (inclusive).
 * @returns An array of availability blocks for the specified week, or null if the block is outside the schedule range.
 */
function getAvailabilityBlocksForWeek(
  serviceTypeAvailability: AvailabilityScheduleFragmentFragment["serviceTypeAvailabilities"][0],
  date: Date,
  scheduleFromDate: string | null,
  scheduleToDate: string | null
) {
  return serviceTypeAvailability.blockRules
    .map(({ fromTime, weekday, toTime, id }) => {
      const closestDayOfWeek = getDateOfRuleDay(date, weekday)

      if (!scheduleFromDate || !scheduleToDate) return null

      // if closestDayOfWeek is before scheduleFromDate or after scheduleToDate, return null
      if (
        closestDayOfWeek < new Date(scheduleFromDate) ||
        closestDayOfWeek > new Date(scheduleToDate)
      ) {
        return null
      }

      if (!closestDayOfWeek || !fromTime || !toTime) return null

      const start = mergeDateAndTime(new Date(closestDayOfWeek), fromTime)
      const end = mergeDateAndTime(new Date(closestDayOfWeek), toTime)

      const timeDifferenceInHours = differenceInHours(start, end)
      const allDay = timeDifferenceInHours >= 24

      return {
        title: "",
        start,
        end,
        allDay,
        resource: {
          ...serviceTypeAvailability,
          type: "availability" as const,
          blockId: id,
        },
      }
    })
    .filter(isNotNull)
}

export const getAvailabilityBlocks = (
  availabilitySchedules: AvailabilityScheduleFragmentFragment[],
  currentDate: Date,
  view: View
) => {
  if (!(view === "day" || view === "week" || view === "work_week")) return []

  return availabilitySchedules
    .flatMap((availability) => {
      if (!availability.fromDate || !availability.toDate) {
        return null
      }

      if (view === "day") {
        const date = startOfDay(currentDate)
        if (
          !isWithinInterval(date, {
            start: new Date(availability.fromDate),
            end: new Date(availability.toDate),
          })
        ) {
          // Date isn't within the availability schedule
          return null
        }

        return availability.serviceTypeAvailabilities.flatMap(
          (serviceTypeAvailability) => {
            return getAvailabilityBlocksForDay(
              serviceTypeAvailability,
              currentDate
            )
          }
        )
      }

      const weekLength = view === "work_week" ? 5 : 7
      // start of the latest of the following:
      // - the start current week
      // - the availability schedule from date
      const startOfCurrentWeek = startOfWeek(currentDate, {
        weekStartsOn: 1,
      })
      const fromDate = max([
        startOfCurrentWeek,
        new Date(availability.fromDate),
      ])

      // end of the earliest of the following:
      // - the end of the current week
      // - the availability schedule to date
      const endOfCurrentWeek = addDays(startOfCurrentWeek, weekLength - 1)
      const toDate = endOfDay(
        min([endOfCurrentWeek, new Date(availability.toDate)])
      )

      // The availability schedule is not within the current week
      if (toDate < fromDate) {
        return null
      }

      return availability.serviceTypeAvailabilities.flatMap(
        (serviceTypeAvailability) =>
          getAvailabilityBlocksForWeek(
            serviceTypeAvailability,
            currentDate,
            availability.fromDate,
            availability.toDate
          )
      )
    })
    .filter(isNotNull)
}

/**
 * Retrieves availability blocks for a list of providers.
 *
 * @param providers - An array of provider objects, each containing an `id` and an array of `availabilitySchedules`.
 * @param currentDate - The current date to base the availability blocks on.
 * @param view - The view context for which the availability blocks are being retrieved.
 * @returns An array of availability blocks, each augmented with the provider's `resourceId`.
 */
export const getAvailabilityBlocksForProvider = (
  providers: {
    id: string
    availabilitySchedules: AvailabilityScheduleFragmentFragment[]
  }[],
  currentDate: Date,
  view: View
) => {
  return providers.flatMap(({ id, availabilitySchedules }) => {
    return getAvailabilityBlocks(availabilitySchedules, currentDate, view).map(
      (availability) => ({
        ...availability,
        resourceId: id,
      })
    )
  })
}
