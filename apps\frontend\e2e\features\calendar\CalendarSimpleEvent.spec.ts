import { test, expect, Page } from "@playwright/test"

import { CheckUrl, navigateFromPowermenu } from "../utils/testUtils"

/* eslint-disable-file playwright/no-unnecessary-waiting */
/* eslint-disable playwright/no-force-option */
/* eslint-disable playwright/no-wait-for-timeout */
/* eslint-disable playwright/no-useless-await */
/* eslint-disable playwright/no-wait-for-timeout */

// Annotate file as serial so that events that are created don't interfere with events in different test cases
test.describe.configure({ mode: "serial" })

test.describe("Simple Calendar event tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("http://localhost:3000/")
    await page.waitForLoadState()
    // Navigate
    await navigateFromPowermenu(page, "Calendar", ["calendar"])
    await page.waitForLoadState()
    await page.click('[data-testid="calendar-view-select"]')
    await page.click('[data-testid="select-option-work_week"]')
    await CheckUrl(
      page,
      /calendar\/work_week\?date=\d{4}-\d{2}-\d{2}&provider=[a-f0-9-]+/
    )
  })

  test("Should open Create modal when double clicking on the Calendar", async ({
    page,
  }) => {
    // Arrange
    // Act
    await clickAnEmptySlot(page)
    await page.waitForTimeout(1000)

    const modal = await page.getByTestId("create-event-instance-form")

    // Assert
    await expect(modal).toBeVisible()
  })

  test("Should require event title to be more than three characters", async ({
    page,
  }) => {
    // Arrange
    // Open the Create event modal
    await clickAnEmptySlot(page)
    await page.waitForLoadState()
    await expect(page.locator('[class*="Modal_contentWrap"]')).toBeVisible()

    // Act
    // Enter title that is too short
    await page.getByRole("button", { name: "Add title" }).click()
    const titleInput = page.locator('input[name="title"]')
    await titleInput.fill("ab")
    // Click the submit button
    await page.click('button[type="submit"]')

    // Assert
    const errorPanel = page.getByText(
      "Title must be at least 3 characters long if no service is selected"
    )
    await expect(errorPanel).toBeVisible()
  })

  test("Should have added current user as participant of new event", async ({
    page,
  }) => {
    // Arrange
    // Open the Create event modal
    await clickAnEmptySlot(page)
    await page.waitForLoadState()
    await expect(page.locator('[class*="Modal_contentWrap"]')).toBeVisible()
    await page.waitForTimeout(3000) //wait for participants list to be rendered
    // Act
    // Assert
    const participantProvider = await page
      .locator("#event-instance-form")
      .getByText("Albus Dumbledore", { exact: true })
    expect(await participantProvider.count()).toBe(1)
  })

  // This test is skipped because it needs maintenance to work with new cancel functionality DEV-4438
  test.skip("Should save event with minimal data, only title", async ({
    page,
  }) => {
    // Arrange
    // Open the Create event modal
    await clickAnEmptySlot(page)
    await page.waitForLoadState()
    await expect(page.locator('[class*="Modal_contentWrap"]')).toBeVisible()

    // Act
    // Enter title
    await page.getByRole("button", { name: "Add title" }).click()
    await page.fill('[name="title"]', "automated test title")
    await page.waitForTimeout(1000) //wait for participants list to be rendered
    // Click the create button
    await page.getByRole("button", { name: "Create" }).click()
    await page.waitForLoadState()

    // Assert
    // Error panel is not visible
    const errorPanel = page.locator(
      '[class*="Panel_panel"][class*="Panel_error"]'
    )
    await expect(errorPanel).toBeHidden()

    // Assert that the event is visible in the calendar
    const event = page.locator(
      'div[class*="Event_eventTitle"]:has-text("automated test title")'
    )
    await expect(event).toBeVisible()

    // Cleanup - delete the event
    await page
      .locator(
        'div[class*="Event_eventTitle"]:has-text("automated test title")'
      )
      .click({ force: true })
    await page.waitForLoadState()
    page.locator('[class*="ViewEventInstance_viewModal"]').isVisible()
    page.locator('[aria-label="Cancel Event"]').click()
    await page.waitForLoadState()
    page.locator('button:has-text("Yes")').isVisible()
    page.locator('button:has-text("Yes")').click()
    await page.waitForLoadState()

    const deletedEvent = page.locator(
      'div[class*="Event_eventTitle"]:has-text("automated test title")'
    )
    await expect(deletedEvent).toBeHidden()
  })

  // This test is skipped because it needs maintenance to work with new cancel functionality DEV-4438
  test.skip("Should delete event when cancelled", async ({ page }) => {
    // Arrange
    // Create an event to be deleted
    await clickAnEmptySlot(page)
    await page.waitForLoadState()
    await expect(page.locator('[class*="Modal_contentWrap"]')).toBeVisible()
    await page.getByRole("button", { name: "Title" }).click()
    await page.fill('[name="title"]', "test me")
    await page.waitForTimeout(3000) // wait for participants list to be rendered
    page.getByRole("button", { name: "Create" }).click({ force: true })
    await page.waitForLoadState()

    // Act
    await page
      .locator('div[class*="Event_eventTitle"]:has-text("test me")')
      .click({ force: true })
    await page.waitForLoadState()
    page.locator('[class*="ViewEventInstance_viewModal"]').isVisible()
    page.locator('[aria-label="Cancel Event"]').click()
    await page.waitForLoadState()
    page.locator('button:has-text("Yes")').isVisible()
    page.locator('button:has-text("Yes")').click()
    await page.waitForLoadState()

    // Assert
    // Event is not visible in the calendar
    const deletedEvent = page.locator(
      'div[class*="Event_eventTitle"]:has-text("test me")'
    )
    await expect(deletedEvent).toBeHidden()
  })

  /* 
    Implement clicking an empty slot in Calendar view, that is a slot where no event is present.
  */
  const usedSlots: number[] = [50]
  let index = 0
  async function clickAnEmptySlot(page: Page) {
    const calendarTimeslotGroups = page.locator("div.rbc-timeslot-group")

    const numberOfCalendarTimeslotGroups = await calendarTimeslotGroups.count()

    // if there are suspiciously few slots, throw an error
    if (numberOfCalendarTimeslotGroups < 20) {
      throw new Error(
        "Could not locate multiple rbc-timeslot-group, check if the test has navigated to the correct view."
      )
    }

    // get the last slot that was used
    const lastUsedSlot = usedSlots[index]
    // check if there are more slots available
    const nextSlot = lastUsedSlot + 3
    if (numberOfCalendarTimeslotGroups > nextSlot) {
      usedSlots.push(nextSlot)
      index = index + 1
      calendarTimeslotGroups.nth(nextSlot).dblclick({ force: true })
    } else {
      throw new Error(
        "Could not locate an empty slot in the calendar view to use for testing."
      )
    }
  }
})
