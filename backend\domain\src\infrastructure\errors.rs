use super::{
    auth::EncryptionError, doctor_letter_and_referral_api::DoctorLetterAndReferralApiError,
    resource::Resource,
};
use crate::{
    auth::PasswordError, prescription_api::PrescriptionApiError,
    service_communicator::ServiceCommunicatorError,
    text_message_integration::TextMessageIntegrationError,
};
use sea_orm::DbErr;
use std::fmt::{Debug, Display};

mod error_context;
pub use error_context::ErrorContext;

pub type Result<T> = std::result::Result<T, Error>;

#[derive(Debug)]
pub enum Error {
    Authorization(AuthorizationError),
    Password(PasswordError),
    Input(InputError),
    NotFound(NotFoundError),
    Permissions(PermissionsError),
    CrossOrganization(CrossOrganisationError),
    Custom(CustomError),
    Internal(anyhow::Error),
    Database(DbErr),
    PrescriptionApi(PrescriptionApiError),
    ServiceCommunicator(ServiceCommunicatorError),
    NotificationService(NotificationIntegrationError),
    TextMessageIntegrationError(TextMessageIntegrationError),
    DoctorLetterAndReferralApi(DoctorLetterAndReferralApiError),
    Encryptor(EncryptionError),
    NotConfigured(NotConfiguredError),
}

impl From<ServiceCommunicatorError> for Error {
    fn from(e: ServiceCommunicatorError) -> Self {
        Self::ServiceCommunicator(e)
    }
}

impl From<PrescriptionApiError> for Error {
    fn from(e: PrescriptionApiError) -> Self {
        Self::PrescriptionApi(e)
    }
}

impl From<NotificationIntegrationError> for Error {
    fn from(e: NotificationIntegrationError) -> Self {
        Self::NotificationService(e)
    }
}

impl From<TextMessageIntegrationError> for Error {
    fn from(e: TextMessageIntegrationError) -> Self {
        Self::TextMessageIntegrationError(e)
    }
}

impl From<DoctorLetterAndReferralApiError> for Error {
    fn from(e: DoctorLetterAndReferralApiError) -> Self {
        Self::DoctorLetterAndReferralApi(e)
    }
}

impl From<DbErr> for Error {
    fn from(e: DbErr) -> Self {
        Self::Database(e)
    }
}

impl From<AuthorizationError> for Error {
    fn from(e: AuthorizationError) -> Self {
        Self::Authorization(e)
    }
}

impl From<PasswordError> for Error {
    fn from(e: PasswordError) -> Self {
        Self::Password(e)
    }
}

impl From<EncryptionError> for Error {
    fn from(e: EncryptionError) -> Self {
        Self::Encryptor(e)
    }
}

impl From<InputError> for Error {
    fn from(e: InputError) -> Self {
        Self::Input(e)
    }
}

impl From<NotFoundError> for Error {
    fn from(e: NotFoundError) -> Self {
        Self::NotFound(e)
    }
}

impl From<PermissionsError> for Error {
    fn from(e: PermissionsError) -> Self {
        Self::Permissions(e)
    }
}

impl From<CrossOrganisationError> for Error {
    fn from(e: CrossOrganisationError) -> Self {
        Self::CrossOrganization(e)
    }
}

impl From<CustomError> for Error {
    fn from(e: CustomError) -> Self {
        Self::Custom(e)
    }
}

impl From<NotConfiguredError> for Error {
    fn from(e: NotConfiguredError) -> Self {
        Self::NotConfigured(e)
    }
}

impl From<anyhow::Error> for Error {
    fn from(e: anyhow::Error) -> Self {
        Self::Internal(e)
    }
}

impl From<sqlx::Error> for Error {
    fn from(e: sqlx::Error) -> Self {
        Self::Internal(e.into())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum NotificationIntegrationError {
    #[error("Notification error: {0}")]
    NotificationError(String),
    #[error("No phone number error: {0}")]
    NoPhoneNumber(String),
    #[error("Text message integration error: {0}")]
    TextMessageIntegrationError(TextMessageIntegrationError),
}

#[derive(Debug, thiserror::Error)]
pub enum AuthorizationError {
    #[error("authorization is required but no access token was provided")]
    NotAuthenticated,
    #[error("actor must be app admin to perform this operation")]
    MustBeAdmin,
    #[error("actor is authorized as admin, but must be user to perform this operation")]
    MustBeUser,
    #[error("malformed access token: {0}")]
    MalformedToken(#[from] anyhow::Error),
    // Catch all error for authorization. Sometimes we don't want to reveal the reason for rejection
    #[error("Unauthorized")]
    Unauthorized,
}

#[derive(Debug, thiserror::Error)]
pub struct InputError {
    arg_name: &'static str,
    message: String,
}

impl InputError {
    #[doc(hidden)]
    pub fn new(arg_name: &'static str, message: impl Into<String>) -> Self {
        Self {
            arg_name,
            message: message.into(),
        }
    }

    pub fn arg_name(&self) -> &'static str {
        self.arg_name
    }
}

impl Display for InputError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "input error: {} - {}", self.arg_name, self.message)
    }
}

#[derive(Debug, thiserror::Error)]
#[error("{resource_kind} with {search_field} \"{resource_id}\" was not found")]
pub struct NotFoundError {
    resource_kind: String,
    resource_id: String,
    search_field: String,
}

impl NotFoundError {
    pub fn new<T: Resource>(resource_id: T::Id) -> Self
    where
        T::Id: Display,
    {
        Self {
            resource_kind: T::name().into(),
            resource_id: resource_id.to_string(),
            search_field: "id".into(),
        }
    }

    pub fn by_key<T: Resource>(resource_id: &str, search_field: &str) -> Self
    where
        T::Id: Display,
    {
        Self {
            resource_kind: T::name().into(),
            resource_id: resource_id.to_string(),
            search_field: search_field.into(),
        }
    }

    pub fn custom(resource_kind: &str, resource_id: &str, search_field: &str) -> Self {
        Self {
            resource_kind: resource_kind.into(),
            resource_id: resource_id.into(),
            search_field: search_field.into(),
        }
    }

    pub fn resource_kind(&self) -> &str {
        &self.resource_kind
    }

    pub fn resource_id(&self) -> &str {
        &self.resource_id
    }
}

#[derive(Debug, thiserror::Error)]
#[error(
    "actor {required_role} of {resource_kind} with id \"{resource_id}\" to perform this operation"
)]
pub struct PermissionsError {
    pub required_role: RequiredRole,
    pub resource_kind: &'static str,
    pub resource_id: String,
}

#[allow(clippy::needless_pass_by_value)]
impl PermissionsError {
    pub fn must_be_owner<T: Resource + ?Sized>(id: T::Id) -> Self {
        Self {
            required_role: RequiredRole::MustBeOwner,
            resource_kind: T::name(),
            resource_id: id.to_string(),
        }
    }

    pub fn must_be_member<T: Resource + ?Sized>(id: T::Id) -> Self {
        Self {
            required_role: RequiredRole::MustBeMember,
            resource_kind: T::name(),
            resource_id: id.to_string(),
        }
    }

    pub fn must_have_access<T: Resource + ?Sized>(id: T::Id) -> Self {
        Self {
            required_role: RequiredRole::MustHaveAccess,
            resource_kind: T::name(),
            resource_id: id.to_string(),
        }
    }
}

#[derive(Debug)]
pub enum RequiredRole {
    MustBeOwner,
    MustBeMember,
    MustHaveAccess,
}

impl Display for RequiredRole {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}",
            match self {
                Self::MustBeOwner => "must be owner",
                Self::MustBeMember => "must be member",
                Self::MustHaveAccess => "must have access",
            }
        )
    }
}

#[derive(Debug, thiserror::Error)]
#[error("{message}")]
pub struct CustomError {
    arg_name: Option<&'static str>,
    message: String,
}

impl CustomError {
    pub fn new(arg_name: Option<&'static str>, message: String) -> Self {
        Self { arg_name, message }
    }

    pub fn arg_name(&self) -> Option<&'static str> {
        self.arg_name
    }
}

macro_rules! custom_error {
    ($arg:expr, $message:expr) => {{
        let _ = &$arg;
        CustomError::new(Some(stringify!($arg)), $message)
    }};
    ($message:expr) => {
        CustomError::new(None, $message)
    };
}

pub(crate) use custom_error as CustomError;

#[derive(Debug, thiserror::Error)]
#[error("attempting to access resources from another organisation")]
pub struct CrossOrganisationError;

impl CrossOrganisationError {
    pub fn new() -> Self {
        Self {}
    }
}

#[derive(Debug, thiserror::Error)]
#[error("service is not configured: {service_name}")]
pub struct NotConfiguredError {
    service_name: String,
}

impl NotConfiguredError {
    pub fn new(service_name: impl Into<String>) -> Self {
        Self {
            service_name: service_name.into(),
        }
    }

    pub fn service_name(&self) -> &str {
        &self.service_name
    }
}

impl From<leviosa_domain_contracts::auth::PasswordError> for Error {
    fn from(e: leviosa_domain_contracts::auth::PasswordError) -> Self {
        // Convert contracts PasswordError to domain PasswordError
        let domain_error = match e {
            leviosa_domain_contracts::auth::PasswordError::InvalidPassword => {
                crate::auth::PasswordError::InvalidPassword
            },
            leviosa_domain_contracts::auth::PasswordError::InvalidSaltCost(msg) => {
                crate::auth::PasswordError::InvalidSaltCost(msg)
            },
            leviosa_domain_contracts::auth::PasswordError::InternalError(err) => {
                crate::auth::PasswordError::InternalError(err)
            },
        };
        Self::Password(domain_error)
    }
}

impl From<leviosa_domain_contracts::errors::Error> for Error {
    fn from(e: leviosa_domain_contracts::errors::Error) -> Self {
        // Convert contracts error to domain error
        match e {
            leviosa_domain_contracts::errors::Error::NotFound(msg) => {
                Self::NotFound(NotFoundError::custom("Resource", &msg, "id"))
            },
            leviosa_domain_contracts::errors::Error::Authorization(auth_err) => {
                // Convert contracts AuthorizationError to domain AuthorizationError
                let domain_auth_err = match auth_err {
                    leviosa_domain_contracts::errors::AuthorizationError::Forbidden => {
                        AuthorizationError::Forbidden
                    },
                    leviosa_domain_contracts::errors::AuthorizationError::Unauthorized => {
                        AuthorizationError::Unauthorized
                    },
                    leviosa_domain_contracts::errors::AuthorizationError::InvalidToken => {
                        AuthorizationError::InvalidToken
                    },
                };
                Self::Authorization(domain_auth_err)
            },
            leviosa_domain_contracts::errors::Error::Validation(custom_err) => {
                // Convert contracts CustomError to domain CustomError
                let domain_custom_err = CustomError::new(
                    custom_err.code.clone(),
                    custom_err.message.clone()
                );
                Self::Custom(domain_custom_err)
            },
            leviosa_domain_contracts::errors::Error::Internal(err) => {
                Self::Internal(err)
            },
        }
    }
}
