@charset "UTF-8";
.rbc-btn {
  color: inherit;
  font: inherit;
  margin: 0;
}

button.rbc-btn {
  overflow: visible;
  text-transform: none;
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled].rbc-btn {
  cursor: not-allowed;
}

button.rbc-input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

.eventSubjectInfo,
.calendarOwnerSubject {
  display: none;
}

.rbc-calendar {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: auto;
  --border: 1px solid var(--color-lev-blue-200);
  --border-radius-event: 8px;
  --color-indicator: var(--color-pink);
  margin: -16px;
  padding: 16px;
}

.rbc-calendar *,
.rbc-calendar *:before,
.rbc-calendar *:after {
  box-sizing: inherit;
}

.rbc-abs-full,
.rbc-row-bg {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.rbc-ellipsis,
.rbc-show-more,
.rbc-row-segment .rbc-event-content {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
}

.rbc-rtl {
  direction: rtl;
}

/* remove off  colors */

.rbc-header {
  overflow: hidden;
  flex: 1 0 0;
}
.rbc-header > a,
.rbc-header > a:active,
.rbc-header > a:visited {
  color: inherit;
  text-decoration: none;
}

.rbc-button-link {
  --color-text: inherit;
  background: none;
  margin: 0;
  border: none;
  cursor: pointer;
  user-select: text;
  width: 100%;
}

.rbc-row-content {
  position: relative;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  z-index: 4;
}

.rbc-row-content-scrollable {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.rbc-row-content-scrollable .rbc-row-content-scroll-container {
  height: 100%;
  overflow-y: scroll;
  /* Hide scrollbar for Chrome, Safari and Opera */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.rbc-row-content-scrollable
  .rbc-row-content-scroll-container::-webkit-scrollbar {
  display: none;
}

.rbc-header.rbc-today {
  background-color: var(--color-lev-green-200);
  border-bottom: 2px solid var(--color-lev-green);
  color: var(--color-lev-green-800);
  margin-bottom: -1px;
  z-index: 1;
}

/* .rbc-toolbar {
  display: flex;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
}
.rbc-toolbar .rbc-toolbar-label {
  flex-grow: 1;
  padding: 0 10px;
  text-align: center;
}
.rbc-toolbar button {
  color: var(--color-gray-50);
  display: inline-block;
  margin: 0;
  text-align: center;
  vertical-align: middle;
  background: none;
  background-image: none;
  border: var(--border);
  padding: 0.375rem 1rem;
  border-radius: 4px;
  line-height: normal;
  white-space: nowrap;
}
.rbc-toolbar button:active,
.rbc-toolbar button.rbc-active {
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px var(--color-gray-20);
  box-shadow: inset 0 3px 5px var(--color-gray-20);
  background-color: var(--color-gray-20);
  border-color: var(--color-gray-40);
}
.rbc-toolbar button:active:hover,
.rbc-toolbar button:active:focus,
.rbc-toolbar button.rbc-active:hover,
.rbc-toolbar button.rbc-active:focus {
  color: var(--color-gray-50);
  background-color: var(--color-gray-30);
  border-color: var(--color-gray-50);
}
.rbc-toolbar button:focus {
  color: var(--color-gray-50);
  background-color: var(--color-gray-20);
  border-color: var(--color-gray-40);
}
.rbc-toolbar button:hover {
  color: var(--color-gray-50);
  background-color: var(--color-gray-20);
  border-color: var(--color-gray-40);
} */

.rbc-btn-group {
  display: inline-block;
  white-space: nowrap;
}
.rbc-btn-group > button:first-child:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rbc-btn-group > button:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rbc-rtl .rbc-btn-group > button:first-child:not(:last-child) {
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rbc-rtl .rbc-btn-group > button:last-child:not(:first-child) {
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rbc-btn-group > button:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.rbc-btn-group button + button {
  margin-left: -1px;
}
.rbc-rtl .rbc-btn-group button + button {
  margin-left: 0;
  margin-right: -1px;
}
.rbc-btn-group + .rbc-btn-group,
.rbc-btn-group + button {
  margin-left: 10px;
}

.rbc-event,
.rbc-day-slot .rbc-background-event {
  width: 100%;
  margin: 0;
  padding: 0;
  border-radius: var(--border-radius-event);
  text-align: left;
  border: none;
  box-sizing: border-box;
  box-shadow: none;
  cursor: pointer;
}
.rbc-event {
  /* This hack is to allow the events to be slightly inset from 
   * the availability schedule background events. */
  --event-inset: 3px;
  border-left: var(--event-inset) solid transparent;
  border-right: var(--event-inset) solid transparent;
  background-clip: padding-box;
  /* The inset (border) messes with the border radius, so in
     order to get it correctly rounded, we need to add the inset to the radius  */
  border-radius: calc(var(--border-radius-event) + var(--event-inset)) /
    var(--border-radius-event);
}

.rbc-event.rbc-event-canceled .rbc-event-content {
  border-radius: var(--border-radius-event);
  background-color: var(--color-white);
  border: 1px solid var(--color-text);
  transition: background-color 0.1s;

  &:hover {
    background-color: var(--color-200);
  }
  &:active {
    background-color: var(--color-300);
  }
}

.rbc-slot-selecting .rbc-event,
.rbc-slot-selecting .rbc-day-slot .rbc-background-event,
.rbc-day-slot .rbc-slot-selecting .rbc-background-event {
  cursor: inherit;
  pointer-events: none;
}

.rbc-event:focus-visible .rbc-addons-dnd-resizable,
.rbc-day-slot .rbc-background-event:focus-visible {
  outline: 2px auto transparent;
}

.rbc-event-overlaps {
  -webkit-box-shadow: -1px 1px 5px 0 var(--color-gray-40);
  box-shadow: -1px 1px 5px 0 var(--color-gray-40);
}

.rbc-event-continues-prior {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rbc-event-continues-after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-event-continues-earlier {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.rbc-event-continues-later {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.rbc-row {
  display: flex;
  flex-direction: row;
}

/* Wrapper around event in month view */
.rbc-row-segment {
  padding: 0 1px 1px 1px;
}
/* .rbc-selected-cell .rbc-event-content {
  background-color: aqua;
  color: var(--color-selected-text);
} */

.rbc-show-more {
  z-index: 4;
  font-size: 85%;
  height: auto;
  text-align: right;
  padding-right: 4px;
  line-height: normal;
  color: var(--color-text-interactive);
}
.rbc-show-more:hover,
.rbc-show-more:focus {
  text-decoration: underline;
}

/* Create stacking context for the calendar so z-index doesn't bleed out to other parts of our system */
.rbc-month-view,
.rbc-time-view {
  z-index: 0;
  position: relative;
}

.rbc-month-view {
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1 0 0;
  width: 100%;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  height: 100%;
}

.rbc-month-header {
  display: flex;
  flex-direction: row;
  border-bottom: var(--border);
}

.rbc-month-row {
  display: flex;
  position: relative;
  flex-direction: column;
  flex: 1 0 0;
  flex-basis: 0;
  overflow: hidden;
  border-left: var(--border);
  border-right: var(--border);
}

.rbc-month-row button {
  width: 100%;
}

.rbc-month-row + .rbc-month-row {
  border-top: var(--border);
}

/* Date in month view */
.rbc-date-cell {
  flex: 1 1 0;
  min-width: 0;
  padding-right: 5px;
  text-align: right;
  margin: 4px;
}
/* Today's date in month view */
.rbc-date-cell.rbc-now {
  font-weight: bold;
  margin-left: 8px;
  color: var(--color-lev-green-800);
  position: relative;
}
.rbc-date-cell.rbc-now::after {
  display: block;
  content: "";
  position: absolute;
  top: -2px;
  width: 35px;
  height: 24px;
  background: var(--color-lev-green-200);
  border-radius: 8px;
  z-index: -1;
  left: -6px;
}
.rbc-date-cell > a,
.rbc-date-cell > a:active,
.rbc-date-cell > a:visited {
  color: inherit;
  text-decoration: none;
}

.rbc-row-bg {
  display: flex;
  flex-direction: row;
  flex: 1 0 0;
  overflow: hidden;
}

.rbc-day-bg {
  flex: 1 0 0;
}
.rbc-day-bg + .rbc-day-bg {
  border-left: var(--border);
}
.rbc-rtl .rbc-day-bg + .rbc-day-bg {
  border-left-width: 0;
  border-right: var(--border);
}

.rbc-overlay {
  position: absolute;
  z-index: 5;
  border: 1px solid var(--color-neutral-300);
  background-color: var(--color-white);
  box-shadow: 0 5px 15px var(--color-neutral-300);
  padding: 10px;
}
.rbc-overlay > * + * {
  margin-top: 1px;
}

.rbc-overlay-header {
  border-bottom: 1px solid var(--color-neutral-20);
  margin: -10px -10px 5px -10px;
  padding: 2px 10px;
}

.rbc-time-column {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}
.rbc-time-column .rbc-timeslot-group {
  flex: 1;
}

.rbc-timeslot-group {
  border-bottom: var(--border);
  min-height: 100px;
  display: flex;
  flex-flow: column nowrap;
}
rbc-time-gutter .rbc-timeslot-group {
  border: none;
}

.rbc-time-gutter,
.rbc-header-gutter {
  flex: none;
}

.rbc-label {
  padding: 0 5px;
}

.rbc-day-slot {
  position: relative;
}
.rbc-day-slot .rbc-events-container {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}
.rbc-day-slot .rbc-events-container.rbc-rtl {
  left: 10px;
  right: 0;
}
.rbc-day-slot .rbc-event,
.rbc-day-slot .rbc-background-event {
  display: flex;
  max-height: 100%;
  min-height: 20px;
  flex-flow: column wrap;
  align-items: flex-start;
  overflow: hidden;
  position: absolute;
}
.rbc-day-slot .rbc-background-event {
  opacity: 0.75;
}

.rbc-day-slot .rbc-event-content {
  width: 100%;
  flex: 1 1 0;
  word-wrap: break-word;
  line-height: 1;
  height: 100%;
  min-height: 1em;
}

.rbc-time-view-resources .rbc-time-gutter,
.rbc-time-view-resources .rbc-time-header-gutter {
  position: sticky;
  left: 0;
  background-color: var(--color-white);
  border-right: var(--border);
  z-index: 10;
  margin-right: -1px;
}
.rbc-time-view-resources .rbc-time-header {
  overflow: hidden;
}
.rbc-time-view-resources .rbc-time-header-content {
  min-width: auto;
  flex: 1 0 0;
  flex-basis: 0;
}
.rbc-time-view-resources .rbc-time-header-cell-single-day {
  display: none;
}
.rbc-time-view-resources .rbc-day-slot {
  min-width: 140px;
}
.rbc-time-view-resources .rbc-header,
.rbc-time-view-resources .rbc-day-bg {
  width: 140px;
  flex: 1 1 0;
  flex-basis: 0;
}

.rbc-time-header-content + .rbc-time-header-content {
  margin-left: -1px;
}

.rbc-time-slot {
  flex: 1 0 0;
  padding-top: 4px;
}
.rbc-time-slot.rbc-now {
  font-weight: bold;
}
.rbc-time-gutter .rbc-timeslot-group {
  translate: 0 -16px;
  border: none;
}
.rbc-time-gutter .rbc-timeslot-group:first-child {
  translate: 0 0;
}

.rbc-day-header {
  text-align: center;
}

.rbc-slot-selection {
  z-index: 10;
  position: absolute;
  width: calc(100% - 2px);
  color: white;
  font-size: 14px;
  padding: 3px;
  margin: 0 1px;
  background-color: var(--color-lev-blue);
  border-radius: var(--border-radius-event);
}

.rbc-slot-selecting {
  cursor: move;
}

.rbc-time-view {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  min-height: 0;
}
.rbc-time-view .rbc-time-gutter {
  white-space: nowrap;
  text-align: right;
}
.rbc-time-view .rbc-allday-cell {
  box-sizing: content-box;
  width: 100%;
  height: 100%;
  position: relative;
  border: var(--border);
  border-bottom: none;
  margin-bottom: -1px;
}
/* .rbc-time-view .rbc-allday-cell + .rbc-allday-cell {
  /* border-left: var(--border);
} */
.rbc-time-view .rbc-allday-events {
  position: relative;
  z-index: 4;
}
.rbc-time-view .rbc-row {
  box-sizing: border-box;
}

.rbc-time-header {
  display: flex;
  flex: 0 0 auto;
  flex-direction: row;
}

.rbc-time-header-cell-single-day {
  display: none;
}

.rbc-time-header-content {
  flex: 1;
  display: flex;
  min-width: 0;
  flex-direction: column;
  /* border-left: var(--border); */
}
.rbc-rtl .rbc-time-header-content {
  border-left-width: 0;
  /* border-right: var(--border); */
}
.rbc-time-header-content > .rbc-row.rbc-row-resource {
  border-bottom: var(--border);
  flex-shrink: 0;
}

.rbc-time-content {
  display: flex;
  flex: 1 0 0%;
  align-items: flex-start;
  width: 100%;
  border-top: var(--border);
  overflow-y: auto;
  position: relative;
}
.rbc-time-content > .rbc-time-gutter {
  flex: none;
}
.rbc-time-content > * + * > * {
  border-left: var(--border);
}
.rbc-rtl .rbc-time-content > * + * > * {
  border-left-width: 0;
  border-right: var(--border);
}
.rbc-time-content > .rbc-day-slot {
  width: 100%;
  user-select: none;
}

.rbc-current-time-indicator {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-indicator);
  pointer-events: none;
}

.rbc-current-time-indicator::before {
  content: "";
  position: absolute;
  top: -5px; /* position the circle at the top */
  left: -7px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--color-indicator);
}

.rbc-time-column:has(.rbc-current-time-indicator)
  + .rbc-time-column
  .rbc-current-time-indicator::before {
  /* only show the ball once. In the day view with multiple providers, the 
   * .rbc-current-time-indicator is rendered multiple times */
  display: none;
}

.rbc-row-segment button {
  width: 100%;
}

.rbc-event-content {
  container-type: size;
  container-name: event;
  min-height: calc(1em + 3px);
  /* adding important since other styles for .rbc-event-content are overwriting the padding */
  padding: 0 !important;
}

.eventWrapper[data-resource-type="holiday"] {
  padding: 1px 2px 2px 3px;
}
