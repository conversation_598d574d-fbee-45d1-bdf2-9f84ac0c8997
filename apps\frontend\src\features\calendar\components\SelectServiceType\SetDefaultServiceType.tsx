import { useTranslation } from "react-i18next"

import { usePersistedPreference } from "hooks/usePersistedPreference"
import { Button } from "ui"

import styles from "./SelectServiceType.module.css"

export const SetDefaultServiceType = ({
  serviceTypeId,
}: {
  serviceTypeId: string | null
}) => {
  const { t } = useTranslation("features", {
    keyPrefix: "calendar",
  })
  const [defaultServiceType, setDefaultServiceType] =
    usePersistedPreference<string>({
      key: "defaultServiceType",
    })

  if (!serviceTypeId || serviceTypeId === "") return null
  if (defaultServiceType && serviceTypeId === defaultServiceType) return null

  return (
    <Button
      size="small"
      variant="clear"
      onClick={() => setDefaultServiceType(serviceTypeId)}
      className={styles.setDefaultServiceTypeButton}
    >
      {t("setAsDefault")}
    </Button>
  )
}
