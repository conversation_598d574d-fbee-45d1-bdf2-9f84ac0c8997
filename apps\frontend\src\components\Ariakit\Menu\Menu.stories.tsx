import { Meta, StoryFn } from "@storybook/react-vite"

import Icon from "../../Icon/Icon"
import { useMenuStore } from "../hooks"
import { Menu, MenuProps, MenuProvider } from "./Menu"
import { MenuButton } from "./MenuButton/MenuButton"
import { MenuItem } from "./MenuItem/MenuItem"

export default {
  title: "Ariakit/Menu",
  component: Menu,
} as Meta

export const MenuExample: StoryFn<MenuProps> = () => {
  const menu = useMenuStore()

  return (
    <MenuProvider store={menu}>
      <MenuButton store={menu} iconEnd={<Icon name="arrow-down-s-line" />}>
        Menu
      </MenuButton>
      <Menu>
        <MenuItem>Item 1</MenuItem>
        <MenuItem>Item 2</MenuItem>
        <MenuItem>Item 3</MenuItem>
        <MenuItem>Item 4</MenuItem>
      </Menu>
    </MenuProvider>
  )
}
