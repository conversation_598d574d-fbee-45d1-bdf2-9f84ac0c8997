on: workflow_dispatch
jobs:
  publish:
    name: Publish
    runs-on: ubuntu-latest
    steps:
      - name: Install openconnect
        run: sudo apt install openconnect
      - name: Checkout
        uses: actions/checkout@v2
      - name: Create Kubeconfig
        run: echo "${{ secrets.PROD_KUBECONFIG }}" > prod-kubeconfig.yaml
      - name: Connect to VPN
        run: echo ${{ secrets.PROD_VPN_PASSWORD }} | sudo openconnect -b --user ${{ secrets.PROD_VPN_USER }} --passwd-on-stdin ${{ secrets.PROD_VPN_HOST }}
      - name: Rollout
        run: helm upgrade --kubeconfig prod-kubeconfig.yaml leviosa ./deployment --reuse-values --set appVersion=${{ github.sha }} --wait --timeout 30m
