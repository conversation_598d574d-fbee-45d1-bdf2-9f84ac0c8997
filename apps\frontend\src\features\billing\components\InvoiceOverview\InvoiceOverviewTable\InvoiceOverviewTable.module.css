.actionRow {
  width: 180px;
  line-height: 0;
}

.tag {
  background-color: var(--color-lev-blue-200);
  color: var(--color-lev-blue-800);
}

.tableRow {
  cursor: pointer;
}

.tableRow:hover .tag {
  background-color: var(--color-white);
}

.invoiceNumber.invoiceNumber {
  pointer-events: none;
  color: var(--color-text);
  cursor: default;
}

.menuButton.menuButton {
  margin: -9px;
  font-size: 24px;
  border: none;
}

.menu {
  min-width: 226px;
}

.numericValue {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.tags {
  display: flex;
  align-items: center;
  gap: 14px;
  width: 202px;
  justify-content: end;
}

.menuItem.menuItem {
  color: var(--color-text);
}

.menuIcon {
  height: 1.125em;
  flex-basis: 1.125em;
}
