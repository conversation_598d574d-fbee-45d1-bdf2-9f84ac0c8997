import { createCookieSessionStorage, Session } from "@remix-run/node"
import { addHours } from "date-fns"

import getFirstMondayOfNextMonth from "./utils/getFirstMondayOfNextMonth"

type SessionData = {
  accessToken: string
  accessTokenExpires: Date
  logoUrl?: string
  checkInData?: {
    fromTime: string | null
    subjectName: string | null
    providerNames: string[] | null
    locationLabel: string | null
  }
}

type SessionFlashData = {
  error: string
}

/**
 * Returns consistent cookie options for setting the session cookie.
 *
 * This function ensures that the session cookie has a defined expiration date,
 * which is important because calling `commitSession()` without an `expires`
 * value will overwrite any previously set expiry and default to a session cookie
 * (i.e., one that disappears when the browser closes).
 *
 * If the session already contains a stored "accessTokenExpires" value (e.g. from a login),
 * that is used to preserve the original expiry across subsequent session commits.
 * Otherwise, it calculates a new expiry: the first Monday of the next month at 4AM.
 *
 * @param session - The current Remix session object
 * @returns An object with the `expires` property for use in `commitSession()`
 */
const getSessionCookieOptions = (session: Session) => {
  const defaultExpiry = session.get("accessTokenExpires")
    ? new Date(session.get("accessTokenExpires"))
    : addHours(getFirstMondayOfNextMonth(), 4)

  return {
    expires: defaultExpiry,
  }
}

/**
 * Creates response headers with a Set-Cookie header for committing the session.
 *
 * This function simplifies the process of generating the proper headers needed
 * when committing a session in a Remix response. It automatically applies the
 * appropriate cookie options using getSessionCookieOptions().
 *
 * @param session - The current Remix session object
 * @returns An object with headers containing the Set-Cookie header
 */
export const getCommitSessionHeaders = async (
  session: Session
): Promise<{ headers: { "Set-Cookie": string } }> => {
  return {
    headers: {
      "Set-Cookie": await commitSession(
        session,
        getSessionCookieOptions(session)
      ),
    },
  }
}

const { getSession, commitSession, destroySession } =
  createCookieSessionStorage<SessionData, SessionFlashData>({
    cookie: {
      name: "leviosa-kiosk-session",
      httpOnly: true,
      secrets: [process.env.KIOSK_SESSION_SECRET || "secret-not-found"],
      secure: true,
    },
  })

export { getSession, commitSession, destroySession }
