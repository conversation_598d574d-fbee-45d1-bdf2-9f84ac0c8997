import { test, expect, Page } from "@playwright/test"

import { login } from "../utils/authenticationUtils"
import {
  CheckUrl,
  modifierKeys,
  simulateKeyCombination,
} from "../utils/testUtils"

/* eslint-disable playwright/no-wait-for-timeout */
/* eslint-disable playwright/no-useless-await */
test.describe("Subject Search tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("http://localhost:3000/")
    await page.locator('[data-testid="subject-search-button"]').focus()
  })

  const arrange = async (page: Page) => {
    // Open the subject search
    await page.getByTestId("subject-search-button").click()
    await page.waitForTimeout(400)
  }

  test("Subject search opens with keyboard shortcut ctrl+shift+k", async ({
    page,
  }) => {
    // Arrange
    await page.waitForTimeout(1000)
    // Act
    // Open the subject search
    await simulateKeyCombination(page, "k", [modifierKeys[0], modifierKeys[2]])
    await page.waitForTimeout(1000)
    const searchInput = page.getByPlaceholder("Search by name or ID")

    // Assert
    await expect(searchInput).toBeVisible()
    await expect(searchInput).toBeEditable()
  })

  test("Subject search opens when clicked", async ({ page }) => {
    // Arrange
    // Act
    // Open the subject search
    await arrange(page)
    const searchInput = page.getByPlaceholder("Search by name or ID")

    // Assert
    await expect(searchInput).toBeVisible()
    await expect(searchInput).toBeEditable()
  })

  test("Subject search allows entering search criteria", async ({ page }) => {
    // Arrange
    await arrange(page)

    // Act
    const searchInput = page.getByPlaceholder("Search by name or ID")

    // Assert
    await expect(searchInput).toBeVisible()
    await expect(searchInput).toBeEditable()
  })

  test("Subject search shows recent subject", async ({ page }) => {
    // Arrange
    await arrange(page)

    // Act
    const recentHeading = page.getByRole("heading", {
      name: "Recently viewed subjects",
    })
    const recentLinks = page.locator('[class*="SubjectSearch_subjectLink"]')

    // Assert
    await expect(recentHeading).toBeVisible()
    await expect(await recentLinks.count()).toBeGreaterThan(0)
  })

  test("Subject search shows search results", async ({ page }) => {
    // Arrange
    await arrange(page)

    // Act
    // Search for someone not in the recent list
    await page.fill('input[placeholder="Search by name or ID"]', "Sam")
    await page.waitForTimeout(1000)

    const resultsHeading = page.getByRole("heading", {
      name: "Search results",
    })
    const subjectLinks = page.locator('[class*="SubjectSearch_subjectLink"]')

    // Assert
    await expect(resultsHeading).toBeVisible()
    await expect(subjectLinks).toHaveCount(4)
  })

  // we need more control of subject interactions to be able to implement this properly
  test.skip("Subject search shows search results and recent list", async ({
    page,
  }) => {
    // Arrange
    await arrange(page)

    // Act
    // Search for someone whose name overlaps a name in the recent list
    await page.fill('input[placeholder="Search by name or ID"]', "Róbert")
    await page.waitForTimeout(1000)

    const recentHeading = page.getByRole("heading", {
      name: "Recently viewed subjects",
    })
    const resultsHeading = page.getByRole("heading", {
      name: "Search results",
    })
    const subjectLinks = page.locator('[class*="SubjectSearch_subjectLink"]')

    // Assert
    await expect(recentHeading).toBeVisible()
    await expect(resultsHeading).toBeVisible()
    await expect(subjectLinks).toHaveCount(2)
  })

  test("Subject search shows empty search results and add button", async ({
    page,
  }) => {
    // Arrange
    await arrange(page)

    // Act
    // input ten numbers to get the add button
    await page.fill('input[placeholder="Search by name or ID"]', "1234567890")
    await page.waitForTimeout(1000)

    const recentHeading = page.getByRole("heading", {
      name: "Recently viewed subjects",
    })
    const resultsHeading = page.getByRole("heading", {
      name: "Search results",
    })
    const notFoundHeading = page.getByRole("heading", {
      name: "No results found",
    })
    const subjectLinks = page.locator('[class*="SubjectSearch_subjectLink"]')
    const addButton = page.getByRole("link", { name: "+ Add new subject" })

    // Assert
    await expect(recentHeading).toBeHidden()
    await expect(resultsHeading).toBeHidden()
    await expect(notFoundHeading).toBeVisible()
    await expect(addButton).toBeVisible()
    await expect(subjectLinks).toHaveCount(0)
  })

  test("Subject search shows empty search results", async ({ page }) => {
    // Arrange
    await arrange(page)

    // Act
    await page.fill('input[placeholder="Search by name or ID"]', "not a name")
    await page.waitForTimeout(1000)

    const recentHeading = page.getByRole("heading", {
      name: "Recently viewed subjects",
    })
    const resultsHeading = page.getByRole("heading", {
      name: "Search results",
    })
    const notFoundHeading = page.getByRole("heading", {
      name: "No results found",
    })
    const subjectLinks = page.locator('[class*="SubjectSearch_subjectLink"]')
    const addButton = page.getByRole("link", { name: "+ Add new subject" })

    // Assert
    await expect(recentHeading).toBeHidden()
    await expect(resultsHeading).toBeHidden()
    await expect(notFoundHeading).toBeVisible()
    await expect(subjectLinks).toHaveCount(0)
    await expect(addButton).toHaveCount(0)
  })

  test("Subject search shows search results when searching by persona id", async ({
    page,
  }) => {
    // Arrange
    await arrange(page)

    // Act
    // Search for someone not in the recent list
    await page.fill('input[placeholder="Search by name or ID"]', "150163")
    await page.waitForTimeout(1000)

    const resultsHeading = page.getByRole("heading", {
      name: "Search results",
    })
    const subjectLinks = page.locator('[class*="SubjectSearch_subjectLink"]')

    // Assert
    await expect(resultsHeading).toBeVisible()
    await expect(subjectLinks).toHaveCount(1)
  })

  // need to configure enabled national registry integration to be able to execute this test
  test.skip("Can add subject not found in leviosa but found in national registry", async ({
    page,
  }) => {
    // Arrange
    await arrange(page)

    // Act
    // Search for someone not in leviosa but in the national registry mock implementation
    await page.fill('input[placeholder="Search by name or ID"]', "0101303019")
    await page.waitForTimeout(1000)

    const recentHeading = page.getByRole("heading", {
      name: "Recently viewed subjects",
    })
    const resultsHeading = page.getByRole("heading", {
      name: "Search results",
    })
    const nrHeading = page.getByRole("heading", {
      name: "National Registry",
    })

    const subjectLinks = page.locator('[class*="SubjectSearch_subjectLink"]')
    const notExistingText = page.getByText(
      "No subjects matching the search criteria were found within your organisation"
    )
    const subjectName = page.getByText("Mock name")
    const addSubjectButton = page.getByRole("button", { name: "Add subject" })

    // Assert
    await expect(recentHeading).toBeHidden()
    await expect(resultsHeading).toBeHidden()
    await expect(nrHeading).toBeVisible()
    await expect(subjectLinks).toHaveCount(0)
    await expect(notExistingText).toBeVisible()
    await expect(subjectName).toBeVisible()
    await expect(addSubjectButton).toHaveCount(1)
    await expect(addSubjectButton).toBeVisible()
  })

  test("Clicking a subject link updates subject box in header", async ({
    page,
  }) => {
    // Arrange
    await arrange(page)

    // Act
    await page.fill('input[placeholder="Search by name or ID"]', "Dana")
    await page.waitForTimeout(500)

    await page.locator('[class*="SubjectSearch_subjectLink"]').first().click()
    await page.waitForTimeout(500)

    const subjectName = await page.getByTestId("subject-name")

    // Assert
    await expect(subjectName).toHaveText("Dana Morgan")
  })

  test("Clicking add subject button navigates to register subject page", async ({
    page,
  }) => {
    // Arrange
    await arrange(page)

    // Act
    await page.fill('input[placeholder="Search by name or ID"]', "0987654321")
    await page.waitForTimeout(500)

    page.getByRole("link", { name: "+ Add new subject" }).click()
    await page.waitForSelector('h1:text("Register subject")', {
      timeout: 10000,
    })

    // Assert
    await CheckUrl(page, "subject/register")
  })
})
