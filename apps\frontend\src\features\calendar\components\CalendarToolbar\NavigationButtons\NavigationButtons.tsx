import { NavigateAction } from "react-big-calendar"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Button } from "ui"

import styles from "./NavigationButtons.module.css"

type NavigationButtonsProps = {
  onNavigate: (navigate: NavigateAction) => void
}

export const NavigationButtons = ({ onNavigate }: NavigationButtonsProps) => {
  const { t } = useTranslation()
  return (
    <nav className={styles.prevNextButtons} aria-label="Calendar">
      <Button
        aria-label="Previous month/week/day"
        onClick={() => onNavigate("PREV")}
        icon={<Icon name="arrow-left-s-line" />}
        className={styles.iconButton}
        data-testid="calendar-view-prev"
        variant="clear"
        size="large"
      />
      <Button
        aria-label="Next month/week/day"
        onClick={() => onNavigate("NEXT")}
        icon={<Icon name="arrow-right-s-line" />}
        className={styles.iconButton}
        data-testid="calendar-view-next"
        variant="clear"
        size="large"
      />
      <Button
        aria-label="Today"
        onClick={() => onNavigate("TODAY")}
        data-testid="calendar-view-today"
        variant="clear"
        className={styles.todayButton}
      >
        {t("Today")}
      </Button>
    </nav>
  )
}
