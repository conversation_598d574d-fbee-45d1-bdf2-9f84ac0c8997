import { set } from "date-fns"
import { memoize } from "lodash"

export function getScrollTimeAt7() {
  const scrollTime = new Date()
  scrollTime.setDate(scrollTime.getDate() + 1)
  scrollTime.setHours(7, 0, 0, 0)

  return scrollTime
}

function clamp(value: number, min: number, max: number) {
  return Math.max(min, Math.min(max, value))
}
const getScrollHour = memoize((scrollToHour: number) => {
  return set(new Date(), {
    hours: scrollToHour,
    minutes: 0,
    seconds: 0,
    milliseconds: 0,
  })
})

export function getScrollTimeDayAndWeekView() {
  const date = new Date()
  const currentHours = date.getHours()
  // scroll to 7:00 if it's before 9:00, so the user can see the whole work day
  const scrollToHour = clamp(currentHours - 2, 7, 14)

  return getScrollHour(scrollToHour)
}
