fragment DepartmentSelection on Department {
  id
  name
  externalEhrId
  owner {
    id
    name
  }
  teams {
    ...TeamsCardFragment
  }
}

query DepartmentEdit($departmentId: UUID!) {
  department(id: $departmentId) {
    ...DepartmentSelection
  }

  providers {
    id
    name
  }
}

mutation UpdateDepartment($input: DepartmentUpdateInput!) {
  updateDepartment(input: $input) {
    ...DepartmentSelection
  }
}

mutation SetDepartmentOwner($departmentId: UUID!, $ownerId: UUID!) {
  setDepartmentOwner(departmentId: $departmentId, ownerId: $ownerId) {
    ...DepartmentSelection
  }
}
