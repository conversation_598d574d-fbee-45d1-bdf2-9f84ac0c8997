import { View } from "react-big-calendar"

import { AvailabilityScheduleStatus } from "generated/graphql"

import { getAvailabilitySchedulesInput } from "./TestData/getAvailabilitySchedulesInput"
import { getExpectedDayViewOutput } from "./TestData/getExpectedDayViewOutput"
import { getExpectedWeekViewOutput } from "./TestData/getExpectedWeekViewOutput"
import { getExpectedWorkWeekViewOutput } from "./TestData/getExpectedWorkWeekViewOutput"
import { getAvailabilityBlocks } from "./getAvailabilityBlocks"

describe("getAvailabilityBlocks", () => {
  it("should return availability blocks for the current day view", () => {
    const currentDate = new Date(2024, 6, 16)
    const view: View = "day"

    const result = getAvailabilityBlocks(
      getAvailabilitySchedulesInput(AvailabilityScheduleStatus.Published),
      currentDate,
      view
    )

    expect(result).toEqual(getExpectedDayViewOutput)
  })

  it("should return availability blocks for the current week view", () => {
    const currentDate = new Date(2024, 6, 16)
    const view: View = "week"

    // COMEBACK: Add events on the weekend
    const result = getAvailabilityBlocks(
      getAvailabilitySchedulesInput(AvailabilityScheduleStatus.Published),
      currentDate,
      view
    )

    expect(result).toEqual(getExpectedWeekViewOutput)
  })

  it("should return availability blocks for the current work week view", () => {
    const currentDate = new Date(2024, 6, 16)
    const view: View = "work_week"

    const result = getAvailabilityBlocks(
      getAvailabilitySchedulesInput(AvailabilityScheduleStatus.Published),
      currentDate,
      view
    )

    expect(result).toEqual(getExpectedWorkWeekViewOutput)
  })

  it("should return empty array if availability blocks are not draft status", () => {
    const currentDate = new Date(2024, 6, 23)
    const view: View = "day"

    const result = getAvailabilityBlocks(
      getAvailabilitySchedulesInput(AvailabilityScheduleStatus.Draft),
      currentDate,
      view
    )

    expect(result).toEqual([])
  })

  it("should return empty array if no availability blocks are in the given date range", () => {
    const currentDate = new Date(2024, 6, 23)
    const view: View = "day"

    const result = getAvailabilityBlocks(
      getAvailabilitySchedulesInput(AvailabilityScheduleStatus.Published),
      currentDate,
      view
    )

    expect(result).toEqual([])
  })
})
