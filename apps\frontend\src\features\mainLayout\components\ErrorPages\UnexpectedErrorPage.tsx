import { Trans, useTranslation } from "react-i18next"

import StethoscopeIllustration from "@leviosa/assets/illustrations/stethoscope.svg?react"

import ErrorPage from "./ErrorPage"

export default function UnexpectedErrorPage() {
  const { t } = useTranslation("features", {
    keyPrefix: "mainLayout.UnexpectedError",
  })

  const suggestions = [
    <Trans i18nKey={t("suggestion1")} components={{ bold: <b /> }} />,
    <Trans i18nKey={t("suggestion2")} components={{ bold: <b /> }} />,
    <Trans i18nKey={t("suggestion3")} components={{ bold: <b /> }} />,
  ]

  return (
    <ErrorPage
      illustration={<StethoscopeIllustration />}
      heading={t("heading")}
      message={t("message")}
      suggestions={suggestions}
      subMessage={t("subMessage")}
    />
  )
}
