import { AvailabilityScheduleFragmentFragment } from "generated/graphql"

export const sortSchedules = (
  a: AvailabilityScheduleFragmentFragment,
  b: AvailabilityScheduleFragmentFragment
) => {
  const dateA = new Date(a.toDate as string)
  const dateB = new Date(b.toDate as string)
  const today = new Date()

  // Check if both dates are in the past
  if (dateA < today && dateB < today) {
    return dateA.getTime() - dateB.getTime()
  }
  // If only dateA is in the past
  else if (dateA < today) {
    return 1
  }
  // If only dateB is in the past
  else if (dateB < today) {
    return -1
  }
  // If both dates are in the future, compare them normally
  else {
    return dateA.getTime() - dateB.getTime()
  }
}
