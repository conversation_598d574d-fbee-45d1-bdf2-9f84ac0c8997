use crate::accounts::Subject;
use crate::auth::AuthenticatedUser;
use crate::billing::IBillingCodeNhiRepo;
use crate::nhi_service::SubjectInsuranceStatus;
use crate::{
    billing::{BillingCodeType, Invoice, InvoiceLine},
    errors::Error,
    nhi_service::NhiService,
};
use std::sync::Arc;

// Adapter to convert between contracts and domain NhiService
pub struct ContractsNhiServiceAdapter {
    contracts_service: Arc<dyn crate::nhi_service::NhiService>,
}

impl ContractsNhiServiceAdapter {
    pub fn new(contracts_service: Arc<dyn crate::nhi_service::NhiService>) -> Self {
        Self { contracts_service }
    }
}

#[async_trait::async_trait]
impl NhiService for ContractsNhiServiceAdapter {
    async fn get_subject_payment_status(
        &self,
        subject: &Subject,
        token: &str,
    ) -> Result<SubjectInsuranceStatus, Error> {
        // Convert domain Subject to contracts SubjectId
        let subject_id = leviosa_domain_types::SubjectId(subject.id().0);

        // Call contracts service
        let contracts_result = self.contracts_service
            .get_subject_payment_status(subject_id, token)
            .await
            .map_err(|e| crate::errors::Error::from(e))?;

        // Convert contracts result to domain result
        let domain_result = match contracts_result {
            crate::nhi_service::SubjectInsuranceStatus::Uninsured => {
                SubjectInsuranceStatus::Uninsured
            },
            crate::nhi_service::SubjectInsuranceStatus::Insured {
                insurance_category,
                maximum_payable_by_subject,
                insurance_percentage,
                payment_status_serial_number,
            } => {
                SubjectInsuranceStatus::Insured {
                    insurance_category,
                    maximum_payable_by_subject,
                    insurance_percentage,
                    payment_status_serial_number: payment_status_serial_number as i32,
                }
            },
            crate::nhi_service::SubjectInsuranceStatus::Unknown => {
                // Treat unknown status as uninsured
                SubjectInsuranceStatus::Uninsured
            }
        };

        Ok(domain_result)
    }

    async fn submit_invoice(
        &self,
        _medical_bill: crate::nhi_service::MedicalBill,
        _on_behalf_of: &str,
        _token: &str,
    ) -> Result<crate::nhi_service::NhiSubmissionResult, Error> {
        // This method is not used by the payment participation calculator
        // but is required by the trait. For now, return an error.
        Err(Error::Custom(crate::errors::CustomError::new(
            None,
            "submit_invoice not implemented in adapter".to_string(),
        )))
    }
}

#[async_trait::async_trait]
pub trait InvoiceTotalCalculator: Send + Sync {
    async fn update_invoice_totals(
        &self,
        code_repo: &dyn IBillingCodeNhiRepo,
        user: &AuthenticatedUser,
        subject: &Subject,
        invoice: Invoice,
        invoice_lines: Vec<InvoiceLine>,
    ) -> Result<Invoice, Error>;
}

pub struct NhiInvoiceTotalCalculator {
    pub nhi_service: Arc<dyn NhiService>,
    pub token: String,
}

#[async_trait::async_trait]
impl InvoiceTotalCalculator for NhiInvoiceTotalCalculator {
    async fn update_invoice_totals(
        &self,
        code_repo: &dyn IBillingCodeNhiRepo,
        user: &AuthenticatedUser,
        subject: &Subject,
        invoice: Invoice,
        invoice_lines: Vec<InvoiceLine>,
    ) -> Result<Invoice, Error> {
        let invoice = invoice.update_invoice_totals(&invoice_lines, user);

        self.calculate_payment_participation(code_repo, subject, invoice.clone(), invoice_lines)
            .await
    }
}

pub struct NhiInvoiceTotals {
    pub total: f64,
    pub nhi_payable_by_subject: f64,
    pub nhi_payable_by_subject_before_discount: f64,
    pub total_payable_by_insurance: f64,
    pub radnumer_si: Option<i32>,
}

impl NhiInvoiceTotalCalculator {
    pub fn new(nhi_service: Arc<dyn NhiService>, token: String) -> Self {
        Self { nhi_service, token }
    }

    pub fn new_with_contracts(
        contracts_service: Arc<dyn crate::nhi_service::NhiService>,
        token: String
    ) -> Self {
        let adapter = Arc::new(ContractsNhiServiceAdapter::new(contracts_service));
        Self {
            nhi_service: adapter,
            token
        }
    }

    /// Insurance participation for specialist doctors is quite complex and NHI doesn't calculate it for us. It follows a set of rules based primarily on
    /// the insurance status category of the subject (general, kid, disabled, etc.) and the amount the subject has already paid for healthcare services.
    /// NHI covers differenct subject status categories by different amounts. E.g. generally people pay 90%, kids pay 30%, disabled people pay 60% etc.
    /// There is also a maximum amount that a subject should pay that is lowered as they use healthcare services. So if a subject should pay 15.000 kr
    /// but the subject's maximum_payable_by_subject is 5000 kr. then the subject should only pay 5.000 kr and the rest is covered by NHI.
    ///
    /// The NHI integration provides information about the subject's insurance category and payment status in the subject_payment_status endpoint:
    ///
    /// `SubjectInsuranceStatus::Uninsured`: Tells us the subject is not insured.
    /// `SubjectInsuranceStatus::Insured { insurance_category, .. }`: Category of the subject: general (ALM), kid (BARN), senior citizen (ELLI), etc.
    /// `SubjectInsuranceStatus::Insured { insurance_percentage, .. }`: Percentage of the bill that is covered by insurance. It needs to be divided by 100 in calculations.
    /// `SubjectInsuranceStatus::Insured { maximum_payable_by_subject, .. }`: The maximum amount a subject should pay on an invoice. If this is 0, NHI covers the full amount.
    ///
    /// General calculation rules:
    ///   Total (NHI):                 Sum of all NHI line totals, this is not the same as the invoice total. The invoice total can contain private practice billing items that NHI shouldn't be billed for.
    ///   Total Payable by subject:    Based on two factors. The insurance percentage, which is directly tied to SubjectInsuranceStatus.insurance_percentage and SubjectInsuranceStatus.maximum_payable_by_subject,
    ///                                which is the maximum amount the subject should pay. Formula: MIN(SubjectInsuranceStatus.maximum_payable_by_subject, Total (NHI) * (1 - SubjectInsuranceStatus.insurance_percentage / 100))
    ///   Total Payable by Insurance:  Total (NHI) - Total Payable by subject
    ///
    /// There are additional rules that apply on top of this general rule:
    ///   1. Some people are uninsured and should pay the full amount. Determined by `SubjectInsuranceStatus.is_insured`.
    ///   2. Some billing items are 100% covered by insurance and should NOT be payed by the subject. These billing codes contain the letters A, D, S or L.
    ///   3. Children pay 0% for some billing categories. This applies when `SubjectInsuranceStatus.status` is 'BARN' and `BillingCodeNhi.category` is either 'Augnlækningar' or 'Kvensjúkdómalækningar'.
    ///      This also applies to emergency depertment visits and on-call services. But those are not in scope since specialist doctors are not providing those services.
    ///   4. Children that have a referral to a specialist doctor pay 0%. This Has not yet been implemented, but we should make a todo test.
    ///   5. Billing codes with a quantity greater than 1 are not payed in full for the extra instances. I.e. removing 2 warts would be billed as 1.5 warts, not 2.
    ///      This is implemented when we create the invoice lines and calculate the line total, so it falls outside the scope of the payment participation calculation.
    ///   6. Because of the complexity of the rules and edge cases we might not have captured we've added an nhi pays all flag. If the user sets this flag the insurance pays all and the subject pays nothing.
    ///   7. NHI pays the full amount for the telemedicine code FT-001-01 (subject pays 0). The subject only gets 2 free telemedicine session with each doctor per year.
    ///      We have not implemented the 2 times per year restriction.
    ///
    /// For further documentation on NHI see: https://leviosa.atlassian.net/wiki/x/DYDDEQ
    async fn calculate_payment_participation(
        &self,
        code_repo: &dyn IBillingCodeNhiRepo,
        subject: &Subject,
        mut invoice: Invoice,
        invoice_lines: Vec<InvoiceLine>,
    ) -> Result<Invoice, Error> {
        let nhi_totals = self
            .calculate_nhi_lines(subject, &invoice_lines, invoice.nhi_pays_all(), code_repo)
            .await?;
        let clinic_lines_total_with_vat = invoice_lines
            .iter()
            .filter(|l| l.billing_code_type() != BillingCodeType::Nhi)
            .map(|l| l.total() * (1.0 + l.vat() / 100.0))
            .sum::<f64>()
            .round();

        // Update Invoice NHI fields
        invoice.contains_nhi_items = Some(nhi_totals.total > 0.0);
        invoice.nhi_payable_by_subject = Some(nhi_totals.nhi_payable_by_subject);
        invoice.total_payable_by_subject_before_discount =
            Some(nhi_totals.nhi_payable_by_subject_before_discount);
        invoice.total_payable_by_insurance = Some(nhi_totals.total_payable_by_insurance);
        invoice.radnumer_si = nhi_totals.radnumer_si;

        // Calculate subject discount for nhi lines
        if invoice.nhi_pays_all() {
            invoice.subject_discount = Some(0.0)
        } else if invoice.subject_discount().unwrap_or_default() > 0.0 {
            let discount = invoice
                .subject_discount()
                .unwrap_or_default()
                .min(nhi_totals.nhi_payable_by_subject);

            invoice.subject_discount = Some(discount);
        }

        // Update Invoice clinic fields
        invoice.clinic_payable_by_subject = Some(clinic_lines_total_with_vat);

        // Update Invoice total fields
        invoice.total_payable_by_subject = Some(
            clinic_lines_total_with_vat + nhi_totals.nhi_payable_by_subject
                - invoice.subject_discount.unwrap_or_default(),
        );

        Ok(invoice)
    }

    async fn calculate_nhi_lines(
        &self,
        subject: &Subject,
        lines: &[InvoiceLine],
        nhi_pays_all: bool,
        code_repo: &dyn IBillingCodeNhiRepo,
    ) -> Result<NhiInvoiceTotals, Error> {
        let nhi_lines: Vec<&InvoiceLine> = lines
            .iter()
            .filter(|l| l.billing_code_type() == BillingCodeType::Nhi)
            .collect();

        if nhi_lines.is_empty() {
            return Ok(NhiInvoiceTotals {
                total: 0.0,
                nhi_payable_by_subject: 0.0,
                nhi_payable_by_subject_before_discount: 0.0,
                total_payable_by_insurance: 0.0,
                radnumer_si: None,
            });
        }

        let total = nhi_lines.iter().map(|l| l.total()).sum();

        let insurance_status = self
            .nhi_service
            .get_subject_payment_status(subject, &self.token)
            .await?;

        match insurance_status {
            SubjectInsuranceStatus::Uninsured => Ok(NhiInvoiceTotals {
                total,
                nhi_payable_by_subject: total,
                nhi_payable_by_subject_before_discount: total,
                total_payable_by_insurance: 0.0,
                radnumer_si: None,
            }),
            SubjectInsuranceStatus::Insured {
                insurance_percentage,
                maximum_payable_by_subject,
                payment_status_serial_number,
                ..
            } => {
                let mut payable_by_subject_total = 0.0;
                for line in nhi_lines {
                    payable_by_subject_total += self
                        .calculate_payable_by_subject_for_line(code_repo, line, &insurance_status)
                        .await?;
                }

                let nhi_payable_by_subject_before_discount =
                    Self::round_half_down(maximum_payable_by_subject.min(
                        payable_by_subject_total * (1.0 - insurance_percentage as f64 / 100.0),
                    ));

                let nhi_payable_by_subject = if nhi_pays_all {
                    0.0
                } else {
                    nhi_payable_by_subject_before_discount
                };
                let total_payable_by_insurance = total - nhi_payable_by_subject;

                Ok(NhiInvoiceTotals {
                    total,
                    nhi_payable_by_subject,
                    nhi_payable_by_subject_before_discount,
                    total_payable_by_insurance,
                    radnumer_si: Some(payment_status_serial_number),
                })
            }
        }
    }

    fn round_half_down(value: f64) -> f64 {
        let rounded = value.round();
        if rounded - value == 0.5 {
            rounded - 1.0
        } else {
            rounded
        }
    }

    async fn calculate_payable_by_subject_for_line(
        &self,
        code_repo: &dyn IBillingCodeNhiRepo,
        line: &InvoiceLine,
        payment_status: &SubjectInsuranceStatus,
    ) -> Result<f64, Error> {
        if line.billing_code_type() != BillingCodeType::Nhi {
            return Ok(line.total());
        }

        match payment_status {
            SubjectInsuranceStatus::Uninsured => Ok(line.total()),
            SubjectInsuranceStatus::Insured {
                insurance_category, ..
            } => {
                let code = code_repo.get(line.billing_code_id().into()).await?;

                if code.code == "FT-001-01" {
                    return Ok(0.0);
                }

                if insurance_category.as_str() == "BARN"
                    && (code.category == "Augnlækningar"
                        || code.category == "Kvensjúkdómalækningar")
                {
                    return Ok(0.0);
                }

                if code.parent_code.is_some() {
                    let code_string = code.code.to_lowercase();
                    if code_string.contains('a')
                        || code_string.contains('d')
                        || code_string.contains('s')
                        || code_string.contains('l')
                    {
                        return Ok(0.0);
                    }
                }

                Ok(line.total())
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        billing::{BillingCodeNhi, MockIBillingCodeNhiRepo, UpdateInvoiceInput},
        nhi_service::{MockNhiService, SubjectInsuranceStatus},
        testing::UnitTestDataBuilder,
    };
    use std::future;
    use std::sync::Arc;

    // General rule with ALM insurance percentage and invoice amount lower than the subject's payment status (maximum amount the subject should pay)
    // Subject should pay 90% and NHI should pay 10%
    #[tokio_shared_rt::test]
    async fn general_rule_alm_insurance_percentage() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![
            test_data.nhi_invoice_line(
                invoice.id(),
                "81-001-001".to_string(),
                "Category".to_string(),
                1.0,
                100.0,
                75.0,
            ),
            test_data.nhi_invoice_line(
                invoice.id(),
                "86-401-001".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
        ];
        let invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            invoice_lines_total * 0.9
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            invoice_lines_total * 0.1
        );
        assert_eq!(
            result.total().expect("Should have total"),
            invoice_lines_total
        );
    }

    // General rule with ELLI insurance percentage and invoice amount lower than the subject's payment status (maximum amount the subject should pay)
    // Subject should pay 60% and NHI should pay 40%
    #[tokio_shared_rt::test]
    async fn general_rule_elli_insurance_percentage() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![
            test_data.nhi_invoice_line(
                invoice.id(),
                "81-001-001".to_string(),
                "Category".to_string(),
                1.0,
                150.0,
                50.0,
            ),
            test_data.nhi_invoice_line(
                invoice.id(),
                "86-401-001".to_string(),
                "Category".to_string(),
                1.0,
                125.0,
                80.0,
            ),
        ];
        let invoice_lines_total = 150.0 * 50.0 + 125.0 * 80.0;
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ELLI", 40);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            invoice_lines_total * 0.6
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            invoice_lines_total * 0.6
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            invoice_lines_total * 0.6
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            invoice_lines_total * 0.4
        );
        assert_eq!(
            result.total().expect("Should have total"),
            invoice_lines_total
        );
    }

    // General rule with ALM insurance percentage and invoice amount higher than the subject's payment status (maximum amount the subject should pay)
    // Subject should pay the payment status amount, NHI should pay the rest
    #[tokio_shared_rt::test]
    async fn general_rule_total_payable_by_subject_higher_than_payment_status() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![
            test_data.nhi_invoice_line(
                invoice.id(),
                "81-001-001".to_string(),
                "Category".to_string(),
                1.0,
                100.0,
                75.0,
            ),
            test_data.nhi_invoice_line(
                invoice.id(),
                "86-401-001".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
        ];
        let invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(5000.0, true, "ELLI", 40);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            5000.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            5000.0
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            5000.0
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            invoice_lines_total - 5000.0
        );
        assert_eq!(
            result.total().expect("Should have total"),
            invoice_lines_total
        );
    }

    // General rule with ALM insurance percentage. Subject payment status has reached 0, which is the maximum amount the subject should pay.
    // Subject should pay 0, NHI should pay all
    #[tokio_shared_rt::test]
    async fn general_rule_subject_has_reached_maximum_payment() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![
            test_data.nhi_invoice_line(
                invoice.id(),
                "81-001-001".to_string(),
                "Category".to_string(),
                1.0,
                100.0,
                75.0,
            ),
            test_data.nhi_invoice_line(
                invoice.id(),
                "86-401-001".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
        ];
        let invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(0.0, true, "ELLI", 40);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            0.0
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            invoice_lines_total
        );
        assert_eq!(
            result.total().expect("Should have total"),
            invoice_lines_total
        );
    }

    // General rule with NHI and clinic codes, NHI amount lower than the subject's payment status (maximum amount the subject should pay)
    // Subject should pay 90% of NHI + clinic lines total. NHI should pay 10% of NHI lines
    #[tokio_shared_rt::test]
    async fn general_rule_nhi_and_clinic_specific_codes() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let nhi_line_1 = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            100.0,
            75.0,
        );
        let nhi_line_2 = test_data.nhi_invoice_line(
            invoice.id(),
            "86-401-001".to_string(),
            "Category".to_string(),
            1.0,
            50.0,
            100.0,
        );
        let clinic_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);

        let nhi_invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let clinic_invoice_lines_total = 80.0 * 115.0;
        let billing_codes = vec![nhi_line_1.1, nhi_line_2.1];

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![nhi_line_1.0, nhi_line_2.0, clinic_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            nhi_invoice_lines_total * 0.9 + clinic_invoice_lines_total * 1.24,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            nhi_invoice_lines_total * 0.1
        );
        assert_eq!(
            result.total().expect("Should have total"),
            nhi_invoice_lines_total + clinic_invoice_lines_total * 1.24
        );
    }

    // General rule with NHI and clinic codes, NHI amount higher than the subject's payment status (maximum amount the subject should pay)
    // Subject should pay their payment status amount + clinic lines total. NHI should pay the NHI total - subject payment status amount
    #[tokio_shared_rt::test]
    async fn general_rule_nhi_and_clinic_specific_codes_total_payable_by_subject_higher_than_maximum(
    ) {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let nhi_line_1 = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            100.0,
            75.0,
        );
        let nhi_line_2 = test_data.nhi_invoice_line(
            invoice.id(),
            "86-401-001".to_string(),
            "Category".to_string(),
            1.0,
            50.0,
            100.0,
        );
        let clinic_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);

        let nhi_invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let clinic_invoice_lines_total = 80.0 * 115.0;
        let billing_codes = vec![nhi_line_1.1, nhi_line_2.1];

        let mock_nhi_service = create_mock_nhi_service(5000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![nhi_line_1.0, nhi_line_2.0, clinic_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            5000.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            5000.0
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            5000.0 + clinic_invoice_lines_total * 1.24,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            nhi_invoice_lines_total - 5000.0
        );
        assert_eq!(
            result.total().expect("Should have total"),
            nhi_invoice_lines_total + clinic_invoice_lines_total * 1.24
        );
    }

    // General rule with NHI and clinic codes. Subject payment status has reached 0, which is the maximum amount the subject should pay.
    // Subject should only pay for clinic lines. NHI should pay the total of the NHI lines.
    #[tokio_shared_rt::test]
    async fn general_rule_nhi_and_clinic_specific_codes_subject_has_reached_maximum_payment() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let nhi_line_1 = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            100.0,
            75.0,
        );
        let nhi_line_2 = test_data.nhi_invoice_line(
            invoice.id(),
            "86-401-001".to_string(),
            "Category".to_string(),
            1.0,
            50.0,
            100.0,
        );
        let clinic_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);

        let nhi_invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let clinic_invoice_lines_total = 80.0 * 115.0;
        let billing_codes = vec![nhi_line_1.1, nhi_line_2.1];

        let mock_nhi_service = create_mock_nhi_service(0.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![nhi_line_1.0, nhi_line_2.0, clinic_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            0.0
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            clinic_invoice_lines_total * 1.24,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            nhi_invoice_lines_total
        );
        assert_eq!(
            result.total().expect("Should have total"),
            nhi_invoice_lines_total + clinic_invoice_lines_total * 1.24
        );
    }

    // No NHI lines, only clinic lines. Subject should pay everything.
    #[tokio_shared_rt::test]
    async fn general_rule_no_nhi_invoice_lines() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);
        let clinic_invoice_lines_total = 80.0 * 115.0;

        let mock_nhi_service = create_mock_nhi_service(0.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(vec![]);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![invoice_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            0.0
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            clinic_invoice_lines_total * 1.24,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            0.0
        );
        assert_eq!(
            result.total().expect("Should have total"),
            clinic_invoice_lines_total * 1.24
        );
    }

    // Subject is uninsured and should pay the full amount.
    #[tokio_shared_rt::test]
    async fn rule1_uninsured_pay_full_amount() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![
            test_data.nhi_invoice_line(
                invoice.id(),
                "81-001-001".to_string(),
                "Category".to_string(),
                1.0,
                100.0,
                75.0,
            ),
            test_data.nhi_invoice_line(
                invoice.id(),
                "86-401-001".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
        ];
        let invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(0.0, false, "", 0);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            invoice_lines_total
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            invoice_lines_total
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            invoice_lines_total
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            0.0
        );
        assert_eq!(
            result.total().expect("Should have total"),
            invoice_lines_total
        );
    }

    // Invoice has both NHI lines that are billed to subject and NHI lines that are billed to insurance.
    // Subject should pay 90% of the lines that are billed to them. NHI should pay 10% of the lines that are billed to the subject
    // and 100% of the lines that are material cost lines.
    #[tokio_shared_rt::test]
    async fn rule2_subjects_dont_pay_for_some_codes() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![
            test_data.nhi_invoice_line(
                invoice.id(),
                "81-001-001".to_string(),
                "Category".to_string(),
                1.0,
                100.0,
                75.0,
            ),
            test_data.nhi_invoice_line_with_parent(
                invoice.id(),
                "86-401-01A".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
            test_data.nhi_invoice_line_with_parent(
                invoice.id(),
                "86-401-01D".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
            test_data.nhi_invoice_line_with_parent(
                invoice.id(),
                "86-401-01L".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
            test_data.nhi_invoice_line_with_parent(
                invoice.id(),
                "86-401-01S".to_string(),
                "Category".to_string(),
                1.0,
                50.0,
                100.0,
            ),
        ];
        let subject_invoice_lines_total = 100.0 * 75.0;
        let insurance_involice_lines_total = 4.0 * 50.0 * 100.0;
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            subject_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            subject_invoice_lines_total * 0.9
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            subject_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            subject_invoice_lines_total * 0.1 + insurance_involice_lines_total
        );
        assert_eq!(
            result.total().expect("Should have total"),
            subject_invoice_lines_total + insurance_involice_lines_total
        );
    }

    // Subject is a child (has a subject status of "BARN") and the NHI bill only contains Augnlækningar and Kvensjúkdómalækningar billing codes.
    // NHI should pay the whole amount, since children don't pay for these services.
    #[tokio_shared_rt::test]
    async fn rule3_children_dont_pay_for_certain_services() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![
            test_data.nhi_invoice_line(
                invoice.id(),
                "81-001-001".to_string(),
                "Augnlækningar".to_string(),
                1.0,
                100.0,
                75.0,
            ),
            test_data.nhi_invoice_line(
                invoice.id(),
                "86-401-001".to_string(),
                "Kvensjúkdómalækningar".to_string(),
                1.0,
                50.0,
                100.0,
            ),
        ];
        let invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "BARN", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            0.0
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            invoice_lines_total
        );
        assert_eq!(
            result.total().expect("Should have total"),
            invoice_lines_total
        );
    }

    // Subject is a child (has a subject status of "BARN") and they have a referral to a specialist.
    // NHI should pay the whole amount, since children don't pay for specialist services when they have a referral.
    #[tokio_shared_rt::test]
    async fn rule4_children_with_referrals_dont_pay_not_implemented() {
        // This has not yet been implemented, but we should leave this test here as a reminder.
    }

    #[tokio_shared_rt::test]
    async fn rule5_quantity_greater_than_1_should_add_half() {
        // This has temporarily been implemented as adding 0.5 to the invoice line total. This is not entierly correct.
        // This is currently a part of the create_invoice_line method and not the payment participation calculation.
        // We'll leave this here for a future refactor.
    }

    #[tokio_shared_rt::test]
    async fn rule5_quantity_greater_than_1_should_add_three_quarters() {
        // This has temporarily been implemented as adding 0.5 to the invoice line total. This is not entierly correct.
        // This is currently a part of the create_invoice_line method and not the payment participation calculation.
        // We'll leave this here for a future refactor.
    }

    // Invoice has NHI and clinic lines. The Provider has marked the Invoice as "NHI pays all" (nhi_pays_all = true).
    // Subject should only pay for clinic lines. NHI should pay the total of the NHI lines.
    #[tokio_shared_rt::test]
    async fn rule6_nhi_pays_all_flag_is_true() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let invoice = invoice.update(
            UpdateInvoiceInput {
                nhi_pays_all: Some(true),
                ..Default::default()
            },
            test_data.user(),
        );

        let subject = test_data.subject();

        let nhi_line_1 = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            100.0,
            75.0,
        );
        let nhi_line_2 = test_data.nhi_invoice_line(
            invoice.id(),
            "86-401-001".to_string(),
            "Category".to_string(),
            1.0,
            50.0,
            100.0,
        );
        let clinic_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);

        let nhi_invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let clinic_invoice_lines_total = 80.0 * 115.0;
        let billing_codes = vec![nhi_line_1.1, nhi_line_2.1];

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![nhi_line_1.0, nhi_line_2.0, clinic_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            clinic_invoice_lines_total * 1.24,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            nhi_invoice_lines_total
        );
        assert_eq!(
            result.total().expect("Should have total"),
            nhi_invoice_lines_total + clinic_invoice_lines_total * 1.24
        );
    }

    // Invoice has NHI and clinic lines. The Provider has given the subject a discount on the insurance lines that is less than the subject's NHI total.
    // Subject should pay full for clinic lines and get the discounted amount on NHI lines. NHI should pay the normal amount for NHI lines as the discount is given by the clinic.
    #[tokio_shared_rt::test]
    async fn subject_insurance_lines_discount_less_than_total() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let invoice = invoice.update(
            UpdateInvoiceInput {
                subject_discount: Some(5000.0),
                ..Default::default()
            },
            test_data.user(),
        );

        let subject = test_data.subject();

        let nhi_line_1 = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            100.0,
            75.0,
        );
        let nhi_line_2 = test_data.nhi_invoice_line(
            invoice.id(),
            "86-401-001".to_string(),
            "Category".to_string(),
            1.0,
            50.0,
            100.0,
        );
        let clinic_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);

        let nhi_invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let clinic_invoice_lines_total = 80.0 * 115.0;
        let billing_codes = vec![nhi_line_1.1, nhi_line_2.1];

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![nhi_line_1.0, nhi_line_2.0, clinic_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            nhi_invoice_lines_total * 0.9 + clinic_invoice_lines_total * 1.24 - 5000.0,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            nhi_invoice_lines_total * 0.1
        );
        assert_eq!(
            result.total().expect("Should have total"),
            nhi_invoice_lines_total + clinic_invoice_lines_total * 1.24
        );
    }

    // Invoice has NHI and clinic lines. The Provider has given the subject a discount on the insurance lines that is more than the subject's NHI total.
    // Subject should pay full for clinic lines and 0 for NHI lines. The subject_discount should be corrected to the subject's NHI total.
    // NHI should pay the normal amount for NHI lines as the discount is given by the clinic.
    #[tokio_shared_rt::test]
    async fn subject_insurance_lines_discount_more_than_total() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let invoice = invoice.update(
            UpdateInvoiceInput {
                subject_discount: Some(30000.0),
                ..Default::default()
            },
            test_data.user(),
        );

        let subject = test_data.subject();

        let nhi_line_1 = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            100.0,
            75.0,
        );
        let nhi_line_2 = test_data.nhi_invoice_line(
            invoice.id(),
            "86-401-001".to_string(),
            "Category".to_string(),
            1.0,
            50.0,
            100.0,
        );
        let clinic_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);

        let nhi_invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let clinic_invoice_lines_total = 80.0 * 115.0;
        let billing_codes = vec![nhi_line_1.1, nhi_line_2.1];

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![nhi_line_1.0, nhi_line_2.0, clinic_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .subject_discount
                .expect("Should have subject discount"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            clinic_invoice_lines_total * 1.24,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            nhi_invoice_lines_total * 0.1
        );
        assert_eq!(
            result.total().expect("Should have total"),
            nhi_invoice_lines_total + clinic_invoice_lines_total * 1.24
        );
    }

    // Invoice has NHI and clinic lines. The Provider has given the subject a discount on the insurance lines that is equal to the subject's NHI total.
    // Subject should pay full for clinic lines and 0 for NHI lines. NHI should pay the normal amount for NHI lines as the discount is given by the clinic.
    #[tokio_shared_rt::test]
    async fn subject_insurance_lines_discount_equal_to_total() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let invoice = invoice.update(
            UpdateInvoiceInput {
                subject_discount: Some(12500.0),
                ..Default::default()
            },
            test_data.user(),
        );

        let subject = test_data.subject();

        let nhi_line_1 = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            100.0,
            75.0,
        );
        let nhi_line_2 = test_data.nhi_invoice_line(
            invoice.id(),
            "86-401-001".to_string(),
            "Category".to_string(),
            1.0,
            50.0,
            100.0,
        );
        let clinic_line = test_data.clinic_invoice_line(invoice.id(), 1.0, 80.0, 115.0, 24.0);

        let nhi_invoice_lines_total = 100.0 * 75.0 + 50.0 * 100.0;
        let clinic_invoice_lines_total = 80.0 * 115.0;
        let billing_codes = vec![nhi_line_1.1, nhi_line_2.1];

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![nhi_line_1.0, nhi_line_2.0, clinic_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .subject_discount
                .expect("Should have subject discount"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            nhi_invoice_lines_total * 0.9
        );
        assert_eq!(
            result.clinic_payable_by_subject,
            Some(clinic_invoice_lines_total * 1.24)
        );
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            clinic_invoice_lines_total * 1.24,
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            nhi_invoice_lines_total * 0.1
        );
        assert_eq!(
            result.total().expect("Should have total"),
            nhi_invoice_lines_total + clinic_invoice_lines_total * 1.24
        );
    }

    // Invoice has an NHI line and insurance percentage so that the subject should pay 500.5 kr. and NHI should pay 500.5 kr.
    // Amounts should be rounded so that the subject pays 500 kr. and NHI pays 501 kr.
    #[tokio_shared_rt::test]
    async fn rounds_correctly_when_subject_and_insurance_each_pay_half_a_krona() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_line = test_data.nhi_invoice_line(
            invoice.id(),
            "81-001-001".to_string(),
            "Category".to_string(),
            1.0,
            1.0,
            1001.0,
        );

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 50);
        let mock_billing_code_repo = create_mock_billing_code_repo(vec![invoice_line.1]);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                vec![invoice_line.0],
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            500.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            500.0
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            500.0
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            501.0
        );
        assert_eq!(result.total().expect("Should have total"), 1001.0);
    }

    // Helper function to create a standard mock nhi service with configurable parameters
    fn create_mock_nhi_service(
        maximum_payable_by_subject: f64,
        is_insured: bool,
        insurance_category: &str,
        insurance_percentage: i32,
    ) -> Arc<MockNhiService> {
        let subject_insurance_status = if is_insured {
            SubjectInsuranceStatus::Insured {
                insurance_category: insurance_category.to_string(),
                maximum_payable_by_subject,
                insurance_percentage,
                payment_status_serial_number: 1_234_567_890,
            }
        } else {
            SubjectInsuranceStatus::Uninsured
        };

        let mut mock_nhi_service = MockNhiService::new();
        mock_nhi_service
            .expect_get_subject_payment_status()
            .returning(move |_, _| Box::pin(future::ready(Ok(subject_insurance_status.clone()))));
        Arc::new(mock_nhi_service)
    }

    // Helper function to create a mock billing code repository
    fn create_mock_billing_code_repo(codes: Vec<BillingCodeNhi>) -> MockIBillingCodeNhiRepo {
        let mut mock_repo = MockIBillingCodeNhiRepo::new();

        mock_repo.expect_get().returning(move |id| {
            Ok(codes
                .iter()
                .find(|code| code.id() == id)
                .cloned()
                .expect("Didn't find invoice line in billing code repo mock"))
        });

        mock_repo
    }

    // NHI pays full for telemedicine code FT-001-01, subject pays 0
    #[tokio_shared_rt::test]
    async fn rule_telemedicine_code_ft_001_01_nhi_pays_full() {
        // arrange
        let test_data = UnitTestDataBuilder::new();
        let invoice = test_data.invoice();
        let subject = test_data.subject();

        let invoice_lines = vec![test_data.nhi_invoice_line(
            invoice.id(),
            "FT-001-01".to_string(),
            "Telemedicine".to_string(),
            1.0,
            7.0,
            1000.0,
        )];
        let billing_codes = invoice_lines.iter().map(|line| line.1.clone()).collect();

        let mock_nhi_service = create_mock_nhi_service(30000.0, true, "ALM", 10);
        let mock_billing_code_repo = create_mock_billing_code_repo(billing_codes);
        let calculator = NhiInvoiceTotalCalculator::new(mock_nhi_service, "fake_token".to_string());

        // act
        let result = calculator
            .update_invoice_totals(
                &mock_billing_code_repo,
                test_data.user(),
                &subject,
                invoice.clone(),
                invoice_lines.into_iter().map(|line| line.0).collect(),
            )
            .await
            .expect("Failed to update invoice totals");

        // assert
        assert_eq!(
            result
                .nhi_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_subject_before_discount
                .expect("Should have total_payable_by_subject_before_discount"),
            0.0
        );
        assert_eq!(result.clinic_payable_by_subject, Some(0.0));
        assert_eq!(
            result
                .total_payable_by_subject
                .expect("Should have total_payable_by_subject"),
            0.0
        );
        assert_eq!(
            result
                .total_payable_by_insurance
                .expect("Should have total_payable_by_insurance"),
            7000.0
        );
        assert_eq!(result.total().expect("Should have total"), 7000.0);
    }
}
