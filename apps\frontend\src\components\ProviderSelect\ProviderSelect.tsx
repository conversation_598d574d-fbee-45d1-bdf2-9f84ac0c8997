import { startTransition, useState, useEffect } from "react"
import { useTranslation } from "react-i18next"

import { ComboboxProvider, SelectLabel } from "components/Ariakit"
import {
  Select,
  SelectArrow,
  SelectProvider,
} from "components/Ariakit/Select/Select"
import Icon from "components/Icon/Icon"
import MenuListItem from "components/MenuListItem/MenuListItem"
import { Button, Text } from "ui"

import { useGetProviderQuery } from "generated/graphql"

import styles from "./ProviderSelect.module.css"
import { ProviderSelectPopover } from "./ProviderSelectPopover"

type ProviderSelectProps = {
  required?: boolean
  name?: string
  placeholder?: string
  providerId?: string
  onChange?: (value: string | null) => void
  isClearable?: boolean
}
export default function ProviderSelect({
  required,
  name = "provider",
  providerId,
  onChange,
  placeholder = "Select provider",
  isClearable = false,
}: ProviderSelectProps) {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })
  const { t } = useTranslation()
  const [searchValue, setSearchValue] = useState("")

  const [selectedProviderId, setSelectedProviderId] = useState<
    string | undefined
  >(providerId)

  useEffect(() => {
    setSelectedProviderId(providerId)
  }, [providerId])

  const handleSelectValue = (value: string) => {
    onChange?.(value)

    if (!value && !required) {
      setSelectedProviderId(undefined)
      return
    }

    setSelectedProviderId(value)
  }

  const handleClear = () => {
    setSelectedProviderId(undefined)
    onChange?.(null)
  }

  const { data } = useGetProviderQuery({
    variables: selectedProviderId ? { userId: selectedProviderId } : undefined,
    skip: !selectedProviderId,
  })

  const selectedProvider = data?.user
  const showClearButton = isClearable && !!selectedProviderId

  return (
    <ComboboxProvider
      resetValueOnHide
      setValue={(value) => {
        startTransition(() => {
          setSearchValue(value)
        })
      }}
    >
      <SelectProvider value={selectedProviderId} setValue={handleSelectValue}>
        <SelectLabel>
          {t("Provider")}
          {required && " *"}
        </SelectLabel>
        <div className={styles.selectContainer}>
          <Select required={required} name={name}>
            <MenuListItem
              className={styles.menuListItem}
              subContent={
                selectedProvider?.specialty
                  ? tEnum(selectedProvider.specialty)
                  : undefined
              }
            >
              {selectedProvider?.name ? (
                selectedProvider.name
              ) : (
                <Text secondary>{placeholder}</Text>
              )}
            </MenuListItem>
            <SelectArrow />
          </Select>
          {showClearButton && (
            <Button
              icon={<Icon name="close-line" />}
              className={styles.clearButton}
              onClick={handleClear}
              variant="clear"
            />
          )}
        </div>

        <ProviderSelectPopover searchValue={searchValue} />
      </SelectProvider>
    </ComboboxProvider>
  )
}
