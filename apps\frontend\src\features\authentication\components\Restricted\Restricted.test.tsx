import { screen } from "@testing-library/react"
import { render } from "test/testUtils"
import { vi, <PERSON><PERSON> } from "vitest"

import usePermissions from "features/authentication/hooks/usePermissions"

import { PermissionKey } from "generated/graphql"

import Restricted, { RestrictedProps } from "./Restricted"

const mockedUsePermissions = usePermissions as Mock

// mock the usePermissions hook
vi.mock("features/authentication/hooks/usePermissions")

describe("Restricted Component", () => {
  const props: RestrictedProps = {
    to: PermissionKey.SubjectJournalInterventionPeriodEdit,
    children: <div>Restricted content</div>,
    fallback: <div>Unauthorized</div>,
  }

  beforeEach(() => {
    mockedUsePermissions.mockReturnValue({
      hasAllPermissions: vi.fn().mockReturnValue(true),
      hasSomePermissions: vi.fn().mockReturnValue(true),
      hasPermission: vi.fn().mockReturnValue(true),
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it("should render the children when the user has the required permission", () => {
    render(<Restricted {...props} />)
    expect(screen.getByText("Restricted content")).toBeInTheDocument()
  })

  it("should render the fallback when the user does not have the required permission", () => {
    mockedUsePermissions.mockReturnValue({
      hasAllPermissions: vi.fn().mockReturnValue(false),
      hasSomePermissions: vi.fn().mockReturnValue(false),
      hasPermission: vi.fn().mockReturnValue(false),
    })

    render(<Restricted {...props} />)
    expect(screen.getByText("Unauthorized")).toBeInTheDocument()
  })

  it("should render the children when the user has at least one of the required permissions (toAny)", () => {
    const toAnyProps: RestrictedProps = {
      toAny: [
        PermissionKey.AccountsProviderCreate,
        PermissionKey.AccountsProviderInvite,
      ],
      children: <div>Restricted content</div>,
      fallback: <div>Unauthorized</div>,
    }

    render(<Restricted {...toAnyProps} />)
    expect(screen.getByText("Restricted content")).toBeInTheDocument()
  })

  it("should render the fallback when the user does not have any of the required permissions (toAny)", () => {
    const toAnyProps: RestrictedProps = {
      toAny: [
        PermissionKey.SubjectJournalInterventionPeriodEdit,
        PermissionKey.SubjectJournalSign,
      ],
      children: <div>Restricted content</div>,
      fallback: <div>Unauthorized</div>,
    }

    mockedUsePermissions.mockReturnValue({
      hasAllPermissions: vi.fn().mockReturnValue(false),
      hasSomePermissions: vi.fn().mockReturnValue(false),
      hasPermission: vi.fn().mockReturnValue(false),
    })

    render(<Restricted {...toAnyProps} />)
    expect(screen.getByText("Unauthorized")).toBeInTheDocument()
  })
})
